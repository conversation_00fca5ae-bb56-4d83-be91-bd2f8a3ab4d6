import type { BraftEditorProps } from 'braft-editor';
import BraftEditor from 'braft-editor';
import 'braft-editor/dist/index.css';
import React, { useCallback, useEffect, useState } from 'react';
import Table from 'braft-extensions/dist/table';
import styles from './styles.less';
import 'braft-extensions/dist/table.css';
import { message, Upload } from 'antd';
import { CameraOutlined } from '@ant-design/icons';
import { ContentUtils } from 'braft-utils';
import type { RcFile } from 'antd/lib/upload';
import { uploadImage } from '@/services/common';
import type { UploadProps } from 'antd';
import { omit } from 'lodash';

const options = {
  defaultColumns: 3, // 默认列数
  defaultRows: 3, // 默认行数
  withDropdown: true, // 插入表格前是否弹出下拉菜单
  exportAttrString: '', // 指定输出HTML时附加到table标签上的属性字符串
};

BraftEditor.use(Table(options));

export type BraftEditorFormProps = {
  value?: string;
  onChange?: (data: string) => void;
} & BraftEditorProps;
const BraftEditorForm = (props: BraftEditorFormProps) => {
  const { value, onChange, ...rest } = props;
  const [content, setContent] = useState<any>();
  const handleChange = useCallback(
    (editorState) => {
      if (onChange) {
        onChange(editorState.toHTML());
      }
    },
    [content],
  );
  const setValue = useCallback(() => {
    if (value) {
      if (!content || value !== content.toHTML()) {
        setContent(BraftEditor.createEditorState(value));
      }
    }
  }, [value]);
  useEffect(() => {
    setValue();
  }, [value]);
  useEffect(() => {
    if (content) {
      handleChange(content);
    }
  }, [content]);

  const uploadHandler: UploadProps['customRequest'] = (params) => {
    if (!params.file) {
      return false;
    }
    const file = params.file as RcFile;
    const uploadFilename = file.name;
    const reader = new FileReader();
    reader.readAsArrayBuffer(file); // 读取图片文件
    reader.onload = async (e) => {
      const res = await uploadImage(uploadFilename, e.target?.result);
      if (res.status.success) {
        const newStatus = ContentUtils.insertMedias(BraftEditor.createEditorState(content), [
          {
            type: 'IMAGE',
            url: res.body.link,
          },
        ]);
        setContent(newStatus);
      } else {
        message.error('上传失败');
      }
    };
    reader.onerror = () => {
      message.error('上传失败');
    };
    return true;
  };

  const extendControls = [
    {
      key: 'antd-uploader',
      type: 'component',
      component: (
        <Upload accept="image/*" showUploadList={false} customRequest={uploadHandler}>
          {/* 这里的按钮最好加上type="button"，以避免在表单容器中触发表单提交，用Antd的Button组件则无需如此 */}
          <button type="button" className="control-item button upload-button" data-title="插入图片">
            <CameraOutlined />
          </button>
        </Upload>
      ) as React.ReactNode,
    },
  ] as unknown as any;

  return (
    <BraftEditor
      {...omit(rest, 'extendControls')}
      controls={[
        'undo',
        'redo',
        'separator',
        'font-size',
        'line-height',
        // 'letter-spacing',
        'separator',
        'text-color',
        'bold',
        'italic',
        'underline',
        'strike-through',
        'separator',
        // 'text-indent',
        // 'text-align',
        'separator',
        'list-ul',
        'list-ol',
        'hr',
        'table',
      ]}
      extendControls={extendControls}
      value={content}
      className={styles.bf}
      onChange={setContent}
    />
  );
};

export default BraftEditorForm;
