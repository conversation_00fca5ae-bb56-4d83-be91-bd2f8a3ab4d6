/**
 * 采购单1688平台状态
 */
export enum PlatformStatusEnum  {
  'waitbuyerpay'= '等待买家付款',
  'waitsellersend'= '等待卖家发货',
  'waitbuyerreceive'='等待买家收货',
  'confirm_goods_but_not_fund'='等待买家还款',
  'confirm_goods'= '已收货',
  'success'='交易成功',
  'cancel'='交易取消',
  'terminated'='交易终止',
};

export enum payStatusEnum {
  '-10'= '无需付款',
  '0'= '初始未付',
  '10'= '申请付定金',
  '20'= '申请付余额',
  '30'= '申请付全款',
  '35'= '账期待付款',
  '40'= '付款进行中',
  '45'= '账期待还款',
  '50'= '驳回',
  '60'= '已付定金',
  '70'= '已付全款'
}

/**
 * 采购单erp状态
 */
export enum OrderStatusEnum  {
  '0'= '草稿',
  '10'= '异常',
  '20'='已下单',
  '30'= '待付款',
  '40'='已付款',
  '45'='部分到货',
  '50'='全部到货',
  '55'='不等待剩余',
  '60'='已上架',
  '70'='作废',
};

export enum OrderStatusTagsEnum  {
  '0'= 'processing',
  '10'= 'error',
  '20'='success',
  '30'= 'warning',
  '40'='success',
  '45'='warning',
  '50'='success',
  '55'='warning',
  '60'='success',
  '70'='error',
};



export enum PayTypeEnum {
  "1"= '银行转账',
  "2"= '现金支付',
  "3"= '支付宝',
  "4"= '余额抵充',
  "5"= '跨境宝',
  "6"= '超级支付宝'
};

/**
 * 采购审核状态
 */
export enum PurchaseAuditStatusEnum  {
  'wait'= '待审核',
  'pass'= '通过',
  'reject'='驳回'
};

/**
 * 采购单付款状态
 */
export enum PaymentStatusEnum {
  '-10' = '无需付款',
  '0'= '初始未付',
  '10'='申请付定金',
  '20'= '申请付余额',
  '30'='申请付全款',
  '35'= '账期待付款',
  '40'='付款进行中',
  '45'='账期待还款',
  '50'='驳回',
  '60'='已付定金',
  '70'='已付全款'
};

export enum payTypeEnum {
  '1'= "银行转账",
  '2'= "现金支付",
  '3'=  "支付宝",
  '4'= "余额抵充",
  '5'= "跨境宝",
  '6'= "超级支付宝",
}

export enum financeErrorTypeEnum  {
  'discounts'= '申请优惠',
  'lageOrderAmount'='大额审核',
  'checkPrice'='价格审核',
  'sumError'='金额异常',
}
export enum alibabaStatusTextEnum  {
  'waitbuyerpay'= '等待买家付款',
  'waitsellersend'='等待卖家发货',
  'waitbuyerreceive'='等待买家收货',
  'confirm_goods'='已收货',
  'success'='交易成功',
  'cancel'='交易取消',
  'terminated'='交易终止',
}

export enum PurchaseOrderAuditStatusText {
  'WAIT' = '待审核',
  'PASS' = '通过',
  'REJECT' = '驳回',
}

export enum PurchaseOrderAuditStatus {
  'WAIT' = 'wait',
  'PASS' = 'pass',
  'REJECT' = 'reject',
}
