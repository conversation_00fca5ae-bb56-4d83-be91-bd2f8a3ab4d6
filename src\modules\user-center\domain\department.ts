export interface Department {
	code: string;
	deptId: string;
	description: string;
	isLeaf: boolean;
	level: number;
	name: string;
	owner: string;
	parentId: string;
	sequence: number;
	state: string;
	treePath: string;
}


export interface DepartmentTree {
	children: DepartmentTree[];
	code: string;
	deptId: string;
	description: string;
	isLeaf: boolean;
	level: number;
	name: string;
	owner: string;
	parentId: string;
	sequence: number;
	state: string;
	treePath: string;
}
