import React from 'react';
import type {ProColumns} from '@ant-design/pro-components';
import {
  Modal,
  ModalProps,
  Card,
  Table,
  Image,
  Descriptions
} from 'antd';
import {PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";
import {ArrowDownOutlined, ArrowUpOutlined} from "@ant-design/icons";
import {PurchaseAfterSales} from "@/pages/purchaseAfterSales/data";
import {useRequest} from "ahooks";
import {getAfterSalesOrderDetail} from "@/modules/purchaseOrder/infra/api/purchaseAfterSales";
import moment from "moment";

// 定义参数格式
export type params = {
  order: PurchaseAfterSales;
  onFinish: () => void;
} & ModalProps;

export default (props: params) => {
  const { onFinish, order, ...rest } = props;
  const {data} = useRequest(() => getAfterSalesOrderDetail(order.afterSalesOrderId).then((res) => res.body));


  const columns: ProColumns<PurchaseOrderGoods>[] = [
    // {
    //   title: "图片",
    //   dataIndex: 'skuImg',
    //   readonly: true,
    //   render: (v, record) => {
    //     return <Image src={record?.skuImg} width={80}/>
    //   }
    // },
    {
      title: 'SKU',
      dataIndex: 'sku',
      readonly: true,
      render: (v, record) => {
        return <>{record?.sku}</>
      },
    },
    {
      title: '商品',
      dataIndex: 'sku',
      readonly: true,
      width: 200,
      render: (v, record) => {
        return <>{record?.name}</>
      },
    },
    {
      title: "上次采购价",
      dataIndex: 'lastPurchasePrice',
      render: (v, record) => {
        return Number(record.lastPurchasePrice)?.toFixed(2);
      }
    },
    {
      title: "当前采购价",
      dataIndex: 'currentPurchasePrice',
      render: (v, record) => {
        return (
          <>
            <span>{Number(record.currentPurchasePrice)?.toFixed(2) }&nbsp;</span>
            <span>{record.lastPurchasePrice > record.currentPurchasePrice ? (
              <ArrowDownOutlined style={{ color: 'green' }} />
            ) : record.lastPurchasePrice < record.currentPurchasePrice ? (
              <ArrowUpOutlined style={{ color: 'red' }} />
            ): null}
            </span>
          </>
        )
      }
    },
    {
      title: '平台价格',
      dataIndex: 'platformPurchasePrice',
      render: (v, record) => {
        return record?.platformPurchasePrice ? Number(record.platformPurchasePrice)?.toFixed(2): "--";
      }
    },
    {
      title: '采购数量',
      dataIndex: 'platformPurchasePrice',
      render: (v, record) => {
        return record?.purchaseQuantity;
      }
    },
    // {
    //   title: '实收数量',
    //   dataIndex: 'platformPurchasePrice',
    //   render: (v, record) => {
    //     return record?.arrivalQuantity;
    //   }
    // },
    {
      title: '申请数量',
      dataIndex: 'afterSalesQuantity',
      render: (v, record) => {
        return record?.afterSalesQuantity;
      }
    },
  ];

  return (
    <>
      <Modal {...rest} title="采购单售后详情" width={"60%"} onOk={onFinish}>
        <Card bordered={false} bodyStyle={{paddingTop: 0, paddingBottom: 0}}>
          <Descriptions column={4} style={{fontWeight:"bolds"}} labelStyle={{fontWeight:"bold"}} >
            <Descriptions.Item label="标题">{data?.title}</Descriptions.Item>
            <Descriptions.Item label="售后单号">{data?.code}</Descriptions.Item>
            <Descriptions.Item label="采购单号">{data?.purchaseOrderCode || '--'}</Descriptions.Item>
            <Descriptions.Item label="平台单号">{data?.platformOrderCode || '--'}</Descriptions.Item>

            <Descriptions.Item label="售后类型">{data?.afterSalesType}</Descriptions.Item>
            <Descriptions.Item label="申请人">{data?.applyUsername}</Descriptions.Item>
            <Descriptions.Item label="状态">{data?.afterSalesStatus}</Descriptions.Item>
            <Descriptions.Item label="申请时间">{moment(data?.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}</Descriptions.Item>

            <Descriptions.Item label="实收金额">{data?.amountReceived?.toFixed(2) || '--'}</Descriptions.Item>
            <Descriptions.Item label="申请金额">{data?.applyAmount?.toFixed(2) || '--'}</Descriptions.Item>
            <Descriptions.Item label="商品金额">{data?.applyGoodsAmount?.toFixed(2) || '--'}</Descriptions.Item>
            <Descriptions.Item label="申请运费">{data?.applyShippingFee?.toFixed(2) || '--'}</Descriptions.Item>

            <Descriptions.Item label="备注">{data?.remark}</Descriptions.Item>
          </Descriptions>
        </Card>
        <Card bordered={false}>
          <Table<PurchaseOrderGoods>
            columns={columns}
            dataSource={data?.orderGoods}
            rowKey="id"
            size={"small"}

            pagination={false}
          />
        </Card>
      </Modal>
    </>
  );
};
