import type { CheckboxGroupProps } from 'antd/lib/checkbox';
import type { InputProps, SelectProps } from "antd";
import { AutoComplete, Checkbox, Input, Row, Select } from "antd";
import type { CheckboxOptionType } from 'antd/es';
import styles from './styles.less'
import { CSSProperties, useEffect } from 'react';
import cn from 'classnames';
import { useControllableValue } from 'ahooks';
import { Access } from 'umi';

const CheckboxButton = (props: CheckboxGroupProps) => {
  const { options, ...rest } = props;
  useEffect(() => {
    if (props?.defaultValue) {
      props?.onChange?.(props.defaultValue)
    }
  }, [])
  return <Checkbox.Group className={styles.checkbox__button} {...rest} defaultValue={[0]} onChange={(v) => {
    props.onChange?.(v.slice(-1));
  }}>
    {options?.map((item) => {
      return <Checkbox key={(item as CheckboxOptionType).value?.toString()}
        value={(item as CheckboxOptionType).value}
        className={cn({ current: props.value?.includes((item as CheckboxOptionType).value) })}
      >
        {(item as CheckboxOptionType).label}
      </Checkbox>
    })}
  </Checkbox.Group>
}


const SupplierSelect = (props: InputProps) => {
  const [value, onChange] = useControllableValue(props);

  return <Input.Group>
    <Row align='middle'>
      <AutoComplete style={{ width: 'calc(100% - 32px)' }} value={value} onChange={onChange} />
      <a style={{ marginLeft: 4 }}>选择</a>
    </Row>
  </Input.Group>
}




export type SelectGroupRenderFormItemProps = {
  value: string | number | string[] | number[];
  onChange?: (value: string | number | string[] | number[]) => void;
  placeholder?: string | string[];
  style?: CSSProperties;
};

export type SelectGroupProps = {
  options: SelectProps['options'],
  defaultValue: SelectProps['defaultValue'];
  placeholder?: string | string[];
  value?: [string, string];
  onChange?: (value?: [string, string]) => void;
  renderFormItem?: (props: SelectGroupRenderFormItemProps) => React.ReactNode;
}
const SelectGroup = (props: SelectGroupProps) => {
  const { placeholder, defaultValue, renderFormItem } = props;
  const [value, setValue] = useControllableValue(props);
  useEffect(() => {
    setValue([defaultValue, ''])
  }, []);
  const formProps: SelectGroupRenderFormItemProps = {
    value: value?.[1],
    style: { width: '60%'},
    placeholder,
    onChange: (v: any) => {
      setValue([value?.[0], v])
    }
  }
  return <Input.Group compact>
    <Select style={{width: '40%'}} value={value?.[0]} defaultValue={defaultValue} options={props.options} onChange={(v) => setValue([v, value?.[1]])} />
    <Access accessible={!renderFormItem} fallback={renderFormItem?.(formProps)}>
      <Input {...formProps as InputProps} onChange={(e) => setValue([value?.[0], e.target.value])} />
    </Access>
  </Input.Group>
}



export type SelectInputProps = Omit<SelectGroupProps, 'renderFormItem'>;
const SelectInput = (props: SelectInputProps) => {
  return <SelectGroup {...props} />
}


export default {
  CheckboxButton,
  SupplierSelect,
  SelectInput,
  SelectGroup
};
