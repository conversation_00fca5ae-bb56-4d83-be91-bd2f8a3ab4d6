import { ProFormSelect } from '@ant-design/pro-form';
import { Form, Modal, ModalProps } from 'antd';
import styles from './index.less';
type Item = {
  value?: string;
  label?: string;
};
// 定义参数格式
export type CreateModalProps = {
  open?: boolean;
  options?: Item[];
  title?: string;
  name?: string;
  label?: string;
  tips?: string;
  onFinish?: (values: any) => void;
} & ModalProps;

const Index = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { title, label, name, options,tips = '', onFinish, ...rest } = props;

  return (
    <Modal title={title} width={380} onOk={() => form.submit()} {...rest}>
      <Form form={form} onFinish={onFinish}>
        <ProFormSelect width="md" name={name} showSearch={true} label={label} options={options} />
      </Form>
      { tips &&  <div className={styles.tips}>{ tips }</div>}
    </Modal>
  );
};

export default Index;
