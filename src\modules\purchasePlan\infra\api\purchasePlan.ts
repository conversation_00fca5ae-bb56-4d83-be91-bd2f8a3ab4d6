import mallRequest from '@/utils/mallRequest';
import mallRequest1 from '@/utils/mallRequest1';
import mallApiConfig from 'config/mallApiConfig';
import { PurchasePlan, PurchasePlanGoods, PurchasePlanLog } from '@/pages/PurchasePlanList/data';
import { PurchasePlanListItem } from '@/pages/PurchasePlanList/PurchasePlanTableList/data';

//分页search参数
export type PageQueryParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
} & API.QueryPageParams;

/***
 * 采购计划主表页面展示
 * @param data
 */
export async function pageQuery(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<PurchasePlan[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/plan/pageQuery',
      data,
    },
  );
}

export type CreatePlanParmas = {
  remark?: string;
  warehouseName?: string;
  importExcelUrl?: string;
} & API.QueryPageParams;

/**
 * 创建采购计划
 */
export async function createPurchasePlan(data?: CreatePlanParmas) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/create',
    data,
  });
}

/**
 * 采购计划销售审核页面获取
 */
export type PageQueryPlanGoodsParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
  channelOrderNo?: string;
  orderState?: string;
  planId?: string;
} & API.QueryPageParams;
export async function pageQueryPlanDetail(data?: PageQueryPlanGoodsParams) {
  return mallRequest<API.ApiQueryPageResult<PurchasePlanGoods[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/getPlanGoods',
      data,
    },
  );
}

export async function createPlanGoods(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/create',
      data,
    },
  );
}

/**
 * 采购计划采购审核页面获取
 * @param data
 */
export async function pageQueryPlanDetailCheck(data?: PageQueryPlanGoodsParams) {
  return mallRequest<API.ApiQueryPageResult<PurchasePlanListItem[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/purchasePageQuery',
      data,
    },
  );
}

/**
 * 查询采购计划日志
 */
export type queryPurchasePlanLogParams = {
  planId: string;
  planGoodsId: string;
};
export async function queryPurchasePlanLogs(data?: queryPurchasePlanLogParams) {
  return mallRequest<API.ApiQueryPageResult<PurchasePlanLog>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/plan/pageQueryPlanLogs',
      data,
    },
  );
}

/**
 * 仓库获取
 */
export async function getWarehouse() {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/getWarehouse',
  });
}

/**
 * 获取采购计划内的供应商列表
 */
export async function getPlanSupplier(ids: string[]) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'get',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/getPlanSupplier',
    params:{
      ids
    }
  });
}

/**
 * 修改采购计划商品状态
 */
export type updatePlanGoodsStatus = {
  ids: number[];
  status: string;
};
export async function updatePlanStatus(data?: updatePlanGoodsStatus) {
  return mallRequest<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/updateStatus',
    data,
  });
}

/**
 * 修改采购计划商品价格
 */
export type updatePlanGoodsPrice = {
  purchasePlanId: number;
  sku: string;
  price: string;
};
export async function updateSkuPrice(data?: updatePlanGoodsPrice) {
  return mallRequest<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/updateSkuPrice',
    data,
  });
}

/**
 * 修改采购计划商品价格
 */
export type updatePlanGoodsQuantity = {
  purchasePlanId: number;
  sku: string;
  purchaseQuantity: number;
};
export async function updateSkuQuantity(data?: updatePlanGoodsQuantity) {
  return mallRequest<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/updateSkuQuantity',
    data,
  });
}

/**
 * 修改采购计划商品是否手功单
 */
export type markManualOrUrgentParams = {
  purchasePlanId: number;
  sku: string;
  isUrgent?: number;
  isManual?: number;
};

export async function markPlanGoodsIsManual(data?: markManualOrUrgentParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/markManualOrUrgent',
    data,
  });
}

export async function markPlanGoodsIsUrgent(data?: markManualOrUrgentParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/markManualOrUrgent',
    data,
  });
}

export async function markPlanGoodsNeedQc(data?: markManualOrUrgentParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/markManualOrUrgent',
    data,
  });
}

//导入采购计划商品
export async function importPlanGoods(data?: CreatePlanParmas) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/import',
    data,
  });
}

export async function updateSupplier(data?: any) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/updateSupplier',
    data,
  });
}

export async function planAnalysis(planId?: string) {
  return mallRequest<API.ApiBaseResult<any>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/goods/planAnalysis',
    params:{
      planId
    },
  });
}



export async function createOrder(data?: any) {
  return mallRequest<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/create',
    data,
  });
}

//下载导入模板
export async function downloadPurchasePlanTemplate() {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'GET',
    requestPath: '/purchase-mgmt-biz/purchase-center/plan/downloadPurchasePlanTemplate',
    responseType: 'blob',
  });
}

