import React, { useEffect, useState } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import CreatePurchasePlanModal from './components/CreatePurchasePlanModal';
import {Button, Modal, notification, Space} from "antd";
import {useParams} from "umi";
import {PurchaesPlanGoods} from "@/pages/PurchasePlanList/data";
import {useRequestTable} from "@/hooks/useRequestTable";
import {pageQueryPlanDetail, updatePlanGoodsStatus, updatePlanStatus} from "@/modules/purchasePlan/infra/api/purchasePlan";
import {PlanState, PlanStateText,PlanGoodsStatus,PlanGoodsStatusText} from "@/modules/purchasePlan/domain/purchasePlan";
import TableTabs, {TabsItem} from "@/components/TableTabs";


const TableList: React.FC = () => {
  const { id: orderId } = useParams<{ id: string }>();
  const [selectedRowsState, setSelectedRows] = useState<PurchaesPlanGoods[]>([]);
  const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [auditStatus,setStatus] =useState<updatePlanGoodsStatus>()


  const { fetchList, actionRef } = useRequestTable((params) => {
    return pageQueryPlanDetail({
      ...params,
      purchasePlanId:orderId,
      status: activeStatusKey === '' ? undefined : activeStatusKey,
    });
  });

  const audit = (values: PurchaesPlanGoods[],status: String) => {
    const param = {
      ids: [],
    };
    values.map((item) => {
      param.ids.push(item.id);
    })
    if(param.ids.length == 0){
      notification.error({message: '请勾选采购计划'});
      return false;
    }
    param.status = status;
    Modal.confirm({
      title: '确认此操作吗',
      onOk: async () => {
        updatePlanStatus(param).then((result)=>{
          if (result.status.success){
            notification.success({message: '操作成功'});
            actionRef.current?.reloadAndRest?.();
          }
        });
      },
    });
  }

  const columns: ProColumns<PurchaesPlanGoods>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
    },
    {
      title: '销售仓库',
      dataIndex: 'salesWarehouse',
    },
    {
      title: '需求数量',
      dataIndex: 'purchaseQuantity',
      hideInSearch: true,
    },
    {
      title: '商品单价',
      dataIndex: 'purchasePrice',
      hideInSearch: true,
    },
    {
      title: '是否加急',
      dataIndex: 'isUrgent',
      valueEnum: {
          true : {
            text:'是',
            status: 'Error'
          },
          false: {
            text:'否',
            status: 'Success'
          }
      }
    },
    {
      title: '商品备注',
      dataIndex: 'goodsRemark',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        [PlanGoodsStatus.PENDING]: {
          text: PlanGoodsStatusText.PENDING,
          status: 'Info',
        }, [PlanGoodsStatus.WAIT]: {
          text: PlanGoodsStatusText.WAIT,
          status: 'Warning',
        },[PlanGoodsStatus.CREATED]: {
          text: PlanGoodsStatusText.CREATED,
          status: 'Success',
        },[PlanGoodsStatus.ABOLISH]: {
          text: PlanGoodsStatusText.ABOLISH,
          status: 'Error',
        },[PlanGoodsStatus.PASS]: {
          text: PlanGoodsStatusText.PASS,
          status: 'Success',
        },
      },
    }
  ];

  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [activeStatusKey]);

  const tabs: TabsItem[] = [
    { key: '', tab: '全部' },
    { key: PlanGoodsStatus.PENDING, tab: '待审核' },
    { key: PlanGoodsStatus.WAIT, tab: '已挂起' },
    { key: PlanGoodsStatus.ABOLISH, tab: '作废' },
    { key: PlanGoodsStatus.PASS, tab: '已审核' },
    { key: PlanGoodsStatus.CREATED, tab: '已生成'}
  ];
  return (
    <>
      <CustomPage <PurchaesPlanGoods>
        actionRef={actionRef}
        rowKey="id"
        request={fetchList}
        columns={columns}
        toolBarRender={() => [
          <Button key="level" size={"small"} style={{fontSize: 13,borderRadius:"5px"}} type="primary" onClick={() => audit(selectedRowsState,PlanGoodsStatus.WAIT)}>
            批量挂起
          </Button>,
          <Button key="level" size={"small"} style={{fontSize: 13,borderRadius:"5px"}} type="primary" onClick={() => audit(selectedRowsState,PlanGoodsStatus.PASS)}>
            审核通过
          </Button>,
          <Button key="level" size={"small"} style={{fontSize: 13,borderRadius:"5px"}} type="primary" onClick={() => audit(selectedRowsState,PlanGoodsStatus.ABOLISH)}>
            审核作废
          </Button>,
        ]}
        tableRender={(tableProps, defaultDom) => {
          return (
            <TableTabs tabs={tabs} activeKey={activeStatusKey} onTabClick={setActiveStatusKey} defaultActiveKey={PlanGoodsStatus.PENDING}>
              {defaultDom}
            </TableTabs>
          );
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <CreatePurchasePlanModal visible={modalVisible} onCancel={() => setModalVisible(false)}/>
    </>
  );
};

export default TableList;
