import React, {useEffect} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import moment from "moment";
import {pageQuerySignList} from "@/modules/warehouse/infra/api/warehouse";

const TableList: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQuerySignList({
      ...params,
    });
  });

  const columns: ProColumns<any>[] = [
    {
      title: '快递单号',
      dataIndex: 'trackingNumber',
      align:"center",
      render: (v,record)=>{
        return <span>{record?.trackingNumber}</span>;
      }
    },
    {
      title: '采购单号',
      dataIndex: 'purchaseOrderCode',
      align:"center",
      render: (v,record)=>{
        return <span>{record?.purchaseOrderCode || '--'}</span>;
      }
    },
    {
      title: '扫描人',
      dataIndex: 'operatorUsername',
      align:"center",
      render: (v,record)=>{
        return <span>{record?.operatorUsername}</span>;
      }
    },
    {
      title: '扫描时间',
      dataIndex: 'gmtModified',
      hideInSearch: true,
      align:"center",
      render: (v,record)=>{
        return <span>{moment(record.gmtModified as number).format("YYYY-MM-DD HH:mm")}</span>;
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);
  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        size={"small"}
        columns={columns}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;
