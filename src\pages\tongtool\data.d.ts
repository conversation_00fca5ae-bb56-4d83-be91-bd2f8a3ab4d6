export type TongtoolGoodsStock = {
    "availableStockQuantity": number,
    "cargoSpace": string,
    "companyId": number,
    "createTime": number,
    "defectsStockQuantity": number,
    "goodsAvgCost": number,
    "goodsCurCost": number,
    "goodsIdKey": string,
    "goodsSku": string,
    "intransitStockQuantity": number,
    "safetyStock": number,
    "updateTime": number,
    "waitingShipmentStockQuantity": number,
    "warehouseIdKey": string,
    "warehouseName": string
}


export type TongtoolGoodsSales = {
  "availableStockQuantity": number,
  "cargoSpace": string,
  "companyId": number,
  "createTime": number,
  "defectsStockQuantity": number,
  "goodsAvgCost": number,
  "goodsCurCost": number,
  "goodsIdKey": string,
  "goodsSku": string,
  "intransitStockQuantity": number,
  "safetyStock": number,
  "updateTime": number,
  "waitingShipmentStockQuantity": number,
  "warehouseIdKey": string,
  "warehouseName": string
}
