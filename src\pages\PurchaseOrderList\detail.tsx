import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card, Col,
  Descriptions,
  Divider,
  Form,
  message,
  Modal,
  notification,
  Row,
  Space,
  Spin,
  Steps,
  Tabs,
  DatePicker, Tag
} from "antd";
import { ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { useParams } from "umi";
import styles from "@/pages/PurchaseOrderList/styles.less";
import TabPane from "@ant-design/pro-card/es/components/TabPane";
import { useRequest } from "ahooks";
import { PlusSquareTwoTone, SnippetsTwoTone, } from "@ant-design/icons";
import {
  addPurchaseTrackingNumber,
  applyPayment, applyPeriodSettle,
  applyReducePayment,
  bundledAlibabaOrder,
  bundledOrderCode, cancelFinanceAudit,
  cancelTradeOrder,
  exportPurchaseContract,
  getOrderDetail, getOrderGoods,
  getTradeOrder,
  publicFlagState,
  updateOrder,
  updateOrderMemo,
  updateOrderStatus, updateOrderTitle
} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import PurchaseOrderGoodsModal from "@/pages/PurchaseOrderList/components/PurchaseOrderGoodsModal";
import BundledAlibabaIdModal from "@/pages/PurchaseOrderList/components/BundledAlibabaIdModal";
import { ProFormTextArea } from "@ant-design/pro-form";
import ApplyPayModal from "@/pages/PurchaseOrderList/components/ApplyPayModal";
import LogisticsTraceModal from "@/pages/PurchaseOrderList/components/LogisticsTraceModal";
import PurchaseOrderLogModal from "@/pages/PurchaseOrderList/components/PurchaseOrderLogModal";
import ApplyReducePaymentModal from "@/pages/PurchaseOrderList/components/ApplyReducePaymentModal";
import {
  OrderStatusEnum,
  payStatusEnum,
  PayTypeEnum,
  PlatformStatusEnum,
  PurchaseAuditStatusEnum
} from "@/modules/purchaseOrder/domain/purchaseOrder";
import SupplierModal from "@/pages/PurchaseOrderList/components/SupplierModal";
import PurchaseOrderImportFileModal from "@/pages/PurchaseOrderList/components/PurchaseOrderImportFileModal";
import { PurchaseOrder, PurchaseOrderGoods } from "@/pages/PurchaseOrderList/data";
import { FormOutlined } from "@ant-design/icons/lib";
import moment from "moment";
import { Link } from "@umijs/preset-dumi/lib/theme";
import { commonExport, copyText } from "@/utils/comUtil";
import { Access } from "@@/plugin-access/access";
import Permission from "@/components/Permission";
import EditOrderModal from "@/pages/PurchaseOrderList/components/EditOrderModal";
import { reqByUrl } from "@/modules/common/infra/api/common";

const TableList: React.FC = () => {
  const { id: orderId } = useParams<{ id: string }>();
  const { data, loading, refresh } = useRequest(() => getOrderDetail(orderId).then((res) => res.body));
  const [AliModalVisible, setAliModalVisible] = useState<boolean>(false);
  const [PayModalVisible, setPayModalVisible] = useState<boolean>(false);
  const [ReducePayModalVisible, setReducePayModalVisible] = useState<boolean>(false);
  const [editOrderVisible, setEditOrderVisible] = useState<boolean>(false);

  const subApplyReducePayment = (values: any) => {
    const obj = {
      "orderId": orderId,
      "remark": values["remark"],
      "applyPromotionAmount": values["applyPromotionAmount"],
      "applyRefundAmount": values["applyRefundAmount"],
    }
    applyReducePayment(obj).then((result) => {
      if (result.status.success) {
        refresh();
        message.success(result.body)
      }
    });
    //关闭弹框
    setReducePayModalVisible(false);
  }

  /**
   * 修改采购计划需求数量
   * @param record
   */
  const [form] = Form.useForm();
  const changeOrderMemoOrRemark = (record: PurchaseOrder, field: string) => {
    Modal.confirm({
      icon: false,
      width: "35%",
      content: (
        <Form form={form}>
          {field == 'title' ? (
            <ProFormText label="标题" name="title" initialValue={record.title} />
          ) : null}
          {field == 'memo' ? (
            <ProFormTextArea label="追加留言" name="memo" />
          ) : null}
          {field == 'remark' ? (
            <ProFormTextArea label="备注" name="remark" initialValue={record.remark} />
          ) : null}
        </Form>
      ),
      onOk: function () {
        const params = {
          memo: form.getFieldValue('memo'),
          remark: form.getFieldValue('remark'),
          title: form.getFieldValue('title'),
          id: record.id,
        };
        if (field == 'memo') {
          updateOrderMemo(params).then((result) => {
            if (result.status.success) {
              refresh();
              notification.success({ message: '修改成功' });
            }
          });
        }
        if (field == "remark") {
          updateOrder(params).then((result) => {
            if (result.status.success) {
              refresh();
              notification.success({ message: '修改成功' });
            }
          });
        }
        if (field == "title") {
          updateOrderTitle(params).then((result) => {
            if (result.status.success) {
              refresh();
              notification.success({ message: '修改成功' });
            }
          });
        }
      },
    });
  };

  //关联1688订单 弹框回调
  const bundledAlibaba = (values: any) => {
    bundledAlibabaOrder(values).then((result) => {
      if (result.status.success) {
        refresh();
        message.success("关联成功")
      }
    });
    //关闭弹框
    setAliModalVisible(false);
  }

  const bundledOrderCodeModal = (data: PurchaseOrder) => {
    Modal.confirm({
      icon: '',
      keyboard: false,
      content: (
        <Form labelCol={{ flex: '100px' }} form={form}>
          <ProFormText
            width="md"
            name="title"
            disabled
            label="标题"
            initialValue={data?.title}
          />
          <ProFormText
            width="md"
            name="supplierName"
            disabled
            label="供应商"
            initialValue={data?.supplierName}
          />
          <ProFormText
            width="md"
            name="orderCode"
            label="采购单号"
          />
        </Form>
      ),
      onOk: function () {
        const params = {
          id: data.id,
          orderCode: form.getFieldValue('orderCode'),
        };
        bundledOrderCode(params).then((result) => {
          if (result.status.success) {
            refresh();
            notification.success({ message: '修改成功' });
          }
        });
      },
    });
  }

  const convertTradeType = (data: PurchaseOrder) => {
    Modal.confirm({
      icon: '',
      keyboard: false,
      content: (
        <Form labelCol={{ flex: '100px' }} form={form}>
          <ProFormSelect
            width="md"
            name="platformTradeType"
            label="交易方式"
            options={[
              { label: '担保交易(fxassure)', value: "fxassure" },
              { label: '担保交易(assureTrade)', value: "assureTrade" },
              { label: '供应商账期', value: "period" },
              { label: '诚意赊', value: "credit" }
            ]}
          />
        </Form>
      ),
      onOk: function () {
        const params = {
          orderId: data.id,
          tradeType: form.getFieldValue('platformTradeType'),
        };
        reqByUrl("/purchase-mgmt-biz/purchase-center/order/convertTradeType", params).then((result) => {
          if (result.status.success) {
            refresh();
            notification.success({ message: '修改成功' });
          }
        });

      },
    });
  }

  //申请付款
  const applyPay = (values: any) => {
    values.orderId = orderId;
    applyPayment(values).then((result) => {
      if (result.status.success) {
        refresh();
        message.success("申请成功")
      }
    });
    //关闭弹框
    setPayModalVisible(false);
  }

  //取消1688订单
  const cancelAlibaba = () => {
    Modal.confirm({
      title: '确认取消订单吗',
      onOk: async () => {
        cancelTradeOrder(data?.id).then((result) => {
          if (result.status.success) {
            refresh();
            message.success("申请成功")
          }
        });
      },
    });
  }

  //标记满足付款
  const flagState = () => {
    const param = {
      "isPayment": data?.isPayment == 1 ? 0 : 1,
      "orderId": data?.id
    }
    Modal.confirm({
      title: data?.isPayment == 0 ? '确认标记此订单吗？？？' : '取消标记此订单吗？？？',
      onOk: async () => {
        publicFlagState(param).then((result) => {
          if (result.status.success) {
            refresh();
            message.success("标记成功")
          }
        });
      },
    });
  }

  //导出采购合同
  const exportPurchaseAgreement = () => {
    exportPurchaseContract(orderId).then(res => {
      commonExport(res, '导出采购合同');
    });
  }

  //申请账期结算
  const applyPeriod = (req: PurchaseOrder) => {
    // getOrderGoods(orderId).then((goods) => {
    // let afterAmount = 0;
    // let arrivalPrice = 0;
    // goods.body?.map((item: PurchaseOrderGoods)=>{
    //   afterAmount +=  item.afterSalesQuantity * Number(item.currentPurchasePrice);
    //   arrivalPrice += item.arrivalQuantity * Number(item.currentPurchasePrice);
    // });
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form labelCol={{ flex: '100px' }} form={form}>
          <Form.Item label="下单金额" style={{ height: "10px", fontWeight: 'bold' }}>
            <span className="ant-form-text">{req?.amountPayable?.toFixed(2) || 0.00}</span>
          </Form.Item>
          {/*<Form.Item label="售后货值" style={{height:"10px",fontWeight:'bold'}}>*/}
          {/*  <span className="ant-form-text">{afterAmount?.toFixed(2) || 0.00}</span>*/}
          {/*</Form.Item>*/}
          {/*<Form.Item label="实收货值" style={{height:"10px",fontWeight:'bold'}}>*/}
          {/*  <span className="ant-form-text">{arrivalPrice?.toFixed(2) || 0.00}</span>*/}
          {/*</Form.Item>*/}
          <Form.Item label="账期应付" style={{ height: "10px", fontWeight: 'bold', marginBottom: 24 }}>
            <span className="ant-form-text">{(req?.amountPayable - req.afterAmount)?.toFixed(2)}</span>
          </Form.Item>
          <ProFormText
            width="md"
            name="periodAmount"
            label="账期金额"
            initialValue={req?.periodAmount}
          />
          <Form.Item name="periodTime" label="结算时间">
            <DatePicker format={"YYYY-MM-DD HH:mm:ss"} />
          </Form.Item>
        </Form>
      ),
      onOk: function () {
        const params = {
          id: req.id,
          periodAmount: form.getFieldValue('periodAmount'),
          periodTime: form.getFieldValue('periodTime'),
        };
        applyPeriodSettle(params).then((result) => {
          if (result.status.success) {
            refresh();
            notification.success({ message: '申请成功' });
          }
        });
      },
    });
    // });
  }

  /**
   * 添加快递单号
   * @param data
   */
  const addTrackingNumber = (item: PurchaseOrder) => {
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form labelCol={{ flex: '100px' }} form={form}>
          <ProFormText
            width="md"
            name="trackingCompany"
            label="快递公司"
          />
          <ProFormText
            width="md"
            name="trackingNumber"
            label="快递单号"
          />
        </Form>
      ),
      onOk: function () {
        const params = {
          id: item?.id,
          trackingNumber: form.getFieldValue('trackingNumber'),
          trackingCompany: form.getFieldValue('trackingCompany'),
        };
        addPurchaseTrackingNumber(params).then((result) => {
          if (result.status.success) {
            notification.success({ message: '添加成功' });
            refresh();
          }
        });
      },
    });
  }

  //编辑采购单
  const editOrder = (record: PurchaseOrder) => {
    setEditOrderVisible(true);
  }

  const cancelFinance = (record: PurchaseOrder) => {
    Modal.confirm({
      title: "确认撤销付款申请?",
      centered: true,
      onOk: function () {
        cancelFinanceAudit(record.id).then((result) => {
          if (result.status.success) {
            notification.success({ message: '撤销成功' });
            refresh();
          }
        });
      },
    });
  }

  const onChange = (key: string) => {
    console.log(key);
  };

  //刷新1688订单
  const refreshAlibabaOrder = () => {
    if (data?.platformOrderCode == null || data?.platformOrderCode == "") {
      notification.error({ message: "该订单未下单" });
      return;
    }
    const array: string[] = [data?.id];
    getTradeOrder(array).then((result) => {
      if (result.status.success) {
        refresh();
        message.success("同步成功")
      } else {
        notification.error({
          duration: null,
          placement: "top",
          description: <Space direction="vertical" size="small">{result.body.split("<br>")}</Space>,
          message: "异常订单",
          style: {
            width: "auto"
          }
        });
      }
    });
  }

  //TODO 暂时使用   用于定义流程图走向
  const ff = {
    "0": {
      "orderCode": 0,
      "aa": 16
    },
    "10": {
      "orderCode": 1,
      "aa": 33
    },
    "20": {
      "orderCode": 2,
      "aa": 49
    },
    "30": {
      "orderCode": 3,
      "aa": 66
    },
    "40": {
      "orderCode": 4,
      "aa": 82
    },
    "45": {
      "orderCode": 5,
      "aa": 100
    },
    "50": {
      "orderCode": 5,
      "aa": 100
    },
    "55": {
      "orderCode": 5,
      "aa": 100
    }

  };
  const orderStatus = data?.orderStatus == undefined ? "0" : data?.orderStatus;
  const ss = ff[orderStatus];
  const current = ss == undefined ? 1 : ss.orderCode;
  const percent = ss == undefined ? 15 : ss.aa;
  //转换下单时间格式
  const platformOrderTime = data?.platformOrderTime == null ? "" : moment(data?.platformOrderTime as number).format("YYYY-MM-DD HH:mm:ss");

  let platformTradeType = '';
  if (data?.platformTradeType == "fxassure") {
    platformTradeType = "担保交易(fxassure)";
  } else if (data?.platformTradeType == "assureTrade") {
    platformTradeType = "担保交易(assureTrade)";
  } else if (data?.platformTradeType == "period") {
    platformTradeType = "供应商账期";
  } else if (data?.platformTradeType == "credit") {
    platformTradeType = "诚意赊";
  } else {
    platformTradeType == null ? "" : data?.platformTradeType;
  }

  const request = async (params) => {
    console.log(params);
    return [
      { label: params.select1, value: 'all' },
      { label: '未解决', value: 'open' },
      { label: '已解决', value: 'closed' },
      { label: '解决中', value: 'processing' },
    ];
  };
  let trackingNumber = [];
  if (data?.trackingNumberRecord) {
    JSON.parse(data?.trackingNumberRecord).map(item => {
      trackingNumber.push(<Tag color={"green"}>{item?.trackingNumber}</Tag>);
    });
    // trackingNumber = trackingNumber.substr(1);
  } else if (data?.trackingNumber) {
    trackingNumber.push(<Tag color={"green"}>{data?.trackingNumber}</Tag>);
  }
  return (
    <>
      <Spin spinning={loading}>
        <Card bodyStyle={{ paddingTop: 10, paddingBottom: 10, paddingRight: 200 }}>
          <Descriptions size={"small"} column={4} style={{ fontWeight: "bolds" }} labelStyle={{ fontWeight: "bold" }} >
            <Descriptions.Item label="标题">
              {data?.title}
              <FormOutlined
                style={{ color: 'blue' }}
                onClick={() => changeOrderMemoOrRemark(data, 'title')}
              />
            </Descriptions.Item>

            <Descriptions.Item label="采购单号">{data?.orderCode}</Descriptions.Item>
            <Descriptions.Item label="平台单号">{data?.platformOrderCode ? <a href={"https://trade.1688.com/order/new_step_order_detail.htm?orderId=" + data.platformOrderCode} target={"_blank"}>{data.platformOrderCode}</a> : '--'}<SnippetsTwoTone onClick={() => copyText(data?.platformOrderCode)} /></Descriptions.Item>
            <Descriptions.Item label="快递单号">{trackingNumber || '--'}<PlusSquareTwoTone style={{}} onClick={() => { addTrackingNumber(data) }} /></Descriptions.Item>

            <Descriptions.Item label="关联单号">{data?.associatedOrderCode ? data.associatedOrderCode : '--'}</Descriptions.Item>
            <Descriptions.Item label="仓库">{data?.purchaseWarehouse}</Descriptions.Item>
            <Descriptions.Item label="采购员">{data?.purchaseUsername}</Descriptions.Item>
            <Descriptions.Item label="跟单员">{data?.merchandiserUsername ? data.merchandiserUsername : '--'}</Descriptions.Item>

            <Descriptions.Item label="渠道">{data?.platform}</Descriptions.Item>
            <Descriptions.Item label="账号">{data?.platformAccount}</Descriptions.Item>
            <Descriptions.Item label="付款类型">{PayTypeEnum[data?.payType]}</Descriptions.Item>
            <Descriptions.Item label="平台交易方式">{platformTradeType}</Descriptions.Item>

            <Descriptions.Item label="订单状态">{OrderStatusEnum[data?.orderStatus]}</Descriptions.Item>
            <Descriptions.Item label="平台状态">{PlatformStatusEnum[data?.platformStatus]}</Descriptions.Item>
            <Descriptions.Item label="审核状态">{PurchaseAuditStatusEnum[data?.purchaseAuditStatus]}</Descriptions.Item>
            <Descriptions.Item label="付款状态">{payStatusEnum[data?.payStatus]}</Descriptions.Item>

            <Descriptions.Item label="加急">{data?.isUrgent == 1 ? ("是") : '否'}</Descriptions.Item>
            <Descriptions.Item label="平台总金额">{!data?.platformOrderAmount ? (data?.orderStatus === 0 ? '--' : '0.00') : data?.platformOrderAmount?.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="平台运费">{!data?.platformShippingFee ? (data?.orderStatus === 0 ? '--' : '0.00') : data?.platformShippingFee?.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="ERP运费">
              {!data?.shippingFee ? 0.00 : data?.shippingFee?.toFixed(2)}
            </Descriptions.Item>
            <Descriptions.Item label="收货地址">{data?.platformAddress}</Descriptions.Item>

            <Descriptions.Item label="创建时间">{!data?.gmtCreate ? "" : moment(data?.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss")}</Descriptions.Item>
            <Descriptions.Item label="下单时间">{platformOrderTime}</Descriptions.Item>
            {/*<Descriptions.Item label="付款时间">{data?.platformPayTime}</Descriptions.Item>*/}
          </Descriptions>


          <Row>
            <Descriptions column={4} labelStyle={{ fontWeight: "bold" }} >
              <Descriptions.Item label="留言">
                <Alert message={data?.memo} type="success" style={{ width: "90%", minHeight: 100 }} />
                <FormOutlined
                  style={{ color: 'blue' }}
                  onClick={() => changeOrderMemoOrRemark(data, 'memo')}
                />
              </Descriptions.Item>
              <Descriptions.Item label='备注'>
                <Alert message={data?.remark} type="success" style={{ width: 400, minHeight: 100 }} />
                <FormOutlined
                  style={{ color: 'blue' }}
                  onClick={() => changeOrderMemoOrRemark(data, 'remark')}
                />
              </Descriptions.Item>
              {data?.exceptionMessage ? (
                <Descriptions.Item label='异常信息'>
                  <Alert message={data?.exceptionMessage} type="error" style={{ width: 300 }} />
                </Descriptions.Item>
              ) : null}
            </Descriptions>
          </Row>
        </Card>
        <Card bordered={false} bodyStyle={{ paddingLeft: 30, paddingTop: 20, paddingBottom: 20, marginTop: 5 }}>
          <Steps style={{ width: "68%" }} current={current} percent={percent}>
            <Steps.Step title="创建采购单" />
            <Steps.Step title="待下单" />
            <Steps.Step title="已下单" />
            <Steps.Step title="待付款" />
            <Steps.Step title="已付款" />
            <Steps.Step title="已到货" />
          </Steps>
        </Card>
        <Card bordered={false} bodyStyle={{ paddingTop: 15, marginTop: 5 }}>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:applyPayDetail"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                setPayModalVisible(true);
              }}
              key="applyPay"
            >
              申请付款
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:editPurchaseOrder"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", borderColor: "orange", backgroundColor: "orange" }}
              onClick={() => {
                cancelFinance(data);
              }}
              key="cancelAlibaba"
            >
              撤销付款申请
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:applyDiscount"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                setReducePayModalVisible(true);
              }}
              key="bundledAlibaba"
            >
              申请优惠减免
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:applyPeriod"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                applyPeriod(data);
              }}
              key="applyPeriod"
            >
              申请账期结算
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:relationPlatformOrder"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                setAliModalVisible(true);
              }}
              key="bundledAlibaba"
            >
              关联平台单号
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:refreshPlatformOrderDetail"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                refreshAlibabaOrder();
              }}
              key="refreshAlibabaOrder"
            >
              同步1688订单
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:cancelPlatformOrder"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                cancelAlibaba();
              }}
              key="cancelAlibaba"
            >
              取消订单
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:confirmCheck"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", border: 0, backgroundColor: data?.isPayment == 0 ? "" : "orange" }}
              onClick={() => {
                flagState();
              }}
              key="cancelAlibaba"
            >
              {data?.isPayment == 0 ? "核对确认" : "取消确认"}
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:bindPurchaseOrder"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                bundledOrderCodeModal(data)
              }}
              key="bundledAlibaba"
            >
              绑定单号
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:editPurchaseOrder"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                editOrder(data);
              }}
              key="cancelAlibaba"
            >
              编辑采购单
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:convertTradeType"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                convertTradeType(data);
              }}
              key="convertTradeType"
            >
              账期变更
            </Button>
          </Permission>
          <Divider style={{ margin: 10, padding: 0 }} />
          <Permission permissionKey={"purchase:purchase:purchaseOrder:exportPurchaseAgreement"}>
            <Button
              type="dashed"
              key="print"
              size={"small"}
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
              onClick={() => {
                exportPurchaseAgreement(data);
              }}
            >
              导出采购合同
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:printPurchaseAgreement"}>
            <Button
              type="dashed"
              key="print"
              size={"small"}
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
            >
              <Link to={`/print/purchaseAgreementPrint/${data?.id}`} target={"_blank"}>打印采购合同</Link>
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:printDeliveryNote"}>
            <Button
              type="dashed"
              key="print"
              size={"small"}
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
            >
              <Link to={`/print/purchaseDeliveryPrint/${data?.id}`} target={"_blank"}>打印送货单</Link>
            </Button>
          </Permission>
        </Card>
        <Card bordered={false} bodyStyle={{ marginTop: 5, paddingTop: 10 }} >
          <Tabs onChange={onChange} type="line" tabBarStyle={{ marginBottom: 4 }}>
            <TabPane tab="商品信息" key="1">
              <>
                <PurchaseOrderGoodsModal orderData={data} orderId={orderId} supplierId={data?.supplierId} aliWangWangLink={data?.aliWangWangLink} />
              </>
            </TabPane>
            <TabPane tab="供应商" key="2">
              <>
                <SupplierModal orderData={data} venderId={data?.supplierId} />
              </>
            </TabPane>
            <TabPane tab="采购附件" key="3">
              <>
                <PurchaseOrderImportFileModal orderData={data} fieldName='purchaseImages' />
              </>
            </TabPane>
            <TabPane tab="水单附件" key="4">
              <>
                <PurchaseOrderImportFileModal orderData={data} fieldName='purchaseBankSlips' />
              </>
            </TabPane>
            <TabPane tab="物流信息" key="5">
              <>
                <LogisticsTraceModal orderId={data?.platformOrderCode} />
              </>
            </TabPane>
            <TabPane tab="操作日志" key="6">
              <>
                <PurchaseOrderLogModal orderId={orderId} />
              </>
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
      <EditOrderModal visible={editOrderVisible} orderId={orderId} onFinish={() => { setEditOrderVisible(false); refresh() }} onCancel={() => setEditOrderVisible(false)} />
      <BundledAlibabaIdModal visible={AliModalVisible} purchaseOrderData={data} onFinish={bundledAlibaba}
        onCancel={() => setAliModalVisible(false)} />
      <Access accessible={PayModalVisible}>
        <ApplyPayModal visible={PayModalVisible} order={data} onFinish={applyPay} onCancel={() => setPayModalVisible(false)} />
      </Access>
      <ApplyReducePaymentModal visible={ReducePayModalVisible} orderId={orderId} onFinish={subApplyReducePayment}
        onCancel={() => setReducePayModalVisible(false)} />
    </>
  );
};

export default TableList;
