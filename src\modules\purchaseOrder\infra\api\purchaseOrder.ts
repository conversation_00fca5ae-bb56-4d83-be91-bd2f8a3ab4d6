import mallRequest from '@/utils/mallRequest';
import mallRequest1 from '@/utils/mallRequest1';

import mallApiConfig from 'config/mallApiConfig';
import {
  AliProduct,
  ApplyReducePayment,
  PurchaseOrder,
  PurchaseOrderGoods,
  PurchaseOrderLog
} from "@/pages/PurchaseOrderList/data";

//分页search参数
export type PageQueryOrderParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
  channelOrderNo?: string;
  orderState?: string;
  distributionOrderId?: string;
} & API.QueryPageParams;

// 采购单列表
export async function pageQueryOrder(data?: PageQueryOrderParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/pageQuery',
    data,
  });
}
// 高级付款订单列表
export async function pageQueryAdvancePayOrder(data?: PageQueryOrderParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/newPageQuery',
    data,
  });
}

// 高级付款保存
export async function saveAdvancePayConfig(data?: any) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/newBatchApplyPayment',
    data,
  });
}

// 批量账期结算保存
export async function batchApplyPeriodSettle(data?: any) {
  return mallRequest<API.ApiBaseResult<PurchaseOrder>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/batchApplyPeriodSettle',
    data,
  });
}

// 获取诚意赊账户
export async function getCreditBalance() {
  return mallRequest<API.ApiBaseResult<PurchaseOrder>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'get',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/account/getCreditBalance',
    params: {},
  });
}

//采购计划查询采购单明细
export async function PurchaseDetailPageQuery(data?: PageQueryOrderParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/PurchaseDetailPageQuery',
    data,
  });
}

// 采购单列表
export async function pageQueryOrderAudit(data?: PageQueryOrderParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/orderAudit/pageQuery',
    data,
  });
}

//导出采购合同
export async function exportPurchaseContract(orderId?: string) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/exportPurchaseContract',
    params: {
      orderId
    },
    responseType: 'blob',
  });
}

// 采购单详情
export async function getOrderDetail(orderId?: string) {
  return mallRequest<API.ApiBaseResult<PurchaseOrder>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'get',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/queryOrderDetail',
    params: {
      orderId,
    },
  });
}

//申请退款、优惠减免
export async function getApplyReducePayment(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<ApplyReducePayment>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getApplyReducePayment',
    params: {
      orderId,
    },
  });
}

//申请退款、优惠减免
export async function applyReducePayment(data?: any) {
  return mallRequest<API.ApiQueryPageResult<ApplyReducePayment>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/applyReducePayment',
    data,
  });
}

//修改采购单
export async function updateOrder(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/update',
    data,
  });
}

//修改采购单标题
export async function updateOrderTitle(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/updateTitle',
    data,
  });
}

//修改采购单留言
export async function updateOrderMemo(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/updateOrderMemo',
    data,
  });
}

//采购单详情列表
export async function getOrderGoods(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderGoods[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'get',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/getOrderGoods',
    params: {
      orderId,
    },
  });
}

//编辑采购单商品
export async function updateOrderGoods(data?: any) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderGoods[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/update',
    data
  });
}

//添加采购单商品
export async function insertOrderGoods(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/create',
    data,
  });
}

//删除采购单商品
export async function deletePurchaseGoods(id?: string) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/deletePurchaseGoods',
    params: {
      id
    },
  });
}

//批量删除采购单商品
export async function batchDeletePurchaseGoods(data: { ids?: string[]; purchaseOrderId?: string }) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/batchDeletePurchaseGoods',
    data
  });
}

export interface ListItem {
  orderId?: string;
  sku?: string;
  currentPurchasePrice?: number;
  purchaseQuantity?: number;
  goodsRemark?: string;
}

//批量创建采购单商品
export async function batchCreatePurchaseGoods(data: { list?: ListItem[]; purchaseOrderId?: string }) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/batchCreate',
    data
  });
}

//采购单详情 修改价格/数量
export async function modifyQuantityAndPrice(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/modifyQuantityAndPrice',
    data,
  });
}

//盘点入库
export async function batchTakeStock(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/batchTakeStock',
    data,
  });
}

//查询入库记录
export async function queryInStorageList(orderId?: string, sku?: string) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/queryInstorageByOrderId',
    params: {
      orderId,
      sku
    },
  });
}

//取消入库
export async function cancelInStorage(inStorageId?: number) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/cancelInStorage',
    params: {
      inStorageId
    },
  });
}

//复制采购单
export async function auditCopyOrder(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/auditCopyOrder',
    data,
  });
}

//复制采购单
export async function copyPurchaseOrder(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/copyPurchaseOrder',
    params: {
      orderId
    }
  });
}

//不等待剩余
export async function nonWaitResidue(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/nonWaitResidue',
    data,
  });
}

//关联1688订单
export async function bundledAlibabaOrder(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/bundledAlibabaId',
    data,
  });
}

//关联采购单号
export async function bundledOrderCode(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/bundledOrderCode',
    data,
  });
}

//申请账期结算
export async function applyPeriodSettle(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/applyPeriodSettle',
    data,
  });
}

//刷新1688订单
export async function getTradeOrder(data?: string[]) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getTradeOrder',
    data,
  });
}

//1688下单
export async function AliCreateOrder(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/AliCreateOrder',
    data,
  });
}

//获取1688商品信息
export async function getAlibabaProductInfo(data?: any) {
  return mallRequest<API.ApiBaseResult<AliProduct>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getAlibabaProductInfo',
    data,
  });
}

//获取1688商品信息
export async function getAlibabaProductInfoByUrl(url: string) {
  return mallRequest<API.ApiBaseResult<AliProduct>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getAlibabaProductInfoByUrl',
    params: { 'url': url },
  });
}

//分配跟单员或采购员
export async function AssigningUsers(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/AssigningUsers',
    data,
  });
}

export async function getUser(roleType: string) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getUser',
    params: {
      roleType
    }
  });
}

//采购单修改状态
export async function updateOrderStatus(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/updateStatus',
    data,
  });
}

//确认付款
export async function confirmThePayment(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/confirmThePayment',
    data,
  });
}

// 批量确认付款
export async function batchConfirmThePayment(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/batchConfirmThePayment',
    data,
  });
}

//批量修改支付方式
export async function batchChangePayment(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/batchUpdatePayType',
    data,
  });
}

//采购单批量修改状态
export async function batchUpdateOrderStatus(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/batchUpdateStatus',
    data,
  });
}

//模拟审核流程
export async function auditProcess(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/auditProcess',
    data,
  });
}

//提交财务审核
export async function applyPayment(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/applyPayment',
    data,
  });
}

//财务批量审核
export async function batchApplyPayment(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/batchApplyPayment',
    data,
  });
}

//查询物流轨迹
export async function queryLogisticsTrace(data: { orderId: string; trackingNumber: string }) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/logistics/queryLogisticsTraceByOrderIdAndTrackNum',
    data,
  });
}

//分页search参数
export type PageQueryOrderLogParams = {
  orderId: string
} & API.QueryPageParams;


//查询采购单日志
export async function pageQueryOrderLog(data?: PageQueryOrderLogParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderLog[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/pageQueryOrderLogs',
    data,
  });
}

//查询采购单日志
export async function cancelTradeOrder(orderId?: number) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderLog[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/cancelTradeOrder',
    params: {
      orderId
    },
  });
}

//标记采购单标签
export async function publicFlagState(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/flagState',
    data,
  });
}

//通用数据汇总
export async function purchaseDataCount(data?: any) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/data-center/purchaseDataCount',
    data,
  });
}

//打印送货单
export async function printOrderDeliveryLis(orderId?: string) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/printOrderDeliveryLis',
    params: {
      orderId: orderId
    },
  });
}

//打印采购合同
export async function printPurchaseContract(orderId?: string) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/printPurchaseContract',
    params: {
      orderId: orderId
    },
  });
}

//查询采购分析
export async function queryPurchaseAnalysis(data?: any) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/queryPurchaseOrderAnalysis',
    data,
  });
}

//打印标签参数
export type PrintGoodsTagsParams = {
  orderId: string,
  sku: string
};

//查询采购单日志
export async function printGoodsTags(data?: PrintGoodsTagsParams) {
  return mallRequest<API.ApiBaseResult<PurchaseOrder>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'GET',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/queryOrderDetail?orderId=297138249632055296',
    data,
  });
}

//打印标签参数
export type PageQueryPurchaseAdvice = {
  sku: string
};

//采购计划查询采购单明细
export async function purchaseAdvicePageQuery(data?: PageQueryPurchaseAdvice) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/purchase-advice/pageQuery',
    data,
  });
}

//申请账期结算
export async function addPurchaseTrackingNumber(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/addTrackingNumber',
    data,
  });
}

export async function cancelFinanceAudit(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/cancelFinanceAudit',
    params: {
      orderId: orderId
    },
  });
}

//不等待剩余
export async function periodCheckRequest(data?: any) {
  return mallRequest<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'post',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getAliPayUrl',
    data,
  });
}

/**
 * 采购单导出列表
 */
export async function exportOrderAsync(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/exportPurchaseOrder',
    data,
  });
}
