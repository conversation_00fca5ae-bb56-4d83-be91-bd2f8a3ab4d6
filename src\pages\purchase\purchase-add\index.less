.page-purchase-add {
  .card {
    margin-bottom: 16px;
    padding: 14px;
    background-color: #fff;
    border-radius: 8px;

    .title {
      margin-bottom: 12px;
      font-weight: bold;
      font-size: 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &>p {
        &>span {
          margin-right: 12px;
          font-weight: normal;
          cursor: pointer;
        }
      }

      .active {
        font-weight: bold;
      }

      &>p:nth-child(2) {
        font-weight: normal;
        font-size: 16px;

        span {
          color: #0000ff;
        }
      }

      &>div {
        width: fit-content;
      }
    }

    .form-items {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      &>.ant-form-item {
        width: 40%;
        margin-bottom: 12px;
        margin-right: 48px !important;

        .ant-form-item-label {
          width: 120px;
        }

        .ant-picker {
          width: 100%;
        }

        .shipping-fee {
          display: flex;
          align-items: center;

          &>.ant-form-item:nth-child(1) {
            flex: 2;
            margin-right: 0 !important;
          }

          &>.ant-form-item:nth-child(2) {
            flex: 1;
            margin-right: 0 !important;
          }
        }
      }
    }
  }

  .goods-info {
    .show {
      margin-top: 12px;
    }

    .goods-info-box {
      display: flex;
      align-items: center;
      text-align: left;

      &>div {
        flex-shrink: 0;
      }

      &>div:nth-child(1) {
        margin-right: 12px;
      }

      &>div:nth-child(2) {
        flex: 1;

        div {
          white-space: wrap;
          word-break: break-all;
        }
      }
    }
  }
}

.add-modal {
  .ant-pro-table {
    .ant-pro-table-search {
      padding-right: 0 !important;

      &>form {
        padding: 10px;

        &>.ant-row {
          &>.ant-col:nth-child(3) {
            .ant-form-item-row {
              .ant-form-item-control {
                max-width: unset !important;
              }
            }
          }
        }
      }
    }
  }
}