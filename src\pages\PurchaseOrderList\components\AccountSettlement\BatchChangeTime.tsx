import { Form, Modal, ModalProps } from 'antd';
import { DatePicker } from 'antd';
import { useEffect } from 'react';
type Item = {
  value?: string;
  label?: string;
};
// 定义参数格式
export type CreateModalProps = {
  open?: boolean;
  options?: Item[];
  title?: string;
  name?: string;
  label?: string;
  tips?: string;
  onFinish?: (values: any) => void;
} & ModalProps;

const Index = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { title, onFinish,name, ...rest } = props;
  useEffect(()=>{
    if(!props?.open) return;
    form.resetFields();
  },[props])
  return (
    <Modal title={title} width={380} onOk={() => form.submit()} {...rest}>
      <Form form={form} onFinish={onFinish}>
        <Form.Item name={name} label="结算时间">
          <DatePicker format={"YYYY-MM-DD"} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Index;
