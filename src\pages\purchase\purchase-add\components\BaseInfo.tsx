import { memo, useCallback, useMemo } from 'react';
import { useFormCom } from '@/components/Common-UI/CommonForm/hook';
import SelectVender from '@/components/Select/SelectVender';
import SelectEntity from '@/components/Select/SelectEntity';
import SelectWarehouse from '@/components/Select/SelectWarehouse';
import type { Vender, WarehouseItem } from '@/components/Select/api';
import type { CommonFormAction } from '@/components/Common-UI/CommonForm/type';
import { querySupplierDetail } from '../api';

const BaseInfo = memo((props: { formRef: React.MutableRefObject<CommonFormAction | undefined>, id?: string, onCompanyChange: () => void, orderCode?: string }) => {
  const { formRef, id, orderCode, onCompanyChange } = props
  const isEdit = useMemo(() => Boolean(id), [id])
  const setComp = useFormCom();

  const onChooseVender = useCallback((item: Vender) => {
    const { venderId, venderName } = item
    formRef.current?.setFieldsValue({ settleType: undefined, settleCircle: undefined, settleCurrency: undefined, payType: undefined, supplierName: venderName })
    querySupplierDetail({ venderId }).then(res => {
      const { settleType, settleCircle, settleCurrency, payType } = res.body
      formRef.current?.setFieldsValue({
        settleType,
        settleCircle,
        settleCurrency,
        payType,
      })
    })
  }, [formRef])
  const onChooseWareHouse = useCallback((item: WarehouseItem) => {
    formRef.current?.setFieldsValue({ detailAddress: item.detailAddress })
  }, [formRef])

  return (
    <div className="base-info card">
      <div className="title">
        <p>基础信息</p>
        {id && <p>采购单号：<span>{orderCode}</span></p>}
      </div>
      <div className="form-items">
        {setComp('供应商名称', 'supplierId', { render: () => <SelectVender onChoose={onChooseVender} disabled={isEdit} /> })}
        {setComp('供应商名称', 'supplierName', { hidden: true, required: false })}
        {setComp('收货仓库', 'purchaseWarehouse', { render: () => <SelectWarehouse onChoose={onChooseWareHouse} disabled={isEdit} /> })}
        {setComp('采购公司主体', 'purchaseEntity', { render: () => <SelectEntity onChange={() => onCompanyChange()} disabled={isEdit} /> })}
        {setComp('收货地址', 'detailAddress', { disabled: true, placeholder: "选择收货仓库后自动带入仓库关联地址" })}
        {setComp('采购备注', 'remark', { maxLength: 500, required: false })}
        {setComp('预计到货时间', 'estimatedTimeArrival', { type: "ProFormDateTimePicker" })}
      </div>
    </div>
  );
});

export default BaseInfo;
