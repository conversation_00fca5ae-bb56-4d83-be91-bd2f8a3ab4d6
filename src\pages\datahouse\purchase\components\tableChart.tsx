import { InfoCircleOutlined } from '@ant-design/icons';
import { Card, Col, Row, Table, Tooltip } from 'antd';
import React from 'react';
import numeral from 'numeral';
import NumberInfo from "@/pages/analysis/components/NumberInfo";
import Trend from "@/pages/analysis/components/Trend";

const columns = [
  {
    title: '排名',
    dataIndex: 'index',
    key: 'index',
  },
  {
    title: '搜索关键词',
    dataIndex: 'keyword',
    key: 'keyword',
    render: (text: React.ReactNode) => <a href="/">{text}</a>,
  },
  {
    title: '用户数',
    dataIndex: 'count',
    key: 'count',
    sorter: (a: { count: number }, b: { count: number }) => a.count - b.count,
  },
  {
    title: '周涨幅',
    dataIndex: 'range',
    key: 'range',
    sorter: (a: { range: number }, b: { range: number }) => a.range - b.range,
    render: (text: React.ReactNode, record: { status: number }) => (
      <Trend flag={record.status === 1 ? 'down' : 'up'}>
        <span style={{ marginRight: 4 }}>{text}%</span>
      </Trend>
    ),
  },
];

export const TableChart = () => (
 <>
    {/*<Row gutter={24}>*/}
    {/*  <Col sm={12} xs={24} style={{ marginBottom: 24 }}>*/}
    {/*    <NumberInfo*/}
    {/*      subTitle={*/}
    {/*        <span>*/}
    {/*          供应商数*/}
    {/*          <Tooltip title="指标说明">*/}
    {/*            <InfoCircleOutlined style={{ marginLeft: 8 }} />*/}
    {/*          </Tooltip>*/}
    {/*        </span>*/}
    {/*      }*/}
    {/*      gap={8}*/}
    {/*      total={numeral(12321).format('0,0')}*/}
    {/*      status="up"*/}
    {/*      subTotal={17.1}*/}
    {/*    />*/}
    {/*    /!*<TinyArea height={45} autoFit smooth )} />*!/*/}
    {/*  </Col>*/}
    {/*  <Col sm={12} xs={24} style={{ marginBottom: 24 }}>*/}
    {/*    <NumberInfo*/}
    {/*      subTitle={*/}
    {/*        <span>*/}
    {/*          日均采购数*/}
    {/*          <Tooltip title="指标说明">*/}
    {/*            <InfoCircleOutlined style={{ marginLeft: 8 }} />*/}
    {/*          </Tooltip>*/}
    {/*        </span>*/}
    {/*      }*/}
    {/*      total={2.7}*/}
    {/*      status="down"*/}
    {/*      subTotal={26.2}*/}
    {/*      gap={8}*/}
    {/*    />*/}
    {/*    /!*<TinyArea height={45} autoFit smooth data={visitData2.map((item) => item.y)} />*!/*/}
    {/*  </Col>*/}
    {/*</Row>*/}
    <Table<any>
      rowKey={(record) => record.index}
      size="small"
      columns={columns}
      dataSource={[]}
      pagination={{
        style: { marginBottom: 0 },
        pageSize: 5,
      }}
    />
  </>
);

export default TableChart;
