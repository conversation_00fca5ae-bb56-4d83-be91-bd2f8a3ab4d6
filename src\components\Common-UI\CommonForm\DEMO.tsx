import CommonForm from '@/components/Common-UI/CommonForm';
import { useFormChild } from '@/components/Common-UI/CommonForm/hook';
import type { CommonFormAction, CommonFormLine } from '@/components/Common-UI/CommonForm/type';
import type { ModalAction } from '@/components/Common-UI/CommonModal';
import CommonModal from '@/components/Common-UI/CommonModal';
import useLocael from '@/hooks/useLocael';
import { forwardRef, memo, useImperativeHandle, useMemo, useRef, useState } from 'react';

const BatchImport = forwardRef((props: { onClose?: () => void }, ref) => {
  const { $t } = useLocael();
  const setChild = useFormChild();
  const [record, setRecord] = useState<any>();
  const modalRef = useRef<ModalAction<any>>();
  const formRef = useRef<CommonFormAction>();

  // 1) Export
  useImperativeHandle(ref, () => ({
    open: modalRef?.current?.open,
  }));

  // 2) Anction
  const onOpen = (row: any) => {
    if (row) {
      // 1 设置当前操作项
      setRecord(row);
      // 2 初始化 form 数据
      setTimeout(() => formRef.current?.setFieldsValue(row), 0);
    }
  };
  const onClose = () => {
    formRef.current?.actionRef?.resetFields();
    setRecord(undefined);
  };
  const onConfirm = async () => {
    // 1 校验数据
    await formRef.current?.actionRef?.validateFields();
    // 2 设置参数
    // const params = {
    //     ...formRef.current?.actionRef?.getFieldsValue(),
    //   }
    // 3 发送请求
    // const res = await queryBatchImport(params);
    // if (res.status.success) message.success($t('上传成功，请到导入导出页面查看'));
    // 4 关闭窗口
    modalRef.current?.close();
    props.onClose && props.onClose();
  };

  // 3) Form Line
  const columns = useMemo<CommonFormLine[]>(() => {
    return [
      setChild('库位类型', 'type', { valueEnum: { NORMAL: '实际库位', VIRTUAL: '虚拟库位' } }),
      setChild('模板', '', { render: () => <a>下载模板</a> }),
      setChild('模板', '', { render: () => <a>下载模板</a> }),
      setChild('模板', '', { render: () => <a>下载模板</a> }),
      setChild('模板', '', { render: () => <a>下载模板</a> }),
    ];
  }, [setChild]);

  return (
    <CommonModal title="批量导入" modalRef={modalRef} onOpen={onOpen} onClose={onClose} onConfirm={onConfirm}>
      <CommonForm layout="horizontal" commonRef={formRef} columns={columns} showBtn={false} />
    </CommonModal>
  );
});

export default memo(BatchImport);
