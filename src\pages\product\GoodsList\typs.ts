export interface SkuInfo {
	sku: string;
	skuName: string;
	skuAttrName: string;
	skuAttrValue: string;
	developer: string;
	purchaser: string;
	salesStatus: string;
	productType: string;
	packingMaterial: string;
	promotionSuggestion: string;
	copywritingSuggestion: string;
	skuId: string;
	customCode: string;
	spuId: string;
	refProductLink: string;
	sizeLength: string;
	sizeWidth: string;
	sizeHeight: string;
	netWeight: string;
	grossWeight: string;
	packingLength: string;
	packingWidth: string;
	packingHeight: string;
	measuringUnit: string;
	specs: string;
	gmtCreate: number;
	gmtModified: number;
}

export interface ImagesTag {
	imageDetailId: string;
	tagId: string;
	tagType: string;
	tagName: string;
	imageUrl: string;
  checked?: boolean;
}

export interface ImageTagGroup {
	tagGroupId: string;
	tagGroupType: string;
	tagGroupName: string;
	description: string;
	imagesTags: ImagesTag[];
}

export interface SkuMap {
	productImageId: string;
	sku: string;
	skuInfo: SkuInfo;
	imageTagGroups: ImageTagGroup[];
}

export interface DelParams {
	productImageId: string;
	sku: string;
	imageDetailId: string;
}

export interface DownParams {
	productImageId: string;
	sku: string;
	imageDetailIds: string[];
}