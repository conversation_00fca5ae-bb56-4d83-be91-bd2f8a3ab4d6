import type {ProColumns} from '@ant-design/pro-table';
import {
  <PERSON><PERSON>,
  Button,
  DatePicker,
  Form,
  message,
  Modal,
  notification,
  Radio,
  Row,
  Select,
  Space,
  Tag,
  Timeline
} from 'antd';
import React, {useState} from 'react';
import {Access, history, Link, useParams} from 'umi';
import Permission from "@/components/Permission";
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import ApplyExpenseModal from "@/pages/financeExpense/components/ApplyExpenseModal";
import {batchPayment, expenseBatchPayment, getExpenseList} from "@/modules/financeExpense/infra/api/expense";
import moment from "moment";
import ExpenseDetailModal from "@/pages/financeExpense/components/ExpenseDetailModal";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import {
  apportionTypeEnum,
  auditModelEnum,
  expensePaymentType, expenseStatusEnum,
  FinanceExpense
} from "@/modules/financeExpense/domain/expense";
import {LoadingOutlined} from "@ant-design/icons";
import {ProForm, ProFormDatePicker, ProFormItem, ProFormTextArea} from '@ant-design/pro-components';
import EditExpenseModal from "@/pages/financeExpense/components/EditExpenseModal";
import {ProFormField} from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import {downloadSkuTagsTemplate, importSkuTags} from "@/modules/product/infra/api/spu";
import {commonExport} from "@/utils/comUtil";

const ExpenseList: React.FC<FinanceExpense> = () => {
  const [applyExpenseModal, setApplyExpenseModal] = useState<boolean>(false);
  const [auditConfigModal, setAuditConfigModal] = useState<boolean>(false);
  const [expenseDetailModal, setExpenseDetailModal] = useState<boolean>(false);
  const [editExpenseModal, setEditExpenseModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<FinanceExpense>();
  const [selectStore, setSelectStore] = useState<any>();
  const [selectedRows, setSelectedRows] = useState<FinanceExpense[]>();
  const [form] = Form.useForm();


  const { actionRef, fetchList } =  useRequestTable((params) => {
    return getExpenseList({
      ...params,
    });
  });

  const batchCheck = () => {
    if(selectedRows?.length == 0){
      message.error("请勾选数据");
      return;
    }
    Modal.confirm({
      title: '确认此操作吗？',
      keyboard: false,
      // content: (
      //   <Form labelCol={{flex: '100px'}} form={form}>
      //     <Form.Item label="收款人" style={{fontWeight:'bold', margin: 0}}>
      //       {accountName}
      //     </Form.Item>
      //     <Form.Item label="总金额" style={{fontWeight:'bold', margin: 0}}>
      //       {totalAmount?.toFixed(2)}
      //     </Form.Item>
      //     <Form.Item label="支付笔数" style={{fontWeight:'bold', margin: 0}}>
      //       {selectedRows?.length}
      //     </Form.Item>
      //     <ProForm.Group>
      //       <ProFormDatePicker width={"sm"} dataFormat={"YYYY-MM-DD"}   name={"paymentTime"} label={"付款时间"} />
      //       <ProFormTextArea
      //         width={"lg"}
      //         label="备注"
      //         name="remark"
      //       />
      //     </ProForm.Group>
      //   </Form>
      // ),
      onOk: async () => {
        message.success("后台执行中，请稍后查看！");
      }
    });
  }

  const downloadGoodsTagsTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadSkuTagsTemplate().then(res=>{
                commonExport(res, '商品标签导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        importSkuTags(link).then(res=>{
          if (res.status.success){
            notification.success({message: '导入成功'});
            actionRef.current?.reloadAndRest?.();
          }
        });
      },
    });

  }

  const columns: ProColumns<FinanceExpense>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      width: 500,
      colSize: (4 / 24),
    },
    {
      title: 'MSKU',
      dataIndex: 'msku',
      width: 100,
      colSize: (4 / 24)
    },
    {
      title: '关键词',
      dataIndex: 'keyword',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '是否侵权',
      dataIndex: 'isQ',
      hideInSearch:true,
      render: (v,record) => {
        return <>
          <div>{record?.isQ == 1 ? <Tag color={"green"}>是</Tag> : <Tag>否</Tag>}</div>
        </>
      }
    },
    {
      title: '同步listing数量',
      dataIndex: 'num',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
    },
    {
      title: '店铺账号',
      dataIndex: 'account',
      width: 100,
      hideInSearch: true,
      render: (v,record) => {
        return <>
          {<Tag style={{margin: 3}} color={"orange"}>{record?.account}</Tag>}
        </>
      }
    },
    {
      title: '同步时间',
      dataIndex: 'gmtCreate',
      hideInSearch: true,
      width: 150,
      render: (v,record) => {
        return <>
          <div>{moment(Number(record.gmtCreate)).format("YYYY-MM-DD HH:mm:ss")}</div>
        </>
      }
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 200,
      align: "center",
      render: (_, record) => {
        return <>
          <Space wrap={true} direction="horizontal" aria-colindex={3}>
              <Button type="primary" style={{borderColor: "red", color: "red"}} size={"small"} ghost={true} onClick={()=>{batchCheck()}}>
                删除
              </Button>
              <Button type="primary" size={"small"} onClick={()=>{message.error("暂无权限")}}>
                编辑
              </Button>
              <Button type="primary" size={"small"} ghost onClick={()=>{message.error("暂无权限")}}>
                查看
              </Button>
          </Space>
        </>
      },
    },
  ];

  const ds = [
    {title: 'SagaSave Universal String Trimmer Attachment Head Replacement Weed Eater',
    'msku': 'HA3680+GZ+1+T9+ZY+79',
    'num': 53,
    'account': 'MASO-DE',
    'site': '德国',
    'status': '成功',
    isQ: 1,
    'keyword': 'Attachment',
    gmtCreate: *************,
  },
    {title: 'SagaSave Universal  Attachment Head Replacement Weed Eater',
      'msku': 'HA3680+GZ+99-SS',
      'num': 23,
      'account': 'MASO-DE',
      'site': '德国',
      'status': '成功',
      isQ: 0,
      'keyword': 'String Trimmer',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal String Trimmer Attachment  Weed Eater',
      'msku': 'HA3680+GZ+SS-ZZ-SS',
      'num': 133,
      'account': 'BETOOL-DE',
      'site': '德国',
      'status': '成功',
      isQ: 0,
      'keyword': 'Head Replacement',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal String Trimmer Attachment Head Replacement Weed Eater',
      'msku': 'HA3680+GZ+1+T9+ZY+79',
      'num': 253,
      'account': 'MASO-DE',
      'site': '德国',
      'status': '成功',
      isQ: 1,
      'keyword': 'Attachment',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal  Attachment Head Replacement Weed Eater',
      'msku': 'HA3680+GZ+99-SS',
      'num': 23,
      'account': 'BETOOL-DE',
      'site': '德国',
      'status': '成功',
      isQ: 1,
      'keyword': 'String Trimmer',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal String Trimmer Attachment  Weed Eater',
      'msku': 'HA3680+GZ+SS-ZZ-SS',
      'num': 133,
      'account': 'MASO-DE',
      'site': '德国',
      'status': '成功',
      isQ: 0,
      'keyword': 'Head Replacement',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal String Trimmer Attachment Head Replacement Weed Eater',
      'msku': 'HA3680+GZ+1+T9+ZY+79',
      'num': 25,
      'account': 'BETOOL-DE',
      'site': '德国',
      'status': '成功',
      isQ: 0,
      'keyword': 'Attachment',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal  Attachment Head Replacement Weed Eater',
      'msku': 'HA3680+GZ+99-SS',
      'num': 223,
      'account': 'MASO-DE',
      'site': '德国',
      'status': '成功',
      isQ: 0,
      'keyword': 'String Trimmer',
      gmtCreate: *************,
    },
    {title: 'SagaSave Universal String Trimmer Attachment  Weed Eater',
      'msku': 'HA3680+GZ+SS-ZZ-SS',
      'num': 13,
      'account': 'BETOOL-DE',
      'site': '德国',
      'status': '成功',
      isQ: 1,
      'keyword': 'Head Replacement',
      gmtCreate: *************,
    },


  ];

  return (
    <>
    <CustomPage<FinanceExpense>
      actionRef={actionRef}
      dataSource={ds}
      rowKey="id"
      search={{
        filterType: 'query',
        layout: 'horizontal',
        span: 24,
        collapseRender: () => null,
        defaultCollapsed: false,
        optionRender: (_, c, dom) => {
          const options = [
            <b>选中数：{selectStore?.selectNum || 0} &nbsp;</b>,
            <Permission permissionKey={"purchase:finance:expense"}>
              <Button size={"small"}  key="expenseAudit" type="primary" onClick={() => {
                downloadGoodsTagsTemplate();
              }}>
                导入Listing
              </Button>
            </Permission>,
            <Permission permissionKey={"purchase:finance:expense"}>
              <Button size={"small"} key="imprestAudit" type="primary" onClick={batchCheck} >
                侵权检测
              </Button>
            </Permission>,
            <Permission permissionKey={"purchase:finance:expense"}>
              <Button size={"small"} key="imprestAudit" type="primary" onClick={batchCheck}>
                同步下架Listing
              </Button>
            </Permission>,
          ];
          return [...options, ...dom];
        },
      }}
      rowSelection={{
        onChange: (_, selectedRows) => {
          let selectAmount = 0;
          selectedRows.forEach((record)=>{
            selectAmount += record?.amount;
          })
          setSelectStore({selectAmount: selectAmount, selectNum: selectedRows.length});
          setSelectedRows(selectedRows);
        },
      }}
      tableAlertRender={false}
      toolBarRender={false}
      // request={fetchList}
      columns={columns}
      bordered={true}
      recordCreator={false}
      recordDelete={false}
      recordUpdater={false}
    />
      <Access accessible={applyExpenseModal}>
        <ApplyExpenseModal visible={applyExpenseModal} expense={currentRow} onFinish={()=>{setApplyExpenseModal(false); actionRef?.current?.reload();}} onCancel={()=>{setApplyExpenseModal(false)}}/>
      </Access>
      <Access accessible={expenseDetailModal}>
        <ExpenseDetailModal visible={expenseDetailModal} expense={currentRow} onFinish={()=>{setExpenseDetailModal(false)}} onCancel={()=>{setExpenseDetailModal(false)}}/>
      </Access>
      <Access accessible={editExpenseModal}>
        <EditExpenseModal visible={editExpenseModal} expense={currentRow} onFinish={()=>{setEditExpenseModal(false);actionRef?.current?.reload();}} onCancel={()=>{setEditExpenseModal(false)}}/>
      </Access>
    </>
  );
};

export default ExpenseList;
