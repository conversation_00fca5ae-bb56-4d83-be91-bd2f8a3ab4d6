import React, {useEffect} from 'react';
import {PlusOutlined} from '@ant-design/icons';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import {Button, Form, Modal, notification, Space} from "antd";
import {ProFormField, ProFormSelect} from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import Permission from "@/components/Permission";
import {downloadWarehouseGoodsTemplate} from "@/modules/warehouse/infra/api/warehouse";
import moment from "moment";
import {getWarehouse} from "@/modules/purchasePlan/infra/api/purchasePlan";
import {commonExport} from "@/utils/comUtil";

const TableList: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/purchase-mgmt-biz/purchase-center/warehouse/goods/pageQuery', {
      ...params,
    });
  });

  const [form] = Form.useForm();
  const downloadTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadWarehouseGoodsTemplate().then(res=>{
                commonExport(res, '商品库存导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        reqByUrl("/purchase-mgmt-biz/purchase-center/warehouse/goods/importWarehouseGoods", {link:link}).then((result) => {
          if (result.status.success) {
            actionRef.current?.reload?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }


  const columns: ProColumns<any>[] = [
    {
      title: '编码',
      dataIndex: 'overseaLocationCode',
      align:"center",
      render: (v,record)=>{
        return <span>{record?.overseaLocationCode}</span>;
      }
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      align:"center",
      render: (v,record)=>{
        return <span>{record?.sku}</span>;
      }
    },
    {
      title: '仓库名称',
      dataIndex: 'warehouseName',
      width: 200,
      request: async () => {//返回的select网络请求
        const params = await getWarehouse();
        const res = [];
        const body = params.body;
        for (const i in body) {
          const temp = {};
          temp.label = body[i];
          temp.value = i;
          res.push(temp)
        }
        return res;
      },
      hideInTable: true
    },
    {
      title: '仓库名称',
      dataIndex: 'warehouseName',
      align:"center",
      hideInSearch: true,
      render: (v,record)=>{
        return <span>{record?.warehouseName || '--'}</span>;
      }
    },
    {
      title: "创建时间",
      dataIndex: 'gmtCreate',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return moment(record?.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    {
      title: "修改时间",
      dataIndex: 'gmtModified',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return moment(record?.gmtModified as number).format("YYYY-MM-DD HH:mm:ss");
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);
  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        scroll={{y: 660}}
        size={"small"}
        columns={columns}
        toolBarRender={() => [
          <Permission permissionKey={"purchase:warehouse:importGoods"}>
            <Button
              type="primary"
              size={"small"}
              onClick={() => downloadTemplate()}
            >
              导入编码
            </Button>
          </Permission>
        ]}
      />
    </>
  );
};

export default TableList;
