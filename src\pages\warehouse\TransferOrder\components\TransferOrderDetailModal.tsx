import React, {useEffect, useState} from 'react';
import {Modal, ModalProps, Form, message, Table, Descriptions, Card, Input, Space} from 'antd';
import {executeSave, reqByBody, reqByUrl} from "@/modules/common/infra/api/common";
import {TransferOrder, TransferOrderGoods} from "@/pages/warehouse/TransferOrder/data";
import {ColumnProps} from "antd/es/table";

// 定义参数格式
export type editProps = {
  rowData: TransferOrder;
} & ModalProps;

export default (props: editProps) => {
  const {rowData, onCancel, ...rest} = props;
  const [ds, setDs] = useState<TransferOrderGoods[]>();

  const printTags = (record: TransferOrderGoods)=>{
    window.open("/purchasing-center/print/outsideGoodsTagsPrint?transferCode="+record?.transferCode+'&sku='+record?.sku+'&quantity='+(record?.printQuantity || record?.quantity));
  }

  useEffect( () => {
      if (rowData) {
        setDs([]);
        loadTransferGoods();
      }
    },
    [rowData]
  );

  const loadTransferGoods = () => {
    reqByBody('/sales-mgmt-biz/sales-center/warehouse/transfer/getByTransferCode',{transferCode: rowData?.transferCode}).then((res) => {
      if (res.status.success) {
        setDs(res?.body);
      }
    })
  }

  const columns: ColumnProps<TransferOrderGoods>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      width: 200,
      align:"left",
      render: (v,record)=>{
        return <span>{record?.sku}</span>;
      }
    },
    {
      title: '海外仓条码',
      dataIndex: 'sku',
      width: 200,
      align:"left",
      render: (v,record)=>{
        return <span>{record?.thirdpartyCoding || '--'}</span>;
      }
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      align:"left",
      render: (v,record)=>{
        return <div>{record?.skuName}</div>;
      }
    },
    {
      title: '数量',
      width: 100,
      dataIndex: 'quantity',
      align:"left",
      render: (v,record)=>{
        return <span>{record?.quantity}</span>;
      }
    },
    {
      title: '打印数量',
      dataIndex: 'printQuantity',
      align:"left",
      width: 100,
      render: (v, record) => {
        return <Input onChange={(value)=>{
          record.printQuantity = (value.target.value);
        }} defaultValue={record?.quantity}></Input>;
      }
    },

    {
      title: '操作',
      dataIndex: 'sku',
      width: 150,
      align:"center",
      render: (v,record)=>{
        return <Space>
          <a style={{color: "red"}} onClick={() => {reqByUrl('/sales-mgmt-biz/sales-center/warehouse/transfer/deleteTransferCodeGoods', {id: record?.id}).then(res=>{if(res?.status.success){message.success("删除成功！");loadTransferGoods();}})}}>删除</a>
          <a onClick={() => printTags(record)}>打印</a>
        </Space>;
      }
    },
  ]

  return (
    <>
      <Modal {...rest} title="调拨单详情" className={'globalEnterKeySubmit'} width={"75%"} onOk={onCancel} onCancel={onCancel} destroyOnClose={true} maskClosable={false}>
        <Card bordered={false} bodyStyle={{padding: 0}}>
          <Descriptions column={4} style={{fontWeight:"bolds"}} labelStyle={{fontWeight:"bold"}} >
            <Descriptions.Item label="调拨单号">{rowData?.transferCode}</Descriptions.Item>
            <Descriptions.Item label="第三方单号">{rowData?.thirdpartyCode}</Descriptions.Item>
            <Descriptions.Item label="收货仓库">{rowData?.warehouseName || '--'}</Descriptions.Item>
            <Descriptions.Item label="创建人">{rowData?.creator || '--'}</Descriptions.Item>
            <Descriptions.Item label="备注">{rowData?.remark}</Descriptions.Item>
          </Descriptions>
        </Card>
        <Table
          dataSource={ds}
          columns={columns}
          scroll={{y: 550}}
          size={"small"}
          pagination={false}
          bordered={true}
        />
      </Modal>
    </>
  );
};
