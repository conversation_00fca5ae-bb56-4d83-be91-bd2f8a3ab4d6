import { UploadImageResult, uploadImage } from '@/services/common';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/lib/upload';
import { isArray } from 'lodash';
import { useEffect, useState } from 'react';
import { PicturesGrid } from './PicturesGrid';

export interface UploadImageProps
  extends Omit<UploadProps<string[]>, 'customRequest' | 'onChange'> {
  onChange?: (imgs: UploadImageResult[]) => void;
  value?: UploadImageResult[];
}

const UploadImageField = (props: UploadImageProps) => {
  const { onChange, value, ...rest } = props;
  const { maxCount } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const handleChange = (list: any[]) => {
    const isUploaded = list.every((item) => item.status === 'done');
    const imageList: UploadImageResult[] = [];
    list.forEach((item) => {
      if (item.status === 'done') {
        imageList.push({
          fileId: item.fileId,
          link: item.link,
        });
      }
    });
    if (isUploaded && onChange) {
      onChange(imageList);
    }
  };

  const handleMaterialUpload: UploadProps<string[]>['customRequest'] = (options) => {
    setLoading(true);
    const { onSuccess } = options;
    const file = options.file as RcFile;
    const uploadFilename = file.name;

    const imgItem = {
      uid: file.uid, // 注意，这个uid一定不能少，否则上传失败
      name: uploadFilename,
      status: 'uploading',
      link: '',
      fileId: '',
      percent: 20, // 注意不要写100。100表示上传完成
      isCanDel: true,
    };
    setFileList([...fileList, imgItem]);
    const reader = new FileReader();
    reader.readAsArrayBuffer(file); // 读取图片文件
    reader.onload = async (e) => {
      const res = await uploadImage(uploadFilename, e.target?.result);
      if (res.status.success) {
        const updloaedImgItem = {
          uid: file.uid,
          name: uploadFilename,
          status: 'done',
          link: res.body.link,
          fileId: res.body.fileId,
          imgUrl: res.body.link,
          url: res.body.link,
          percent: 100,
          isCanDel: false,
        };
        setFileList([...fileList, updloaedImgItem]);
        handleChange([...fileList, updloaedImgItem]);
        onSuccess?.call(true, true, true as any);
      }
      setLoading(false);
    };
    reader.onerror = () => {
      setLoading(false);
    };
  };

  const handleRemove: UploadProps['onRemove'] = (file) => {
    const newFileList = fileList?.filter((f) => f.uid !== file.uid);
    setFileList(newFileList);
    handleChange(newFileList);
    return true;
  };

  useEffect(() => {
    if (isArray(value)) {
      console.log(value);
      const newFileList = value?.map((item, index) => {
        return {
          uid: index,
          name: index,
          status: 'done',
          url: item.link,
          imgUrl: item.link,
          link: item.link,
          fileId: item.fileId,
          percent: 100,
          isCanDel: true,
        };
      });
      setFileList(newFileList);
    }
  }, [value]);

  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  const handlePicturesGridChange = (values: { fileList: UploadFile<any>[] }) => {
    const isDarg = values.fileList.every((item) => item.status === 'done');
    if (isDarg) {
      setFileList(values.fileList);
      handleChange(values.fileList);
    }
  };

  return (
    <>
      <PicturesGrid
        listType="picture-card"
        customRequest={handleMaterialUpload}
        fileList={fileList}
        onChange={handlePicturesGridChange}
        onRemove={handleRemove}
        {...rest}
      >
        {(maxCount && fileList && fileList.length >= maxCount) || rest.disabled
          ? null
          : uploadButton}
      </PicturesGrid>
    </>
  );
};

export default UploadImageField;
