
import { useSystemRoles } from '@/modules/user-center/application/role';
import { Role } from '@/modules/user-center/domain/role';
import { EndPointUser } from '@/modules/user-center/domain/user';
import { getUserRole, BindUserRoleOpt, bingUserRole } from '@/modules/user-center/infra/user';
import { UserManagerContext } from '@/pages/UserManager';
import { Checkbox, Col, message, Modal, Row, Spin, Typography } from 'antd';
import { difference, find } from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import UserRolePermissionTree from './UserRolePermissionTree';

export interface UserRoleModalProps {
  modalVisible: boolean;
  onCancel: () => void;
  user: EndPointUser;
  onFinish?: () => void;
}

const UserRoleModal = (props: UserRoleModalProps) => {
  const { modalVisible, onCancel, user, onFinish } = props;
  const [userRole, setUserRole] = useState<Role[]>([]);
  const { endpoint } = useContext(UserManagerContext);
  const { systemRoles, loadSystemRoles } = useSystemRoles(true);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentRole, setCurrentRole] = useState<Role>();
  const fetchUserRole = () => {
    setLoading(true);
    getUserRole({ userId: user.userId })
      .then((res) => {
        const userRoles = res.body?.roles || [];
        setUserRole(userRoles);
        setSelectedKeys(userRoles.map((r) => r.roleId));
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    loadSystemRoles({ endpoint })
  }, [endpoint])
  useEffect(() => {
    if (modalVisible) {
      setCurrentRole(systemRoles?.length ? systemRoles[0] : undefined);
    }
  }, [modalVisible, systemRoles]);

  useEffect(() => {
    if (systemRoles?.length) {
      if (userRole.length) {
        const [userFirstRole] = userRole;
        if (userFirstRole) {
          const role = find(systemRoles, { roleId: userFirstRole.roleId });
          setCurrentRole(role);
        }
      } else {
        setCurrentRole(systemRoles[0]);
      }
    }
  }, [userRole, systemRoles]);

  useEffect(() => {
    if (user.userId) {
      fetchUserRole();
    }
  }, [user.userId, modalVisible]);

  const changeOpt = useMemo(() => {
    const defaultRoleId = userRole.map((r) => r.roleId);
    const addRole = difference(selectedKeys, defaultRoleId);
    const deleteRole = difference(defaultRoleId, selectedKeys);
    const opt: BindUserRoleOpt[] = [];
    addRole.forEach((key) => {
      opt.push({
        opt: 0,
        roleId: key as string,
      });
    });
    deleteRole.forEach((key) => {
      opt.push({
        opt: 1,
        roleId: key as unknown as string,
      });
    });
    return opt;
  }, [selectedKeys]);

  const onChange = (values: any) => {
    console.log(values, currentRole);
    setSelectedKeys(values);
  };

  const handleSubmit = () => {
    bingUserRole({
      userId: user.userId,
      roleOpts: changeOpt,
    }).then((res) => {
      if (res.status.success) {
        message.success('更改用户角色成功');
        fetchUserRole();
        onFinish?.();
      } else {
        message.error('更改用户角色失败，请稍后重试');
      }
    });
  };

  return (
    <Modal
      destroyOnClose
      title="用户角色"
      visible={modalVisible}
      onCancel={() => {
        onCancel();
        setSelectedKeys([]);
      }}
      onOk={handleSubmit}
      okButtonProps={{
        disabled: !changeOpt.length,
      }}
      width={900}
    >
      <Row gutter={16}>
        <Col span={currentRole ? 8 : 24}>
          <Row>
            <Typography.Text type="secondary">请选择当前可分配的用户角色</Typography.Text>
          </Row>
          <Spin spinning={loading}>
            <Checkbox.Group value={selectedKeys} style={{ width: '100%' }} onChange={onChange}>
              <Row>
                {systemRoles?.map((item, index) => {
                  return (
                    <Col span={currentRole ? 24 : 8} key={index}>
                      <Checkbox
                        style={{ marginTop: '5px' }}
                        value={item.roleId}
                      >
                        <span onClick={() => setCurrentRole(item)} style={{cursor: 'pointer'}}>
                          {currentRole?.roleId === item.roleId ? (
                            <a>{item.roleName}</a>
                          ) : (
                            item.roleName
                          )}
                        </span>

                      </Checkbox>
                    </Col>
                  );
                })}
              </Row>
            </Checkbox.Group>
          </Spin>
        </Col>

        {currentRole && (
          <Col span={16} style={{ borderLeft: '1px solid #eee' }}>
            <div>
              <Typography.Title level={5}>角色菜单-【{currentRole?.roleName}】 </Typography.Title>
            </div>
            <UserRolePermissionTree disabled role={currentRole} />
          </Col>
        )}
      </Row>
    </Modal>
  );
};

export default UserRoleModal;
