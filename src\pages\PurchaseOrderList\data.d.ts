import {SupplierGoods} from "@/modules/supplier/domain/vender";


export type PurchaseOrder = {
  periodAfterSalesReceived: number;
  arrivalPrice: number;
  afterSalesPrice: number;
  "applyPromotionAmount": number,
  "applyRefundAmount": number,
  "gmtCreate": number,
  "gmtModified": number,
  "isUrgent": number,
  "isSign": number,
  "isAfterOrder": number,
  "isManual": number,
  "isPayment": number;
  "needQc": boolean,
  "id": string;
  "memo": string,
  "remark": string,
  "merchandiserUsername": string,
  "purchaseWarehouse": string,
  "orderCode": string,
  "trackingNumber": string,
  "trackingNumberRecord": string,
  "associatedOrderCode": string,
  "orderStatus": string,
  "organizationName": string,
  "periodTime": string,
  "periodStatus": number,
  "periodAmount": number,
  "afterAmount": number,
  "account": string,
  "bankName": string,
  "accountName": string,
  "auditor": string,
  "auditStatus": string,
  "auditLabel": string,
  "platform": string,
  "payStatus": number,
  "copyOrderStatus": string,
  "payType": number,
  "platformAccount": string,
  "paymentTime": string,
  "platformOrderAmount": number,
  "platformOrderCode": string,
  "platformOrderTime": number,
  "signDate": number,
  "totalPrice": number,
  "platformPayTime": string,
  "platformStatus": string,
  "platformPurchaseAmount": number,
  "platformSyncDataTime": string,
  "platformShippingFee": number,
  "shippingFee": number,
  "purchasePlanId": string,
  "purchaseTotalAmount": number,
  "purchaseUsername": string,
  "supplierId": string,
  "supplierName": string,
  "financeAfterAmount": number,
  "amountPayable": number,
  "title": string,
  "purchaseOrderGoodsList": PurchaseOrderGoods[],
  "aliWangWangLink": string,
  "purchaseAuditStatus": string,
  "financeErrorList": string[],
  settleType: string,
  settleCircle: string,
  exceptionMessage: string,
  contrator: string,
  cellphone: string,
  detailAddress: string,
  purchaseEntity: string,
  platformFullName: string,
  platformMobile: string,
  platformAddress: string,
  platformTradeType: string,
  warehouseEntryTime?: number | string,
  trackingNumbers?: string[];
}


export type PurchaseOrderGoods = {
  "goodsRemark": string,
  "id": string,
  "name": string,
  "isUrgent": boolean,
  "purchasePlanId": string,
  "salesWarehouse": string,
  "lastPurchasePrice": string,
  "platformPurchasePrice": string;
  "currentPurchasePrice": string,
  "auditPrice": number,
  "auditQuantity": number,
  "purchaseQuantity": number,
  "purchaseRemark": string,
  "sku": string,
  "status": number,
  "supplierId": string,
  "supplierName": string,
  "totalPrice": number,
  "gmtCreate": number,
  "supplierGoods": SupplierGoods,
  "skuImg": string,
  "arrivalQuantity": number,
  "afterSalesQuantity": number,
  "receivingQuantity": number,
  "netWeight": number;
  "sizeLength": number;
  "sizeWidth": number;
  "sizeHeight": number;
  "printQuantity": number;
  "orderCode": string;
  "orderId": string;
  "needQc": boolean;
  aliLink?: string;
}


export type PurchaseOrderLog = {
   purchaseOrderId: string,
   content: string,
   operatorUsername: string,
   gmtCreate: number
}


export type ApplyReducePayment ={
  purchaseTotalAmount: string,
  platformPurchaseAmount: string,
  platformShippingFee: string,
  platformOrderAmount: string,
  applyPromotionAmount: string,
  applyRefundAmount: string
}


export type AliProduct={
  sellerLoginId: string;
  minOrderQuantity: number,
  aliLink: string,
  supplierId: string,
  erpImg: string,
  name: string,
  sku: string,
  img: string,
  productId: string,
  productTypeList: AliProductType[],
}

export type AliProductType={
  goodsType: string,
  specId: string,
  price: number,
  amountOnSale:number,
  skuId: string
}
