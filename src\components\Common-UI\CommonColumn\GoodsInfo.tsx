import { Row, Image } from 'antd';
import { memo } from 'react';
import styled from 'styled-components';

const StyleWrapper = styled(Row)`
  .common-goods-info {
    flex-wrap: nowrap;

    img {
      object-fit: cover;
    }

    & > div {
      flex-shrink: 0;
      flex-wrap: nowrap;
    }

    .info-box {
      flex: 1;
      margin: 0 12px;
      white-space: pre-wrap;
      word-break: break-all;

      div {
        margin-top: 6px;
      }
    }
  }

  .option-box {
  }
  .not-option {
    align-items: center;
  }
`;

const CommonColumn = memo((props: { title?: string; mainImage?: string; options?: [string, string | number] }) => {
  const { title, mainImage, options } = props;

  return (
    <StyleWrapper>
      <Row className={`common-goods-info ${options ? 'option-box' : 'not-option'}`}>
        <Image
          src={mainImage || 'https://static.elephantpal.com/common/mall/static/load-img-icon.png'}
          fallback="https://static.elephantpal.com/common/mall/static/load-img-icon.png"
          width={100}
          height={100}
        />
        <div className="info-box">
          <a>{title}</a>
        </div>
      </Row>
    </StyleWrapper>
  );
});

export default CommonColumn;
