import { queryAllCurrencyList } from '@/services/common';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';

export function useCurrency() {
  const {
    data: currencyList,
    loading,
    refreshAsync: reloadCurrencyList,
  } = useRequest(() => queryAllCurrencyList().then((res) => res.body));
  const currencyMap = useMemo(() => {
    return currencyList?.reduce((map, item) => {
      return {
        ...map,
        [item.alphabeticCode]: item.nameZh,
      };
    }, {});
  }, [currencyList]);

  return {
    currencyList,
    reloadCurrencyList,
    loading,
    currencyMap,
  };
}
