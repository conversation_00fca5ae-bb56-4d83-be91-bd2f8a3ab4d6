import {ProFormSelect, ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {
  Card,
  Col,
  Divider,
  Form,
  message,
  Modal,
  ModalProps,
  notification,
  Radio,
  Rate,
  Row,
  Steps,
  Table,
  Tag,
} from "antd";
import React, {useState} from "react";
import {
  auditFinanceExpense,
  getExpenseDetail,
  getFinanceSubjectList,
  getPaymentBankList
} from "@/modules/financeExpense/infra/api/expense";
import {ProDescriptions, ProForm, ProFormDatePicker} from '@ant-design/pro-components';
import {LoadingOutlined, UserOutlined} from "@ant-design/icons";
import {useRequest} from "ahooks";
import moment from "moment";
import {ColumnProps} from "antd/es/table";
import {
  apportionTypeEnum,
  auditModelEnum,
  FinanceExpense,
  FinanceExpenseAudit
} from "@/modules/financeExpense/domain/expense";
import {useModel} from "@@/plugin-model/useModel";
import UploadFile from "@/components/UploadFile";
import {DingdingOutlined} from "@ant-design/icons/lib";
import {reqByUrl} from "@/modules/common/infra/api/common";

// 定义参数格式
export type props = {
  onFinish: () => void;
  expense: FinanceExpense;
} & ModalProps;

const ExpenseDetailModal = (props: props) => {
  const [form] = Form.useForm();
  const {onFinish, expense, ...rest} = props;
  const [auditStepList, setAuditStepList] = useState<any>([]);
  const [bankList, setBankList] = useState<any>();
  const [required, setRequired] = useState<boolean>(true);
  const [financeSubjectList, setFinanceSubjectList] = useState<any>();
  const [currentAuditData, setCurrentAuditData] = useState<FinanceExpenseAudit>();
  const { initialState, setInitialState } = useModel('@@initialState');
  const [loading, setLoading] = useState<boolean>(false);
  const { data, refresh } = useRequest(() => getExpenseDetail(expense?.id).then((res)=>{
    //审核流程
    let auditStep: { title: any; status: string; icon: JSX.Element; description: any;}[] = [];
    res.body?.financeExpenseAuditInfo?.map(item=>{
      auditStep.push({
        title: <span style={{fontSize: 14, fontWeight:"bold", color: item.auditStatus=='REJECT'?"red":""}}>{item?.auditRole}{item.auditStatus=='REJECT'?"【驳回】":""}</span>,
        status: item.auditStatus=='PASS' ? 'finish' : item.auditStatus=='NONE' ? 'wait' : 'process',
        icon: item.auditStatus=='WAIT' ? <LoadingOutlined /> : <UserOutlined />,
        description: <>
          <div style={{fontSize: 12}}>{item?.auditDate ? moment(item?.auditDate).format("YYYY-MM-DD HH:mm:ss") : null}</div>
          <div style={{fontSize: 13}}>{item?.auditor}{item?.remark ? "："+item.remark : null}</div>
          {item.auditStatus=='WAIT' ? <a onClick={()=>sendDingTalk(item?.id)} style={{fontSize: 12}}><DingdingOutlined/>催一下</a> : null}
        </>
      })
    })
    const currentAudit = res.body?.financeExpenseAuditInfo?.find(item=>item.auditStatus == 'WAIT');
    setCurrentAuditData(currentAudit);
    setAuditStepList(auditStep);

    if(!bankList){
      //付款银行
      getPaymentBankList().then(res=>{
        let list: never[] = [];
        if(res?.body?.length > 0){
          res.body?.map((item: any) => {
            // @ts-ignore
            list[item?.paymentBankName] = item?.paymentBankName;
          })
        }
        setBankList(list);
      })
    }
    if(!financeSubjectList){
      //付款银行
      getFinanceSubjectList().then(res=>{
        let list: never[] = [];
        if(res?.body?.length > 0){
          res.body?.map((item: any) => {
            // @ts-ignore
            list[item?.name] =item?.name;
          })
        }
        setFinanceSubjectList(list);
      })
    }

    //付款银行转译
    form.setFieldValue("paymentBank", res.body?.paymentBank == null ? [] : JSON.parse(res.body?.paymentBank))
    return res.body;
  }));


  const sendDingTalk=(id: string)=>{

    reqByUrl("/sales-mgmt-biz/sales-center/finance/expense/reminderAudit", {auditId:id}).then((result) => {
      if (result.status.success) {
        refresh();
        notification.success({message: '发送成功'});
      }
    });

  }

  //数据提交
  const handleSubmit = () => {
    if(currentAuditData==undefined || currentAuditData?.auditor!=initialState?.currentUser?.nickName){
      message.error("无需审核！");
      return;
    }
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      setLoading(true);
      auditFinanceExpense(params).then(res => {
        setLoading(false);
        if (res.status.success) {
          message.success("审核成功");
          refresh();
        }
      })
    })
  }

  const columns: ColumnProps<any>[] = [
    {
      title: '用途',
      dataIndex: 'content',
      width: 300,
      render: (v, record) => {
        return <>
          <div>{record.content}</div>
        </>
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      render: (v, record) => {
        return <>
          <div>{record.amount}</div>
        </>
      }
    },
    {
      title: '汇率',
      key: 'exchangeRate',
      dataIndex: 'exchangeRate',
      width: 100,
      render: (v, record) => {
        return <>
          <div>{record.exchangeRate}</div>
        </>
      }
    },
    {
      title: '币种',
      key: 'currency',
      dataIndex: 'currency',
      width: 100,
      render: (v, record) => {
        return <>
          <div>{record.currency}</div>
        </>
      }
    }
  ];

  const columnsShare: ColumnProps<any>[] = [
    {
      title: '核算小组',
      dataIndex: 'organizationName',
      width: 150,
      render: (v, record) => {
        return <>
          <div>{record.organizationName}</div>
        </>
      }
    },
    {
      title: '比例',
      dataIndex: 'rate',
      width: 100,
      render: (v, record) => {
        return <>
          <div>{record.rate}</div>
        </>
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      render: (v, record) => {
        return <>
          <div>{record.amount}</div>
        </>
      }
    }
  ];
  const auditLogColumn: ColumnProps<any>[] = [
    {
      title: '审核人',
      dataIndex: 'userName',
      width: 80,
      render: (v, record) => {
        return <>
          <div>{record.userName}</div>
        </>
      }
    },
    {
      title: '信息',
      dataIndex: 'content',
      width: 300,
      render: (v, record) => {
        return <>
          <div>{record.content}</div>
        </>
      }
    },
    {
      title: '时间',
      dataIndex: 'gmtCreate',
      width: 150,
      render: (v, record) => {
        return <>
            <div style={{fontSize: 12}}>{moment(record.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}</div>
        </>
      }
    }
  ];
  return <Modal {...rest} title="报销详情" width={"80%"} onOk={handleSubmit} confirmLoading={loading} maskClosable={false}>
    <div  style={{maxHeight: 650, overflow: "scroll", overflowX: "hidden"}}>
    <Card bodyStyle={{padding: "0 10px 0 10px"}} bordered={false}>
      <div style={{marginBottom: 10}}>
        <div style={{fontWeight: "bold",fontSize: 14, color:"darkblue"}}>基础信息</div>
        <Divider type="horizontal" style={{margin: 0, padding: 0}}/>
      </div>
      <ProDescriptions
        labelStyle={{fontWeight: "bold"}}
        size={"small"}
        dataSource={data}
        columns={[
          {
            title: '标题',
            key: 'text',
            dataIndex: 'title',
            span: 24,
            ellipsis: true,
            copyable: true,
          }
        ]}
      />
      <ProDescriptions column={4} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="流水号" valueType="text" copyable={true}>
          {data?.serialNumber}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="申请人" valueType="text" >
          {data?.applyUsername}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="申请小组" valueType="text">
          {data?.applyOrganizationName}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="是否加急" valueType="text">
          {data?.priority==1? <span style={{color: "red"}}>是</span> : "否"}
        </ProDescriptions.Item>
      </ProDescriptions>

      <ProDescriptions column={4} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="分摊类型" valueType="select" valueEnum={apportionTypeEnum}>
          {data?.shareType}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="分摊公司" valueType="text" >
          {data?.companyName}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="支付方式" valueType="select" valueEnum={{
          'BANK': '银行转账',
          'ONLINE': '在线支付',
        }}>
          {data?.payment}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="单据类型" valueType="select" valueEnum={auditModelEnum}>
          {data?.auditModel}
        </ProDescriptions.Item>
      </ProDescriptions>

      <ProDescriptions column={4} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="收款人" valueType="text" hideInDescriptions={data?.payment!="BANK"}>
          {data?.accountName}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="收款账号" valueType="text" hideInDescriptions={data?.payment!="BANK"}>
          {data?.paymentAccount}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="收款银行" valueType="text" hideInDescriptions={data?.payment!="BANK"}>
          {data?.accountBank}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="身份证" valueType="text" hideInDescriptions={data?.payment!="BANK"}>
        {data?.payeeIdNumber}
        </ProDescriptions.Item>
      </ProDescriptions>

      <ProDescriptions column={4} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="是否核算" valueType="text">
          {data?.isCost==1 ? <span style={{color: "green"}}>是</span> : '否'}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="报销科目" valueType="text" >
          {data?.financeSubject || '--'}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="付款链接" valueType="text" hideInDescriptions={data?.payment=="BANK"}>
          {data?.paymentLink || '--'}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="付款银行" valueType="text" hideInDescriptions={data?.payment!="BANK"}>
          {data?.paymentBank ? JSON.parse(data?.paymentBank).map(item =>{return <Tag style={{margin: 3}} color={"orange"}>{item}</Tag>}) : '--'}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="付款时间" valueType="text" >
          {data?.paymentTime!=undefined? <div>{moment(data.paymentTime).format("YYYY-MM-DD HH:mm:ss")}</div> : '--'}
        </ProDescriptions.Item>
      </ProDescriptions>

      <ProDescriptions column={4} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="申请总额" valueType="text">
          {data?.amount?.toFixed(2)}&nbsp;{data?.currency}
        </ProDescriptions.Item>
        {/*<ProDescriptions.Item label="核算总额" valueType="textarea">*/}
        {/*  {data?.amount}&nbsp;{data?.currency}*/}
        {/*</ProDescriptions.Item>*/}
      </ProDescriptions>
      <ProDescriptions column={2} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="备注" valueType="textarea" span={2}>
          <Tag color={'green'} style={{fontSize: 14}}>{data?.remark}</Tag>
        </ProDescriptions.Item>
      </ProDescriptions>
      <ProDescriptions column={2} size={"small"} labelStyle={{fontWeight: "bold"}}>
        <ProDescriptions.Item label="附件" valueType="text" span={2}>
          {data?.expenseFile!=undefined  ? <UploadFile disabled={true} onChange={(e)=>{console.log(e)}} value={JSON.parse(data.expenseFile)} /> : '--'}
        </ProDescriptions.Item>
      </ProDescriptions>
    </Card>

    <Card bodyStyle={{padding: "0 80px 0 10px"}} bordered={false}>
      <Steps
        items={auditStepList}
        style={{padding: 0}}
      />
    </Card>
    <Card style={{marginTop: 10}} bodyStyle={{padding: 0}} bordered={false} >
      <Row>
        <Col span={12}>
          <div style={{marginBottom: 10}}>
            <div style={{fontWeight: "bold",fontSize: 14, color:"darkblue"}}>申请事项</div>
            <Divider type="horizontal" style={{margin: 0, padding: 0}}/>
          </div>
          <Table
            size={"small"}
            dataSource={data?.financeExpenseDetailInfo}
            columns={columns}
            bordered={true}
            pagination={false}
          />
        </Col>
        <Col span={12} style={{paddingLeft: 10}} hidden={currentAuditData==undefined || currentAuditData?.auditor!=initialState?.currentUser?.nickName}>
          <div style={{marginBottom: 10}}>
            <div style={{fontWeight: "bold",fontSize: 14, color:"darkblue"}}>审核区域</div>
            <Divider type="horizontal" style={{margin: 0, padding: 0}}/>
          </div>
          <Form form={form} labelCol={{flex: '80px'}} size={"small"}>
            <ProForm.Group>
              <ProFormText
                name="id"
                hidden={true}
                width={"sm"}
                initialValue={expense?.id}
              />
              <ProFormText
                name="auditStatus"
                label="审核"
                width={"sm"}
                style={{width: 250}}
                rules={[{ required: true}]}
              >
                <Radio.Group onChange={(e)=>{
                  e.target.value == 'PASS' ? setRequired(true) : setRequired(false);
                  form.setFieldValue("auditStatus", e.target.value);
                }}>
                  <Radio value="PASS">{currentAuditData?.isPaymentAudit==1 ? "确认付款" : "通过"}</Radio>
                  <Radio value="REJECT">驳回</Radio>
                </Radio.Group>
              </ProFormText>
            </ProForm.Group>
            {currentAuditData?.isCostAudit==1 ? <ProFormText
              name="isCost"
              label="是否核算"
              width={300}
              rules={[{ required: required}]}
            >
              <Radio.Group>
                <Radio value="1">是</Radio>
                <Radio value="0">否</Radio>
              </Radio.Group>
            </ProFormText> : null}
            {currentAuditData?.isCostAudit==1 ? <>
              <ProFormSelect
                name="paymentBank"
                label="付款银行"
                width={"sm"}
                rules={[{ required: required}]}
                mode="tags"
                valueEnum={bankList}
              />
              <ProFormSelect
                name="financeSubject"
                label="财务科目"
                width={"sm"}
                hidden={currentAuditData?.isCostAudit!=1}
                showSearch={true}
                valueEnum={financeSubjectList}
              />
            </> : null}
            {currentAuditData?.isPaymentAudit==1 ?
              <>
                <ProFormSelect
                  name="paymentBank"
                  label="付款银行"
                  width={"sm"}
                  mode="tags"
                  rules={[{ required: required}]}
                  valueEnum={bankList}
                />
                <ProFormDatePicker  width={"sm"} dataFormat={"YYYY-MM-DD"}   name={"paymentTime"} label={"付款时间"} />
                <ProFormText
                  name="paymentBank1"
                  label="五星好评"
                  width={"sm"}
                >
                  <Rate allowHalf defaultValue={2.5} />
                </ProFormText>

              </>
              : null}
            <ProFormTextArea
              width={"lg"}
              name="remark"
              label="备注"
              colProps={{ span: 0.8 }}
            />
          </Form>
        </Col>
      </Row>
    </Card>

    <Card size={"small"} style={{marginTop: 10}} bodyStyle={{padding: 0}} bordered={false}>
      <Row>
        <Col span={12}>
          <div style={{marginBottom: 10}}>
            <div style={{fontWeight: "bold",fontSize: 14, color:"darkblue"}}>部门分摊</div>
            <Divider type="horizontal" style={{margin: 0, padding: 0}}/>
          </div>
          <Table
            size={"small"}
            dataSource={data?.financeExpenseApportionInfo}
            bordered={true}
            columns={columnsShare}
            pagination={false}
          />
        </Col>
        <Col span={12} style={{paddingLeft: 10}}>
          <div style={{marginBottom: 10}}>
            <div style={{fontWeight: "bold",fontSize: 14, color:"darkblue"}}>审核日志</div>
            <Divider type="horizontal" style={{margin: 0, padding: 0}}/>
          </div>
          <Table
            size={"small"}
            dataSource={data?.financeExpenseRemarkInfo}
            bordered={false}
            columns={auditLogColumn}
            pagination={{pageSize: 4}}
          />
        </Col>
      </Row>
    </Card>
    </div>
  </Modal>
}

export default ExpenseDetailModal;
