import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;




// 采购计划列表
export async function pageQueryPlanConfig(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<any[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/plan/pageQuery',
    data,
  });
}




