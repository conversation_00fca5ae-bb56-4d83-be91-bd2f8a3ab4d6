import {Col, message, Modal, ModalProps, Row, Select} from 'antd';
import React, { useEffect } from 'react';
import { useState } from 'react';
import ProForm, {
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import {Goods} from "@/modules/goods/domain/goods";
import {createGoodsSkuInfo, getGoodsSkuInfo, updateGoodsSkuInfo} from "@/modules/goods/infra/api/goods";
import CustomUploadImageGroup from "@/components/CustomUploadImageGroup";
import CurrencySelect from '@/components/common/CurrencySelect';

export type  skuEditParams = {
  goodsData: Goods,
  onFinish: () => void;
} & ModalProps;
export const SkuEditModal =  (props: skuEditParams) =>  {

  const {goodsData, onFinish, ...rest } = props;
  const [form] = ProForm.useForm();

  const [submitting, setSubmitting] = useState<boolean>(false);
  const [goodsImages, setGoodsImages] = useState<any>();
  useEffect(() => {
    form.resetFields()
    if(goodsData?.skuId){
      getGoodsSkuInfo({ skuId: goodsData?.skuId }).then((res) => {
        if(res.status.success){
          var imageList = [];
          imageList.push(res.body?.mainSpecInfo?.image);
          setGoodsImages(imageList)
          form.setFieldValue("goodsImages", imageList);
          form.setFieldsValue({
            ...res.body,
          })
        }
      })
    }
  }, [goodsData]);

  /**
   * 提交修改信息
   * @param values
   */
  const onOk = async () => {
    setSubmitting(true);
    console.log(form.getFieldsValue())
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();

      params["mainSpecInfo"] ={image:params?.goodsImages[0]};

      if(goodsData?.skuId){
        //修改
        updateGoodsSkuInfo(params).then((res) => {
          if(res.status.success){
            message.success("修改成功")
            onFinish();
          }
        })
      }else {
        //添加
        createGoodsSkuInfo(params).then((res) => {
          if(res.status.success){
            message.success("添加成功")
            onFinish();
          }
        })
      }
    })
    setSubmitting(false);
  };

  return (
    <Modal confirmLoading={submitting} {...rest} title="商品SKU" closable={false} width="60%" onOk={onOk} onCancel={onFinish}>
      <ProForm
        form={form}
        layout="horizontal"
        labelCol={{ span: 5 }}
        submitter={false}
      >
        <Row gutter={24}>
          <Col lg={12} md={12} sm={24}>
            <ProFormText
              label={"spuId"}
              name="spuId"
              hidden={true}
              initialValue={goodsData?.spuId}
            />
            <ProFormText
              label={"skuId"}
              name="skuId"
              hidden={true}
            />
            <ProFormText
              label={"SKU"}
              name="sku"
              disabled={goodsData?.skuId?true:false}
              rules={[{ required: true, message: '请输入SKU' }]}
            />
            <ProFormText
              label={"名称"}
              name="skuName"
              rules={[{ required: true, message: '请输入名称' }]}
            />
            <ProFormText
              label="商品类型"
              name="productType"
            >
              <Select
                allowClear={true}
                options={[
                  { value: "NORMAL", label: '普通' },
                  { value: "BIND", label: '捆绑' },
                  { value: "ASSEMBLE", label: '组装' },
                ]}
              />
            </ProFormText>
            <ProFormText
              label={"产品材质"}
              name="productMaterial"
            />
            <ProFormText
              label={"币种"}
              name="currency"
              rules={[{ required: true, message: '请选择币种' }]}
            >
              <CurrencySelect showSearch />
            </ProFormText>
            <ProFormDigit
              label={"开发价格"}
              name="referenceCost"
              disabled={!!goodsData?.skuId}
              rules={[{ required: true, message: '请输入开发价格' }]}
            />
            <ProFormText
              label={"属性名"}
              name="skuAttrName"
            />
            <ProFormDigit
              label={"长(cm)"}
              name="sizeLength"
            />
            <ProFormDigit
              label={"高(cm)"}
              name="sizeHeight"
            />
            <ProFormTextArea
              label={"运营备注"}
              name="promotionSuggestion"
              fieldProps={{
                rows: 4,
                maxLength: 500,
                showCount: true,
              }}
            />
            <ProFormText
              label={"参考链接"}
              name="refProductLink"
            />
            <ProFormText
              label={"图片"}
              name="goodsImages"
              rules={[{ required: true, message: '请上传图片' }]}
            >
              <CustomUploadImageGroup value={goodsImages}  maxCount={1} onChange={(e)=>{
                setGoodsImages(e);
                form.setFieldValue("goodsImages", e);
              }}/>
            </ProFormText>
          </Col>
          <Col lg={12} md={12} sm={24}>
            <ProFormText
              rules={[{ required: true, message: '请选择销售状态' }]}
              label="销售状态"
              name="salesStatus"
            >
              <Select
                allowClear={true}
                options={[
                  { value: "10", label: '新品' },
                  { value: "20", label: '正常' },
                  { value: "30", label: '热销' },
                  { value: "40", label: '滞销' },
                  { value: "50", label: '清货' },
                  { value: "60", label: '停售' },
                  { value: "70", label: '销毁' },
                ]}
              />
            </ProFormText>
            <ProFormText
              label={"英文名称"}
              name="nameEn"
            />
            <ProFormText
              label="包装材料"
              name="packingMaterial"
            >
              <Select
                allowClear={true}
                options={[
                  { value: "PLASTIC_BAG", label: '塑料袋' },
                  { value: "BUBBLE_BAG", label: '泡泡袋' },
                  { value: "NEUTRAL_BOX", label: '中性纸盒' },
                  { value: "COLOR_BOX", label: '彩盒' },
                ]}
              />
            </ProFormText>
            <ProFormDigit
              label={"最小采购量"}
              name="minPurchaseQuantity"
              fieldProps={{
                precision: 0,
                min: 1,
              }}
            />
            <ProFormDigit
              label={"包装费"}
              name="packingFee"
            />
            <ProFormDigit
              label={"上次采购价"}
              name="lastPurchasePrice"
              disabled={true}
            />
            <ProFormText
              label={"属性值"}
              name="skuAttrValue"
            />
            <ProFormDigit
              label={"宽(cm)"}
              name="sizeWidth"
            />
            <ProFormDigit
              label={"毛重(g)"}
              name="grossWeight"
            />
            <ProFormTextArea
              label={"文案备注"}
              name="copywritingSuggestion"
              fieldProps={{
                rows: 4,
                maxLength: 500,
                showCount: true,
              }}
            />
          </Col>
        </Row>
      </ProForm>
    </Modal>
  );
};

export default SkuEditModal;
