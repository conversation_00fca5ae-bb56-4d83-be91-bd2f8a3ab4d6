import type {ModalProps} from 'antd';
import {Alert, message, Modal, notification, Space, Table, Typography} from 'antd';
import type {ProColumns} from '@ant-design/pro-table';
import React, {useEffect, useState} from 'react';
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {financeAudit, financeAuditQuery} from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import {useRequest} from 'ahooks';
import {financeErrorTypeEnum, PaymentStatusEnum, payTypeEnum} from "@/modules/purchaseOrder/domain/purchaseOrder";
import moment from "moment";
const { Text } = Typography;

const auditData= async (param: any)=>{
  const data=await financeAuditQuery(param);
  return data;
}

const CheckedOrderModal = (props: CheckedOrderModalProps) => {
  const {onFinish, orderData, ...rest} = props;

  const param = {
    "orderIdList": orderData.map((item) => item.id),
  }

  const { data, refresh, loading} = useRequest(() => auditData(param));

  const onOkManage = () =>{

    financeAudit(param).then((result) => {
      if (result.status.success) {
        notification.success({ message: '审核成功' });
        refresh();
      }
    });

  }

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '采购单号',
      dataIndex: 'orderCode'
    },
    {
      title: '标题',
      hideInSearch: true,
      dataIndex: 'title'
    },
    {
      title: '账号',
      hideInSearch: true,
      dataIndex: 'platformAccount'
    },
    {
      title: '1688订单号',
      hideInSearch: true,
      dataIndex: 'platformOrderCode'
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      render: (v, record) => {
        const payStatus = payTypeEnum[record?.payType];
        const options = [
          payStatus != undefined ? (
            <span>
              <span className="ant-badge-status-dot ant-badge-status-success"></span>
              <span className="ant-badge-status-text">{payStatus}</span>
            </span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '申请付款时间',
      hideInSearch: true,
      dataIndex: 'paymentTime',
      render: (v, record) => {
        return <div>{moment(record?.paymentTime)?.format("YYYY-MM-DD HH:mm:ss")}</div>;
      }
    },
    {
      title: '1688总价',
      hideInSearch: true,
      dataIndex: 'platformOrderAmount',
      render: (v, record) => {
        return record.platformOrderAmount?.toFixed(2);
      }
    },
    {
      title: '应付货款',
      hideInSearch: true,
      dataIndex: 'totalPrice',
      render: (v, record) => {
        const amount=record.totalPrice+record.shippingFee+record.applyRefundAmount-record.applyPromotionAmount;
        return amount?.toFixed(2);
      }
    },
    {
      title: '申请支付金额',
      hideInSearch: true,
      dataIndex: 'platformOrderAmount',
      render: (v, record) => {
        return record.platformOrderAmount?.toFixed(2);
      }
    },
    {
      title: 'ERP运费',
      hideInSearch: true,
      dataIndex: 'shippingFee',
      render: (v, record) => {
        return record.shippingFee?.toFixed(2);
      }
    },
    {
      title: '1688运费',
      hideInSearch: true,
      dataIndex: 'platformShippingFee',
      render: (v, record) => {
        return record.platformShippingFee?.toFixed(2);
      }
    },
    {
      title: '优惠金额',
      hideInSearch: true,
      dataIndex: 'applyPromotionAmount',
      render: (v, record) => {
        return record.applyPromotionAmount?.toFixed(2);
      }
    },
    {
      title: '后台刷新时间',
      hideInSearch: true,
      dataIndex: 'platformSyncDataTime'
    },
    {
      title: '异常',
      hideInSearch: true,
      dataIndex: 'paymentType',
      render: (v, record) => {
        const options = [];
        record.financeErrorList.map((item)=>{
            if(item!=null && item!=""){
              options.push(<span>
              <span className="ant-badge-status-dot ant-badge-status-error"></span>
              <span className="ant-badge-status-text">{financeErrorTypeEnum[item]}</span>
            </span>)
            };
        });
        return <Space direction="vertical">{options}</Space>;
      }
    },


  ];

  return (
    <Modal {...rest} title="财务审核" closable={false}   width="90"  closeIcon={"×"}  cancelText={"关闭"} okText={"审核"} onOk={() => onOkManage()}  destroyOnClose>
      <Alert type={"warning"} message={"点击审核时系统会自动判断 1688总金额 是否等于  应付货款，如果不一致则驳回，相同则进入下个流程。"} />
      <Table
        dataSource={data?.body}
        columns={columns}
        rowKey="id"
        scroll={{ y: 550 }}
        pagination = {false}
        loading={loading}
        summary={(Data) => {
          let erpTotal = 0;

          let aliTotal = 0;
          for(const i in Data){
            erpTotal+=Data[i].totalPrice+Data[i].shippingFee+Data[i].applyRefundAmount-Data[i].applyPromotionAmount;
            aliTotal+=Data[i].platformOrderAmount;
          }

          return (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}>合计：</Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <Text>erp总金额&nbsp;{erpTotal?.toFixed(2)}</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2}>
                  <Text>1688总金额&nbsp;{aliTotal?.toFixed(2)}</Text>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </>
          );
        }}
      />
    </Modal>
  );
}

// 定义参数格式
export type CheckedOrderModalProps = {
  orderData: PurchaseOrder[];
  onFinish: (values: PurchaseOrder[]) => void;

} & ModalProps;
export default CheckedOrderModal;
