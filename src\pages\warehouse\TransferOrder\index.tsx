import React, {useEffect, useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import {Button, Form, Modal, notification, Space} from "antd";
import {ProFormField} from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import Permission from "@/components/Permission";
import {downloadTransferOrder} from "@/modules/warehouse/infra/api/warehouse";
import moment from "moment";
import TransferOrderDetailModal from "@/pages/warehouse/TransferOrder/components/TransferOrderDetailModal";
import {TransferOrder, TransferOrderGoods} from "@/pages/warehouse/TransferOrder/data";
import {getTagsPrintInfo} from "@/modules/goods/infra/api/goods";
import {getWarehouse} from "@/modules/purchasePlan/infra/api/purchasePlan";
import {commonExport} from "@/utils/comUtil";

const TableList: React.FC = () => {

  const [detailModal, setDetailModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<TransferOrder>();

  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/warehouse/transfer/pageQuery', {
      ...params,
    });
  });

  const [form] = Form.useForm();
  const downloadTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadTransferOrder().then(res=>{
                commonExport(res, '调拨单导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        reqByUrl("/sales-mgmt-biz/sales-center/warehouse/transfer/importTransferOrder", {link:link}).then((result) => {
          if (result.status.success) {
            actionRef.current?.reload?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }

  const columns: ProColumns<TransferOrder>[] = [
    {
      title: '单号',
      dataIndex: 'transferCode',
      width: 200,
      align:"left",
      render: (v,record)=>{
        return <span>{record?.transferCode}</span>;
      }
    },
    {
      title: '第三方单号',
      dataIndex: 'thirdpartyCode',
      width: 200,
      align:"left",
      render: (v,record)=>{
        return <span>{record?.thirdpartyCode}</span>;
      }
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      align:"left",
      render: (v,record)=>{
        return <span>{record?.creator}</span>;
      }
    },
    {
      title: '仓库名称',
      dataIndex: 'warehouseName',
      width: 200,
      request: async () => {//返回的select网络请求
        const params = await getWarehouse();
        const res = [];
        const body = params.body;
        for (const i in body) {
          const temp = {};
          temp.label = body[i];
          temp.value = i;
          res.push(temp)
        }
        return res;
      },
      hideInTable: true
    },
    {
      title: '仓库名称',
      dataIndex: 'warehouseName',
      hideInSearch: true,
      align:"left",
      render: (v,record)=>{
        return <span>{record?.warehouseName || '--'}</span>;
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align:"left",
      hideInSearch: true,
      width: 450,
      render: (v,record)=>{
        return <div>{record?.remark || '--'}</div>;
      }
    },
    {
      title: "创建时间",
      dataIndex: 'gmtCreate',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return moment(record?.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    {
      title: "修改时间",
      dataIndex: 'gmtModified',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return moment(record?.gmtModified as number).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    {
      title: '操作',
      align: "center",
      width: 160,
      hideInSearch: true,
      render: (_, record) => {
        return <>
          <Space split={<span style={{color: "rgb(24, 144, 255)"}}>|</span>}>
            <a type="primary" key={"edit"} onClick={() => {
              window.open("/purchasing-center/print/outsideGoodsTagsPrint?transferCode="+record?.transferCode);
            }} >
              打印
            </a>
            <a type="primary" key={"audit"}  onClick={()=>{setCurrentRow(record);setDetailModal(true)}}>
              详情
            </a>
          </Space>
        </>
      },
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);
  return (
    <>
      <CustomPage<TransferOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        size={"small"}
        columns={columns}
        toolBarRender={() => [
          <Permission permissionKey={"purchase:warehouse:importGoods"}>
            <Button
              type="primary"
              size={"small"}
              onClick={() => downloadTemplate()}
            >
              导入调拨单
            </Button>
          </Permission>
        ]}
      />
      <TransferOrderDetailModal visible={detailModal} rowData={currentRow} onCancel={()=>{setDetailModal(false)}}/>
    </>
  );
};

export default TableList;
