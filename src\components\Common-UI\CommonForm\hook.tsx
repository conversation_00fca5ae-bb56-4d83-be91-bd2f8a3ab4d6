import ProForm, { ProFormDependency, ProFormList } from '@ant-design/pro-form';
import { ProFromListCommonProps } from '@ant-design/pro-form/lib/components/List/ListItem';
import { getComponent } from './index';
import type { ChildRest, CommonFormChild, CommonFormColumn } from './type';

export const useFormLine = () => {
  return (title: string, childrens: (CommonFormChild | CommonFormChild[])[]) => {
    return {
      title,
      childrens,
    } as CommonFormColumn;
  };
};

export const useFormChild = (commonProps: ChildRest = {}) => {
  return (label: string, key: string, rest?: ChildRest) => {
    // 1 判断显隐
    if (rest?.show === false) {
      return null;
    }

    return {
      ...commonProps,
      label,
      key,
      // 默认为必填项
      rules: commonProps?.required === false || rest?.required === false ? undefined : [{ required: true, message: rest?.errTip }],
      ...rest,
    } as CommonFormChild;
  };
};

export const useFormCom = (commonProps: ChildRest = {}) => {
  const setChild = useFormChild();
  return (label: string, key: string, rest?: ChildRest) => getComponent(setChild(label, key, { ...rest, ...commonProps }));
};

export const useFormDependency = (commonProps: ChildRest = {}) => {
  return (dependency: string[], onDependency: (values: any) => React.ReactNode) => <ProFormDependency name={dependency}>{onDependency}</ProFormDependency>;
};

export const useFormList = (commonProps: ChildRest = {}) => {
  return (label: string, key: string, items: any[], rest: ProFromListCommonProps = {}, itemRest: ChildRest = {}) => (
    <ProForm.Item label={label} {...commonProps} {...itemRest}>
      <ProFormList name={key} {...rest}>
        {items}
      </ProFormList>
    </ProForm.Item>
  );
};
