import ImageModal from "@/components/ImageModal";
import { Image } from 'antd';
import { useRef } from 'react';
const Index = (props: any) => {
  const { src, title, ...rest } = props;
  const imageModalRef = useRef();
  return (
    <>
      {/* @ts-ignore */}
      <Image src={src} onClick={() => imageModalRef?.current?.open({ imgUrl: src, title: title })} style={{ cursor: 'pointer' }} preview={{ visible: false }} { ...rest } />
      {/* @ts-ignore */}
      <ImageModal ref={imageModalRef} />
    </>
  )
}
export default Index;