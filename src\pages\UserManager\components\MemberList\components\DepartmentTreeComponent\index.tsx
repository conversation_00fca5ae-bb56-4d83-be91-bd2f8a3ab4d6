import { DepartmentTree } from "@/modules/user-center/domain/department";
import { getDeptTreeByEndpoint } from "@/modules/user-center/infra/organize";
import { UserManagerContext } from "@/pages/UserManager";
import { useControllableValue, useRequest } from "ahooks";
import { useContext, useState, useMemo } from "react";
import CreateOrUpdateDepartmentModal from "../../../DepartmentList/CreateOrUpdateDepartmentModal";
import { Button, Input, Space, Tree, Spin } from 'antd';
import styles from './styles.less'
import { CarryOutOutlined, FileOutlined } from "@ant-design/icons";
import { history } from "umi";
import { GlobalWindow } from "@/types/global";

export type DepartmentTreeComponentProps = {
  value: string[];
  onChange: (value: string[]) => void;
}



export const formattedDepartTreeData = (data: DepartmentTree[] = []): any[] => {
  if (!data.length) {
    return []
  }
  return data.map(item => {
    const children = formattedDepartTreeData(item.children || []);
    if (children.length) {
      return {
        title: item.name,
        key: item.deptId,
        children,
      }
    }
    return {
      // ...item,
      icon: <FileOutlined />,
      title: item.name,
      key: item.deptId,
    }
  })
}
export const DepartmentTreeComponent = (props: DepartmentTreeComponentProps) => {
  const useManagerService = useContext(UserManagerContext);
  const [searchValue, setSearchValue] = useState<string>();
  const [selectedKeys, setSelectedKeys] = useControllableValue<string[]>(props);
  const { data, run, loading, refresh } = useRequest((params) => {
    return getDeptTreeByEndpoint({
      endpoint: useManagerService.endpoint,
      ...params,
    })
  }, { refreshDeps: [useManagerService.endpoint] })
  const treeData = useMemo(() => {
    return [{
      title: '全部',
      key: '0',
      children: formattedDepartTreeData(data?.body || []),
    }]
  }, [data?.body]);

  return <div>
    <Input style={{ width: '100%' }} value={searchValue} onChange={(e) => {
      setSearchValue(e.target.value);
    }} onPressEnter={() => {
      run({ name: searchValue })
    }} />
    <Space style={{ padding: '10px 0' }}>
      <CreateOrUpdateDepartmentModal trigger={<Button>添加部门</Button>} onFinish={async () => refresh()} />
      <Button onClick={() => {
        if ((window as GlobalWindow).onHistoryChange) {
          (window as GlobalWindow).onHistoryChange?.('/dept');
        } else {
          history.push('/dept' as any);
        }
      }}>部门管理</Button>
    </Space>
    <Spin spinning={loading}>
      <Tree.DirectoryTree
        defaultExpandAll
        showLine
        onSelect={(keys) => {
          setSelectedKeys(keys as string[]);
        }}
        showIcon
        className={styles.tree}
        treeData={treeData}
        selectedKeys={selectedKeys}
      />
    </Spin>
  </div>
}

export default DepartmentTreeComponent;
