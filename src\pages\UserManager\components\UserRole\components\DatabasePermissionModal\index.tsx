import RenderTitle from '@/components/RenderTitle';
import BrandSelect from '@/modules/product/infra/components/BrandSelect';
import { useRoleDataRule } from '@/modules/user/application/role';
import type { Role, UserRoleDataRule } from '@/modules/user/domian/role';
import { getDataRuleInfo } from '@/modules/user/infra/api/dataRules';
import { ProFormDependency, ProFormField, ProFormText } from '@ant-design/pro-form';
import { Button, Form, Radio, Spin, Table } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { useEffect } from 'react';
import styles from './styles.less';
import { findCurrentKey } from './utils';

export interface DatabasePermissionModalProps {
  role: Role;
}

const DatabasePermissionModal = (props: DatabasePermissionModalProps) => {
  const { role } = props;
  const [form] = Form.useForm();
  const { dataRules, loading, loadDataRules, save } = useRoleDataRule(role);

  useEffect(() => {
    const transformDataSource = async () => {
      if (dataRules?.length) {
        const data = [...dataRules];
        let i = 0;
        while (i < dataRules?.length) {
          const current = data[i];
          current.allowedItems = current.allowedItems || [];
          if (current.allowedMode === 'PARTIAL') {
            const res = await getDataRuleInfo({ roleId: role.roleId, rulesKey: current.rulesKey });
            current.allowedItems = res.body.allowedItems || [];
          }
          i++;
        }
        form.setFieldsValue({
          rulesList: data,
        });
      }
    };
    transformDataSource();
  }, [dataRules]);

  useEffect(() => {
    loadDataRules();
  }, [role]);

  const columns: ColumnsType<UserRoleDataRule> = [
    {
      title: '权限点',
      dataIndex: 'title',
      width: '15%',
      render: (v, record) => {
        const item = findCurrentKey(record.rulesKey);
        return item?.title;
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: '25%',
      render: (v, record) => {
        const item = findCurrentKey(record.rulesKey);
        return item?.description;
      },
    },
    {
      title: '数据范围',
      dataIndex: 'allowedItems',
      render: (v, record, index) => {
        return (
          <>
            <ProFormText
              name={['rulesList', index, 'rulesKey']}
              initialValue={record.rulesKey}
              hidden
            />
            <ProFormField
              name={['rulesList', index, 'allowedMode']}
              initialValue={record.allowedMode}
              formItemProps={{ style: { marginBottom: 0 } }}
            >
              <Radio.Group>
                <Radio value="ALL">全部可见</Radio>
                <Radio value="PARTIAL">部分可见</Radio>
                <Radio value="LIMIT_ALL">全部不可见</Radio>
              </Radio.Group>
            </ProFormField>
            <ProFormDependency name={['rulesList']} dependencies={['rulesList']}>
              {({ rulesList }) => {
                const value = rulesList[index];
                if (value.allowedMode !== 'PARTIAL') {
                  return null;
                }
                return (
                  <div className={styles.container}>
                    <ProFormField
                      name={['rulesList', index, 'allowedItems']}
                      label="请选择品牌"
                      rules={[{ required: true, message: '请选择品牌' }]}
                      initialValue={record.allowedItems || []}
                    >
                      <BrandSelect mode="multiple" />
                    </ProFormField>
                  </div>
                );
              }}
            </ProFormDependency>
          </>
        );
      },
    },
  ];
  return (
    <Spin spinning={loading}>
      <RenderTitle style={{ paddingTop: 0 }}>权限设置</RenderTitle>
      <Form initialValues={{ type: 1 }} onFinish={save} form={form}>
        <Table
          rowKey="rulesKey"
          dataSource={dataRules}
          pagination={false}
          size="small"
          bordered
          columns={columns}
        />
      </Form>
      <Button style={{ marginTop: 10 }} type="primary" onClick={() => form.submit()}>
        保存
      </Button>
    </Spin>
  );
};
export default DatabasePermissionModal;
