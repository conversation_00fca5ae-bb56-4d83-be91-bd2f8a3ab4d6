import useBatchFunction from '@/hooks/useBatchFunction';
import { useRequestTable } from '@/hooks/useRequestTable';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { history } from 'umi';
import type { Spu } from '../domain/spu';
import type { UpdateSpuParams } from '../infra/api/spu';
import { copySpu } from '../infra/api/spu';
import { queryPageSpu } from '../infra/api/spu';
import { createSpu, deleteSpu, getSpuDetail, updateSpu } from '../infra/api/spu';

export const useSpuListTable = () => {
  const productListService = useRequestTable(queryPageSpu);
  return {
    ...productListService,
  };
};

export function useSpuLoad() {
  const productLoad = useRequest(
    async (spuId: string) => {
      if (spuId) {
        const res = await getSpuDetail(spuId);
        return res.body;
      }
      return undefined;
    },
    { manual: true },
  );

  return productLoad;
}

export const useSpu = () => {
  const productList = useSpuListTable();
  const batchDelete = useBatchFunction((spuId: string) => deleteSpu({ spuId }));
  const productLoad = useSpuLoad();

  const copy = async (spuId: string) => {
    const res = await copySpu({ spuId });
    if (res.status.success) {
      message.success('操作成功');
      productList.actionRef.current?.reload();
    } else {
      message.error('操作失败');
    }
  };

  const remove = async (spuId: string) => {
    const res = await deleteSpu({ spuId });
    if (res.status.success) {
      message.success('操作成功');
      productList.actionRef.current?.reload();
    } else {
      message.error('操作失败');
    }
  };

  const create = async (data: Spu) => {
    const res = await createSpu(data);
    if (res.status.success) {
      message.success('创建成功');
      productList.actionRef.current?.reload();
      history.push('/product_manager/spu');
    } else {
      message.error('创建失败');
    }
  };

  const update = async (data: UpdateSpuParams, callback?: () => void) => {
    const res = await updateSpu(data);
    if (res.status.success) {
      message.success('编辑成功');
      productList.actionRef.current?.reload();
      callback?.();
    } else {
      message.error('编辑失败');
    }
  };

  return {
    ...productList,
    batchDelete,
    copy,
    remove,
    create,
    update,
    productLoad,
  };
};
