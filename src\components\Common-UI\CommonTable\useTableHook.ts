import { useEffect, useState } from 'react';

// 1) 判断搜索栏行数
const commonMap = {
  1400: 8,
  1600: 6,
  2000: 4,
};
const unLabelMap = {
  1400: 6,
  1600: 4,
};
export const useTableSearch = (contentRef: React.MutableRefObject<HTMLDivElement>, hasSearch: boolean, isUnLabel?: boolean) => {
  const [rowSpan, setRowSpan] = useState(8);

  useEffect(() => {
    if (!hasSearch) return;
    const wMap = isUnLabel ? unLabelMap : commonMap;
    const judgeWidth = () => {
      const width = contentRef.current?.offsetWidth || document.documentElement.offsetWidth;
      const keys = Object.keys(wMap);
      const index = keys.findIndex((key) => width < +key);
      setRowSpan(index !== -1 ? wMap[keys[index]] : 4);
    };
    judgeWidth();
    window.addEventListener('resize', judgeWidth);

    return () => {
      window.removeEventListener('resize', judgeWidth);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    rowSpan,
  };
};
