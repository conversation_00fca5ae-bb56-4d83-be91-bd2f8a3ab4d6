import { getDeptTreeByEndpoint } from '@/modules/user-center/infra/organize';
import { useControllableValue, useRequest } from 'ahooks';
import { ModalProps } from 'antd';
import { Button, Modal, Row, Select, Space, Transfer } from 'antd';
import { TransferDirection, TransferItem } from 'antd/es/transfer';
import { DataNode } from 'antd/es/tree';
import { useContext, useEffect, useMemo, useState } from 'react';
import { UserManagerContext } from '../..';
import { formattedDepartTreeData } from '../MemberList/components/DepartmentTreeComponent';
import styles from './styles.less';
import SearchTree from './SearchTree';

export type DepartmentSelectProps = {
  value?: DataNode[];
  onChange?: (value: DataNode[]) => void;
};

export type SelectDepartmentModalProps = DepartmentSelectProps &
  Pick<ModalProps, 'visible' | 'onCancel'>;

interface TreeTransferProps {
  dataSource: DataNode[];
  targetKeys: string[];
  onChange: (targetKeys: string[], direction: TransferDirection, moveKeys: string[]) => void;
  onSelect?: (selectedRows: DataNode[]) => void;
}


const generateTree = (treeNodes: DataNode[] = [], checkedKeys: string[] = []): DataNode[] =>
  treeNodes.map(({ children, ...props }) => ({
    ...props,
    disabled: checkedKeys.includes(props.key as string),
    children: generateTree(children, checkedKeys),
  }));

const isChecked = (selectedKeys: (string | number)[], eventKey: string | number) =>
  selectedKeys.includes(eventKey);
const TreeTransfer = ({ dataSource, targetKeys, onSelect, onChange, ...restProps }: TreeTransferProps) => {
  const transferDataSource: TransferItem[] = [];
  function flatten(list: DataNode[] = []) {
    list.forEach((item) => {
      transferDataSource.push({ ...item as TransferItem });
      flatten([...item.children || []]);
    });
  }
  flatten(dataSource);

  const handleSelect = (keys: string[]) => {
    onSelect?.(transferDataSource.filter(item => item.key && keys.includes(item.key)) as any);
  }

  return (
    <Transfer
      titles={['全部', '已选择']}
      {...restProps}
      targetKeys={targetKeys}
      dataSource={transferDataSource}
      className="tree-transfer"
      render={(item) => item.title!}
      showSelectAll={true}
      onChange={(keys, ...args) => {
        onChange(keys, ...args);
        handleSelect(keys);
      }}
      oneWay
    >
      {({ direction, onItemSelect, selectedKeys, ...rest }) => {
        if (direction === 'left') {
          const checkedKeys = [...selectedKeys, ...targetKeys];
          return (
            <div style={{ padding: '8px' }}>
              <SearchTree
                className={styles.tree}
                showLine
                blockNode
                checkable
                checkStrictly
                defaultExpandAll
                checkedKeys={checkedKeys}
                treeData={generateTree(dataSource as any, targetKeys)}
                onCheck={(v, { node: { key, }, checkedNodes, ...rest }) => {
                  // const checked = !isChecked(checkedKeys, key);
                  // if (checked) {
                  //   const keys = [...checkedKeys, key];
                  //   onChange(keys as any, 'left', []);
                  //   onChange(keys as any, 'right', []);
                  //   handleSelect(keys as any);
                  // } else {
                  //   const keys = checkedKeys.filter(item => item !== key);
                  //   onChange(keys, 'left', [key as string])
                  //   onChange(keys, 'right', [key as string]);
                  //   handleSelect(keys);
                  // }
                  onItemSelect(key as string, !isChecked(checkedKeys, key));
                }}
                onSelect={(_, { node: { key }, }) => {
                  onItemSelect(key as string, !isChecked(checkedKeys, key));
                }}
              />
            </div>
          );
        }
      }}
    </Transfer>
  );
};
function SelectDepartmentModal(props: SelectDepartmentModalProps) {
  const { value, onChange, ...rest } = props;
  const { endpoint } = useContext(UserManagerContext);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<DataNode[]>(value || []);
  useEffect(() => {
    setTargetKeys(value?.map(item => item.key as string) || []);
    setSelectedRows(value || []);
  }, [value])
  const onTransferChange = (keys: string[]) => {
    setTargetKeys(keys);
  };
  const { data } = useRequest((params) => {
    return getDeptTreeByEndpoint({
      endpoint: endpoint,
      ...params,
    })
  });
  const dataSource = useMemo(() => {
    return formattedDepartTreeData(data?.body || [])
  }, [data?.body])



  const footer = (
    <Row justify="end">
      <Space>
        <Button onClick={(e) => props.onCancel?.(e as any)}>取消</Button>
        <Button type="primary" onClick={(e) => {
          onChange?.(selectedRows);
          props.onCancel?.(e as any)
        }}>确定</Button>
      </Space>
    </Row>
  );

  return (
    <>
      <Modal {...rest} title='选择部门' footer={<Row justify='end'>{footer}</Row>}>
        <TreeTransfer
          targetKeys={targetKeys}
          dataSource={dataSource}
          onChange={onTransferChange}
          onSelect={setSelectedRows}
        />
      </Modal>
    </>
  );
}

export function DepartmentSelect(props: DepartmentSelectProps) {
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useControllableValue<DataNode[]>(props);
  return (
    <>
      <Select mode='tags'
        open={false}
        onDropdownVisibleChange={setOpen}
        value={value?.map((item) => item.title)}
        // tagRender={(tagProps) => {
        //   return <Tag closable onClose={(e) => {
        //     e.stopPropagation();
        //     console.log(value?.filter(item => item.key !== tagProps.value));
        //     setValue(value?.filter(item => item.key !== tagProps.value))
        //   }}>{tagProps.label}</Tag>
        // }}
        onChange={(v) => {
          setValue(value?.filter(item => v.includes(item.title)))
        }}
      />
      <SelectDepartmentModal
        visible={open}
        onCancel={() => setOpen(false)}
        value={value}
        onChange={setValue}
      />
    </>
  );
}

export default SelectDepartmentModal;
