import { useState } from 'react';
import { useEffect } from 'react';

function useMountState<S = number | string | any>(
  initRequest: (params?: any) => Promise<API.ApiBaseResult<S>>,
  initState?: any,
): [S | undefined, (params?: any) => void, boolean] {
  const [state, setState] = useState<S>(initState);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchData = (data?: any) => {
    setLoading(true);
    initRequest(data)
      .then((res) => {
        setState(res.body);
        return res.body;
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return [state, fetchData, loading];
}

export default useMountState;
