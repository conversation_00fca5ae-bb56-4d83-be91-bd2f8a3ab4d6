import { useBrandList } from '@/modules/product/application/brand';
import type { SelectProps } from 'antd';
import { Select } from 'antd';

const BrandSelect = (props: SelectProps<any>) => {
  const { brandList } = useBrandList();
  return (
    <Select
      options={brandList?.map((item) => ({
        value: item.brandId,
        label: item.name,
      }))}
      {...props}
    />
  );
};

export default BrandSelect;
