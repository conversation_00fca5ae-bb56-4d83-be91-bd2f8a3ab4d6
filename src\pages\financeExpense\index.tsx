import type {ProColumns} from '@ant-design/pro-table';
import {<PERSON><PERSON>, Badge, Button, Form, message, Modal, Radio, Space, Tabs, Tag} from 'antd';
import React, {useState} from 'react';
import {Access, Link} from 'umi';
import Permission from "@/components/Permission";
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import ApplyExpenseModal from "@/pages/financeExpense/components/ApplyExpenseModal";
import {
  copyExpenseById, editExpense,
  expenseBatchPayment,
  exportFinanceExpense,
  financeExpenseDataCount,
  getExpenseList, getFinanceSubjectList,
  getPaymentBankList, invalidExpense
} from "@/modules/financeExpense/infra/api/expense";
import moment from "moment";
import ExpenseDetailModal from "@/pages/financeExpense/components/ExpenseDetailModal";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import {
  apportionTypeEnum,
  auditModelEnum,
  expensePaymentType,
  expenseStatusEnum,
  FinanceExpense
} from "@/modules/financeExpense/domain/expense";
import {LoadingOutlined, ThunderboltOutlined} from "@ant-design/icons";
import {ProForm, ProFormDatePicker, ProFormDateRangePicker, ProFormTextArea} from '@ant-design/pro-components';
import EditExpenseModal from "@/pages/financeExpense/components/EditExpenseModal";
import {useRequest} from "ahooks";
import {ProFormSelect} from "@ant-design/pro-form";
import {commonExport, copyText} from "@/utils/comUtil";
import {SnippetsTwoTone} from "@ant-design/icons/lib";
import {TableDropdown} from "@ant-design/pro-table";
import {useModel} from "@@/plugin-model/useModel";


const ExpenseList: React.FC<FinanceExpense> = () => {
  const [applyExpenseModal, setApplyExpenseModal] = useState<boolean>(false);
  const [expenseDetailModal, setExpenseDetailModal] = useState<boolean>(false);
  const [editExpenseModal, setEditExpenseModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<FinanceExpense>();
  const [selectStore, setSelectStore] = useState<any>();
  const [selectedRows, setSelectedRows] = useState<FinanceExpense[]>();
  const [form] = Form.useForm();
  const { initialState, setInitialState } = useModel('@@initialState');
  const { data,refresh } = useRequest(() => financeExpenseDataCount().then((res) => res.body));


  const { actionRef, fetchList } =  useRequestTable((params) => {
    return getExpenseList({
      ...params,
    });
  });

  /**
   * 作废报销单
   * @param record
   */
  const invalidFinanceExpense=(record: FinanceExpense)=>{
    const params={
      id:record.id,
      status:-10
    }
    invalidExpense(params).then(res => {
      if (res.status.success) {
        message.success("作废成功");
        actionRef.current?.reload();
      }
    })
  }

  const exportFinanceExpenseData=()=>{

    Modal.confirm({
      icon: '',
      width: 500,
      content: (
        <Form form={form}>
          <ProFormDateRangePicker name={"date"} label={"时间"}/>
        </Form>
      ),
      onOk: async () => {
        const formValues = form.getFieldsValue();
        if(!formValues?.date){
          message.error("请选择时间区间")
          return;
        }
        const params = {
          'dateKey': 'createDate',
          'dateValue': formValues?.date?.map((item)=>{return item.toISOString()})
        };
        exportFinanceExpense(params).then(res=>{
          commonExport(res, '财务报销导出');
        });
      },
    });

  }

  const [bankList, setBankList] = useState<any>();

  const batchPayment = () => {
    if(selectedRows?.length == 0){
      message.error("请勾选需处理报销单");
      return;
    }
    let totalAmount = 0;
    let accountName = '';
    {selectedRows?.map(item =>{
      totalAmount += item?.amount;
      accountName = item.accountName;
    })}

    Modal.confirm({
      icon: '',
      keyboard: false,
      content: (
        <Form labelCol={{flex: '100px'}} form={form}>
          <Form.Item label="收款人" style={{fontWeight:'bold', margin: 0}}>
            {accountName}
          </Form.Item>
          <Form.Item label="总金额" style={{fontWeight:'bold', margin: 0}}>
            {totalAmount?.toFixed(2)}
          </Form.Item>
          <Form.Item label="支付笔数" style={{fontWeight:'bold', margin: 0}}>
            {selectedRows?.length}
          </Form.Item>
          <ProForm.Group>
            <ProFormDatePicker width={"sm"} dataFormat={"YYYY-MM-DD"}   name={"paymentTime"} label={"付款时间"} />
            <ProFormSelect
              name="paymentBank"
              label="付款银行"
              width={"sm"}
              mode="tags"
              request={async () =>  {//返回的select网络请求
                const params = await getPaymentBankList();
                const res = [];
                const body = params.body;
                for(let i in body){
                  const temp = {};
                  temp['label'] = body[i].paymentBankName;
                  temp['value'] = body[i].paymentBankName;
                  res.push(temp)
                }
                return res;
              }}
            />
            <ProFormTextArea
              width={"lg"}
              label="备注"
              name="remark"
            />
          </ProForm.Group>
        </Form>
      ),
      onOk: async () => {
        const params = form.getFieldsValue();
        params.auditStatus = 'PASS';
        params.ids = selectedRows?.map(item => {
          return item?.id;
        })
        expenseBatchPayment(params).then(res => {
          if(res?.status.success){
            message.success("支付成功");
            actionRef.current?.reload();
          }else{
            // message.error(<Space direction="vertical" size="small">{res.status?.message?.split(";")}</Space>,5);
          }
        })
      }
    });
  }

  const copyExpense = (record: FinanceExpense) => {
    Modal.confirm({
      title: '确认复制吗？',
      onOk: async () => {
        copyExpenseById(record?.id).then(res => {
          if(res?.status.success){
            message.success("复制成功");
            actionRef.current?.reload();
          }
        })
      }
    });
  }

  const columns: ProColumns<FinanceExpense>[] = [
    {
      title: '单据类型',
      dataIndex: 'auditModel',
      fieldProps:{searchTitle: "单据",showSearch: true},
      width: 200,
      valueType: 'select',
      hideInTable: true,
      valueEnum: auditModelEnum,
    },
    {
      title: '付款银行',
      dataIndex: 'paymentBank',
      width: 200,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {showSearch: true},
      request: async () => {
        const res = await getPaymentBankList();
        return res.body?.map((item: any) => {
          return {label: item?.paymentBankName, value: item?.paymentBankName};
        })
      }
    },
    {
      title: '财务科目',
      dataIndex: 'financeSubject',
      hideInTable: true,
      width: 200,
      valueType: 'select',
      fieldProps: {showSearch: true},
      request: async () => {
        const res = await getFinanceSubjectList();
        return res.body?.map((item: any) => {
          return {label: item?.name, value: item?.name};
        })
      }
    },
    {
      title: '标题',
      dataIndex: 'title',
      hideInTable: true,
      width: 200,
    },
    {
      title: '流水号',
      dataIndex: 'serialNumber',
      hideInTable: true,
      width: 100,
      colSize: (4 / 24),
      render: (v,record) => {
        return <>
          <div>{record?.serialNumber}</div>
        </>
      }
    },
    {
      title: '付款状态',
      dataIndex: 'isPayment',
      hideInTable:true,
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '已付款', '0': '未付款'},
    },
    {
      title: '是否加急',
      dataIndex: 'priority',
      hideInTable:true,
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '加急', '0': '正常'},
    },
    {
      title: '申请人',
      dataIndex: 'applyUsername',
      colSize: (4 / 24),
      width: 100,
      hideInTable: true,
      render: (v,record) => {
        return <>
          <div>{record?.applyUsername}</div>
        </>
      }
    },
    {
      title: '收款人',
      dataIndex: 'accountName',
      width: 100,
      colSize: (4 / 24),
      hideInTable: true
    },
    {
      title: '标题/流水号/申请人',
      dataIndex: 'title',
      width: 300,
      hideInSearch: true,
      render: (v,record) => {
        return <>
          <div>标题：{record?.title}{record?.priority==1? <>&nbsp;<ThunderboltOutlined style={{color: "red"}} /></> : null}</div>
          <div>流水号：{record?.serialNumber}<SnippetsTwoTone onClick={() => copyText(record?.serialNumber)}/></div>
          <div>申请人：{record?.applyUsername}</div>
        </>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch:true,
      width: 120,
      render: (v,record) => {
        return <>
           <div>
              状态：
              <span className="ant-badge-status-dot ant-badge-status-success"></span>
              <span style={{fontSize:12}} className="ant-badge-status-text">{expenseStatusEnum[record?.status] || '-'}</span>
          </div>
          <div>付款：{record?.isPayment == 1 ?
            <><span className="ant-badge-status-dot ant-badge-status-success"></span>
            <span style={{fontSize:12}} className="ant-badge-status-text">已付款</span></> :
            <><span className="ant-badge-status-dot ant-badge-status-warning"></span>
              <span style={{fontSize:12}} className="ant-badge-status-text">未付款</span></>}
          </div>
        </>
      }
    },
    {
      title: '总金额/付款方式/单据类型',
      dataIndex: 'amount',
      hideInSearch: true,
      width: 200,
      render: (v,record) => {
        return <>
          <div>总金额：{record?.amount?.toFixed(2) || 0.00}</div>
          <div>付款方式：{expensePaymentType[record?.payment] || '-'}</div>
          <div>单据：{auditModelEnum[record?.auditModel]}</div>
        </>
      }
    },
    {
      title: '科目/收款人/付款银行',
      dataIndex: 'financeSubject',
      hideInSearch: true,
      width: 300,
      render: (v,record) => {
        const paymentBank = record?.paymentBank ? JSON.parse(record?.paymentBank) : [];
        return <>
          <div>科目：{record?.financeSubject || '--'}</div>
          <div>收款人：{record?.accountName || '--'}</div>
          <div>付款银行：{paymentBank.map(item =>{return <Tag style={{margin: 3}} color={"orange"}>{item}</Tag>})}</div>
        </>
      }
    },
    {
      title: '分摊公司/类型',
      dataIndex: 'companyName',
      hideInSearch: true,
      width: 120,
      render: (v,record) => {
        return <>
          <div>公司：{record?.companyName || '-'}</div>
          <div>类型：{apportionTypeEnum[record?.shareType] || '-'}</div>
        </>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      fieldProps:{searchTitle: "状态"},
      width: 1800,
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return <Filters.CheckboxButton
             defaultValue={["1"]}
             options={[
               {value: "1", label: '全部'},
               {value: "0", label: '草稿'},
               {value: "10", label: '处理中'},
               {value: "20", label: '已完成'},
               {value: "-10", label: '作废'},
               {value: "-20", label: (<Badge overflowCount={9999} size={"small"} style={{backgroundColor:"#FF3333",marginTop:-6,marginRight:-6}} count={data?.REJECT}>驳回</Badge>)},
             ]}
             onChange={(e) => {
               if(e.length>0 && e!=formInstance.getFieldValue('status')){
                 formInstance.setFieldValue('status', e);
               }
               actionRef.current?.reload();
               setSelectStore({});
               setSelectedRows([]);
             }}
          />
      }
    },
    {
      title: '审核',
      dataIndex: 'auditStatus',
      fieldProps:{searchTitle: "审核"},
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return <Filters.CheckboxButton
           defaultValue={["1"]}
           onChange={(e) => {
             if(e.length>0 && e!=formInstance.getFieldValue('auditStatus')){
               formInstance.setFieldValue('auditStatus', e);
             }
             actionRef.current?.reload();
             setSelectStore({});
             setSelectedRows([]);
           }}
           options={[
             {value: "1", label: '全部'},
             {value: "5", label: (<Badge overflowCount={9999} size={"small"} style={{backgroundColor:"#FF3333",marginTop:-6,marginRight:-6}} count={data?.WAIT}>本人审核</Badge>)},
           ]}
        />
      }
    },
    {
      title: '审核阶段',
      dataIndex: 'gmtCreate',
      width: 350,
      hideInSearch: true,
      render: (v,record) => {
        let options: JSX.Element[] = [];
        record?.financeExpenseAuditInfo.map(item=>{
          options.push(<Tag style={{margin: 2,width: 140}} color={item?.auditStatus=='REJECT'?"red":item?.auditStatus!='NONE'?"blue":'default'}>{item?.auditStatus=='WAIT' ? <LoadingOutlined /> : null}&nbsp;{item?.auditRole}（{item?.auditor}）</Tag>);
        })
        return <>
          {options}
        </>
      }
    },
    {
      title: '时间',
      dataIndex: 'gmtCreate',
      hideInSearch: true,
      width: 180,
      render: (v,record) => {
        return <>
          <div>创建：{moment(Number(record.gmtCreate)).format("YYYY-MM-DD HH:mm:ss")}</div>
          <div>修改：{moment(Number(record.gmtModified)).format("YYYY-MM-DD HH:mm:ss")}</div>
          {record.paymentTime? <div>付款：{moment(record.paymentTime).format("YYYY-MM-DD HH:mm:ss")}</div> : null}
        </>
      }
    },
    {
      title: '操作',
      align: "left",
      width: 160,
      hideInSearch: true,
      render: (_, record) => {
        return <>
          <Space split={<span style={{color: "rgb(24, 144, 255)"}}>|</span>}>
            <a type="primary" key={"audit"}  onClick={()=>{setExpenseDetailModal(true);setCurrentRow(record)}}>
              审核
            </a>
            {initialState?.currentUser?.userId==record?.applyUid ? <a type="primary" key={"edit"} onClick={()=>{setApplyExpenseModal(true);setCurrentRow(record)}}>
              编辑
            </a> : null}
            <TableDropdown
              key="more"
              menus={[
                { key: 'purchaseDetail',
                  name:
                    <Permission permissionKey={"purchase:finance:expense:finance"}>
                      <a type="primary" key={"change"} style={{borderColor: "green", color: "green", fontSize: 12}} onClick={()=>{setEditExpenseModal(true);setCurrentRow(record)}}>
                        修改科目
                      </a>
                    </Permission>
                },
                { key: 'supplier',
                  name:  <a type="primary" key={"copy"}  style={{borderColor: "orange", color: "orange",fontSize: 12}} onClick={()=>{copyExpense(record)}}>
                    复制
                  </a>
                },
                { key: 'invalidExpense',
                  name:  <a type="primary" key={"change"} style={{borderColor: "red", color: "red", fontSize: 12}} onClick={()=>{invalidFinanceExpense(record)}}>
                    作废
                  </a>
                }
              ]}
            >
              <span style={{fontSize: 12}}>更多</span>
            </TableDropdown>
          </Space>
        </>
      },
    },
  ];

  return (
    <>
    <CustomPage<FinanceExpense>
      actionRef={actionRef}
      rowKey="id"
      scroll={{y: 583}}
      rowSelection={{
        onChange: (_, selectedRows) => {
          let selectAmount = 0;
          selectedRows.forEach((record)=>{
            selectAmount += record?.amount;
          })
          setSelectStore({selectAmount: selectAmount, selectNum: selectedRows.length});
          setSelectedRows(selectedRows);
        },
      }}
      tableAlertRender={false}
      toolBarRender={() => [
        <b>选中单数：{selectStore?.selectNum || 0} &nbsp;</b>,
        <b>单据总额：{selectStore?.selectAmount == null ? 0.00 : selectStore.selectAmount?.toFixed(2)} &nbsp;</b>,
          <Button size={"small"}  key="expenseAudit" type="primary" onClick={() => {
            setApplyExpenseModal(true);
            setCurrentRow({});
          }}>
            申请报销
          </Button>,
        <Permission permissionKey={"purchase:finance:expense:batchPayment"}>
          <Button size={"small"} style={{color: "orange", borderColor: "orange"}} ghost={true}  key="pay" type="primary" onClick={batchPayment}>
            合并支付
          </Button>
        </Permission>,
        <Permission permissionKey={"purchase:finance:expense:finance"}>
          <Link to={"/setting/audit-config/financeExpenseAuditConfig"}>
            <Button size={"small"} ghost={true} key="auditConfig" type="primary">
              审核配置
            </Button>
          </Link>
        </Permission>,
        <Permission permissionKey={"purchase:finance:expense:finance"}>
          <Link to={"/setting/audit-config/financeExpenseApportionConfig"}>
            <Button size={"small"} ghost={true} key="approtion" type="primary">
              分摊配置
            </Button>
          </Link>
        </Permission>,
        <Permission permissionKey={"purchase:finance:expense:finance"}>
          <Button size={"small"} style={{color: "orange", borderColor: "orange"}} ghost={true}  key="pay" type="primary" onClick={exportFinanceExpenseData}>
            导出
          </Button>
        </Permission>,
      ]}
      request={fetchList}
      columns={columns}
      onRow={(record) => {
        return {
          onDoubleClick: (e) => {
            setExpenseDetailModal(true);
            setCurrentRow(record);
          },
        };
      }}
      bordered={true}
      recordCreator={false}
      recordDelete={false}
      recordUpdater={false}
    />
      <Access accessible={applyExpenseModal}>
        <ApplyExpenseModal visible={applyExpenseModal} expense={currentRow} onFinish={()=>{setApplyExpenseModal(false); actionRef?.current?.reload();}} onCancel={()=>{setApplyExpenseModal(false);}}/>
      </Access>
      <Access accessible={expenseDetailModal}>
        <ExpenseDetailModal visible={expenseDetailModal} expense={currentRow} onFinish={()=>{setExpenseDetailModal(false); actionRef?.current?.reload();}} onCancel={()=>{setExpenseDetailModal(false); actionRef?.current?.reload();}}/>
      </Access>
      <Access accessible={editExpenseModal}>
        <EditExpenseModal visible={editExpenseModal} expense={currentRow} onFinish={()=>{setEditExpenseModal(false);actionRef?.current?.reload();}} onCancel={()=>{setEditExpenseModal(false); actionRef?.current?.reload();}}/>
      </Access>
    </>
  );
};

export default ExpenseList;
