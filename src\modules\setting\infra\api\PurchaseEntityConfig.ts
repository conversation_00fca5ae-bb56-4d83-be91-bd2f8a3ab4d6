import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';


export async function loadPurchaseEntityList(data: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/purchase-entity/getPurchaseEntityList',
    data
  });
}

export async function getUserPurchaseEntityList() {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/purchase-entity/getUserPurchaseEntityList'
  });
}




