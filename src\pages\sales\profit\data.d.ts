export type AsinkingMskuProfit = {
  "id": string,
  "sid": string,
  "reportdatemonth": string,
  "posteddatelocale": string,
  "isdisplaydetail": string,
  "smallimageurl": string,
  "msku": string,
  "asin": string,
  "parentasin": string,
  "storename": string,
  "country": string,
  "countrycode": string,
  "localname": string,
  "localsku": string,
  "itemname": string,
  "model": string,
  "principalrealname": string,
  "productdeveloperrealname": string,
  "categoryname": string,
  "brandname": string,
  "currencycode": string,
  "currencyicon": string,
  "fbainventorycreditquantity": string,
  "disposalquantity": string,
  "removalquantity": string,
  "others": string,
  "customorderfee": string,
  "customorderfeeprincipal": string,
  "customorderfeecommission": string,
  "listingtagids": string,
  "totalfbaandfbmquantity": number,
  "totalfbaandfbmamount": string,
  "totalsalesquantity": number,
  "fbasalesquantity": number,
  "fbmsalesquantity": number,
  "totalreshipquantity": number,
  "reshipfbmproductsalesquantity": number,
  "reshipfbmproductsalerefundsquantity": number,
  "reshipfbaproductsalesquantity": number,
  "reshipfbaproductsalerefundsquantity": number,
  "mcfbafulfillmentfeesquantity": number,
  "cgabsquantity": number,
  "cgquantity": number,
  "totaladssales": string,
  "adssdsales": string,
  "adsspsales": string,
  "sharedadssbsales": string,
  "sharedadssbvsales": string,
  "totaladssalesquantity": number,
  "adssdsalesquantity": number,
  "adsspsalesquantity": number,
  "sharedadssbsalesquantity": number,
  "sharedadssbvsalesquantity": number,
  "totalsalesamount": string,
  "fbasaleamount": string,
  "fbmsaleamount": string,
  "shippingcredits": string,
  "promotionalrebates": string,
  "fbainventorycredit": string,
  "cashondelivery": string,
  "otherinamount": string,
  "fbaliquidationproceeds": string,
  "fbaliquidationproceedsadjustments": string,
  "amazonshippingreimbursement": string,
  "safetreimbursement": string,
  "netcotransaction": string,
  "reimbursements": string,
  "clawbacks": string,
  "sharedcomminglingvatincome": string,
  "giftwrapcredits": string,
  "guaranteeclaims": string,
  "costofpointegersgranted": string,
  "totalsalesrefunds": string,
  "fbasalesrefunds": string,
  "fbmsalesrefunds": string,
  "shippingcreditrefunds": string,
  "giftwrapcreditrefunds": string,
  "chargebacks": string,
  "costofpointegersreturned": string,
  "promotionalrebaterefunds": string,
  "totalfeerefunds": string,
  "sellingfeerefunds": string,
  "fbatransactionfeerefunds": string,
  "refundadministrationfees": string,
  "othertransactionfeerefunds": string,
  "refundforadvertiser": string,
  "pointsadjusted": string,
  "shippinglabelrefunds": string,
  "refundsquantity": number,
  "refundsrate": string,
  "fbareturnsquantity": number,
  "fbareturnssaleablequantity": number,
  "fbareturnsunsaleablequantity": number,
  "fbareturnsquantityrate": string,
  "platformfee": string,
  "fbadeliveryfee": string,
  "mcfbadeliveryfee": string,
  "totalfbadeliveryfee": string,
  "othertransactionfees": string,
  "totaladscost": string,
  "adsspcost": string,
  "adssbcost": string,
  "adssbvcost": string,
  "adssdcost": string,
  "sharedcostofadvertising": string,
  "promotionfee": string,
  "sharedsubscriptionfee": string,
  "sharedldfee": string,
  "sharedcouponfee": string,
  "sharedearlyreviewerprogramfee": string,
  "sharedvinefee": string,
  "totalstoragefee": string,
  "fbastoragefee": string,
  "sharedfbastoragefee": string,
  "longtermstoragefee": string,
  "sharedlongtermstoragefee": string,
  "sharedstoragerenewalbilling": string,
  "sharedfbadisposalfee": string,
  "sharedfbaremovalfee": string,
  "sharedfbainboundtransportationprogramfee": string,
  "sharedlabelingfee": string,
  "sharedpolybaggingfee": string,
  "sharedbubblewrapfee": string,
  "sharedtapingfee": string,
  "sharedfbacustomerreturnfee": string,
  "sharedfbainbounddefectfee": string,
  "sharedfbaoveragefee": string,
  "sharedamazonpartneredcarriershipmentfee": string,
  "sharedfbainboundconveniencefee": string,
  "shareditemfeeadjustment": string,
  "sharedotherfbainventoryfees": string,
  "fbastoragefeeaccrual": string,
  "fbastoragefeeaccrualdifference": string,
  "longtermstoragefeeaccrual": string,
  "longtermstoragefeeaccrualdifference": string,
  "sharedfbaintegerernationalinboundfee": string,
  "adjustments": string,
  "totalplatformotherfee": string,
  "shippinglabelpurchases": string,
  "sharedcarriershippinglabeladjustments": string,
  "sharedliquidationsfees": string,
  "sharedmanualprocessingfee": string,
  "sharedotherservicefees": string,
  "totalsalestax": string,
  "taxcollected": string,
  "tcsigstcollected": string,
  "tcssgstcollected": string,
  "tcscgstcollected": string,
  "sharedcomminglingvatexpenses": string,
  "sharedtaxadjustment": string,
  "salestaxrefund": string,
  "taxrefunded": string,
  "tcsigstrefunded": string,
  "tcssgstrefunded": string,
  "tcscgstrefunded": string,
  "salestaxwithheld": string,
  "refundtaxwithheld": string,
  "tdssection194onet": string,
  "cgpricetotal": string,
  "cgpriceabstotal": string,
  "hascgpricedetail": number,
  "cgunitprice": string,
  "proportionofcg": string,
  "cgtransportcoststotal": string,
  "hascgtransportcostsdetail": number,
  "cgtransportunitcosts": string,
  "proportionofcgtransport": string,
  "totalcost": string,
  "proportionoftotalcost": string,
  "cgothercoststotal": string,
  "cgotherunitcosts": string,
  "hascgothercostsdetail": number,
  "proportionofcgothercosts": string,
  "grossprofit": string,
  "grossrate": string,
  "grossprofitincome": string,
  "otherfeestr": string,

}
