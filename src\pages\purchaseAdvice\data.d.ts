import type { PlanState } from "@/modules/purchasePlan/domain/purchasePlan";


export type PurchaseAdviceBatch = {
  id: string,
  batchCode: string
}

export type PurchaseAdviceResult = {
  "id": number,
  "batchCode": string,
  "sku": string,
  "productName": string,
  "productCograte": string,
  "statuts": string,
  "organizationName": string,
  "isPurchase": number,
  "level": string,
  "gzStock": number,
  "gzOutOfStockQuantity": number,
  "needQuantity": number,
  "minPurchaseQuantity": number,
  "price": number,
  "purchaseQuantity": number,
  "purchaseAmount": number,
  "note": string,
  "updateDate": string,
  "adviceList": PurchaseAdvice[]
}


export type PurchaseAdvice = {
  "id": number,
  "batchId": number,
  "batchCode": string,
  "goodsSku": string,
  "goodsStatus": string,
  "goodsStatusName": string,
  "categoryName": string,
  "organizationName": string,
  "deliverWarehouseName": string,
  "salesWarehouseName": string,
  "intransitStockQuantity": number,
  "availableStockQuantity": number,
  "virtualIntransitStockQuantity": number,
  "outOfStockQuantity": number,
  "purchaseFormula": string,
  "salesTrend": string,
  "salesDayAvgQuantity": number,
  "upDay": number,
  "downDay": number,
  "availableSalesDay": number,
  "purchaseQuantity": number,
  "lastPurchasePrice": number,
  "sales7Days": number,
  "sales14Days": number,
  "sales15Days": number,
  "sales21Days": number,
  "sales30Days": number,
  "expectQuantity": number,
  "goodsMinPurchaseQuantity": number,
  "isPurchase": number,
  "isScrew": number,
  "screwSku": string,
  "defaultSupplierName": string,
  "warehouseType": string,
  "gzRealStock": number,
  "totalOutsideQuantity": number,
  "createTime": string,
  "updateTime": string,
  "bs": string,
  "level": string,
}
