import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

export interface ProductSpecData {
  name: string;
  required: boolean;
  specId: string;
  values: ProductSpecValues;
}

export type ProductSpecValues = string[];

// 查询分类下的规格
export async function querySpecByCategoryId(params: { categoryId: string }) {
  return mallRequest<
    API.ApiBaseResult<{
      current: ProductSpecData[];
      parent: ProductSpecData[];
    }>
  >(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/spec/queryByCategoryId',
    method: 'GET',
    params,
  });
}

export type AddSpecValueParams = {
  specId: string;
  value: string;
};

// 添加规格值
export async function addSpecValue(data: AddSpecValueParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/spec/addValue',
    method: 'POST',
    data,
  });
}

export interface CreateSpecParams {
  categoryId: string;
  name: string;
  required: boolean;
  values: ProductSpecValues;
}

// 添加规格值
export async function createSpec(data: CreateSpecParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/spec/create',
    method: 'POST',
    data,
  });
}

export type UpdateSpecParams = Pick<ProductSpecData, 'specId' | 'required' | 'values'>;

// 添加规格值
export async function updateSpec(data: UpdateSpecParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/spec/update',
    method: 'POST',
    data,
  });
}
