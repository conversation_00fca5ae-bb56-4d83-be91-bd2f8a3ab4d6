import type {
  AddUserCustomCategoryValueParams,
  CreateUserCustomCategorySpecParams,
  QueryAllCategorySpec,
} from '../infra/api/categorySpec';
import {
  addUserCustomCategorySpecValue,
  createUserCustomCategorySpec,
  getAllCategorySpec,
  getAllUserCustomSpec,
  getUserCustomValueBySpecId,
} from '../infra/api/categorySpec';

// 用户纬度的分类规格
export const useUserCustomSpec = () => {
  const getAll = (categoryId: string) => {
    return getAllUserCustomSpec({ categoryId });
  };
  const addValue = (params: AddUserCustomCategoryValueParams) => {
    return addUserCustomCategorySpecValue(params);
  };
  const addSpec = (params: CreateUserCustomCategorySpecParams) => {
    return createUserCustomCategorySpec(params);
  };
  const getValues = (specId: string) => {
    return getUserCustomValueBySpecId({ specId });
  };
  return {
    getAll,
    addValue,
    addSpec,
    getValues,
  };
};

// 平台纬度的分类规格
export const usePlatformCategorySpec = () => {
  const getSpecListByCategoryId = (categoryId: string) => {
    return getAllCategorySpec({ categoryId });
  };
  return {
    getSpecListByCategoryId,
  };
};

export const useCategorySpec = () => {
  const userCustomCategorySpec = useUserCustomSpec();
  const platformCategorySpec = usePlatformCategorySpec();
  // 获取分类的规格
  const getCategorySpec = async (categoryId: string) => {
    const transformSpecList = (res: API.ApiBaseResult<QueryAllCategorySpec>) => {
      const { current, parent } = res?.body;
      if (current.length || parent.length) {
        return [...(current || []), ...(parent || [])];
      }
      return [];
    };
    const categorySpecResult = await platformCategorySpec.getSpecListByCategoryId(categoryId);
    let specList = transformSpecList(categorySpecResult);
    if (specList.length) {
      return specList;
    }
    const customSpec = await userCustomCategorySpec.getAll(categoryId);
    specList = transformSpecList(customSpec);
    return specList;
  };
  return {
    getCategorySpec,
    userCustomCategorySpec,
    platformCategorySpec,
  };
};
