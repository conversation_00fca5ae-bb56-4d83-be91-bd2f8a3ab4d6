import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Modal, notification, Space} from "antd";
import {downloadDataTemplate, reqByBody, reqByPage} from "@/modules/common/infra/api/common";
import {GoodsComposeRelation} from "@/modules/product/domain/goodsSync";
import EditGoodsComposeModal from "@/pages/product/GoodsSync/components/EditGoodsComposeModal";
import {ProductTypeEnum} from "@/modules/product/domain/spu";
import {commonExport} from "@/utils/comUtil";

const TableList: React.FC = () => {

  const [editComposeModal, setEditComposeModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<GoodsComposeRelation>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/goods-compose/pageQuery',{
      ...params,
    });
  });

  const syncAsinkingGoods = (record: any) => {
    Modal.confirm({
      title: "确认同步领星商品?",
      centered: true,
      onOk: function () {
        reqByBody('/sales-mgmt-biz/sales-center/goods-compose/createAsinkingGoods',record).then((result) => {
          if (result.status.success) {
            notification.success({message: '同步成功'});
            actionRef.current?.reload?.();
          }
        });
      },
    });
  }

  const createLocalGoods = (record: any) => {
    Modal.confirm({
      title: "确认同步本地商品?",
      centered: true,
      onOk: function () {
        reqByBody('/sales-mgmt-biz/sales-center/goods-compose/createLocalGoods', record).then((result) => {
          if (result.status.success) {
            notification.success({message: '同步成功'});
            actionRef.current?.reload?.();
          }
        });
      },
    });
  }


  const exportMskuBind = () => {
    Modal.confirm({
      title: "确认导出通途捆绑商品创建模版数据?",
      centered: true,
      onOk: function () {
        downloadDataTemplate('/sales-mgmt-biz/sales-center/goods-compose/exportMskuBind').then(res=>{
          commonExport(res, '捆绑商品');
        });
      },
    });
  }

  const exportMskuCompose = () => {
    Modal.confirm({
      title: "确认导出通途组装商品创建模版数据?",
      centered: true,
      onOk: function () {
        downloadDataTemplate('/sales-mgmt-biz/sales-center/goods-compose/exportMskuCompose').then(res=>{
          commonExport(res, '组合商品');
        });
      },
    });
  }


  const syncTongtoolGoods = () => {
    Modal.confirm({
      title: "检测通途商品?",
      centered: true,
      onOk: function () {
        reqByBody('/sales-mgmt-biz/sales-center/goods-compose/syncTongtoolGoods',{}).then(result=>{
          if (result.status.success) {
            notification.success({message: '同步成功'});
            actionRef.current?.reload?.();
          }
        });
      },
    });
  }

  const columns: ProColumns<GoodsComposeRelation>[] = [
    {
      title: '标准SKU',
      dataIndex: 'standardSku',
      colSize: (4 / 24)
    },
    {
      title: '组合关系',
      dataIndex: 'relation',
      colSize: (4 / 24),
    },
    {
      title: '销售类型',
      dataIndex: 'composeType',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: ProductTypeEnum,
    },
    {
      title: '通途已创建',
      dataIndex: 'tongtoolSyncStatus',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '是', '0': '否','-1':'异常'},
      hideInTable: true
    },
    {
      title: '领星已创建',
      dataIndex: 'asinkingSyncStatus',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '是', '0': '否','-1':'异常'},
      hideInTable: true
    },
    {
      title: '本地已创建',
      dataIndex: 'localSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.localSyncStatus==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.localSyncStatus==1?<span style={{color: "green"}}>是</span>:  <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '通途SKU',
      dataIndex: 'tongtoolSku',
      hideInSearch: true,
    },
    {
      title: '通途已创建',
      dataIndex: 'tongtoolSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.tongtoolSyncStatus==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.tongtoolSyncStatus==1?<span style={{color: "green"}}>是</span>:  <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '领星SKU',
      dataIndex: 'asinkingSku',
      hideInSearch: true,
    },
    {
      title: '领星已创建',
      dataIndex: 'asinkingSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.asinkingSyncStatus==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.asinkingSyncStatus==1?<span style={{color: "green"}}>是</span>: <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 100,
      render: (v, record) => {
        return (
          <Space >
            <a type="primary" onClick={()=>{syncAsinkingGoods({id:record.id})}}>
              领星同步
            </a>
            <a type="primary" onClick={()=>{createLocalGoods({id:record.id})}}>
              同步本地商品
            </a>
            {/*<a type="primary" onClick={()=>{setEditComposeModal(true);setCurrentRow(record)}}>*/}
            {/*  通途同步*/}
            {/*</a>*/}
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<GoodsComposeRelation>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        toolBarRender={() => [
          <Button size={"small"} key="level" type="primary" onClick={() => {exportMskuCompose()}}>
            导出通途商品-组装
          </Button>,
          <Button size={"small"} key="level" type="primary" onClick={() => {exportMskuBind()}}>
            导出通途商品-捆绑
          </Button>,
          <Button size={"small"} key="level" type="primary" ghost={true} onClick={() => {syncTongtoolGoods()}}>
            检测通途商品
          </Button>
        ]}
      />
      <EditGoodsComposeModal visible={editComposeModal} goodsCompose={currentRow} onCancel={()=>setEditComposeModal(false)} onFinish={()=>{setEditComposeModal(false);actionRef.current?.reload()}}/>
    </>
  );
};

export default TableList;
