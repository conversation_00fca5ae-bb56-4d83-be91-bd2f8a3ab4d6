import type { CategoryData } from '@/modules/tags/domain/tags';
import type {
  CreateCategoryParams,
  UpdateCategoryParams,
} from '@/modules/tags/infra/api/tags';
import { useCategoryTable } from './categoryTable';
import { useCategoryTree } from './categoryTree';

export function useTagsUi() {
  const categoryTree = useCategoryTree();
  const categoryTable = useCategoryTable(categoryTree.selectedCategory);

  const { selectedCategory, setSelectedCategory } = categoryTree;

  // 重新刷新整个页面数据
  const refreshTreeAndTable = () => {
    categoryTree.initTreeData();
    categoryTable.actionRef.current?.reload();
  };

  // 1、如果当前选择的分类是更新分类的父节点，更新当前选择分类节点的数据,并且更新Table的分类数据
  // 2、如果当前更新的分类不是当前选择的分类，则重新拉取分类树数据，并更新Table的分类数据
  const createOrUpdateRefreshData = (params: CreateCategoryParams | UpdateCategoryParams) => {
    if (params.parentCategoryId === selectedCategory?.key) {
      if (selectedCategory?.isLeaf) {
        // 如果当前选择的分类是最末端节点, 则重新更新分类节点数据
        categoryTree.initTreeData();
      }
      categoryTree.loadDataById({ key: selectedCategory?.key });
      categoryTable.actionRef.current?.reload();
    } else {
      refreshTreeAndTable();
    }
  };

  // 更新完成后刷新表单和分类的数据
  const refreshData = (newData: any, options: 'update' | 'create' | 'delete' | 'batchMove') => {
    switch (options) {
      case 'create':
        createOrUpdateRefreshData(newData as CreateCategoryParams);
        break;
      case 'update':
        createOrUpdateRefreshData(newData as UpdateCategoryParams);
        break;
      case 'delete':
        const ids = newData as string[];
        if (selectedCategory?.key && ids.includes(selectedCategory?.key as string)) {
          setSelectedCategory({
            key: (selectedCategory as unknown as CategoryData).parentId || '0',
          });
          refreshTreeAndTable();
        } else {
          refreshTreeAndTable();
          categoryTree.onExpand(categoryTree.expandedKeys || []);
        }
        break;
      case 'batchMove':
        refreshTreeAndTable();
      default:
        break;
    }
  };

  return {
    categoryTable,
    categoryTree,
    refreshData,
    refreshTreeAndTable,
  };
}
