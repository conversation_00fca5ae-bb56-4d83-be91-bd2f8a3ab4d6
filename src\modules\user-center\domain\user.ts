import type { Rule } from 'antd/lib/form';
export enum UserState {
  'ACTIVATED' = 'ACTIVATED',
  'DISABLED' = 'DISABLED'
}
export interface Account {
  account: string;
  type: string;
}

export interface Dept {
  code: string;
  deptId: string;
  name: string;
}

export interface Permission {
  bizContext: string;
  endpoint: string;
  permission: string;
  resourceType: string;
}

export interface Role {
  gmtCreate: number;
  gmtModified: number;
  roleDesc: string;
  roleId: string;
  roleName: string;
}

export interface EndPointUser {
  accounts: Account[];
  avatarUrl: string;
  bizContext: string;
  depts: Dept[];
  email: string;
  endpoint: string;
  gender: string;
  nickName: string;
  permissions: Permission[];
  phoneNumber: string;
  roles: Role[];
  state: UserState;
  tenantUserType: string;
  userId: string;
  userName: string;
  gmtCreate: number;
  lastLoginTime: number;
  userDataRoleInfo: UserDataRoleInfo
}

export interface UserDataRoleInfo {
  purchaseEntity: string;
  purchaseOrderFliter: string;
  purchasePlanFliter: string;
}

export type EndPoint =
  | 'MALL_MGMT'
  | 'MALL'
  | 'OPERATION_MGMT'
  | 'VENDER_MGMT'
  | 'DISTRIBUTION_MGMT'
  | 'DISTRIBUTION_MALL';

export const userPasswordRules: Rule[] = [
  { required: true, message: '请输入登录密码' },
  { pattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*--+/\\.])[\da-zA-Z~!@#$%^&*--+/\\.]{8,}$/, message: '密码由8位至少包含一个大小写字母、数字、特殊符号组成'}
];

export const userAccountRules: Rule[] = [
  { required: true, message: '请输入登录账号' },
  { pattern: /^0?1[1-9][0-9]\d{8}$/, message: '请输入正确手机号码' },
];

export const checkConfirmPassword = (value?: string, password?: string) => {
  if (!value || password === value) {
    return Promise.resolve();
  }
  return Promise.reject(new Error('两次密码不一致，请重新输入'));
};
