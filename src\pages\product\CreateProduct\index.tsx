import React from "react";
import type { ProFormColumnsType } from '@ant-design/pro-form';
import {<PERSON>ton, Card, Checkbox, Col, Row, Select, Spin, Tabs} from 'antd';
import { FooterToolbar } from '@ant-design/pro-layout';
import { useLockFn } from 'ahooks';
import { isArray, omit } from 'lodash';
import type { CustomBetaSchemaFormValueType } from '@/components/CustomBetaJsonForm';
import CustomBetaSchemaForm from '@/components/CustomBetaJsonForm';
import { ProFormField } from '@ant-design/pro-components';
import { useSpuFormService } from './hooks/spuFormService';
import TabPane from "@ant-design/pro-card/es/components/TabPane";
import ProductSupplierModal from "@/pages/product/CreateProduct/components/ProductSupplierModal";
import ProductSkusModal from "@/pages/product/CreateProduct/components/ProductSkusModal";
import Permission from "@/components/Permission";

const CreateOrUpdateSpu = () => {
  const spuFormService = useSpuFormService();
  const { loading, form, onFinish, onValuesChange, onFinishFailed, submitting, spuId, skuId, spuDetail } = spuFormService;

  const submit = useLockFn(async (values) => {
    return onFinish(values);
  });
  const columns: ProFormColumnsType<unknown, CustomBetaSchemaFormValueType>[] = [
    {
      valueType: 'formCard',
      formItemProps: {
        labelCol: { flex: '120' },
        wrapperCol: { span: 24 },
      },
      columns: [
        {
          formItemProps: {
            style: {
              marginBottom: 5,
              height: 38
            },
          },
          renderFormItem: () => {
            return (
              <Row gutter={24}>
                <Col span={6}>
                  <ProFormField
                    rules={[{ required: true, message: '请输入SPU' }]}
                    labelCol={{ flex: '120px' }}
                    label="SPU"
                    name="spu"
                    disabled={spuId ? true: false}
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    rules={[{ required: true, message: '请输入标题' }]}
                    labelCol={{ flex: '120px' }}
                    label="标题"
                    name="title"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="单位"
                    name="unit"
                  >
                    <Select
                      allowClear={true}
                      options={[
                        { value: "个", label: '个' },
                        { value: "袋", label: '袋' },
                        { value: "箱", label: '箱' },
                      ]}
                    />
                  </ProFormField>
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="备货周期"
                    name="purchaseCycle"
                  />
                </Col>
              </Row>
            );
          },
        },
        {
          formItemProps: {
            style: {
              marginBottom: 5,
              height: 38
            },
          },
          renderFormItem: () => {
            return (
              <Row gutter={24}>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="品牌"
                    name="brandId"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="中文申报名"
                    name="zhDeclaredName"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="英文申报名"
                    name="enDeclaredName"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="申报货值"
                    name="declaredPrice"
                  />
                </Col>
              </Row>
            );
          },
        },
        {
          formItemProps: {
            style: {
              marginBottom: 5,
              height: 38
            },
          },
          renderFormItem: () => {
            return (
              <Row gutter={24}>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="标签"
                    name="tagss"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="是否过CE"
                    name="isCE"
                  >
                    <Select
                      allowClear={true}
                      options={[
                        { value: "0", label: '否' },
                        { value: "1", label: '是' },
                      ]}
                    />
                  </ProFormField>
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="是否有证书"
                    name="isCertificate"
                  >
                    <Select
                      allowClear={true}
                      options={[
                        { value: "0", label: '否' },
                        { value: "1", label: '是' },
                      ]}
                    />
                  </ProFormField>
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="是否可开增值税"
                    name="isTax"
                  >
                    <Select
                      allowClear={true}
                      options={[
                        { value: "0", label: '否' },
                        { value: "1", label: '是' },
                      ]}
                    />
                  </ProFormField>
                </Col>
              </Row>
            );
          },
        },
        {
          formItemProps: {
            style: {
              marginBottom: 5,
              height: 38
            },
          },
          renderFormItem: () => {
            return (
              <Row gutter={24}>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="开发员"
                    name="developer"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="采购员"
                    name="purchaser"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="维护人"
                    name="maintainer"
                  />
                </Col>
                <Col span={6}>
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="文案/美工"
                    name="designer"
                  />
                </Col>
              </Row>
            );
          },
        },
        {
          formItemProps: {
            style: {
              marginBottom: 5,
              height: 56
            },
          },
          renderFormItem: () => {
            return (
              <Row gutter={24} >
                  <ProFormField
                    labelCol={{ flex: '120px' }}
                    label="特/普货"
                    name="specialTags"
                    onChange={(e)=>{
                      console.log(form.getFieldValue("specialTags"),e);


                      //form.setFieldValue("specialTags", [e, ...form.getFieldValue("specialTags")]);
                    }}
                  >
                    <Checkbox.Group>
                      <Checkbox style={{marginLeft: 8}} key={"纯普货"} value={"纯普货"}>纯普货</Checkbox>
                      <Checkbox style={{marginLeft: 8}} key={"普货（羽毛&木质）"} value={"普货（羽毛&木质）"}>普货（羽毛&木质）</Checkbox>
                      <Checkbox key={"需欧盟能效认证"} value={"需欧盟能效认证"}>需欧盟能效认证</Checkbox>
                      <Checkbox key={"需要CE认证"} value={"需要CE认证"}>需要CE认证</Checkbox>
                      <Checkbox key={"成人用品带电池"} value={"成人用品带电池"}>成人用品带电池</Checkbox>
                      <Checkbox key={"成人用品普货"} value={"成人用品普货"}>成人用品普货</Checkbox>
                      <Checkbox key={"粉末"} value={"粉末"}>粉末</Checkbox>
                      <Checkbox key={"纯电池"} value={"纯电池"}>纯电池</Checkbox>
                      <Checkbox key={"内置锂离子电池"} value={"内置锂离子电池"}>内置锂离子电池</Checkbox>
                      <Checkbox key={"外置锂离子电池"} value={"外置锂离子电池"}>外置锂离子电池</Checkbox>
                      <Checkbox key={"内置锂金属电池"} value={"内置锂金属电池"}>内置锂金属电池</Checkbox>
                      <Checkbox key={"外置锂金属电池"} value={"外置锂金属电池"}>外置锂金属电池</Checkbox>
                      <Checkbox key={"内置干电池"} value={"内置干电池"}>内置干电池</Checkbox>
                      <Checkbox key={"外置干电池"} value={"外置干电池"}>外置干电池</Checkbox>
                      <Checkbox key={"内置其它电池"} value={"内置其它电池"}>内置其它电池</Checkbox>
                      <Checkbox key={"外置其它电池"} value={"外置其它电池"}>外置其它电池</Checkbox>
                      <Checkbox key={"电子类"} value={"电子类"}>电子类</Checkbox>
                      <Checkbox key={"蓝牙功能"} value={"蓝牙功能"}>蓝牙功能</Checkbox>
                      <Checkbox key={"原木"} value={"原木"}>原木</Checkbox>
                      <Checkbox key={"加工木质"} value={"加工木质"}>加工木质</Checkbox>
                      <Checkbox key={"眼镜类"} value={"眼镜类"}>眼镜类</Checkbox>
                      <Checkbox key={"家庭刀具类"} value={"家庭刀具类"}>家庭刀具类</Checkbox>
                      <Checkbox key={"非家庭刀具类"} value={"非家庭刀具类"}>非家庭刀具类</Checkbox>
                      <Checkbox key={"弱磁"} value={"弱磁"}>弱磁</Checkbox>
                      <Checkbox key={"强磁"} value={"强磁"}>强磁</Checkbox>
                      <Checkbox key={"带胶水"} value={"带胶水"}>带胶水</Checkbox>
                      <Checkbox key={"太阳能面板"} value={"太阳能面板"}>太阳能面板</Checkbox>
                      <Checkbox key={"LED类"} value={"LED类"}>LED类</Checkbox>
                      <Checkbox key={"纺织品"} value={"纺织品"}>纺织品</Checkbox>
                      <Checkbox key={"液体"} value={"液体"}>液体</Checkbox>
                      <Checkbox key={"防疫用品"} value={"防疫用品"}>防疫用品</Checkbox>
                    </Checkbox.Group>
                  </ProFormField>
                {/*<Col span={6}>*/}
                {/*  <ProFormField*/}
                {/*    labelCol={{ flex: '120px' }}*/}
                {/*    label="是否带电"*/}
                {/*    name="isCharged"*/}
                {/*  >*/}
                {/*    <Select*/}
                {/*      allowClear={true}*/}
                {/*      options={[*/}
                {/*        { value: true, label: '是' },*/}
                {/*        { value: false, label: '否' },*/}
                {/*      ]}*/}
                {/*    />*/}
                {/*  </ProFormField>*/}
                {/*</Col>*/}
                {/*<Col span={6}>*/}
                {/*  <ProFormField*/}
                {/*    labelCol={{ flex: '120px' }}*/}
                {/*    label="是否特货"*/}
                {/*    name="isContainSpecial"*/}
                {/*  >*/}
                {/*    <Select*/}
                {/*      allowClear={true}*/}
                {/*      options={[*/}
                {/*        { value: true, label: '是' },*/}
                {/*        { value: false, label: '否' },*/}
                {/*      ]}*/}
                {/*    />*/}
                {/*  </ProFormField>*/}
                {/*</Col>*/}
                {/*<Col span={6}>*/}
                {/*  <ProFormField*/}
                {/*    labelCol={{ flex: '120px' }}*/}
                {/*    label="是否液体"*/}
                {/*    name="isContainLiquid"*/}
                {/*  >*/}
                {/*    <Select*/}
                {/*      allowClear={true}*/}
                {/*      options={[*/}
                {/*        { value: true, label: '是' },*/}
                {/*        { value: false, label: '否' },*/}
                {/*      ]}*/}
                {/*    />*/}
                {/*  </ProFormField>*/}
                {/*</Col>*/}
              </Row>
            );
          },
        },
        {
          title: '分类',
          dataIndex: ['categoryInfo', 'category'],
          formItemProps: {
            rules: [{ required: true, message: '请选择商品分类' }],
          },
          transform: (value: any, namePath: string, allValues: any) => {
            return {
              categoryInfo: {
                categoryId: allValues.category.key,
                customParams: allValues?.customParams || [],
                standardParams: allValues?.standardParams || [],
              },
            };
          },
          valueType: 'categorySelect',
          width: 'md',
        },
        {
          title: '商品主图',
          dataIndex: 'mainImages',
          valueType: 'uploadImageGroup',
          fieldProps: {
            maxCount: 15,
            accept: 'image/*',
          },
          formItemProps: {
            wrapperCol: { md: 21, lg: 22 },
            extra: '可拖拽图片改变顺序，最多可选15张',
            required: true,
            rules: [
              {
                validator: (rule, value) => {
                  try {
                    if (!value) {
                      return Promise.reject(new Error('请上传商品主图'));
                    }
                    if (isArray(value) && value.length < 1) {
                      return Promise.reject(new Error('至少上传一张商品主图'));
                    }
                    return Promise.resolve();
                  } catch (error) {
                    return Promise.reject(new Error('请上传商品主图'));
                  }
                },
              },
            ],
          },

        },
      ],
    }
  ];

  return (
    <Spin spinning={loading}>
      <CustomBetaSchemaForm
        form={form}
        submitter={false}
        layout="horizontal"
        labelCol={{ flex: '120px' }}
        wrapperCol={{ md: 8, lg: 8 }}
        onFinish={submit}
        labelWidth={120}
        initialValues={{
          useMultiSpec: true,
        }}
        onFinishFailed={onFinishFailed as any}
        onValuesChange={onValuesChange as any}
        columns={columns}
        scrollToFirstError
      />
      {spuId ? (
        <Card bordered={false} style={{marginTop: -10, marginBottom:58}}>
          <Tabs  type="line">
            <TabPane tab="多规格" key="1">
              <>
                <ProductSkusModal spuId={spuId} skuId={skuId}/>
              </>
            </TabPane>
            <TabPane tab="供应商" key="2">
              <>
                <ProductSupplierModal productInfo={spuDetail} skuId={skuId}/>
              </>
            </TabPane>
            <TabPane tab="维护人" key="3">
              <>
                虚位以待
              </>
            </TabPane>
            <TabPane tab="商品附件" key="4">
              <>
                虚位以待
              </>
            </TabPane>
          </Tabs>
        </Card>
      ): null}
      <FooterToolbar>
        <Permission permissionKey={"purchase:product_manager:sku:spuEdit"}>
          <Button type="primary" onClick={() => form.submit()} loading={submitting}>
            保存
          </Button>
        </Permission>
      </FooterToolbar>
    </Spin>
  );
};

export default CreateOrUpdateSpu;
