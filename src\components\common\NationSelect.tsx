import { useNation } from '@/modules/common/application/nations';
import type { NationData } from '@/services/common';
import type { SelectProps } from 'antd';
import { Select } from 'antd';
import { useMemo } from 'react';

type NationSelectProps = SelectProps<any> & {
  valueKey?: keyof NationData;
};
const NationSelect = (props: NationSelectProps) => {
  const { valueKey = 'name', ...rest } = props;
  const { nations } = useNation();

  const options = useMemo(() => {
    return nations?.map((item) => ({ label: item.name, value: item[valueKey] }));
  }, [valueKey, nations]);

  return (
    <Select
      allowClear
      showSearch
      options={options}
      filterOption={(input, option) => {
        return (
          (option?.label as string).toLowerCase().includes(input.toLowerCase()) ||
          (option?.value as string).includes(input.toLowerCase())
        );
      }}
      placeholder='请选择'
      {...rest}
    />
  );
};

export default NationSelect;
