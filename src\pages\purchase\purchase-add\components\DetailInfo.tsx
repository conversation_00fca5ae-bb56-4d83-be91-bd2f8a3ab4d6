import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn } from '@/components/Common-UI/CommonTable/hook';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { Image } from "antd"
import type { CommonTableAction, CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import type { StockinDetail } from '../api';
import { queryStockDetail, StockinOrderState } from '../api';
import { formatDate } from '@/utils/utils';

// const mockData = () => ({
//   "status": {
//     "returnCode": "string",
//     "message": "string",
//     "domainCode": "string",
//     "path": "string"
//   },
//   "body": {
//     "stockinOrderId": "SO031651321",
//     "referencePurchaseOrderId": "R36513251351",
//     "stockinOrderState": "STOCK_IN",
//     "stockinOrderDetailInfoList": [
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//       {
//         "skuId": "32165132",
//         "goodsName": "测试商品名称",
//         "mainImage": "随便放图把",
//         "quantity": 0,
//         "actualQuantity": 0,
//         "goodQuantity": 0,
//         "badQuantity": 0,
//         "warehousingTime": 1725521115000
//       },
//     ],
//     "creatorName": "服装",
//     "gmtCreate": 1725521115000
//   }
// })

type TableProps = CommonTableProps<StockinDetail>;

const DetailInfo = memo((props: { purchaseOrderCode: string }) => {
  const { purchaseOrderCode } = props
  const setColumn = useColumn<StockinDetail>({ align: "center" });
  const [currentPage, setCurrentPage] = useState(1);
  const tableRef = useRef<CommonTableAction>();

  // 1) Request
  const fetchRequest = useMemo<TableProps['fetchRequest']>(() => {
    return async () => {
      // 0 校验
      if (!purchaseOrderCode) {
        return {
          data: [],
          total: 0,
        }
      }
      // 1 查询参数
      const { body } = await queryStockDetail({ purchaseOrderCode });
      // const { body } = mockData();
      // 2 返回值
      return {
        data: body?.stockinOrderDetailInfoList?.map(v => {
          return {
            ...v,
            gmtCreate: body?.gmtCreate,
            creatorName: body?.creatorName,
            stockinOrderId: body?.stockinOrderId,
            referencePurchaseOrderId: body?.referencePurchaseOrderId,
            stockinOrderState: body?.stockinOrderState,
          }
        }) || [],
        total: body?.stockinOrderDetailInfoList?.length || 0,
      };
    };
  }, [purchaseOrderCode]);

  // 2) Column
  const columns = useMemo(() => {
    return [
      setColumn('序号', '_index', { width: 50, render: (_, row, index) => ((currentPage - 1) * 10) + index + 1 }),
      setColumn('采购入库单号', 'stockinOrderId'),
      setColumn('关联采购单号', 'referencePurchaseOrderId'),
      setColumn('单据状态', 'stockinOrderState', { valueEnum: StockinOrderState }),
      setColumn('商品信息', '_info', {
        render: (_, row) => {
          return <div className='goods-info-box'>
            <Image src={row?.mainImage} width={80} height={80} />
            <div className="info-box">
              <div>{row?.goodsName}</div>
              <div>商品SKU：{row?.skuId || "-"}</div>
            </div>
          </div>
        },
        width: 260
      }),
      setColumn('应收数量', 'quantity'),
      setColumn('实收总数', 'actualQuantity'),
      setColumn('实收良品数', 'goodQuantity'),
      setColumn('实收不良品数', 'badQuantity'),
      setColumn('入库时间', 'warehousingTime', { format: formatDate }),
      setColumn('创建人', 'creatorName'),
      setColumn('创建时间', 'gmtCreate', { format: formatDate }),
    ];
  }, [currentPage, setColumn]);

  // 3) 监听 OrderCode
  useEffect(() => tableRef.current?.reloadTable(), [purchaseOrderCode])

  return (
    <div className="detail-info">
      <CommonTable<StockinDetail>
        rowKey="skuId"
        tableRef={tableRef}
        scrollY={600}
        fetchRequest={fetchRequest}
        columns={columns}
        tableRest={{
          search: false,
          onChange: (pagination) => setCurrentPage(pagination.current as number),
        }}
      />
    </div>
  );
});

export default DetailInfo;
