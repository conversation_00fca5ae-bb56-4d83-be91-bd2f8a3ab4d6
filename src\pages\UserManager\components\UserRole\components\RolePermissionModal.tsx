import type { Role } from '@/modules/user/domian/role';
import { bindRoleMenus, getMenus, getRoleMenus } from '@/modules/user/infra/api/role';
import { But<PERSON>, Drawer, message, Row, Spin, Tree } from 'antd';
import { difference } from 'lodash';
import React, { useEffect, useState } from 'react';
import type { MenuItem, RolePermissionItem } from '../../../../modules/user/infra/api/role';

export interface RolePermissionModalProps {
  visible: boolean;
  onClose: () => void;
  role: Role;
}

export const formatTreeData = (menus: MenuItem[]): any => {
  return menus.map((item) => {
    return {
      title: item.menuName,
      key: item.menuId,
      children: item.childList ? formatTreeData(item.childList) : [],
    };
  });
};

export const findMenu = (menus: MenuItem[], id: string): MenuItem | undefined => {
  let currentItem: MenuItem | undefined;
  menus.forEach((item) => {
    if (item.menuId === id) {
      currentItem = item;
    }
    if (!currentItem && item.childList) {
      currentItem = findMenu(item.childList, id);
    }
  });
  return currentItem;
};

export const getMenuKeys = (menus: MenuItem[], keys: React.Key[]) => {
  const newKeys = menus.reduce((k: React.Key[], item: MenuItem) => {
    let prev = k || [];
    if (item.menuId) {
      prev.push(item.menuId);
    }
    if (item.childList) {
      prev = getMenuKeys(item.childList, prev);
    }
    return prev;
  }, keys);
  return newKeys;
};

const RolePermissionModal = (props: RolePermissionModalProps) => {
  const { onClose, visible, role } = props;
  const [rolePermission, setRolePermission] = useState<string[]>([]);
  const [checkedPermissionKeysValue, setCheckedPermissionKeysValue] = useState<React.Key[]>([]);
  const [halfCheckedKeys, setHalfCheckedKeys] = useState<React.Key[]>([]);
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>();

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  const fetchRoleMenus = () => {
    setLoading(true);
    getRoleMenus({
      roleId: role.roleId,
    })
      .then((res) => {
        const menusIds = (res.body?.menus || []).map((item) => {
          return item.menuId;
        });
        setRolePermission(menusIds);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (role.roleId && visible) {
      fetchRoleMenus();
    }
  }, [role.roleId, visible]);

  useEffect(() => {
    getMenus().then((res) => {
      const data = res.body || [];
      const allMenuKeys = getMenuKeys(data, []);
      setExpandedKeys(allMenuKeys);
      setMenus(data);
    });
  }, []);

  /** 筛选不全选的树节点 */
  useEffect(() => {
    const checkedKeys: string[] = [];
    const defaultHalfCheckedKeys: string[] = [];
    if (menus) {
      rolePermission.forEach((item) => {
        const menu = findMenu(menus, item);
        if (menu && menu.childList) {
          const childList = menu.childList.map((c) => c.menuId);
          const isAllIn = childList.every((i) => rolePermission.includes(i));
          if (isAllIn) {
            checkedKeys.push(item);
          } else {
            defaultHalfCheckedKeys.push(item);
          }
        } else {
          checkedKeys.push(item);
        }
      });
      setExpandedKeys(menus.map((i) => i.menuId));
      setHalfCheckedKeys(defaultHalfCheckedKeys);
      setCheckedPermissionKeysValue(checkedKeys);
    }
  }, [menus, rolePermission]);

  const onCheck = (checkedKeysValue: any, info: any) => {
    setCheckedPermissionKeysValue(checkedKeysValue);
    setHalfCheckedKeys(info.halfCheckedKeys);
  };

  const onSubmit = () => {
    const allSelectedMenuKeys = checkedPermissionKeysValue.concat(halfCheckedKeys);
    const addKeys = difference(allSelectedMenuKeys, rolePermission);
    const deleteKeys = difference(rolePermission, allSelectedMenuKeys);
    const opt: RolePermissionItem[] = [];
    addKeys.forEach((key) => {
      opt.push({
        action: 0,
        menuId: key as string,
      });
    });
    deleteKeys.forEach((key) => {
      opt.push({
        action: 1,
        menuId: key as unknown as string,
      });
    });
    bindRoleMenus({
      roleId: role.roleId,
      roleMenuOpts: opt,
    }).then((res) => {
      if (res.status.success) {
        message.success('保存成功');
        fetchRoleMenus();
      } else {
        message.error('保存失败');
      }
    });
  };

  return (
    <Drawer
      title={`【${role.roleName}】菜单权限`}
      closable={false}
      onClose={onClose}
      visible={visible}
      width={400}
      footer={
        <Row style={{ justifyContent: 'flex-end' }}>
          <Button type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Row>
      }
    >
      <Spin spinning={loading}>
        <Tree
          checkable
          treeData={formatTreeData(menus)}
          defaultExpandedKeys={menus.map((item) => item.menuId)}
          checkedKeys={checkedPermissionKeysValue}
          onCheck={onCheck}
          onExpand={onExpand}
          expandedKeys={expandedKeys}
        />
      </Spin>
    </Drawer>
  );
};

export default RolePermissionModal;
