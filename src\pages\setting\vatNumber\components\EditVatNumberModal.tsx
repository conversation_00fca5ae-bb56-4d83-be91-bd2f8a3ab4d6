import React, {useEffect, useState} from 'react';
import {Modal, ModalProps, Form, message} from 'antd';
import ProForm, {ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {ProFormSelect} from "@ant-design/pro-components";
import {getDeptTreeByEndpoint} from "@/modules/user-center/infra/organize";
import mallApiConfig from "../../../../../config/mallApiConfig";
import {executeSave, reqByUrl} from "@/modules/common/infra/api/common";
import {VatNumber} from "@/pages/setting/vatNumber/data";

// 定义参数格式
export type editProps = {
  onFinish: () => void;
  rowData: VatNumber;
} & ModalProps;

export default (props: editProps) => {
  const [form] = ProForm.useForm();
  const {onFinish, rowData, ...rest} = props;
  useEffect( () => {
    form.resetFields();
    if (rowData?.id) {
      reqByUrl('/sales-mgmt-biz/sales-center/setting/vat-account/getById',{id: rowData?.id}).then((res) => {
        if (res.status.success) {
          form.setFieldsValue(res?.body);
          form.setFieldValue("relationAccount", res.body?.relationAccount == null ? [] : JSON.parse(res.body?.relationAccount))
        }
      })
    }
    },
    [rowData?.id]
  );

  const onSubmit = () => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      executeSave('/sales-mgmt-biz/sales-center/setting/vat-account/saveVatNumber', params).then((result) => {
        if (result.status.success) {
          message.success('操作成功');
          onFinish();
        }
      });
    })
  }

  return (
    <>
      <Modal {...rest} title="VAT号码" className={'globalEnterKeySubmit'} onOk={() => onSubmit()} destroyOnClose={true} maskClosable={false}>
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProFormText name="id" hidden={true}/>
          <ProFormText width={"md"} label="VAT" name="vat" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="名称" name="name" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="申请公司" name="applicant" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="申请人" name="contactFirst" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="注册电话" name="phoneFirst" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="注册邮箱" name="emailFirst" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="联系人" name="contactSecond" />
          <ProFormText width={"md"} label="联系电话" name="phoneSecond" />
          <ProFormText width={"md"} label="联系邮箱" name="emailSecond" />
          <ProFormText width={"md"} label="国家" name="nation" />
          <ProFormSelect width={"md"} mode={"tags"} label="关联账号"  name="relationAccount" />
          <ProFormTextArea width={"md"} label="申请人地址" name="address"/>
          <ProFormTextArea width={"md"} label="备注" name="remark"/>
        </Form>
      </Modal>
    </>
  );
};
