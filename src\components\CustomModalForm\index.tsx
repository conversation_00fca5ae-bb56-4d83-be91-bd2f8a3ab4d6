import type { ModalFormProps } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';

export interface CustomModalFormProps<T> extends ModalFormProps<T> {
  onCancel?: () => void;
}
function CustomModalForm<T = Record<string, any>>(props: CustomModalFormProps<T>) {
  const { onFinish, children, ...rest } = props;

  return (
    <ModalForm<T>
      width={500}
      onFinish={onFinish}
      layout="horizontal"
      labelCol={{ span: 7 }}
      {...rest}
    >
      {children}
    </ModalForm>
  );
}

export default CustomModalForm;
