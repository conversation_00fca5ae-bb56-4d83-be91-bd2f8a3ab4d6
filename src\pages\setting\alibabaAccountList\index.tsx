import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import type {PurchasePlan} from "@/pages/PurchasePlanList/data";
import {alibabaAccount} from "@/pages/setting/alibabaAccountList/data";
import {useRequestTable} from "@/hooks/useRequestTable";
import {pageQueryAccount, saveAlibabaAccount, syncLogistics} from "@/modules/setting/infra/api/AlibabaAccount";
import {Button, Form, Modal, notification, Space} from "antd";
import {ProFormText} from "@ant-design/pro-components";

const TableList: React.FC = () => {

  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryAccount({
      ...params,
    });
  });

  const syncLogisticsData=()=>{
    syncLogistics({}).then((res)=>{
      if (res.status.success){
        notification.success({message: '同步成功'});
      }
    })
  }

  const [selectedRows, setSelectedRows] = useState<alibabaAccount[]>([]);

  const [form] = Form.useForm();
  const addOrEditAliAccount = (type: string, data: alibabaAccount) => {

    Modal.confirm({
      title: type == "add" ? "新增" : "编辑",
      icon: false,
      centered: true,
      content: (
        <Form labelCol={{flex: '100px'}} form={form}>
          <ProFormText label="id" name="id" hidden={true} initialValue={type == "add" ? null : data.id}/>
          <ProFormText label="账号名称" name="accountName" initialValue={type == "add" ? null : data.accountName}/>
          <ProFormText label="appKey" name="appKey" initialValue={type == "add" ? null : data.appKey}/>
          <ProFormText label="app秘钥" name="secKey" initialValue={type == "add" ? null : data.secKey}/>
          <ProFormText label="token" name="accessToken" initialValue={type == "add" ? null : data.accessToken}/>
          <ProFormText label="采购主体" name="purchaseEntity" initialValue={type == "add" ? null : data.purchaseEntity}/>
        </Form>
      ),
      onOk: function () {
        const params = {
          id: form.getFieldValue('id'),
          accountId: form.getFieldValue('accountId'),
          accountName: form.getFieldValue('accountName'),
          appKey: form.getFieldValue('appKey'),
          secKey: form.getFieldValue('secKey'),
          purchaseEntity: form.getFieldValue('purchaseEntity'),
          accessToken: form.getFieldValue('accessToken'),
        };

        saveAlibabaAccount(params).then((result) => {
          if (result.status.success) {
            notification.success({message: '创建成功'});
            actionRef.current?.reloadAndRest?.();
          }
        });

      },
    });

    form.resetFields();

  }


  const columns: ProColumns<alibabaAccount>[] = [
    {
      title: '账号名称',
      dataIndex: 'accountName',
    },
    {
      title: 'appkey',
      dataIndex: 'appKey',
      hideInSearch: true,
    },
    {
      title: '关联采购主体',
      dataIndex: 'purchaseEntity',
      hideInSearch: true,
    },
    // {
    //   title: 'token',
    //   dataIndex: 'accessToken',
    //   hideInSearch: true,
    // },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 400,
      render: (v, record) => {
        return (
          <Space>
            <a onClick={() => addOrEditAliAccount("edit", record)}>编辑</a>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<PurchasePlan>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        toolBarRender={() => [
          <Button key="level" type="primary" size={"small"} onClick={() => addOrEditAliAccount("add", null)}>
            新增
          </Button>,
          <Button key="level" type="primary" size={"small"} onClick={() => syncLogisticsData()}>
            同步1688物流信息
          </Button>,
        ]}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;
