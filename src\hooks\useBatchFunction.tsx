import { useCallback } from 'react';

const useBatchFunction = (fn: Function) => {
  const newFn = useCallback((list: any[]) => {
    return new Promise((resolve, reject) => {
      const promiseList = list.map(async (item) => {
        const res = await fn(item);
        return res.status.success;
      });
      Promise.all(promiseList).then((res) => {
        const isSuceess = res.every((i) => !!i);
        if (isSuceess) {
          resolve(isSuceess);
        } else {
          reject(new Error('执行失败'));
        }
      });
    });
  }, []);
  return newFn;
};

export default useBatchFunction;
