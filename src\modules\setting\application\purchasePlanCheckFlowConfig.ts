import { useRequestTable } from '@/hooks/useRequestTable';
import {pageQueryPlanConfig} from "@/modules/setting/infra/api/purchasePlanCheckFlowConfig";

/**
 * 采购计划配置
 */
export const usePurchasePlanConfigList = () => {
  return useRequestTable(pageQueryPlanConfig);
};

export const usePurchasePlanConfig = () => {
  const purchasePlanConfigList = usePurchasePlanConfigList();
  return {purchasePlanConfigList};
};
