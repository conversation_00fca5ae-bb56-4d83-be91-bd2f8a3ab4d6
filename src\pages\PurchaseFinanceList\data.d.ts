import {OrderState} from "@/modules/order/domain/order";


export type PurchaseOrder = {
  "applyPromotionAmount": number,
  "applyRefundAmount": number,
  "gmtCreate": number,
  "gmtModified": number,
  "id": string,
  "isUrgent": boolean,
  "memo": string,
  "merchandiserUsername": string,
  "orderCode": string,
  "orderStatus": OrderState,
  "organizationName": string,
  "platform": string,
  "platformAccount": string,
  "platformOrderAmount": number,
  "platformOrderCode": string,
  "platformOrderTime": string,
  "platformPaymentTime": string,
  "exceptionMessage": string,
  "platformPurchaseAmount": number,
  "platformShippingFee": number,
  "purchasePlanId": string,
  "purchaseTotalAmount": number,
  "purchaseUsername": string,
  "supplierId": number,
  "supplierName": string,
  "title": string
}


export type PurchaseOrderGoods = {
  "goodsRemark": string,
  "id": string,
  "isUrgent": boolean,
  "purchasePlanId": string,
  "salesWarehouse": string,
  "purchasePrice": number,
  "purchaseQuantity": number,
  "purchaseRemark": string,
  "sku": string,
  "status": number,
  "supplierId": string,
  "supplierName": string,
  "totalPrice": number
}
