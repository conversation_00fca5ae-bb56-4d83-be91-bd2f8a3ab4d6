import { FooterToolbar, RouteContext } from '@ant-design/pro-layout';
import type { FooterToolbarProps } from '@ant-design/pro-layout/lib/components/FooterToolbar';
import { useContext } from 'react';

export interface TableFooterToolbarProps extends FooterToolbarProps {
  children?: React.ReactNode;
}
const CustomFooterToolbar = (props: TableFooterToolbarProps) => {
  const { collapsed, siderWidth } = useContext(RouteContext);
  const { children, ...rest } = props;
  return (
    <FooterToolbar style={{ left: collapsed ? 48 : siderWidth, right: 0, width: 'auto' }} {...rest}>
      <>{children}</>
    </FooterToolbar>
  );
};

export default CustomFooterToolbar;
