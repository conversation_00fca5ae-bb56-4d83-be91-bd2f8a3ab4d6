import type { ProductSpecData } from '@/modules/product/domain/categorySpec';
import { Button, Select, Space, Typography } from 'antd';
import { find, findIndex } from 'lodash';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import { useMemo } from 'react';
import type { SpecItemData, SpuSpecValue } from '..';
import EmptyCustomSpecField from '../EmptyCustomSpecField';
import { v4 as uuid } from 'uuid';
import { DeleteOutlined } from '@ant-design/icons';
import { Access } from 'umi';

export type CategorySpecFieldProps = {
  categoryId: string;
  value?: SpuSpecValue;
  onChange?: (value: SpuSpecValue) => void;
  dataSource?: ProductSpecData[];
};

type PlatformSpecItemSelectNameOptions = {
  value: string;
  label: string;
  specId: string;
  required: boolean;
};

const PlatformSpecItemSelect = (props: {
  dataSource: ProductSpecData[];
  onChange: (v: SpecItemData) => void;
  value: SpecItemData;
}) => {
  const { dataSource } = props;
  const [value, setValue] = useMergedState<SpecItemData>(
    { specName: '', specValues: [] },
    {
      value: props.value,
      onChange: props.onChange,
    },
  );
  const specNameOptions = dataSource.map((item) => ({
    value: item.name,
    label: item.name,
    specId: item.specId,
    required: item.required,
  }));
  const valuesOptions = useMemo(() => {
    return find(dataSource, { name: value.specName })?.values.map((item) => {
      return {
        value: item,
        label: item,
      };
    });
  }, [value]);

  const onChangeName = (v: string, option: PlatformSpecItemSelectNameOptions) => {
    const currentData = find(dataSource, { name: v });
    if (currentData) {
      currentData.disabled = true;
    }
    setValue({
      ...value,
      specName: v,
      specId: (option as any).specId,
      required: (option as any).required,
    });
  };

  return (
    <div>
      <Space>
        <Select
          options={specNameOptions}
          value={value.specName}
          style={{ width: 180 }}
          disabled={value.required}
          onChange={(v, options) => onChangeName(v, options as any)}
        />
        <Select
          mode="multiple"
          style={{ width: 350 }}
          value={value.specValues}
          options={valuesOptions}
          onChange={(v) => {
            setValue({
              ...value,
              specValues: v,
            });
          }}
        />
      </Space>
    </div>
  );
};

const PlatformSpecField = (props: CategorySpecFieldProps) => {
  const { dataSource } = props;
  const [value, setValue] = useMergedState<SpuSpecValue>([], {
    value: props.value,
    onChange: props.onChange,
  });
  const data = useMemo(() => {
    const requiredSpecList: ProductSpecData[] = [];
    const noRequiredSpecList: ProductSpecData[] = [];
    const customSpecList = value.filter((item) => !item.specId);
    const platformSpecList: SpecItemData[] = [...value.filter((item) => item.specId)];
    dataSource?.map((item) => {
      if (item.required) {
        requiredSpecList.push(item);
      } else {
        noRequiredSpecList.push(item);
      }
    });
    return {
      customSpecList,
      requiredSpecList,
      noRequiredSpecList,
      platformSpecList,
    };
  }, [dataSource, value]);

  const handleCreate = async () => {
    setValue([...value, { specName: '', specValues: [], specId: uuid() }]);
  };
  const onDelete = (index: number) => {
    const newSpecList = value.map((item) => ({ ...item }));
    newSpecList.splice(index, 1);
    setValue(newSpecList);
  };
  return (
    <div>
      <Space direction="vertical">
        <Access
          accessible={Boolean(dataSource && data.platformSpecList.length < dataSource?.length)}
        >
          <Button onClick={handleCreate} type="primary" ghost>
            添加规格
          </Button>
        </Access>
        <div>
          <Space direction="vertical">
            {data.platformSpecList?.map((item) => {
              // 筛选已经被选择的规格
              const filterInValueData = (dataSource || []).filter((d) => {
                const valuesKey = value.map((v) => v.specName);
                return item.specName == d.name || !valuesKey.includes(d.name);
              });
              return (
                <Space key={item.specId}>
                  <PlatformSpecItemSelect
                    dataSource={filterInValueData}
                    value={item}
                    onChange={(v) => {
                      const newValue = value.map((sp) => {
                        if (sp.specId === item.specId) {
                          return v;
                        }
                        return sp;
                      });
                      setValue([...newValue]);
                    }}
                  />
                  <Access
                    accessible={!item.required}
                    fallback={<Typography.Text type="danger"> * </Typography.Text>}
                  >
                    <DeleteOutlined
                      onClick={() => onDelete(findIndex(value, { specId: item.specId }))}
                    />
                  </Access>
                </Space>
              );
            })}
          </Space>
        </div>
        <EmptyCustomSpecField
          value={data.customSpecList}
          onChange={(v) => {
            const newValue: SpuSpecValue = [];
            value.map((item) => {
              const inCustom = find(data.customSpecList, { specName: item.specName });
              if (inCustom) {
                const currentValue = find(v, { specName: item.specName });
                if (currentValue) {
                  item.specValues = currentValue.specValues;
                  newValue.push(item);
                }
              } else {
                newValue.push(item);
              }
            });
            setValue(newValue);
          }}
        />
      </Space>
    </div>
  );
};

export default PlatformSpecField;
