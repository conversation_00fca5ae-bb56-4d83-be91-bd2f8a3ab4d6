import { BetaSchemaForm } from '@ant-design/pro-form';
import { Card } from 'antd';
import { TableListItem } from '..';

const FormCard: React.FC<{
  value?: {
    key: string;
    label: string;
  }[];
  onChange?: (
    value: {
      key: string;
      label: string;
    }[],
  ) => void;
  columns: any;
  title: string;
}> = ({ title, columns }) => {
  return (
    <Card title={title} style={{ background: '#fff' }}>
      <BetaSchemaForm<TableListItem, 'link' | 'tags' | 'formCard'>
        columns={columns}
        layoutType="Embed"
      />
    </Card>
  );
};

export default FormCard;
