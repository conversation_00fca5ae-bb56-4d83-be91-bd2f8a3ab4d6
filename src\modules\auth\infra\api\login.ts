import mallApiConfig from '../../../../../config/mallApiConfig';
import mallRequest from '@/utils/mallRequest';

/** 登录接口 POST /api/login/account */
export async function loginAccount(
  body: API.LoginParams,
  options?: Record<string, any>,
) {
  return mallRequest<API.ApiBaseResult<API.LoginResult>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/auth/auth-biz/passwordLogin',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export type Tenant = {
  endpoints: string[];
  tenantId: string;
  tenantName: string;
};

export async function queryAuthorizedTenants(params?: { endpoint: string }) {
  return mallRequest<API.ApiBaseResult<Tenant[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/auth/auth-biz/queryAuthorizedTenants',
      method: 'GET',
      params,
    },
  );
}

export async function loginEndpoint(params?: { endpoint: string }) {
  return mallRequest<API.ApiBaseResult<API.LoginResult>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/user-center/auth/auth-biz/loginEndpoint',
      data: params,
    },
  );
}

export type TenantItemData = {
  endpoints: string[];
  tenantId: string;
  tenantName: string;
};

export type LoginTenantParams = { tenantId: string };

export async function loginTenant(params?: { tenantId: string }) {
  return mallRequest<API.ApiBaseResult<API.LoginResult>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/auth/auth-biz/loginTenant',
      method: 'POST',
      data: params,
    },
  );
}
