import type { CategoryData } from '@/modules/category/domain/category';
import type { CascaderProps } from 'antd';
import { Cascader } from 'antd';
import { forwardRef, useEffect, useImperativeHandle } from 'react';
import useCategoryCascader from './useCategoryCascader';

export type CategoryCascaderActions = {
  reload: () => void;
};

const CategoryCascader = forwardRef((props: CascaderProps<CategoryData>, ref) => {
  const { options, initTreeData, loadData } = useCategoryCascader();

  const actions: CategoryCascaderActions = {
    reload: initTreeData,
  };

  useImperativeHandle(ref, () => {
    return actions;
  });

  useEffect(() => {
    actions.reload();
  }, []);

  return <Cascader options={options as any[]} loadData={loadData} {...props} changeOnSelect />;
});

export default CategoryCascader;
