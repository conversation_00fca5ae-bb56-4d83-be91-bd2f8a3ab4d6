import type { CategoryData } from '@/modules/tags/domain/tags';
import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

export interface CreateCategoryParams {
  name: string;
  parentCategoryId: string | number;
  sameLevelSequence?: number;
}

// 创建分类
export async function createCategory(data: CreateCategoryParams) {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/tags/create',
    method: 'POST',
    data,
  });
}

// 删除分类
export async function deleteCategory(categoryIds: string[]) {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/tags/delete',
    method: 'POST',
    data: {
      categoryIds,
    },
  });
}

export interface MoveCategoryParams {
  categoryIds: string[];
  moveTo: string;
}

// 批量移动分类
export async function moveCategory(data: MoveCategoryParams) {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/tags/move',
    method: 'POST',
    data,
  });
}

export interface QueryPageSubCategoryParams extends API.QueryPageParams {
  parentCategoryId?: string | number;
  name?: string;
}

// 管理类目-分页查询下级类目
export async function pageQuerySubCategory(data: QueryPageSubCategoryParams) {
  return mallRequest<API.ApiQueryPageResult<CategoryData[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/tags/pageQuerySubCategory',
      method: 'POST',
      data,
    },
  );
}

export interface QuerySubCategoryParams {
  parentCategoryId?: string | number;
  name?: string;
}

export async function querySubCategory(data: QuerySubCategoryParams) {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/tags/querySubCategory',
    method: 'POST',
    data,
  });
}

export async function queryCategoryList() {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/tags/queryCategoryList',
    method: 'POST'
  });
}

export type CategoryPath = {
  id: string;
  name: string;
};

export interface CategoryDetailInfo extends CategoryData {
  gmtCreate: string;
  gmtModified: string;
  paths: CategoryPath[];
  parentCategoryId: string;
  sameLevelSequence: number;
}

// 管理类目-类目详情
export async function queryCategoryInfo(categoryId: string) {
  return mallRequest<API.ApiBaseResult<CategoryDetailInfo>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/tags/queryInfo',
      method: 'GET',
      params: {
        categoryId,
      },
    },
  );
}

export interface UpdateCategoryParams {
  categoryId: string;
  name: string;
  parentCategoryId: string;
  sameLevelSequence?: number;
}

// 管理类目-更新类目
export async function updateCategory(data: UpdateCategoryParams) {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/tags/update',
    method: 'POST',
    data,
  });
}
