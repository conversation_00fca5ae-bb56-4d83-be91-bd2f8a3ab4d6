
import { Role } from '@/modules/user-center/domain/user';
import {
  MenuItem,
  getRoleMenus,
  getMenus,
  RolePermissionItem,
  bindRoleMenus,
  createMenus
} from '@/modules/user-center/infra/role';
import {Button, Divider, Form, message, Modal, Row, Spin, Tree} from 'antd';
import { difference } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import {ProFormTextArea, ProFormField, ProFormSelect} from "@ant-design/pro-form";
import mallApiConfig from "../../../../../../config/mallApiConfig";

export interface RolePermissionModalProps {
  visible: boolean;
  onClose: () => void;
  role: Role;
}


export const findMenu = (menus: MenuItem[], id: string): MenuItem | undefined => {
  let currentItem: MenuItem | undefined;
  menus.forEach((item) => {
    if (item.menuId === id) {
      currentItem = item;
    }
    if (!currentItem && item.childrenMenu) {
      currentItem = findMenu(item.childrenMenu, id);
    }
  });
  return currentItem;
};

export const getMenuKeys = (menus: MenuItem[], keys: React.Key[]) => {
  const newKeys = menus.reduce((k: React.Key[], item: MenuItem) => {
    let prev = k || [];
    if (item.menuId) {
      prev.push(item.menuId);
    }
    if (item.childrenMenu) {
      prev = getMenuKeys(item.childrenMenu, prev);
    }
    return prev;
  }, keys);
  return newKeys;
};

export interface RolePermissionListProps {
  role: Role;
}

const RolePermissionList = (props: RolePermissionListProps) => {
  const { role } = props;
  const [rolePermission, setRolePermission] = useState<string[]>([]);
  const [checkedPermissionKeysValue, setCheckedPermissionKeysValue] = useState<React.Key[]>([]);
  const [halfCheckedKeys, setHalfCheckedKeys] = useState<React.Key[]>([]);
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>();
  const [form] = Form.useForm();

  const createMenu = (parentMenu: MenuItem) =>{
    Modal.confirm({
      icon: false,
      content: (
        <>
          <Form labelCol={{flex: '80px'}} form={form}>
            <ProFormField
              label="菜单名称"
              name="name"
            />
            <ProFormSelect
              name="resourceType"
              label="菜单类型"
              options={[
                {label: 'MENU',value: "MENU"},
                {label: 'BUTTON',value: "BUTTON"}
              ]}
            />
            <ProFormField
              label="权限简码"
              name="permission"
            />
            <ProFormTextArea
              label="描述"
              name="description"
            />
            <ProFormField
              name="endpoint"
              initialValue={mallApiConfig.currTerminal}
              hidden={true}
            />
            <ProFormField
              label="权限简码"
              name="version"
              initialValue={"1.0.0"}
              hidden={true}
            />
          </Form>
        </>
      ),
      onOk: async () => {
        const params = form.getFieldsValue();
        params.parentId = parentMenu.menuId;
        createMenus(params).then(res => {
          if(res.status.success){
            message.success("创建成功");
            fetchRoleMenus();
          }
        })
      }
    });
  }

  const formatTreeData = (menus: MenuItem[]): any => {
    return menus.map((item) => {
      return {
        title: <div onDoubleClick={()=>createMenu(item)}>{item.title}</div>,
        key: item.menuId,
        children: item.childrenMenu ? formatTreeData(item.childrenMenu) : [],
      };
    });
  };

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  const fetchRoleMenus = () => {
    setLoading(true);
    getRoleMenus({
      roleId: role.roleId,
    })
      .then((res) => {
        const menusIds = (res.body?.menus || []).map((item) => {
          return item.menuId;
        });
        setRolePermission(menusIds);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (role.roleId) {
      fetchRoleMenus();
    }
  }, [role.roleId]);

  useEffect(() => {
    getMenus().then((res) => {
      const data = res.body || [];
      const allMenuKeys = getMenuKeys(data, []);
      setExpandedKeys(allMenuKeys);
      setMenus(data);
    });
  }, []);

  /** 筛选不全选的树节点 */
  useEffect(() => {
    const checkedKeys: string[] = [];
    const defaultHalfCheckedKeys: string[] = [];
    if (menus) {
      rolePermission.forEach((item) => {
        const menu = findMenu(menus, item);
        if (menu && menu.childrenMenu) {
          const childList = menu.childrenMenu.map((c) => c.menuId);
          const isAllIn = childList.every((i) => rolePermission.includes(i));
          if (isAllIn) {
            checkedKeys.push(item);
          } else {
            defaultHalfCheckedKeys.push(item);
          }
        } else {
          checkedKeys.push(item);
        }
      });
      setExpandedKeys(menus.map((i) => i.menuId));
      setHalfCheckedKeys(defaultHalfCheckedKeys);
      setCheckedPermissionKeysValue(checkedKeys);
    }
  }, [menus, rolePermission]);

  const changeOpt = useMemo(() => {
    const allSelectedMenuKeys = checkedPermissionKeysValue.concat(halfCheckedKeys);
    const addKeys = difference(allSelectedMenuKeys, rolePermission);
    const deleteKeys = difference(rolePermission, allSelectedMenuKeys);
    const opt: RolePermissionItem[] = [];
    addKeys.forEach((key) => {
      opt.push({
        opt: 0,
        menuId: key as string,
      });
    });
    deleteKeys.forEach((key) => {
      opt.push({
        opt: 1,
        menuId: key as unknown as string,
      });
    });
    return opt;
  }, [checkedPermissionKeysValue,halfCheckedKeys,rolePermission ])

  const onCheck = (checkedKeysValue: any, info: any) => {
    setCheckedPermissionKeysValue(checkedKeysValue);
    setHalfCheckedKeys(info.halfCheckedKeys);
  };

  const onSubmit = () => {

    bindRoleMenus({
      roleId: role.roleId,
      menuOpts: changeOpt,
    }).then((res) => {
      if (res.status.success) {
        message.success('保存成功');
        fetchRoleMenus();
      } else {
        message.error('保存失败');
      }
    });
  };

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <Spin spinning={loading}>
        <Tree
          autoExpandParent
          checkable
          treeData={formatTreeData(menus)}
          defaultExpandedKeys={expandedKeys}
          checkedKeys={checkedPermissionKeysValue}
          onCheck={onCheck}
          onExpand={onExpand}
          expandedKeys={expandedKeys}
        />
      </Spin>
      <Divider />
      <Row style={{ justifyContent: 'flex-end' }}>
        <Button type="primary" onClick={onSubmit} disabled={!changeOpt?.length}>
          保存
        </Button>
      </Row>
    </div>
  );
};

export default RolePermissionList;
