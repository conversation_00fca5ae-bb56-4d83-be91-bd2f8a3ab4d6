import {PlusOutlined} from '@ant-design/icons';
import {Button, Descriptions, Form, Modal, notification, Space, Tag} from 'antd';
import React, {useEffect, useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import {history, useIntl} from 'umi';
import CustomTable from '@/components/CustomTable';
import useTableSelection from '@/hooks/useTableSelection';
import {isArray} from 'lodash';
import CustomFooterToolbar from '@/components/CustomFooterToolbar';
import type {SpuListItem} from '@/modules/product/domain/spu';
import CategoryCascader from '@/components/CategoryCascader';
import ExpandRowRender from './components/ExpandRowRender';
import {useSpu} from '@/modules/product/application/spu';
import styles from './styles.less';
import {formatDate} from '@/utils/utils';
import {ProFormField, ProFormRadio} from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import {
  downloadGoodsEditTemplate,
  downloadGoodsTemplate,
  importGoods,
  importSkuImage
} from "@/modules/product/infra/api/spu";
import Permission from "@/components/Permission";
import {ProFormText} from "@ant-design/pro-components";
import {commonExport} from "@/utils/comUtil";

const ProductList: React.FC<{}> = () => {
  const intl = useIntl();
  const { selectedRowKeys, rowSelection, setSelectedRows } = useTableSelection<SpuListItem>();
  const [activeStatusKey, setActiveStatusKey] = useState<string>('all');
  const spuDomain = useSpu();
  const { actionRef } = spuDomain;

  const [form] = Form.useForm();
  const downloadTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form} labelCol={{span:6}}>
          <ProFormRadio.Group
            label="是否添加"
            name="flag"
            initialValue='add'
            options={[
              { label: '新增', value: 'add' },
              { label: '编辑', value: 'edit' },
            ]}
          />
          <ProFormText  name="12" label="模板">
            <a onClick={function () {
              downloadGoodsTemplate().then(res=>{
                commonExport(res, '商品新增模板');
              });
            }}>新增模板</a>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadGoodsEditTemplate().then(res=>{
                commonExport(res, '商品编辑模板');
              });
            }}>编辑模板</a>
          </ProFormText>
          <ProFormField  name="importExcelUrl" label="文件">
            <UploadFile limit={1} />
          </ProFormField>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        const flag = form.getFieldValue('flag');

        importGoods(link,flag).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }

  const improtGoodsImage=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;

        importSkuImage(link).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }


  const columns: ProColumns<SpuListItem>[] = [
    {
      title: '基础信息',
      dataIndex: 'title',
      valueType: 'textarea',
      hideInSearch: true,
      width: 350,
      render: (v, record) => {
        return (
          <Space>
            <div style={{ width: 80 }}>
              {record.mainImage ? <img src={record.mainImage} width={80} /> : null}
            </div>
            <div>
              <Descriptions className={styles.list} column={1}>
                <Descriptions.Item label="标题">{record.title}</Descriptions.Item>
                <Descriptions.Item label="SPU">{record.spu}</Descriptions.Item>
              </Descriptions>
            </div>
          </Space>
        );
      },
    },
    {
      title: '商品名称',
      dataIndex: 'title',
      hideInTable: true,
    },
    {
      title: 'SPU',
      dataIndex: 'spuCustomCode',
      hideInTable: true,
    },
    {
      title: 'SKU',
      dataIndex: 'skuCustomCode',
      hideInTable: true,
    },
    {
      title: '商品类目',
      dataIndex: 'categoryName',
      width:100,
      render: (v, record) => {
            return <>{record.categoryPaths.map((item) => item.name).join('>')}</>;
            },
    },
    // {
    //   title: '商品类目',
    //   dataIndex: 'categoryName',
    //   valueType: 'textarea',
    //   width: 100,
    //   search: {
    //     transform: (value) => {
    //       if (isArray(value)) {
    //         return {
    //           categoryId: value[value.length - 1],
    //         };
    //       }
    //       return value;
    //     },
    //   },
    //   renderFormItem: () => {
    //     return <CategoryCascader />;
    //   },
    //   render: (v, record) => {
    //     return <>{record.categoryPaths.map((item) => item.name).join('>')}</>;
    //   },
    // },

    {
      title: '产品维护',
      hideInSearch: true,
      width: 180,
      render: (v, record) => {
        return (
          <Descriptions className={styles.list} column={1}>
            <Descriptions.Item label="采购员">{record.purchaser || '--'}</Descriptions.Item>
            <Descriptions.Item label="开发员">{record.developer || '--'}</Descriptions.Item>
            <Descriptions.Item label="维护人">{record.maintainer || '--'}</Descriptions.Item>
            <Descriptions.Item label="美工">{record.designer || '--'}</Descriptions.Item>
          </Descriptions>
        );
      },
    },
    {
      title: '销售状态',
      hideInSearch: true,
      dataIndex: 'info',
      width: '10%',
      render: () => {
        return (
         "在售"
        );
      },
    },
    {
      title: '产品标签',
      hideInSearch: true,
      dataIndex: 'tag',
      width: '10%',
      render: (v, record) => {
        return (
          <Space wrap size={[2, 2]}>
            {record?.tags?.map((item, index) => (
              <Tag color="blue" key={index}>
                {item}
              </Tag>
            ))}
          </Space>
        );
      },
    },
    {
      title: '时间',
      hideInSearch: true,
      width: 180,
      render: (v, record) => {
        return (
          <Descriptions className={styles.list} column={1}>
            <Descriptions.Item label="创建">{formatDate(record.gmtCreate)}</Descriptions.Item>
            <Descriptions.Item label="更新">{formatDate(record.gmtCreate)}</Descriptions.Item>
          </Descriptions>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        // <Popconfirm
        //   key="del"
        //   title="删除后无法恢复哦,是否确认删除"
        //   onConfirm={async () => {
        //     await spuDomain.remove(record.spuId);
        //   }}
        // >
        //   <Typography.Text type="danger">禁用</Typography.Text>
        // </Popconfirm>,
        <a
          key="show"
          onClick={() => history.push({pathname: `/product-manager/goods/code/spu-${record.spu}`, state: { title: record.spu }})}
        >
          查看
        </a>,
      ],
    },
  ];

  useEffect(() => {
    if (actionRef?.current) {
      actionRef.current.reload();
    }
  }, [activeStatusKey]);

  return (
    <>
      <CustomTable<SpuListItem>
        headerTitle=""
        actionRef={actionRef}
        rowKey="spuId"
        scroll={{ x: 1200 }}
        request={(params, sort, filter) => {
          const { category, ...rest } = params;
          return spuDomain.fetchList(
            {
              ...rest,
              developState:
                activeStatusKey !== 'all' && activeStatusKey
                  ? activeStatusKey
                  : params?.developState,
            },
            sort,
            filter,
          );
        }}
        toolBarRender={() => [
          <Permission permissionKey={"purchase:product_manager:sku:spuCreate"}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => history.push('/product-manager/goods/sku/create')}
            >
              新增商品
            </Button>
          </Permission>,
          <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => improtGoodsImage()}
            >
              导入商品图片
            </Button>
          </Permission>,
          <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => downloadTemplate()}
            >
              导入商品
            </Button>
          </Permission>
        ]}
        expandable={{
          expandedRowRender: (record) => {
            return <ExpandRowRender dataSource={record} />;
          },
        }}
        columns={columns}
        // rowSelection={{
        //   ...rowSelection,
        // }}
      />
      {selectedRowKeys?.length > 0 && (
        <CustomFooterToolbar
          extra={
            <Space>
              <div>
                已选择 <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a> 项&nbsp;&nbsp;
              </div>
              <Button type="link" onClick={() => setSelectedRows([])}>
                取消选择
              </Button>
            </Space>
          }
        >
          {/*<Button*/}
          {/*  type="primary"*/}
          {/*  onClick={async () => {*/}
          {/*    await spuDomain.batchDelete(selectedRowKeys);*/}
          {/*    setSelectedRows([]);*/}
          {/*    actionRef?.current?.reloadAndRest?.();*/}
          {/*  }}*/}
          {/*>*/}
          {/*  批量删除*/}
          {/*</Button>*/}
        </CustomFooterToolbar>
      )}
    </>
  );
};

export default ProductList;
