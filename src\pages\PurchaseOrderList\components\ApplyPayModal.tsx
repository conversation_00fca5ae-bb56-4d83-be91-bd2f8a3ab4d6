import {ProFormSelect, ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {Form, ModalProps} from "antd";
import { Modal } from "antd";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import React, {useState} from "react";
import {useRequest} from "ahooks";
import {getApplyReducePayment} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {ProFormCheckbox} from "@ant-design/pro-components";

// 定义参数格式
export type CreateModalProps = {
  order: PurchaseOrder,
  onFinish: (values: any) => void;
} & ModalProps;

const ApplyPayModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish,order, ...rest } = props;
  const { data } = useRequest(() => getApplyReducePayment(order.id).then((res)=>res.body));


  return <Modal {...rest} title="申请付款" onOk={()=>form.submit()}>
    <Form form={form} labelCol={{flex: '110px'}} onFinish={onFinish}>
      <Form.Item label="平台总金额" style={{height:"10px",fontWeight:'bold',marginTop:"-10px"}}>
        <span className="ant-form-text">{data?.platformOrderAmount == null ? '--' : data.platformOrderAmount?.toFixed(2)}</span>
        <span className="ant-form-text">平台运费：{data?.platformShippingFee == null ? '--' : data.platformShippingFee?.toFixed(2)}</span>
      </Form.Item>
      <Form.Item label="本地总金额" style={{height:"10px",fontWeight:'bold'}}>
          <span className="ant-form-text">
            {data?.shippingFee == null && data?.purchaseTotalAmount == null ? '--' : (data.purchaseTotalAmount+data.shippingFee+data.applyRefundAmount-data.applyPromotionAmount)?.toFixed(2)}
          </span>
        <span className="ant-form-text">本地运费：{data?.shippingFee == null ? '--' : data.shippingFee?.toFixed(2)}</span>
        <span className="ant-form-text">商品金额：{data?.purchaseTotalAmount == null ? '--' : data.purchaseTotalAmount?.toFixed(2)}</span>
      </Form.Item>
      <Form.Item label="差额" style={{height:"10px",fontWeight:'bold', marginLeft:"42px",paddingBottom:"20px"}}>
          <span className="ant-form-text">{(data?.platformOrderAmount == null || data?.shippingFee == null || data?.purchaseTotalAmount == null) ? '--' :
            (data.platformOrderAmount - (data.purchaseTotalAmount+data.shippingFee+data.applyRefundAmount-data.applyPromotionAmount))?.toFixed(2)}</span>
      </Form.Item>
      <ProFormCheckbox
        width="md"
        name="platformShppingFeeCheck"
        label="平台运费校验"
        initialValue={true}
        tooltip={"平台运费高于100块时提示异常，取消勾选则不限制"}
      />
      <ProFormText
        width="md"
        name="accountName"
        label="供应商收款户名"
        initialValue={order?.accountName}
      />
      <ProFormText
        width="md"
        name="bankName"
        label="供应商收款银行"
        initialValue={order?.bankName}
      />
      <ProFormText
        width="md"
        name="account"
        label="供应商收款账号"
        initialValue={order?.account}
      />
      <ProFormSelect
        width="md"
        name="payStatus"
        label="申请类型"
        options={[
          {label: '申请付余额', value: "20"},
          {label: '申请付定金', value: "10"},
          {label: '无需付款', value: "-10"}
        ]}
      />
      <ProFormSelect
        width="md"
        name="payType"
        label="支付方式"
        options={[
          {label: '跨境宝', value: "5"},
          {label: '银行转账', value: "1"},
          {label: '现金支付', value: "2"},
          {label: '支付宝', value: "3"},
          {label: '余额抵充', value: "4"},
          {label: '超级支付宝', value: "6"}
        ]}
      />
      <ProFormTextArea
        width="md"
        name="remark"
        label="申请备注"
      />
    </Form>
  </Modal>
}

export default ApplyPayModal;
