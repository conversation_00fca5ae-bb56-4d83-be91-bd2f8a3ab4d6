import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {PurchaseInstorage} from "@/pages/warehouse/ScanArrival/data";
import mallRequest1 from "@/utils/mallRequest1";

/**
 * 扫描到货
 */
export type scanArrivalParams = {
  code: string;
  scanArrivalType: string;
};
export async function scanArrival(data?: scanArrivalParams) {
  return mallRequest<API.ApiBaseResult<scanArrivalParams>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/warehouse/scannedArrivalList',
      data,
    },
  );
}


//下载商品模板
export async function downloadWarehouseGoodsTemplate() {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/warehouse/goods/downloadWarehouseTemplate',
      method: 'GET',
      responseType: 'blob',
    },
  );
}


//下载调拨单模板
export async function downloadTransferOrder() {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/sales-mgmt-biz/sales-center/warehouse/transfer/downloadTransferOrder',
      method: 'GET',
      responseType: 'blob',
    },
  );
}

/**
 * 扫描签收
 */
export type scanSignParams = {
  code: string;
  scanSignType: string;
};
export async function scanSign(data?: scanSignParams) {
  return mallRequest<API.ApiBaseResult<scanSignParams>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/warehouse/scanSign',
      data,
    },
  );
}

/**
 * 签收列表
 */
export type pageQuerySignListParams = {
  trackingNumber: string;
  purchaseOrderCode: string;
  operatorUsername: string;
};
export async function pageQuerySignList(data?: pageQuerySignListParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/warehouse/pageQuerySignList',
    data,
  });
}

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;

export async function pageQueryArrivalList(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseInstorage[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/warehouse/pageQueryInstorage',
    data,
  });
}






