import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {alibabaAccount} from "@/pages/setting/alibabaAccountList/data";

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;


// 采购计划列表
export async function pageQueryAccount(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<alibabaAccount[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/account/pageQuery',
    data,
  });
}

export async function saveAlibabaAccount(data?: PageQueryParams) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/account/saveAlibabaAccount',
    data,
  });
}

export async function syncLogistics(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/logistics/syncLogistics',
    data,
  });
}

export async function loadAliAccountList() {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/account/getAliAccountList',
  });
}




