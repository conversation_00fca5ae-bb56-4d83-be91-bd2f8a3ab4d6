import { useState } from 'react';
import { message } from 'antd';
import {
  pageQueryAdvancePayOrder, 
  saveAdvancePayConfig,
  getCreditBalance
} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";

export const settlementSearchType =  [
  { label: '所有', value: 'ALL' },
  { label: '账期结算', value: 'ACCOUNT_PERIOD' },
  { label: '款到发货', value: 'DELIVERY_ON_ARRIVAL' },
];
export const SettleTypeMap = {
  CASH_ON_DELIVERY: "货到付款",
  DELIVERY_ON_ARRIVAL: "款到发货",
  ACCOUNT_PERIOD: "账期结算",
};
export const creditSearchType = [
  { label: '所有', value: 'ALL' },
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];

export const crossSearchType = [
  { label: '所有', value: 'ALL' },
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];

export const orderTypeMap = {
  "online": "1688",
  "onlineIsManual": "1688手工单",
  "isManual": "手工单"
}

// 申请类型
export const ApplyTypeOptions = [
  {label: '申请付余额', value: 20},
  {label: '申请付定金', value: 10},
  {label: '无需付款', value: -10 }
]
// 支付方式下啦菜单
export const payTypeOptions = [
  {label: '跨境宝', value: "5"},
  {label: '银行转账', value: "1"},
  {label: '现金支付', value: "2"},
  {label: '支付宝', value: "3"},
  {label: '余额抵充', value: "4"},
  {label: '超级支付宝', value: "6"}
]
export const settlementTypeOptions = [
  { label: '1688担保交易',value: 'fxassure' },
  { label: '1688供应商账期',value: 'period' },
  { label: '1688诚意赊',value: 'credit' },
]

//
export const cardTitleMap = {
  'ronghe': '诚意赊1688融贸',
  'ziguang': '诚意赊1688紫光',
}
export const useAdvancedPayment = () => {
  const [loading,setLoading] = useState<boolean>(false);
  const [tableData,setTableData] = useState<PurchaseOrder[]>([]);
  const [creditBalance,setCreditBalance] = useState<any>()
  const [total,setTotal] = useState<number>(0);
  const getTableData = async (params: any) => {
    setLoading(true);
    const res = await pageQueryAdvancePayOrder(params);
    setLoading(false);
    const { items = [], pageMeta = {} } = (res?.body || {})
    // 预处理，去掉后端默认值
    // @ts-ignore
    setTableData(items?.map(v => ({
      ...v,
      payStatus: v?.payStatus === 0 ? '' : v?.payStatus,
      // @ts-ignore
      payType: v?.payType === 'NOT_MATCH' ? '' : v?.payType,
      platformTradeType: v?.platformTradeType === 'assureTrade' ? 'fxassure' : v?.platformTradeType,
      periodAmount: v?.periodAmount || (v?.amountPayable && v?.afterAmount ? v?.amountPayable - v?.afterAmount : v?.amountPayable) || 0
    })));
    setTotal(pageMeta?.total || 0)
  }
  const saveConfig = async(params: [], callback?: () =>  void) => {
    const res = await saveAdvancePayConfig({purchaseOrderApplyPaymentRequest: params});
    if (res.status.success) {
      message.success('操作成功');
      callback && callback();
    } else {
      message.error('操作失败,请稍后重试');
    }
  }
  const getCreditBalanceDetail = async() => {
    const res = await getCreditBalance();
    setCreditBalance(res?.body || [])
  }
  return{
    loading,
    tableData,
    creditBalance,
    total,
    setTableData,
    getTableData,
    saveConfig,
    getCreditBalanceDetail,
  }
}