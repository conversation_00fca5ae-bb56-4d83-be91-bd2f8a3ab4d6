import type {ProColumns} from '@ant-design/pro-table';
import React, {useState} from 'react';
import {getOrderGoods, insertOrderGoods} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {useRequest} from "ahooks";
import {Button, Card, Image, message, Spin, Table} from "antd";
import moment from "moment";
import {PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";
import BatchTakeStock from "@/pages/warehouse/TakeStock/components/BatchTakeStock";

export type PurchaseOrderGoodsListProps = {
  orderId: string;
  supplierId: string;
};

export const PurchaseOrderGoodsList = (props: PurchaseOrderGoodsListProps) => {
  const { orderId,supplierId } = props;
  const { data, refresh,loading } = useRequest(() => getOrderGoods(orderId).then((res) => res.body));
  const [selectedRowsState, setSelectedRows] = useState<PurchaseOrderGoods[]>([]);
  const [BatchTakeStockModal, setBatchTakeStockModal] = useState<boolean>(false);
  const [orderGoodsCreate, setOrderGoodsCreate] = useState<boolean>(false);

  const modify = () =>{
    if (selectedRowsState.length<=0){
      message.error("请选择需要修改的sku");
      return null;
    }
    setBatchTakeStockModal(true);
  }

  const create = (values: any) =>{
    values.orderId=orderId;
    insertOrderGoods(values).then((res) =>{
      if (res.status.success){
        refresh();
        message.success("添加成功")
      }
    });
    setOrderGoodsCreate(false);
  }

  const columns: ProColumns<PurchaseOrderGoods>[] = [
    {
      title: "sku",
      dataIndex: 'sku',
    },
    {
      title: "图片",
      dataIndex: 'skuImg',
      render: (v, record) => {
        return <Image src={record.skuImg} width={80}/>
      }
    },
    {
      title: "采购数量",
      dataIndex: 'purchaseQuantity',
    },
    {
      title: '已收数量',
      dataIndex: 'arrivalQuantity',
    },
    {
      title: "商品备注",
      dataIndex: 'goodsRemark',
    },
    {
      title: "创建时间",
      dataIndex: 'gmtCreate',
      render: (v, record) => {
      return moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss");
    }
    },
  ];
  return (
    <>
      <Spin spinning={loading} style={{paddingTop:0}}>
        <Card style={{border:"none",padding:"0"}}>
            <Button
              type="primary"
              style={{marginRight:20}}
              key="bundledAlibaba"
              onClick={() => modify() }
            >
              盘点入库
            </Button>

        </Card>
        <Card>
          <Table
            dataSource={data}
            columns={columns}
            rowKey="id"
            pagination = {false}
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectedRows(selectedRows);
              },
            }}
          />
        </Card>
      </Spin>
      <BatchTakeStock visible={BatchTakeStockModal} data={selectedRowsState} onCancel={() => setBatchTakeStockModal(false)}
                              onFinish={function (){
                                setBatchTakeStockModal(false);
                                refresh();
                              }}/>
    </>
  );
};

export type PurchaseOrderGoodsModalProps = {
  orderId: string;
  supplierId: string;
};

export default (props: PurchaseOrderGoodsModalProps) => {
  const { orderId,supplierId } = props;
  return (
    <>
        <PurchaseOrderGoodsList orderId={orderId} supplierId={supplierId}/>
    </>
  );
};
