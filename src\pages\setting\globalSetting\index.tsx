import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Space, Switch} from "antd";
import {executeSave, reqByPage} from "@/modules/common/infra/api/common";
import {GlobalSetting} from "@/pages/setting/globalSetting/data";
import EditGlobalSettingModal from "@/pages/setting/globalSetting/components/EditGlobalSettingModal";
import {Access} from "umi";
import Permission from "@/components/Permission";

const TableList: React.FC = () => {

  const [editModal, setEditModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<GlobalSetting>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/purchase-mgmt-biz/purchase-center/setting/dict/pageQuery',{
      ...params,
    });
  });

  const changeStatus = (record: GlobalSetting) => {
    executeSave('/purchase-mgmt-biz/purchase-center/setting/dict/saveSysDict', record);
  }

  const columns: ProColumns<GlobalSetting>[] = [
    {
      title: '名称',
      dataIndex: 'dictName',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.dictName || '--'}</div>;
      }
    },
    {
      title: '简码',
      dataIndex: 'dictCode',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.dictCode || '--'}</div>;
      }
    },
    {
      title: '业务键',
      dataIndex: 'itemText',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.itemText || '--'}</div>;
      }
    },
    {
      title: '业务值',
      dataIndex: 'dictValue',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.dictValue || '--'}</div>;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.description || '--'}</div>;
      }
    },
    {
      title: '开启/关闭',
      align: 'center',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return (
            <Switch onChange={(e)=>{record.status = e;changeStatus(record)}}  defaultChecked={record?.status}/>
        );
      }
    },
    {
      title: '操作',
      align: 'center',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return (
          <a type="primary" onClick={()=>{setEditModal(true);setCurrentRow(record)}}>
            编辑
          </a>
        );
      }
    }



  ];

  return (
    <>
      <CustomPage<GlobalSetting>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        scroll={{y: 660}}
        search={false}
        toolBarRender={()=>[
          <Permission permissionKey={"purchase:purchase"}>
            <Button size={"small"} key="level" type="primary" onClick={() => {
              setEditModal(true);
              setCurrentRow({});
            }}>
              添加配置
            </Button>
          </Permission>
        ]}
      />
      <Access accessible={editModal}>
        <EditGlobalSettingModal visible={editModal} globalSetting={currentRow} onCancel={()=>setEditModal(false)} onFinish={()=>{setEditModal(false);actionRef.current?.reload()}}/>
      </Access>
    </>
  );
};

export default TableList;
