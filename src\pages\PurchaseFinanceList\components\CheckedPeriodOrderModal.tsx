import { Button, Input, Modal, ModalProps, notification, Row, Space, Table, Typography } from 'antd';
import React, { useState } from 'react';
import { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import { financeAuditQuery, periodFinanceAudit } from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import { useRequest } from 'ahooks';
import { payTypeEnum } from "@/modules/purchaseOrder/domain/purchaseOrder";
import moment from "moment";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { ColumnProps } from "antd/es/table";

const CheckedOrderModal = (props: CheckedOrderModalProps) => {
  const { onFinish, orderData, ...rest } = props
  const { onCancel } = rest;
  const [remark, setRemark] = useState<string>();
  const [amountSum, setAmountSum] = useState<any>();
  const param = {
    "orderIdList": orderData.map((item) => item.id),
  }
  const { data, refresh, loading } = useRequest(() => financeAuditQuery(param).then((res) => {
    const sum = { totalApplyAmount: 0, totalErpAmount: 0, totalOrderAmount: 0 };
    res.body?.map(item => {
      sum.totalApplyAmount += item.periodAmount;
      sum.totalErpAmount += item.amountPayable - item.afterAmount;
      sum.totalOrderAmount += item.amountPayable;
    })
    setAmountSum(sum);
    return res.body;
  }));


  const onOkManage = (auditStatus: string) => {
    param.auditStatus = auditStatus;
    param.remark = remark;
    periodFinanceAudit(param).then((result) => {
      if (result.status.success) {
        notification.success({ message: result.status.message ? result.status.message : "审核完成" });
        refresh();
      }
    });
  }

  const columns: ColumnProps<PurchaseOrder>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.title}</span>;
      }
    },
    {
      title: '采购/平台单号',
      dataIndex: 'orderCode',
      width: 200,
      render: (v, record) => {
        return <><div>{record?.orderCode}</div><div>{record?.platformOrderCode}</div></>;
      }
    },
    {
      title: '采购员/跟单员',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.purchaseUsername}/{record?.merchandiserUsername}</span>;
      }
    },
    {
      title: '收款人',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span>{record?.accountName || '--'}</span>;
      }
    },
    {
      title: '账期审核状态',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.periodStatus || '--'}</span>;
      }
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      render: (v, record) => {
        const payStatus = payTypeEnum[record?.payType];
        const options = [
          payStatus != undefined ? (
            <span>
              <span className="ant-badge-status-dot ant-badge-status-success"></span>
              <span className="ant-badge-status-text" style={{ fontSize: 12 }}>{payStatus}</span>
            </span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '申请结算金额',
      dataIndex: 'periodAmount',
      render: (v, record) => {
        return <>
          <div>
            {record?.periodAmount?.toFixed(2) || 0.00}
            {record?.periodAmount?.toFixed(2) != (record?.amountPayable - record?.afterAmount)?.toFixed(2) ? (
              <ExclamationCircleOutlined style={{ color: 'red', fontSize: 10, float: "right" }} />
            ) : null}
          </div>
        </>;
      }
    },
    {
      title: '应付金额',
      width: 300,
      dataIndex: 'amountPayable',
      render: (v, record) => {
        return <><span style={{ fontSize: 12 }}>{(record?.amountPayable - record?.afterAmount)?.toFixed(2)}(账期应付) =  {record?.amountPayable?.toFixed(2) || 0.00}(下单金额) - {record?.afterAmount?.toFixed(2) || 0.00}(售后金额) </span></>
      }
    },
    {
      title: '申请结算时间',
      dataIndex: 'periodTime',
      render: (v, record) => {
        return record?.periodTime ? moment(record?.periodTime)?.format("YYYY-MM-DD HH:mm") : '--';
      }
    }
  ];

  return (
    <Modal {...rest} title="财务审核" width="90%"
      footer={
        <>
          <Row justify="space-between" >
            <div>
              <span style={{ fontWeight: "bold", color: amountSum?.totalApplyAmount?.toFixed(2) != amountSum?.totalErpAmount?.toFixed(2) ? "red" : "" }}>申请总额： {amountSum?.totalApplyAmount?.toFixed(2) || 0.00}</span>
              <span style={{ marginLeft: 10, fontWeight: "bold" }}>应付总额： {amountSum?.totalErpAmount?.toFixed(2) || 0.00}</span>
              <span style={{ marginLeft: 10, fontWeight: "bold" }}>下单总额： {amountSum?.totalOrderAmount?.toFixed(2) || 0.00}</span>
            </div>
            <div>
              <span style={{ fontWeight: "bold" }}>备注： <Input style={{ width: 300 }} key={""} onChange={(e) => { setRemark(e.target.value) }} name={"remark"} /></span>
              <Button style={{ marginLeft: 10 }} type="primary" size={"small"} onClick={() => onOkManage("PASS")} >
                通过
              </Button>
              <Button type="primary" ghost size={"small"} style={{ color: "red", borderColor: "red" }} onClick={() => onOkManage("REJECT")}>
                驳回
              </Button>
              <Button type="primary" size={"small"} ghost onClick={onCancel}>
                取消
              </Button>
            </div>
          </Row>
        </>} destroyOnClose>
      <Table
        dataSource={data}
        columns={columns}
        rowKey="id"
        size={"small"}
        scroll={{ y: 550 }}
        pagination={false}
        loading={loading}
      />
    </Modal>
  );
}

// 定义参数格式
export type CheckedOrderModalProps = {
  orderData: PurchaseOrder[];
  onFinish: (values: PurchaseOrder[]) => void;

} & ModalProps;
export default CheckedOrderModal;
