import type { UploadImageResult } from '@/modules/common/infra/api/common';
import { uploadVideo } from '@/modules/common/infra/api/common';
import { uploadImage } from '@/modules/common/infra/api/common';
import { UploadOutlined } from '@ant-design/icons';
import { useMountMergeState } from '@ant-design/pro-utils';
import type { UploadProps } from 'antd';
import { Button, Input, message, Upload } from 'antd';
import type { RcFile } from 'antd/lib/upload';
import { useMemo, useState } from 'react';
import PreviewImageList from '../ImageList';
import { isImg } from '@ant-design/pro-utils';

export interface ImageListItem extends Partial<UploadImageResult> {
  externalUrl?: string;
  type?: 'IMAGE' | 'VIDEO';
  fileUrl?: string;
  fileId?: string;
}
export type CustomUploadImageGroupProps = {
  value?: ImageListItem[];
  onChange?: (data: ImageListItem[]) => void;
  maxCount?: number;
} & Pick<UploadProps, 'accept'>;

const CustomUploadImageGroup = (props: CustomUploadImageGroupProps) => {
  const { maxCount } = props;
  const [value, onChange] = useMountMergeState<ImageListItem[]>([], {
    value: props.value,
    onChange: props.onChange,
  });

  const canUpload = useMemo(() => {
    if (maxCount && value) {
      return maxCount > value?.length;
    }
    return true;
  }, [maxCount, value]);

  const [inputLink, setInputLink] = useState<string>();

  const [loading, setLoading] = useState<boolean>();

  const handleUpload = () => {
    if (inputLink) {
      onChange([
        ...value,
        {
          externalUrl: inputLink,
          type: isImg(inputLink) ? 'IMAGE' : 'VIDEO',
          fileUrl: '',
          fileId: '',
        },
      ]);
      setInputLink(undefined);
    }
  };

  const uploadProps: UploadProps = {
    accept: props.accept || 'image/*,video/*',
    showUploadList: false,
    customRequest: (options) => {
      setLoading(true);
      const { onSuccess } = options;
      const file = options.file as RcFile;
      const uploadFilename = file.name;
      const reader = new FileReader();
      reader.readAsArrayBuffer(file); // 读取图片文件
      reader.onload = async (e) => {
        let res;
        if (file.type.includes('video')) {
          res = await uploadVideo(uploadFilename, e.target?.result);
        } else {
          res = await uploadImage(uploadFilename, e.target?.result);
        }
        const type = file.type.includes('video') ? 'VIDEO' : 'IMAGE';
        if (res.status.success) {
          onChange([
            ...value,
            { fileId: res.body.fileId, fileUrl: res.body.link, type, externalUrl: '' },
          ]);
          message.success('上传成功');
          onSuccess?.call(true, true, true as any);
        } else {
          message.error('图片上传失败, 请稍后重试');
        }
        setLoading(false);
      };
      reader.onerror = () => {
        setLoading(false);
      };
    },
  };

  return (
    <div>
      {canUpload ? (
        <Input.Group compact>
          <Input
            style={{ width: 230 }}
            value={inputLink}
            onChange={(e) => setInputLink(e.target.value)}
            placeholder="请输入网络图片链接"
          />
          <Button onClick={handleUpload} disabled={!inputLink}>
            提取
          </Button>
          <Upload {...uploadProps}>
            <Button icon={<UploadOutlined />} loading={loading}>
              上传
            </Button>
          </Upload>
        </Input.Group>
      ) : null}
      <div style={{ marginTop: 20 }}>
        <PreviewImageList
          imageList={value?.map((item) => item.externalUrl || item.fileUrl) as string[]}
          // showRemoveIcon={true}
          onRemove={(file) => {
            const newList = [...value];
            const newFileList = newList?.filter((f) => f.fileUrl !== file.uid && f.externalUrl !== file.uid);
            console.log(file, newFileList)
            onChange([...newFileList]);
          }}
        />
      </div>
    </div>
  );
};

export default CustomUploadImageGroup;
