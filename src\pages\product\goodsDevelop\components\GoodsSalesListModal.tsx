import type { ModalProps } from 'antd';
import {Modal, Space} from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import type { PurchasePlan, PurchasePlanLog } from '@/pages/PurchasePlanList/data';
import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import React, { useEffect, useRef } from 'react';
import { ProTable } from '@ant-design/pro-components';
import {SupplierGoods} from "@/modules/supplier/domain/vender";
import {pageQueryGoodsSales} from "@/modules/tongtool/infra/api/tongtool";

// 定义参数格式
export type GoodsSalesModalProps = {
  goodsSalesParam: any;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const GoodsSalesListModal = (props: GoodsSalesModalProps) => {
  const { onFinish, goodsSalesParam, ...rest } = props;

  const actionRef = useRef<ActionType>();

  const fetchList = usePageQueryRequest((params) => {
    return pageQueryGoodsSales({
      ...params,
      goodsSku: goodsSalesParam.sku
    });
  });

  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [goodsSalesParam]);

  const columns: ProColumns<SupplierGoods>[] = [
    {
      title: '订单号',
      align: 'left',
      dataIndex: 'orderIdCode',
    },
    {
      title: '平台SKU',
      align: 'left',
      dataIndex: 'webstoreCustomLabel',
    },
    {
      title: 'SKU',
      align: 'center',
      dataIndex: 'goodsMatchedSku',
    },
    {
      title: '数量',
      align: 'center',
      dataIndex: 'goodsMatchedQuantity'
    },
    {
      title: '平台',
      align: 'center',
      dataIndex: 'platformCode',
    },
    {
      title: '站点',
      align: 'center',
      dataIndex: 'ebaySiteEnName',
    },
    {
      title: '发货仓库',
      align: 'center',
      dataIndex: 'warehouseName',
    },
    {
      title: '账号',
      align: 'center',
      dataIndex: 'saleAccount',
    },
    // {
    //   title: '物流商类型',
    //   align: 'center',
    //   dataIndex: 'dispathTypeName',
    // },
    {
      title: '状态',
      align: 'center',
      dataIndex: 'orderStatus',
    },
    {
      title: '下单时间',
      align: 'center',
      dataIndex: 'saleTime',
    },
    {
      title: '发货时间',
      align: 'center',
      dataIndex: 'despatchCompleteTime',
    },
    {
      title: '退款时间',
      align: 'center',
      dataIndex: 'refundedTime',
    },
    {
      title: '作废',
      align: 'center',
      dataIndex: 'isInvalid',
    },
    {
      title: '人工审核',
      align: 'center',
      dataIndex: 'isSuspended',
    },
  ];

  return (
    <Modal {...rest} title="销量" closable={false} width="95%" onOk={onFinish}>
      <ProTable<PurchasePlanLog>
        options={false}
        actionRef={actionRef}
        request={fetchList}
        columns={columns}
        size={"small"}
        rowKey="id"
        bordered
      />
    </Modal>
  );
};

export default GoodsSalesListModal;
