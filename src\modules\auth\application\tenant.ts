import mallApiConfig from 'config/mallApiConfig';
import type { TenantItemData } from '../infra/api/login';
import {
  queryAuthorizedTenants,
  loginTenant as loginTenantApi,
  loginEndpoint as loginEndpointApi,
} from '../infra/api/login';

export const useTenant = () => {
  const getCurrentTenant = async () => {
    const res = await queryAuthorizedTenants();
    let tenant: TenantItemData | null = null;
    if (res.body) {
      const tenantList = res.body;
      [tenant] = tenantList;
    }
    return tenant;
  };
  // 登录租户
  const loginTenant = async (tenant?: TenantItemData) => {
    if (tenant) {
      const tenantRes = await loginTenantApi({ tenantId: tenant.tenantId });
      if (!tenantRes.status.success) {
        return false;
      }

      return tenantRes.status.success;
    }
    return false;
  };

  // 登录终端
  const loginEndpoint = async () => {
    const endPointRes = await loginEndpointApi({
      endpoint: mallApiConfig.currTerminal,
    });
    return endPointRes.status.success;
  };

  const login = async () => {
    try {
      // 获取当前账号的Tenant;
      const tenant = await getCurrentTenant();
      if (!tenant) {
        return false;
      }
      const tenantRes = await loginTenant(tenant);
      if (!tenantRes) {
        return false;
      }
      return await loginEndpoint();
    } catch (error) {
      return false;
    }
  };

  return {
    login,
  };
};
