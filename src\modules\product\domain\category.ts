import type { DataNode } from 'antd/lib/tree';

export type Category = {
  parentCategory: string;
  categoryName: string;
  gmtCreateDate: number;
  id: string;
};

export type CategoryData = {
  categoryId: string;
  leaf: boolean;
  level: number;
  name: string;
  parentId: string;
  sequence: number;
};

export type SimpleCategoryData = {
  categoryId: string;
  categoryName: string;
};

export function updateTreeData(list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] {
  return list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children,
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children),
      };
    }
    return node;
  });
}

// 根据分类的path 获取分类名称
export function getCategoryNameByCategoryPaths(paths: SimpleCategoryData[]) {
  return paths.map((item) => item.categoryName).join('/');
}
