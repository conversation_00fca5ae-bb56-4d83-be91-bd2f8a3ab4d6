import { Select } from 'antd';
import { memo, useEffect, useState } from 'react';
import { queryAvailableLogistics } from './api';

const SelectLogistice = memo((props: Record<keyof any, any>) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);
  const { getChannel } = props;
  useEffect(() => {
    queryAvailableLogistics({}).then((res) => {
      const { body } = res;
      const channel = body?.map((item) => ({
        value: item.logisticsId,
        label: item.name,
      }));
      setSelectOption(channel);
      const obj: any = {};
      body?.map((item) => (obj[item.logisticsId] = item.name));
      getChannel && getChannel(obj);
    });
  }, []);

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择物流渠道"
      optionFilterProp="children"
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
    />
  );
});

export default SelectLogistice;
