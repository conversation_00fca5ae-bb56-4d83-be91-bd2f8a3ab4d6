import {ProFormSelect, ProFormText} from "@ant-design/pro-form";
import {Card, Form, message, ModalProps} from "antd";
import { Modal } from "antd";
import React, {useEffect, useState} from "react";
import {
  editExpense, getFinanceSubjectList, getPaymentBankList,
} from "@/modules/financeExpense/infra/api/expense";
import {FinanceExpense} from "@/modules/financeExpense/domain/expense";

// 定义参数格式
export type props = {
  onFinish: () => void;
  expense: FinanceExpense;
} & ModalProps;

const   EditExpenseModal = (props: props) => {
  const [form] = Form.useForm();
  const {onFinish, expense, ...rest} = props;
  const [bankList, setBankList] = useState<any>();
  const [financeSubjectList, setFinanceSubjectList] = useState<any>();

  /**
   * 提交
   */
  const handleSubmit = () => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      editExpense(params).then(res => {
        if (res.status.success) {
          message.success("修改成功");
          onFinish();
        }
      })
    })
  }

  /**
   * 初始话数据
   */
  useEffect(() => {
    form.setFieldsValue(expense);
    if(!expense?.paymentBank){
      form.setFieldValue("paymentBank", []);
    }else{
      form.setFieldValue("paymentBank", JSON.parse(expense?.paymentBank)||[]);
    }
    if(!bankList){
      //付款银行
      getPaymentBankList().then(res=>{
        let list: never[] = [];
        if(res?.body?.length > 0){
          res.body?.map((item: any) => {
            // @ts-ignore
            list[item?.paymentBankName] = item?.paymentBankName;
          })
          setBankList(list);
        }
      })
    }
    if(!financeSubjectList){
      //付款银行
      getFinanceSubjectList().then(res=>{
        let list: never[] = [];
        if(res?.body?.length > 0){
          res.body?.map((item: any) => {
            // @ts-ignore
            list[item?.name] = item?.name;
          })
        }
        setFinanceSubjectList(list);
      })
    }
  }, [expense]);

  return <Modal {...rest} title="修改报销单" onOk={handleSubmit} destroyOnClose={true}>
    <Card bodyStyle={{padding: 0}} bordered={false}>
      <Form form={form} labelCol={{flex: '100px'}}  onFinish={onFinish}>
          <ProFormText
            width="sm"
            name="title"
            label="标题"
            disabled={true}
            initialValue={expense?.title}
          />
          <ProFormText
            name="id"
            hidden={true}
            initialValue={expense?.id}
          />
        <ProFormSelect
          name="financeSubject"
          label="财务科目"
          width={"sm"}
          showSearch={true}
          rules={[{required: true}]}
          valueEnum={financeSubjectList}
        />
        <ProFormSelect
          name="paymentBank"
          label="付款银行"
          width={"sm"}
          mode="tags"
          rules={[{required: true}]}
          valueEnum={bankList}
        />
      </Form>
    </Card>
  </Modal>
}
export default EditExpenseModal;
