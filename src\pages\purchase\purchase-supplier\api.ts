import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "@/../config/mallApiConfig";

export type PageCondition = {
  pageNum?: number;
  pageSize?: number;
}
export type PageQueryParams = {
  pageCondition?: PageCondition;
  supplierId?: string;
  status?: string;
  orderBy?: {
    filed?: string;
    direction?: number;
  }
}
export type Item = {
  venderId: string;
  venderName?: string;
  venderCode?: string;
  foreignName?: string;
  status?: string;
  settleCurrency?: string;
  settleType?: string;
  settleCircle?: string;
  payType?: string;
  supplierAddressDTO?: {
    nationName?: string;
    provinceName?: string;
    cityName?: string;
    areaName?: string;
    streetName?: string;
    detail?: string;
    zipCode?: string;
    addressId?: string;
  };
  venderAddressInfoDTO?: {
    nationName?: string;
    provinceName?: string;
    cityName?: string;
    areaName?: string;
    streetName?: string;
    detail?: string;
    zipCode?: string;
    addressId?: string;
  };
  venderContractorDTO?: {
    contractor?: string;
    cellphone?: string;
    email?: string;
  };
  gmtCreate?: number;
}

export type Detail = {
  venderId?: string;
  venderName?: string;
  venderCode?: string;
  contractor?: string;
  cellphone?: string;
  email?: string;
  nationName?: string;
  detail?: string;
  payType?: string;
  settleType?: string;
  settleCircle?: string;
  settleCurrency?: string;
}

// 结算类型
export const SettleTypeMap = {
  CASH_ON_DELIVERY: "货到付款",
  DELIVERY_ON_ARRIVAL: "款到发货",
  ACCOUNT_PERIOD: "账期结算",
};

// 结算周期
export const SettleCircleTypeMap = {
  'ONLINE7': "线上7天",
  'ONLINE10': "线上10天",
  'ONLINE20': "线上20天",
  'ONLINE30': "线上30天",
  'OFFLINE7': "线下7天",
  'OFFLINE15': "线下15天",
  'OFFLINE30': "线下30天"
};

// 结算方式
export const PayTypeMap = {
  CAHS: '现金',
  BANK_TRANS: '银行转账',
  ALIPAY: '支付宝',
  CROSS_BORDER: '跨境宝',
};

// 状态
export const VenderStatusMap = {
  'ACTIVATED': '启用',
  'DISABLED': '禁用',
};

// 供应商列表
export function pageQuery(data: PageQueryParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Item[]>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseSupplier/pageQuery',
    method: 'POST',
    data,
  },
  );
}
// 采购主体列表
export function purchaseEntityQuery(data: PageQueryParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Item[]>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseEntity/pageQuery',
    method: 'POST',
    data,
  },
  );
}
// 获取所有供应商数据
export function pageQueryAll() {
  return mallRequest<API.ApiBaseResult<API.ApiBaseResult<Item[]>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/getAll',
    method: 'GET',
  },
  );
}

// 禁用
export function disableItem(data: { venderId?: string }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<string>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/disable',
    method: 'POST',
    data,
  },
  );
}
// 禁用
export function activeItem(data: { venderId?: string }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<string>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/active',
    method: 'POST',
    data,
  },
  );
}


// 创建
export function createItem(data: Detail) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<string>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseSupplier/create',
    method: 'POST',
    data,
  },
  );
}

// 编辑
export function editItem(data: Detail) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<string>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/update',
    method: 'POST',
    data,
  },
  );
}

// 详情
export function itemDetail(params: { venderId: string }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Detail>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/render',
    method: 'GET',
    params,
  },
  );
}
