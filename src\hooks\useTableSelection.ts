import type { TableRowSelection } from 'antd/lib/table/interface';
import React, { useState } from 'react';

function useTableSelection<T>() {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [selectedRowsState, setSelectedRows] = useState<T[]>([]);
  const rowSelection: TableRowSelection<T> = {
    selectedRowKeys,
    onChange: (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };
  return {
    selectedRowKeys,
    setSelectedRowKeys,
    selectedRowsState,
    setSelectedRows,
    rowSelection,
  };
}

export default useTableSelection;
