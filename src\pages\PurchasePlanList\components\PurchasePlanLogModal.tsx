import type { ModalProps } from 'antd';
import { Modal } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import type { PurchasePlan, PurchasePlanLog } from '@/pages/PurchasePlanList/data';
import type { queryPurchasePlanLogParams } from '@/modules/purchasePlan/infra/api/purchasePlan';
import { queryPurchasePlanLogs } from '@/modules/purchasePlan/infra/api/purchasePlan';
import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import { useEffect, useRef } from 'react';
import { ProTable } from '@ant-design/pro-components';

// 定义参数格式
export type PlanLogModalProps = {
  searchPlanLogParams: queryPurchasePlanLogParams;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const PurchasePlanLogModal = (props: PlanLogModalProps) => {
  const { onFinish, searchPlanLogParams, ...rest } = props;
  const actionRef = useRef<ActionType>();
  const fetchList = usePageQueryRequest((params) => {
    return queryPurchasePlanLogs({
      ...params,
      planId: searchPlanLogParams.planId,
      planGoodsId: searchPlanLogParams.planGoodsId,
    });
  });
  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [searchPlanLogParams]);

  const columns: ProColumns<PurchasePlanLog>[] = [
    {
      title: '操作人',
      width: 150,
      align: 'left',
      dataIndex: 'operatorUsername',
    },
    {
      title: '内容',
      align: 'left',
      dataIndex: 'content',
    },
    // {
    //   title: '操作类型',
    //   align: "center",
    //   dataIndex: 'contents',
    //   initialValue: '系统操作',
    // },
    {
      title: '时间',
      align: 'left',
      width: 200,
      dataIndex: 'gmtCreate',
      valueType: 'dateTime',
    },
  ];

  return (
    <Modal {...rest} title="采购计划日志" closable={false} width="800px" onOk={onFinish}>
      <ProTable<PurchasePlanLog>
        search={false}
        options={false}
        size="small"
        actionRef={actionRef}
        request={fetchList}
        columns={columns}
        rowKey="id"
        bordered
      />
    </Modal>
  );
};

export default PurchasePlanLogModal;
