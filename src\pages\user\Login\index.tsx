import Footer from '@/components/Footer';
import { useAuthenticate } from '@/modules/auth/application/auth';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-form';
import { <PERSON><PERSON>, Button, Form, message } from 'antd';
import React, { useState, useEffect } from 'react';
import { useIntl,history } from 'umi';
import styles from './index.less';
import {queryCurrentUser} from "@/modules/auth/infra/api/auth";
import {getUserMenus} from "@/modules/user/infra/user";
import {useModel} from "@@/plugin-model/useModel";

/** 此方法会跳转到 redirect 参数所在的位置 */
const goto = () => {
  if (!history) return;
  setTimeout(() => {
    const { query } = history.location;
    const { redirect } = query as { redirect: string };
    // window.location.replace(redirect || '/');
    history.push(redirect || '/');
  }, 10);
};

/** 此方法会跳转到 redirect 参数所在的位置 */
const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [platform, setPlatform] = useState('升威运营平台');
  const auth = useAuthenticate();
  const intl = useIntl();
  const { initialState, setInitialState } = useModel('@@initialState');

  const fetchUserInfo = async () => {
    const res = await queryCurrentUser();
    if (res.body) {
      const currentMenu = await getUserMenus();
      setInitialState({
        ...initialState,
        currentUser: res.body,
        currentMenu: currentMenu?.body?.menus,
        isSuperAdmin: currentMenu?.body?.isSuperAdmin === '1',
      });
    }
  };

  const handleSubmit = async (values: API.LoginParams) => {
    setSubmitting(true);
    try {
      // 登录
      const success = await auth.login({ ...values });
      setSubmitting(false);
      if (success) {
        const defaultloginSuccessMessage = intl.formatMessage({
          id: 'pages.login.success',
          defaultMessage: '登录成功！',
        });
        message.success(defaultloginSuccessMessage);
        await fetchUserInfo();
        goto();
        return;
      }
    } catch (error) {
      const defaultLoginFailureMessage = intl.formatMessage({
        id: 'pages.login.failure',
        defaultMessage: '登录失败，请重试！',
      });

      message.error(defaultLoginFailureMessage);
    }
    setSubmitting(false);
  };

  useEffect(() => {
    setTimeout(() => {
      // 融合供应链ERP
      document.title = `升威运营平台`;
    }, 500);
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.wrapper}>
          <div className={styles.top}>
            {/* <div className={styles.header}>logo占位</div> */}
            <div className={styles.desc}>{platform}</div>
          </div>

          <div className={styles.main}>
            <LoginForm<Pick<API.LoginParams, 'account' | 'password'>>
              size="large"
              form={form}
              onFinish={handleSubmit}
              submitter={{
                render: () => {
                  return (
                    <Button block type="primary" loading={submitting} onClick={form.submit}>
                      登录
                    </Button>
                  );
                },
              }}
              onValuesChange={(changeValue) => {
                if (changeValue.account) {
                  form.setFieldsValue({
                    account: String(changeValue.account).trim(),
                  });
                }
              }}
            >
              {/*{userLoginState && !userLoginState?.success && (*/}
              {/*  <LoginMessage*/}
              {/*    content={intl.formatMessage({*/}
              {/*      id: 'pages.login.accountLogin.errorMessage',*/}
              {/*      defaultMessage: userLoginState?.message || '账户或密码错误',*/}
              {/*    })}*/}
              {/*  />*/}
              {/*)}*/}
              <ProFormText
                name="account"
                rules={[{ required: true, message: '请输入账户' }]}
                fieldProps={{
                  placeholder: '请输入账户',
                  prefix: <UserOutlined />,
                }}
                bordered={false}
              />
              <ProFormText.Password
                name="password"
                rules={[{ required: true, message: '请输入密码' }]}
                fieldProps={{
                  placeholder: '请输入密码',
                  prefix: <LockOutlined />,
                }}
              />
            </LoginForm>
          </div>
        </div>
      </div>
      <Footer
        style={{
          backgroundColor: '#fff',
        }}
      />
    </div>
  );
};

export default Login;
