import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {orderAuditConfig} from "@/pages/setting/orderAuditConfig/data";

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;


//
export async function pageQueryOrderAudit(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<orderAuditConfig[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/orderAudit/pageQuery',
    data,
  });
}
