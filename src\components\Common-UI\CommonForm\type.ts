import type { ProFormInstance, ProFormItemProps, ProFormProps } from '@ant-design/pro-form';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';
import type { InputNumberProps, RadioGroupProps } from 'antd';
import type { FieldProps } from 'rc-field-form/es/Field';
import React from 'react';

export type Type = '' | '';
export type CommonFormLine = CommonFormColumn | CommonFormChild | null;

export type CommonFormColumn = {
  title: string;
  childrens: CommonFormChild[];
};

export type CommonFormChild = {
  label: string;
  key: string;
} & ChildRest;

export type ChildRest = {
  // 组件类型
  type?: 'ProFormTextArea' | 'ProFormDateTimePicker' | 'ProFormDateTimeRangePicker' | 'ProFormCheckbox' | 'ProFormSelect';
  // 选择组件
  valueEnum?: ProFormSelectProps['valueEnum'];
  // 单选组件
  options?: RadioGroupProps['options'];
  // 数字组件
  min?: number;
  max?: number;
  // 自定义渲染
  render?: () => React.ReactNode;
  wrapper?: boolean;

  // 组件原生属性
  fieldProps?: FieldProps & InputNumberProps<string | number>;
  // 宽度（部分组件拥有）
  width?: number | string | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  // 是否必需
  required?: boolean;
  // 是否只读
  readonly?: boolean;
  disabled?: boolean;
  // 是否是整数
  isInteger?: boolean;

  // 限制字数
  maxLength?: number | string;
  // 提示
  placeholder?: string;
  // 错误提示
  errTip?: string;
  // 高度
  height?: string | number;
  // 绑定函数
  onChange?: (value: any) => void;

  // 是否显示
  show?: boolean;
} & ProFormItemProps;

// Props
export interface CommonFormAction {
  actionRef?: ProFormInstance;
  setFieldsValue: ProFormInstance['setFieldsValue'];
}
export type CommonFormProps = {
  columns?: (CommonFormColumn | CommonFormChild | null)[];
  actions?: [];
  commonRef?: React.Ref<CommonFormAction | undefined>;
  readonly?: boolean;
  showBtn?: boolean; // 显示表格按钮
  enSpan?: number | string; // 英文环境下的文字占比
  cnSpan?: number | string; // 中文环境下的文字占比
} & ProFormProps;
