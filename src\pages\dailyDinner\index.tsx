import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Form, message, Modal, Space, Tag} from "antd";
import Permission from "@/components/Permission";
import {downloadData, downloadDataByParams, executeSave, reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import moment from "moment";
import {DailyDinner} from "@/pages/dailyDinner/data";
import OrderDailyDinnerModal from "@/pages/dailyDinner/components/OrderDailyDinnerModal";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import {Access, Link} from "umi";
import ProForm, {ProFormSelect} from "@ant-design/pro-form";
import {ProFormCheckbox, ProFormDateRangePicker} from "@ant-design/pro-components";
import {commonExport} from "@/utils/comUtil";

const TableList: React.FC = () => {

  const [orderDinnerModal, setOrderDinnerModal] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<DailyDinner[]>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/setting/daily-dinner/pageQuery',{
      ...params,
    });
  });

  const deleteDinner = (record: DailyDinner) =>{
    Modal.confirm({
      title: '确认删除吗?',
      onOk: async () => {
        reqByUrl('/sales-mgmt-biz/sales-center/setting/daily-dinner/deleteDailyDinner', {'id': record?.id}).then((result) => {
          if (result.status.success) {
            message.success('删除成功');
            actionRef?.current?.reload();
          }
        });
      },
    });
  }
  const [form] = ProForm.useForm();
  const checkDinner = () => {
    const ids = selectedRows?.map(item=>{return item?.id});
    if(ids?.length == 0 || !ids){
      message.error("请勾选订餐菜单！");
      return false;
    }
    Modal.confirm({
      title: '确认订餐列表吗?',
      content: (
        <Form form={form}>
          <ProFormCheckbox
            width="md"
            name="checkStatus"
            label="取消确认"
            tooltip={"勾选时则批量取消确认"}
          />
        </Form>
      ),
      onOk: async () => {
        const checkStatus = form.getFieldValue('checkStatus') ? 'uncheck' : 'checked';
        executeSave('/sales-mgmt-biz/sales-center/setting/daily-dinner/checkDailyDinner', {'ids': ids,'status':checkStatus}).then((result) => {
          if (result.status.success) {
            message.success('确认成功');
            actionRef?.current?.reload();
          }
        });
      },
    });
  }


  const exportDailyDinner=()=>{
    Modal.confirm({
      icon: '',
      // title: '确认导出吗？',
      width: 500,
      content:(<Form form={form}>
        <ProFormDateRangePicker name={"date"} label={"下单时间"}/>
      </Form>),
      onOk: async () => {
        downloadDataByParams("/sales-mgmt-biz/sales-center/setting/daily-dinner/exportDailyDinner",{'orderDate': form.getFieldValue('date')}).then(res=>{
          commonExport(res, '日常点餐导出');
        });
      },
    });

  }

  const columns: ProColumns<DailyDinner>[] = [
    {
      title: '员工名称',
      fieldProps:{searchTitle: "员工"},
      dataIndex: 'creator',
      width: 200,
      hideInTable: true,
    },
    {
      title: '餐厅',
      dataIndex: 'canteenName',
      width: 200,
      hideInTable: true,
    },
    {
      dataIndex: 'status',
      hideInTable:true,
      valueType: 'select',
      valueEnum: {'uncheck': '待确认', 'checked': '已确认'},
    },
    {
      title: '订餐',
      dataIndex: 'dinner',
      fieldProps:{searchTitle: "订餐"},
      width: 1800,
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return <Filters.CheckboxButton
          defaultValue={["1"]}
          options={[
            {value: "1", label: '全部'},
            {value: "currentDay", label: '今日订餐'},
          ]}
          onChange={(e) => {
            if(e.length>0 && e!=formInstance.getFieldValue('dinner')){
              formInstance.setFieldValue('dinner', e);
            }
            actionRef.current?.reload();
          }}
        />
      }
    },
    {
      title: '菜名',
      dataIndex: 'name',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{record?.name}</div>
        </>;
      }
    },
    {
      title: '员工名称',
      dataIndex: 'creator',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{record?.creator}</div>
        </>;
      }
    },
    {
      title: '餐厅',
      dataIndex: 'canteenName',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{record?.canteenName}</div>
        </>;
      }
    },
    {
      title: '订餐状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{record?.status=='checked' ? <Tag color={"green"}>已确认</Tag> : <Tag>待确认</Tag>}</div>
        </>;
      }
    },
    {
      title: '下单时间',
      dataIndex: 'date',
      hideInSearch: true,
      align: 'left',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}>{moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm")}</div>
        </>
      }
    },
    {
      title: '订餐金额',
      dataIndex: 'price',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{record?.price?.toFixed(2)}</div>
        </>;
      }
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{record?.quantity || '--'}</div>
        </>;
      }
    },
    {
      title: '总金额',
      dataIndex: 'contactFirst',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>{(record?.quantity*record?.price)?.toFixed(2)}</div>
        </>;
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 300,
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.remark}</div>;
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <Space>
            <a style={{color: "red"}} onClick={()=>{deleteDinner(record)}}>
              删除
            </a>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<DailyDinner>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        scroll={{y: 620}}
        rowSelection={{onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          }
        }}
        toolBarRender={()=>[
          <Button size={"small"} ghost={true} key="dinner" type="primary" onClick={() => {
            setOrderDinnerModal(true);
          }}>
            订餐
          </Button>,
          <Permission permissionKey={"purchase:setting:editDinnerMenu"}>
            <Link to={"/setting/setting/dinnerMenu"}>
              <Button size={"small"} key="level1" type="primary">
                菜单维护
              </Button>
            </Link>
          </Permission>,
          <Permission permissionKey={"purchase:setting:checkDinner"}>
            <Button size={"small"} key="level2" type="primary" onClick={() => {checkDinner()}}>
              订餐确认
            </Button>
          </Permission>,
          <Permission permissionKey={"purchase:setting:exportDailyDinner"}>
            <Button size={"small"} key="level3" type="primary" onClick={() => {exportDailyDinner()}}>
              导出
            </Button>
          </Permission>
        ]}
      />
      <Access  accessible={orderDinnerModal}>
        <OrderDailyDinnerModal visible={orderDinnerModal} onCancel={()=>setOrderDinnerModal(false)} onFinish={()=>{setOrderDinnerModal(false);actionRef.current?.reload()}}/>
      </Access>
    </>
  );
};

export default TableList;
