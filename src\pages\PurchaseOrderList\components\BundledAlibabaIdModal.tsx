import {ProFormSelect, ProFormField,ProFormText} from "@ant-design/pro-form";
import {Form, ModalProps} from "antd";
import { Modal } from "antd";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {useState} from "react";

// 定义参数格式
export type CreateModalProps = {
  purchaseOrderData: PurchaseOrder,
  onFinish: (values: any) => void;
} & ModalProps;

const BundledAlibabaIdModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish,purchaseOrderData, ...rest } = props;
  const [purchaseOrder] = useState<PurchaseOrder>();
  //TODO  暂时使用防止对象为空报错
  const data=purchaseOrderData==undefined?purchaseOrder:purchaseOrderData;

  return <Modal {...rest} title="关联平台单号" onOk={()=>form.submit()}>
    <Form form={form}  onFinish={onFinish} labelCol={{flex: '80px'}}>
      <ProFormText
        width="md"
        disabled
        hidden
        name="id"
        label="采购单id"
        initialValue={data?.id}
      />
      <ProFormText
        width="md"
        name="title"
        disabled
        label="标题"
        initialValue={data?.title}
      />
      <ProFormText
        width="md"
        name="supplierName"
        disabled
        label="供应商"
        initialValue={data?.supplierName}
      />
      <ProFormText
        width="md"
        name="platformOrderCode"
        label="平台单号"
        initialValue={data?.platformOrderCode}
      />

    </Form>
  </Modal>
}

export default BundledAlibabaIdModal;
