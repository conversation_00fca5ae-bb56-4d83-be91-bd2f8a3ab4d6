import type { ModalProps } from 'antd';
import {Modal, Space} from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import type { PurchasePlan, PurchasePlanLog } from '@/pages/PurchasePlanList/data';
import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import React, { useEffect, useRef } from 'react';
import { ProTable } from '@ant-design/pro-components';
import {queryGoodsSupplier, queryGoodsSupplierParams} from "@/modules/goods/infra/api/goods";
import {SupplierGoods} from "@/modules/supplier/domain/vender";

// 定义参数格式
export type GoodsSupplierModalProps = {
  goodsSupplierParam: queryGoodsSupplierParams;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const GoodsSupplierListModal = (props: GoodsSupplierModalProps) => {
  const { onFinish, goodsSupplierParam, ...rest } = props;
  const actionRef = useRef<ActionType>();
  const fetchList = usePageQueryRequest((params) => {
    return queryGoodsSupplier({
      ...params,
      goodsSku: goodsSupplierParam.goodsSku
    });
  });
  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [goodsSupplierParam]);

  const columns: ProColumns<SupplierGoods>[] = [
    {
      title: '供应商',
      width: 150,
      align: 'center',
      dataIndex: 'supplierName',
    },
    {
      title: '最小采购量',
      align: 'center',
      dataIndex: 'content',
    },
    {
      title: '采购价',
      align: 'center',
      dataIndex: 'purchasePrice',
    },
    {
      title: '交期',
      align: 'center',
      dataIndex: 'content',
    },
    {
      title: '是否首选',
      align: 'center',
      dataIndex: 'isDefault',
      render: (_, record) => {
        const  options = [
          record.isDefault == "1" ? "是" : "否",
        ]
        return options;
      },
    },
    // {
    //   title: '操作类型',
    //   align: "center",
    //   dataIndex: 'contents',
    //   initialValue: '系统操作',
    // },
    {
      title: '时间',
      align: 'center',
      width: 200,
      dataIndex: 'gmtCreate',
      valueType: 'dateTime',
    },
  ];

  return (
    <Modal {...rest} title="供应商列表" closable={false} width="1000px" height={"800px"} onOk={onFinish}>
      <ProTable<PurchasePlanLog>
        search={false}
        options={false}
        actionRef={actionRef}
        request={fetchList}
        columns={columns}
        rowKey="id"
        bordered
      />
    </Modal>
  );
};

export default GoodsSupplierListModal;
