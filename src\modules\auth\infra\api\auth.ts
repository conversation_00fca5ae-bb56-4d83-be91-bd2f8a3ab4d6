import mallApiConfig from '../../../../../config/mallApiConfig';
import mallRequest from '@/utils/mallRequest';
import type { CurrentUser } from '@/modules/auth/domain/auth';

export async function queryCurrentUser() {
  return mallRequest<API.ApiBaseResult<CurrentUser>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/user/user-biz/getDisplay',
      method: 'GET',
    },
  );
}
