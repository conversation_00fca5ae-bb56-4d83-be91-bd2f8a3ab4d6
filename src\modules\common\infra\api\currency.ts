import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

export interface BaseCurrencyListItem {
  alphabeticCode: string;
  minorUnit: number;
  name: string;
  nameZh: string;
  numericCode: string;
}

// 获取系统所有币种列表
export async function queryAllCurrencyList() {
  return mallRequest<API.ApiBaseResult<BaseCurrencyListItem[]>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/base/currency/queryAll',
    method: 'GET',
  });
}

// 通过AlphabeticCode获取币种信息
export async function queryByAlphabeticCode(alphabeticCode: string) {
  return mallRequest<API.ApiBaseResult<BaseCurrencyListItem>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/base/currency/queryByAlphabeticCode',
    method: 'GET',
    params: {
      alphabeticCode,
    },
  });
}
