@import '~antd/es/style/themes/default.less';

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: #fff;
}

.lang {
  width: 100%;
  height: 40px;
  line-height: 44px;
  text-align: right;

  :global(.ant-dropdown-trigger) {
    margin-right: 24px;
  }
}

.content {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 32px 0;

  .wrapper {
    padding: 40px 40px 20px;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  }
}

@media (min-width: @screen-md-min) {
  .container {
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center 110px;
    background-size: 100%;
  }

  .content {
    padding: 32px 0 24px;
  }
}

.top {
  text-align: center;
}

.header {
  height: 44px;
  line-height: 44px;

  a {
    text-decoration: none;
  }
}

.logo {
  height: 44px;
  margin-right: 16px;
  vertical-align: top;
}

.title {
  position: relative;
  top: 2px;
  color: @heading-color;
  font-weight: 600;
  font-size: 33px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
}

.desc {
  // margin-top: 12px;
  margin-bottom: 20px;
  color: #000;
  font-size: 26px;
}

.main {
  // width: 368px;
  margin: 0 auto;
  padding: 30px 20px;
  background-color: #fff;

  @media screen and (max-width: @screen-sm) {
    width: 95%;
    max-width: 328px;
  }

  :global {
    .@{ant-prefix}-tabs-nav-list {
      margin: auto;
      font-size: 16px;
    }

    .ant-input-affix-wrapper {
      border: 0;
      border-bottom: 1px solid #d9d9d9;
    }

    .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
      border-color: 1px solid #d9d9d9;
      box-shadow: none;
    }

    .ant-input-affix-wrapper:focus,
    .ant-input-affix-wrapper-focused {
      box-shadow: none;
    }

    input:-webkit-autofill {
      background-color: transparent;
      background-image: none;
      -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
      transition: background-color 50000s ease-in-out 0s;
      -webkit-text-fill-color: #000 !important;
    }

    input {
      background-color: transparent;
      caret-color: #000; // 光标颜色
    }
  }

  :global(.ant-pro-form-login-container) {
    background-color: transparent;
  }

  .icon {
    margin-left: 16px;
    color: rgba(0, 0, 0, 0.2);
    font-size: 24px;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: @primary-color;
    }
  }

  .other {
    margin-top: 24px;
    line-height: 22px;
    text-align: left;

    .register {
      float: right;
    }
  }

  .prefixIcon {
    color: @primary-color;
    font-size: @font-size-base;
  }
}
