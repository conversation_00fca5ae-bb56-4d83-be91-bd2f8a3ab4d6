export type PurchaseOrderAudit = {
  "applyPromotionAmount": number,
  "applyRefundAmount": number,
  "gmtCreate": number,
  "gmtModified": number,
  "isUrgent": number,
  "isManual": number,
  "id": string,
  "memo": string,
  "remark": string,
  "merchandiserUsername": string,
  "purchaseWarehouse": string,
  "orderCode": string,
  "trackingNumber": string,
  "orderStatus": string,
  "organizationName": string,
  "auditor": string,
  "auditStatus": string,
  "auditLabel": string,
  "platform": string,
  "payStatus": number,
  "payType": number,
  "platformAccount": string,
  "paymentTime": string,
  "platformOrderAmount": number,
  "platformOrderCode": string,
  "platformOrderTime": number,
  "totalPrice": number,
  "platformPayTime": number,
  "platformStatus": string,
  "platformPurchaseAmount": number,
  "platformShippingFee": number,
  "shippingFee": number,
  "purchasePlanId": string,
  "purchaseTotalAmount": number,
  "purchaseUsername": string,
  "supplierId": string,
  "supplierName": string,
  "title": string,
  "purchaseOrderGoodsList": PurchaseOrderGoods[],
  "purchaseOrderAuditRecordInfoList": PurchaseOrderAuditRecord[]
}

export type PurchaseOrderAuditRecord = {
  "id": string,
  "purchaseOrderId": string,
  "applyUid": string,
  "applyUsername": string,
  "auditUid": string,
  "auditUsername": string,
  "auditType": string,
  "auditStatus": string,
  "auditData": string,
  "remark": string,
  "gmtCreate": string,
  "gmtModified": string
}
