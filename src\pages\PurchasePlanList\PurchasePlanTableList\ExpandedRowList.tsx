import React, { useState } from 'react';
import { Form, Image, Modal, notification, Select, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import type { PlanGoodsInfo, PurchasePlanListItem } from './data';
import SelfImage from "@/components/SelfImage";
import { GoodsStatusEnum } from "@/modules/goods/domain/goods";
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  ClearOutlined,
  FormOutlined,
  SnippetsTwoTone,
  TagOutlined,
  BookOutlined,
  ThunderboltOutlined,
  VerifiedOutlined,
  AuditOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { PlanGoodsStatus, PlanGoodsText } from '@/modules/purchasePlan/domain/purchasePlan';
import {
  markPlanGoodsIsManual,
  markPlanGoodsIsUrgent,
  markPlanGoodsNeedQc,
  updatePlanStatus,
  updateSkuPrice,
  updateSkuQuantity,
} from '@/modules/purchasePlan/infra/api/purchasePlan';
import type { ActionType } from '@ant-design/pro-table';
import { ProForm<PERSON>ield, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import moment from "moment";
import PurchasePlanLogModal from "@/pages/PurchasePlanList/components/PurchasePlanLogModal";
import PurchaseDetailListModal from "@/pages/PurchasePlanList/components/PurchaseDetailListModal";
import PurchaseAdviceModal from "@/pages/PurchasePlanList/components/PurchaseAdviceModal";
import { Access } from "@@/plugin-access/access";
import { copyText } from "@/utils/comUtil";
import $ from 'jquery';
import { Link, history } from "umi";

type ExpandedRowListProps = {
  record: PurchasePlanListItem;
  actionRef: ActionType;
};

export default (props: ExpandedRowListProps) => {
  const { record: dataSource, actionRef } = props;
  const [PlanLogModal, setPlanLogModal] = useState<boolean>(false);
  const [purchaseDetailListModal, setPurchaseDetailListModal] = useState<boolean>(false);
  const [searchPlanLogParams, setSearchPlanLogParams] = useState<any>();
  const [searchPurchaseOrderParams, setSearchPurchaseOrderParams] = useState<any>();
  const [purchaseAdviceModal, setPurchaseAdviceModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * 根据PlanGoodsInfo获取ids
   * @param record
   */
  const loadIds = (record: PlanGoodsInfo) => {
    return record.warehouseInfos.reduce((ids: number[], item) => {
      ids.push(item.id);
      return [...ids];
    }, []);
  };

  /**
   * 修改采购计划需求数量
   * @param record
   */
  const [form] = Form.useForm();
  const changePurchaseQuantityPrice = (record: PlanGoodsInfo, field: string) => {
    Modal.confirm({
      icon: false,
      className: 'globalEnterKeySubmit',
      content: (
        <Form form={form}>
          {field == 'price' ? (
            <ProFormText label="价格" name="price" initialValue={record.purchasePrice} />
          ) : null}
          {field == 'quantity' ? (
            <ProFormText label="数量" name="quantity" initialValue={record.purchaseQuantity} />
          ) : null}
        </Form>
      ),
      onOk: function () {
        const params = {
          price: form.getFieldValue('price'),
          purchaseQuantity: form.getFieldValue('quantity'),
          purchasePlanId: record.purchasePlanId,
          sku: record.sku,
        };
        if (field == 'price') {
          updateSkuPrice(params).then((result) => {
            if (result.status.success) {
              notification.success({ message: '价格修改成功！' });
              record.purchasePrice = Number(params.price);
              $(".curPurchasePrice" + record.id).text(Number(params.price));
              //计算修改后的总价格
              var priceList = $(".planSupplierTotalAmount" + record.supplierName).closest("tr").find(".PurchasePlanPrice");
              var totalAmount = 0;
              priceList.each(function (k, v) {
                var num = Number($(v).closest(".priceQuantityGroup").find(".PurchasePlanQuantity").text());
                console.log(Number($(v).text()) * num)
                totalAmount += Number($(v).text()) * num;
              })
              $(".planSupplierTotalAmount" + record.supplierName).text(totalAmount?.toFixed(2));
            }
          });
        } else {
          updateSkuQuantity(params).then((result) => {
            if (result.status.success) {
              notification.success({ message: '数量修改成功！' });
              record.purchaseQuantity = Number(params.purchaseQuantity);
              $(".curPurchaseQuantity" + record.id).text(Number(params.purchaseQuantity));
              //计算修改后的总价格
              var priceList = $(".planSupplierTotalAmount" + record.supplierName).closest("tr").find(".PurchasePlanPrice");
              var totalAmount = 0;
              priceList.each(function (k, v) {
                var num = Number($(v).closest(".priceQuantityGroup").find(".PurchasePlanQuantity").text());
                console.log(Number($(v).text()) * num)
                totalAmount += Number($(v).text()) * num;
              })
              $(".planSupplierTotalAmount" + record.supplierName).text(totalAmount?.toFixed(2));
            }
          });
        }
      },
    });
  };

  /**
   * 作废删除采购计划sku
   * 获取当前sku的采购计划商品ids
   * @param record
   */
  const cancelPlanSku = (record: PlanGoodsInfo) => {
    Modal.confirm({
      icon: null,
      content: (
        <Form form={form}>
          <ProFormField
            label="原因"
            name="remark"
          >
            <Select placeholder={""}
              options={[
                { value: "一单多量引发备货", label: '一单多量引发备货' },
                { value: "积压库存刚消耗完", label: '积压库存刚消耗完' },
                { value: "有库存够需求", label: '有库存够需求' },
                { value: "有在途够需求", label: '有在途够需求' },
                { value: "销量差无缺货", label: '销量差无缺货' },
                { value: "货值低无缺货", label: '货值低无缺货' },
                { value: "季节性产品", label: '季节性产品' },
                { value: "待整合备货", label: '待整合备货' },
                { value: "重复提需求", label: '重复提需求' },
                { value: "其余原因", label: '其余原因' },
              ]}
            />
          </ProFormField>
        </Form>
      ),
      onOk: async () => {
        const param = {
          ids: loadIds(record),
          status: PlanGoodsStatus.ABOLISH,
          remark: form.getFieldValue('remark'),
        };

        updatePlanStatus(param).then((result) => {
          if (result.status.success) {
            notification.success({ message: '操作成功' });
            actionRef.current?.reload?.();
          }
        });

        form.resetFields();
      },
    });
  };


  /**
   * 是否手工单
   * @param record
   */
  const markIsManual = (record: PlanGoodsInfo, isManual: boolean) => {
    const param = {
      isManual: isManual ? 1 : 0,
      purchasePlanId: record.purchasePlanId,
      sku: record.sku,
    };
    setLoading(true);
    markPlanGoodsIsManual(param).then((result) => {
      setLoading(false);
      if (result.status.success) {
        notification.success({ message: '操作成功' });
        record.isManual = Boolean(param.isManual);
        $(".curPlanManualTags" + record.id).css("color", isManual ? "red" : "#000")
      }
    });
  };
  /**
   * 是否加急
   * @param record
   */
  const markisUrgent = (record: PlanGoodsInfo, isUrgent: boolean) => {
    const param = {
      isUrgent: isUrgent ? 1 : 0,
      purchasePlanId: record.purchasePlanId,
      sku: record.sku,
    };
    setLoading(true);
    markPlanGoodsIsUrgent(param).then((result) => {
      setLoading(false);
      if (result.status.success) {
        notification.success({ message: '操作成功' });
        record.isUrgent = Boolean(param.isUrgent);
        $(".curPlanUrgentTags" + record.id).css("color", isUrgent ? "red" : "#000")
      }
    });
  };
    /**
   * 是否需要质检
   * @param record
   */
  const markNeedQc = (record: PlanGoodsInfo, needQc: boolean) => {
    const param = {
      needQc: needQc ? 1 : 0,
      purchasePlanId: record.purchasePlanId,
      sku: record.sku,
    };
    setLoading(true);
    markPlanGoodsNeedQc(param).then((result) => {
      setLoading(false);
      if (result.status.success) {
        notification.success({ message: '操作成功' });
        record.needQc = Boolean(param.needQc);
        $(".curPlanQcTags" + record.id).css("color", needQc ? "red" : "#000")
      }
    });
  };
  const columns: ColumnsType<PlanGoodsInfo> = [
    {
      dataIndex: 'price',
      width: 10,
      render: (v, record) => {
        return (
          <input type={"checkbox"} className={"planSupplierCheckBox planGoodsItemCheck"} style={{ float: "left" }} value={record.id} />
        );
      },
    },
    {
      dataIndex: 'images',
      render: (v, record) => {
        return (
          <>
            <div>
              <SelfImage src={record?.link} title={record?.title} width={80} />
            </div>
            <div>
              <span style={{ cursor: 'pointer', color: '#1890ff' }} onClick={() => history.push({ pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })}>{record.sku}</span>
              <SnippetsTwoTone onClick={() => copyText(record.sku)} /></div>
          </>
        );
      },
    },
    {
      dataIndex: 'images',
      width: 400,
      render: (v, record) => {
        return (
          <>
            <div style={{ fontSize: 13 }}>
              <b>标题：{record.title?.match("手工单") ? <span style={{ color: "red", fontWeight: "bold" }}>{record.title}</span> : record.title}</b>&nbsp;
              {record.isUrgent ? <Tag color="red" style={{ fontSize: 13 }}>加急</Tag> : ''}
              <ClearOutlined style={{ color: 'red' }} onClick={() => cancelPlanSku(record)} />
            </div>
            <div style={{ fontSize: 13 }}><b>备注：{record.goodsRemark}</b>
              {record?.purchaseEntity ? (<span style={{ fontSize: 12, color: "#FFA488", fontWeight: "bold" }}>&nbsp;【{record?.purchaseEntity}】</span>) : null}
              {record.isReject ? (<span style={{ fontSize: 12, color: "#FF3333 ", fontWeight: "bold" }}>&nbsp;【驳回】</span>) : null}
            </div>
            <div style={{ fontSize: 13 }}>
              采购员：{record.purchaseUsername}
            </div>
            {record.status != null ? (<div style={{ fontSize: 13 }}>状态：{PlanGoodsText[record.status]}</div>) : null}
            {record.auditor != null ? (<div style={{ fontSize: 13 }}>审核人：{record.auditor}</div>) : null}
            {record.auditStatus != null ? (<div style={{ fontSize: 13 }}>审核状态：{record.auditStatus}</div>) : null}
            {record.auditLabel != null ? (<div style={{ fontSize: 13 }}>审核步骤：{record.auditLabel}</div>) : null}
            <div style={{ fontSize: 13 }}>
              创建时间：{moment(Number(record.gmtCreate)).format("YYYY-MM-DD HH:mm:ss")}
            </div>
          </>
        );
      },
    },
    {
      dataIndex: 'images',
      width: 250,
      render: (v, record) => {
        return (
          <>
            <div style={{ fontSize: 13 }}>上次采购价：{record?.lastPurchasePrice?.toFixed(2)}</div>
            <div className={"priceQuantityGroup"}>
              <div style={{ fontSize: 13 }}>
                当前采购价：
                {record.lastPurchasePrice == null ? (
                  ''
                ) : record.lastPurchasePrice > record.purchasePrice ? (
                  <ArrowDownOutlined style={{ color: 'green' }} />
                ) : record.lastPurchasePrice < record.purchasePrice ? (
                  <ArrowUpOutlined style={{ color: 'red' }} />
                ) : (
                  ''
                )}
                &nbsp;
                <span className={"curPurchasePrice" + record.id + " PurchasePlanPrice"}>{record.purchasePrice?.toFixed(2)}</span>{' '}&nbsp;
                <FormOutlined
                  style={{ color: 'blue' }}
                  onClick={(e) => changePurchaseQuantityPrice(record, 'price')}
                />
              </div>
              <div style={{ fontSize: 13 }}>
                数量：<span style={{ fontWeight: "bold" }} className={"curPurchaseQuantity" + record.id + " PurchasePlanQuantity"}>{record.purchaseQuantity || 0}</span> &nbsp;
                <FormOutlined
                  style={{ color: 'blue' }}
                  onClick={() => changePurchaseQuantityPrice(record, 'quantity')}
                />
              </div>
            </div>
            <div style={{ fontSize: 13 }}>
              备货建议：
              <a key={record.id} onClick={function () {
                const param = {
                  sku: record.sku,
                  createTime: record.gmtCreate
                }
                setPurchaseAdviceModal(true)
                setSearchPurchaseOrderParams(param);
              }
              }><BookOutlined /></a>
            </div>
            <div style={{ fontSize: 13 }}>
              标记手工单：{' '}
              <TagOutlined className={"curPlanManualTags" + record.id}
                style={record.isManual ? { color: 'red' } : {}}
                onClick={() => markIsManual(record, !record.isManual)}
              />
            </div>
            <div style={{ fontSize: 13 }}>
              是否加急：{' '}
              <ThunderboltOutlined className={"curPlanUrgentTags" + record.id}
                style={record.isUrgent ? { color: 'red' } : {}}
                onClick={() => markisUrgent(record, !record.isUrgent)}
              />
            </div>
            <div style={{ fontSize: 13 }}>
              是否需质检：{' '}
              <AuditOutlined className={"curPlanQcTags" + record.id}
                style={record.needQc ? { color: 'red' } : {}}
                onClick={() => markNeedQc(record, !record.needQc)}
              />
            </div>
            <div style={{ fontSize: 13 }}>
              操作日志：
              <a key={record.id} onClick={function () {
                const param = {
                  planId: record.purchasePlanId,
                  planGoodsId: record.id
                }
                setPlanLogModal(true)
                setSearchPlanLogParams(param);
              }
              }><BookOutlined /></a>
            </div>
            <div style={{ fontSize: 13 }}>
              采购明细：
              <a key={record.id} onClick={function () {
                const param = {
                  sku: record.sku,
                }
                setPurchaseDetailListModal(true)
                setSearchPurchaseOrderParams(param);
              }
              }><BookOutlined /></a>
            </div>
          </>
        );
      },
    },
    {
      dataIndex: 'warehouse',
      render: (v, record) => {
        let body = "";
        const skuWarehouseList = record.skuWarehouseList;
        for (let i in skuWarehouseList) {
          body += "<tr style=\"font-size:13px\">\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].warehouseName + "</td>\n" +
            "                    <td align=\"center\">" + GoodsStatusEnum[skuWarehouseList[i].goodsStatus] + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].availableStockQuantity + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].intransitStockQuantity + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].purchaseIntransit + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].newSysPcIntransit + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].salesSevenDays + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].salesFifteenDays + "</td>\n" +
            "                    <td align=\"center\">" + skuWarehouseList[i].salesThirtyDays + "</td>\n" +
            "                    <td align=\"center\"><span " + (skuWarehouseList[i].outOfStock > 0 ? "style='color:red;font-weight:bold;'" : null) + ">" + skuWarehouseList[i].outOfStock + "</span></td>\n" +
            "                  </tr>"
        }

        return (
          <>
            <div className="showSummary" data-rel="209685">
              {' '}
              <table width="100%" >
                <thead>
                  <tr style={{ fontSize: 13 }}>
                    <th align={"center"}>仓库</th>
                    <th align={"center"}>生命周期</th>
                    <th align={"center"}>通途可用库存</th>
                    <th align={"center"}>通途在途库存</th>
                    <th align={"center"}>采购/转仓</th>
                    <th align={"center"}>新系统在途</th>
                    <th align={"center"}>通途近7天销量</th>
                    <th align={"center"}>通途15天销量</th>
                    <th align={"center"}>通途30天销量</th>
                    <th align={"center"}>通途未配货</th>
                  </tr>
                </thead>
                <tbody dangerouslySetInnerHTML={{ __html: body }}>
                </tbody>
              </table>
            </div>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Table<PlanGoodsInfo>
        style={{ margin: 0, padding: 0, width: "100%" }}
        size="small"
        columns={columns}
        dataSource={dataSource.planGoodsInfos}
        showHeader={false}
        bordered={false}
        pagination={false}
        loading={loading}
      />
      <PurchasePlanLogModal visible={PlanLogModal} onCancel={() => setPlanLogModal(false)} searchPlanLogParams={searchPlanLogParams} onFinish={function () { setPlanLogModal(false) }} />
      <PurchaseDetailListModal visible={purchaseDetailListModal} onCancel={() => setPurchaseDetailListModal(false)} data={searchPurchaseOrderParams} onFinish={function () { setPurchaseDetailListModal(false) }} />
      <Access accessible={purchaseAdviceModal}>
        <PurchaseAdviceModal visible={purchaseAdviceModal} onCancel={() => setPurchaseAdviceModal(false)} planData={searchPurchaseOrderParams} onFinish={function () { setPurchaseAdviceModal(false) }} />
      </Access>
    </>
  );
};
