import { Department, DepartmentTree } from '@/modules/user-center/domain/department';
import { CreateDepartmentParams, getDeptTreeByEndpoint, update, UpdateDeptParams } from '@/modules/user-center/infra/organize';
import { createDepartment } from '@/modules/user-center/infra/organize';
import { GlobalWindow } from '@/types/global';
import { ModalFormProps, ProFormSelect, ProFormTextArea, ProFormTreeSelect } from '@ant-design/pro-form';
import { ProFormDependency, ProFormDigit } from '@ant-design/pro-form';
import { ModalForm, ProFormRadio, ProFormText } from '@ant-design/pro-form';
import { useControllableValue } from 'ahooks';
import { Form } from 'antd';
import { omit } from 'lodash';
import React, { useEffect, useState } from 'react';

type CreateFormProps = Pick<ModalFormProps, 'visible' | 'onVisibleChange' | 'trigger' | 'onFinish' | 'title'> & {
  parentId?: string | number;
  record?: Department;
};

const formattedDepartTreeData = (data: DepartmentTree[] = []): any[] => {
  if (!data.length) {
    return []
  }
  return data.map(item => {
    const children = formattedDepartTreeData(item.children || [])
    return {
      title: item.name,
      value: item.deptId,
      children,
    }
  })
}

const CreateOrUpdateDepartmentModal: React.FC<CreateFormProps> = (props) => {
  const [form] = Form.useForm();
  const { parentId, onFinish, record, ...rest } = props;
  const [visible, onVisibleChange] = useState<boolean>(props.visible || false);

  useEffect(() => {
    onVisibleChange(props?.visible ||  false)
  }, [props.visible])
  useEffect(()  => {
    props.onVisibleChange?.(visible);
  }, [visible])

  useEffect(() => {
    if (record || parentId) {
      let isChild = false;
      let currentParentId = parentId;
      if (record && record?.parentId !== '0') {
        currentParentId = record.parentId;
        isChild = true;
      }
      if (currentParentId) {
        isChild = true;
      }
      form.setFieldsValue({
        ...record,
        parentId: currentParentId,
        isChild: isChild,
        sort: record?.sequence
      });
    }
  }, [parentId, record, visible])

  const onSubmit = async (value: UpdateDeptParams) => {
    let res: API.ApiBaseResult<any>;
    if (value.deptId) {
      res = await update({
        ...omit(value, ['isChild']),
        parentId: value.parentId || 0,
      } as any)
    } else {
      res = await createDepartment({
        ...omit(value, ['isChild']),
        parentId: value.parentId || 0,
      });
    }
    if (res.status.success) {
      form.resetFields();
      await onFinish?.(value);
    }
    return res.status.success;
  }

  return (
    <ModalForm
      width={600}
      form={form}
      {...rest}
      onFinish={onSubmit}
      open={visible}
      onOpenChange={(v) => {
        if(!v){
          form.resetFields();
        }
        onVisibleChange(v);
      }}
      initialValues={{
        isChild: false,
        state: 'ACTIVATED'
      }}
    >
      <ProFormText
        label="部门名称"
        name="name"
        placeholder="请输入部门名称"
        rules={[{ required: true, message: '请输入部门名称' }]}
      />
      <ProFormTextArea
        label="部门描述"
        name="description"
        placeholder="请输入部门描述"
        fieldProps={{
          maxLength: 200,
          showCount: true,
        }}
      />
      <ProFormRadio.Group
        label="是否子部门"
        name="isChild"
        options={[
          { label: '否', value: false },
          { label: '是', value: true },
        ]}
      />
      <ProFormDependency name={['isChild']}>
        {({ isChild }) => {
          return isChild && <ProFormTreeSelect
            label="上级部门"
            name="parentId"
            rules={[{ required: true, message: '请选择上级部门' }]}
            request={() => {
              return getDeptTreeByEndpoint({
                endpoint: (window as GlobalWindow).endpoint as string,
              }).then(res => {
                return formattedDepartTreeData(res.body || [])
              })
            }}
            fieldProps={{
              treeDefaultExpandAll: true,
              treeLine: true,
            }}
          />
        }}
      </ProFormDependency>
      <ProFormRadio.Group
        label="部门状态"
        name="state"
        options={[
          { label: '启用', value: 'ACTIVATED' },
          { label: '禁用', value: 'DISABLED' },
        ]}
      />
      <ProFormText hidden name='deptId'></ProFormText>
      <ProFormText label="部门编码" name="code" />
      <ProFormDigit label="部门排序" name="sort" initialValue={1} fieldProps={{ precision: 0 }} />
    </ModalForm>
  );
};

export default CreateOrUpdateDepartmentModal;
