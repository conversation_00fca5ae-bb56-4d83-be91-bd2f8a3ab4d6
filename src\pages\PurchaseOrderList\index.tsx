import React, { useEffect, useState } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import { <PERSON><PERSON>, Badge, Button, Checkbox, DatePicker, Form, message, Modal, notification, Select, Space, Tag } from "antd";
import CustomPage from "@/components/CustomPage";
import { useRequestTable } from "@/hooks/useRequestTable";
import aliwangwang from "@/assets/images/aliwangwang.gif";
import { Link, history } from 'umi';
import { copyText } from "@/utils/comUtil";
import SelectVender from '@/components/Select/SelectVender';
import {
  AliCreateOrder,
  AssigningUsers, auditCopyOrder,
  batchApplyPayment,
  batchUpdateOrderStatus,
  copyPurchaseOrder,
  getTradeOrder, nonWaitResidue,
  pageQueryOrder, periodCheckRequest,
  publicFlagState,
  purchaseDataCount, updateOrder, updateOrderMemo,
  updateOrderStatus,
  updateOrderTitle,
  exportOrderAsync,
} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import {
  BankOutlined, ClockCircleOutlined, ClockCircleTwoTone, ContactsOutlined,
  ExclamationCircleOutlined, FlagOutlined, PaperClipOutlined, PushpinOutlined,
  RocketOutlined,
  SnippetsTwoTone,
  UserOutlined
} from "@ant-design/icons";
import moment from "moment";
import AllotMerchandiserModal from "@/pages/PurchaseOrderList/components/AllotMerchandiserModal";
import {
  OrderStatusEnum, OrderStatusTagsEnum,
  payStatusEnum,
  PlatformStatusEnum,
  PurchaseAuditStatusEnum
} from "@/modules/purchaseOrder/domain/purchaseOrder";
import AllotPurchaseModal from "@/pages/PurchaseOrderList/components/AllotPurchaseModal";
import Filters from './components/Filters';
import { ProForm, ProFormCheckbox, ProFormText, ProFormDigit } from '@ant-design/pro-components';
import { ProFormField, ProFormSelect, ProFormTextArea } from "@ant-design/pro-form";
import PurchaseAnalysisModal from "@/pages/PurchaseOrderList/components/PurchaseAnalysisModal";
import { useRequest } from "ahooks";
import { SettleCircleTypeMap, SettleTypeMap } from "@/modules/supplier/domain/vender";
import { useHistoryRouterContext } from "@/components/BasicLayout/hooks/useHistoryRouter";
import { LogisticsTraceList } from "@/pages/PurchaseOrderList/components/LogisticsTraceModal";
import $ from 'jquery'
import Permission from "@/components/Permission";
import EditOrderModal from "@/pages/PurchaseOrderList/components/EditOrderModal";
import AdvancedPayment from './components/AdvancedPayment';
import AccountSettlement from './components/AccountSettlement';
import { FormOutlined } from "@ant-design/icons/lib";
const RangePicker = DatePicker.RangePicker as any;
const dataCount = async (type: string) => {
  const param = {
    "type": type,
  }
  const data = await purchaseDataCount(param);
  return data;
}

const TableList: React.FC = () => {
  const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalVisible1, setModalVisible1] = useState<boolean>(false);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [paymentVisible, setPaymentVisible] = useState<boolean>(false);
  const [accountSettlementVisible, setAccountSettlementVisible] = useState<boolean>(false);
  const [submitRefreshLoading, setSubmitRefreshLoading] = useState<boolean>(false);
  const [purchaseAnalysisModal, setPurchaseAnalysisModal] = useState<boolean>(false);
  const [editOrderVisible, setEditOrderVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<PurchaseOrder>();
  const [selectedRowsState, setSelectedRows] = useState<PurchaseOrder[]>([]);
  const [amountStore, setAmountStore] = useState<any>();
  const [queryParams, setQueryParams] = useState<any>({});
  const historyRouter = useHistoryRouterContext();

  const { fetchList, actionRef } = useRequestTable((params) => {
    setQueryParams(params);
    return pageQueryOrder({
      ...params,
    });
  });
  const { data, refresh } = useRequest(() => dataCount("checkPrice,auditReject,payReject,finance,purchaseOrder,periodStatus").then((res) => res.body));

  const AssigningPersonInCharge = (values: any) => {
    const dataList = selectedRowsState;

    if (dataList.length <= 0) {
      message.error("请选择需要分配的采购单!")
      setModalVisible(false);
      return null;
    }

    const ids: string[] = [];

    dataList.map(item => {
      ids.push(item.id);
    })

    const obj = {
      "PurchaseOrderIds": ids,
      "merchandiserName": values.merchandiserUsername != undefined ? values.merchandiserUsername : undefined,
      "purchaseUsername": values.purchaseUsername != undefined ? values.purchaseUsername : undefined,
    }

    AssigningUsers(obj).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
        message.success("分配成功")
      }
    });

    setModalVisible1(false);
    setModalVisible(false);
  }

  //1688下单
  const createAlibabaOrder = (values: PurchaseOrder[]) => {
    if (values.length <= 0) {
      message.error("请选择需要刷新的采购单!");
      return null;
    }

    const ids: string[] = [];

    values.map(item => {
      ids.push(item.id);
    })

    const obj = {
      "orderIds": ids
    }

    Modal.confirm({
      title: '确认下单吗?',
      content: (
        <Form form={form}>
          <Space>
            <ProFormCheckbox tooltip={"比价最优策略下单,将会以比价后的最优配置进行拆单"} name="isComparePrices" label="比价最优策略下单" />
          </Space>
        </Form>
      ),
      onOk: async () => {
        setSubmitLoading(true);
        AliCreateOrder(obj).then((res) => {
          setSubmitLoading(false);
          if (res.status.success) {
            actionRef.current?.reloadAndRest?.();
            const body = res.body;
            const number = body.indexOf("下单失败数量：");
            const errorNum = body.substring(number + 7, body.indexOf("<br>"));
            if (Number(errorNum) > 0) {
              notification.error({
                duration: null,
                placement: "top",
                description: <Space direction="vertical" size="small">{body.split("<br>")}</Space>,
                message: "异常订单",
                style: {
                  width: "auto"
                }
              });
            } else {
              notification.success({
                placement: "top",
                description: <Space direction="vertical" size="small">{body.split("<br>")}</Space>,
                message: "下单成功",
                style: {
                  width: "auto"
                }
              });
            }
          }
        });
      },
    });
  }

  const nonWaitResidueButton = (data: PurchaseOrder) => {
    nonWaitResidue({ id: data.id, orderStatus: 55 }).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
        message.success("操作成功")
      }
    });
  }

  /**
   * 账期确认
   * @param record
   */
  const periodCheck = (record: PurchaseOrder) => {
    periodCheckRequest({ id: record.id }).then((result) => {
      if (result.status.success) {
        window.open(result?.body);
      } else {
        message.error("异常：" + result.status.message);
      }
    });
  }

  //复制采购单
  const copyOrder = (orderId: string) => {
    Modal.confirm({
      title: '确认复制此订单吗?',
      onOk: async () => {
        copyPurchaseOrder(orderId).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            message.success(result.body)
          }
        });
      },
    });
  }

  //采购单修改状态
  const [form] = Form.useForm();
  const updateStatus = (orderId: string, orderStatus: number, payStatus: number) => {
    const obj = {
      id: orderId,
      orderStatus: orderStatus,
      payStatus: payStatus
    }

    Modal.confirm({
      title: '确认此操作吗?',
      content: (
        <>
          <Form form={form}>
            <ProFormField
              label="原因"
              name="remark"
            >
              <Select placeholder={"请选择"}
                options={[
                  { value: "供应已停产", label: '供应已停产' },
                  { value: "供应货期长", label: '供应货期长' },
                  { value: "起订数量高", label: '起订数量高' },
                  { value: "供应已放假", label: '供应已放假' },
                  { value: "店铺已失效", label: '店铺已失效' },
                  { value: "价格涨幅大", label: '价格涨幅大' },
                  { value: "其余原因", label: '其余原因' },
                ]}
              />
            </ProFormField>
          </Form>
        </>
      ),
      onOk: async () => {
        obj["remark"] = form.getFieldValue('remark');
        //驳回添加备注
        if (orderStatus == 70 && (obj["remark"] == "" || obj["remark"] == null)) {
          notification.error({ message: '请填写驳回备注' });
        } else {
          updateOrderStatus(obj).then((result) => {
            if (result.status.success) {
              actionRef.current?.reloadAndRest?.();
              message.success("修改成功")
            }
          });
        }
      }
    });
    form.resetFields();
  }

  //复制采购单审核
  const orderCopyAudit = (orderId: string) => {
    Modal.confirm({
      title: '确认复制此订单吗?',
      content: (
        <Form labelCol={{ flex: '120px' }} form={form}>
          <ProFormSelect
            name="status"
            label="是否通过"
            options={[
              { label: '通过', value: "pass" },
              { label: '驳回', value: "reject" }
            ]}
          />
          <ProFormText label="备注" name="remark" />
        </Form>
      ),
      onOk: async () => {
        const params = {
          id: orderId,
          copyOrderStatus: form.getFieldValue("status"),
          remark: form.getFieldValue('remark')
        };

        auditCopyOrder(params).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            message.success(result.body)
          }
        });
      },
    });
    form.resetFields();
  }

  //申请付款
  const applyPay = (values: PurchaseOrder[]) => {
    if (values.length <= 0) {
      message.error("请选择需要申请付款的采购单!");
      return null;
    }

    const ids: string[] = [];

    values.map(item => {
      ids.push(item.id);
    })

    const param = {
      "orderIds": ids
    }

    Modal.confirm({
      title: '批量申请付款',
      icon: false,
      className: 'globalEnterKeySubmit',
      content: (
        <Form form={form}>
          <ProFormSelect
            style={{ marginTop: 10 }}
            width="md"
            name="payStatus"
            label="申请类型"
            options={[
              { label: '申请付余额', value: "20" },
              { label: '申请付定金', value: "10" },
              { label: '无需付款', value: "-10" }
            ]}
          />
          <ProFormSelect
            width="md"
            name="payType"
            label="支付方式"
            options={[
              { label: '跨境宝', value: "5" },
              { label: '银行转账', value: "1" },
              { label: '现金支付', value: "2" },
              { label: '支付宝', value: "3" },
              { label: '余额抵充', value: "4" },
              { label: '超级支付宝', value: "6" }
            ]}
          />
          <ProFormCheckbox
            width="md"
            name="platformShppingFeeCheck"
            label="平台运费校验"
            initialValue={true}
            tooltip={"平台运费高于100块时提示异常，取消勾选则不限制"}
          />
        </Form>
      ),
      onOk: async () => {
        param["payStatus"] = form.getFieldValue('payStatus');
        param["payType"] = form.getFieldValue('payType');
        param["platformShppingFeeCheck"] = form.getFieldValue('platformShppingFeeCheck');

        //驳回添加备注
        batchApplyPayment(param).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            message.success("申请成功")
          }
        });

        form.resetFields();
      },
    });

  }
  // 高级申请付款
  const adcancedPayment = () => {
    setPaymentVisible(true)
  }
  // 申请账期结算
  const accountSettlement = () => {
    setAccountSettlementVisible(true)
  }
  //采购单修改状态
  const batchUpdateStatus = (orders: PurchaseOrder[], status: number) => {
    if (orders.length <= 0) {
      message.error("请选择采购单!");
      return null;
    }

    const obj = {
      ids: orders.map(item => item.id),
      orderStatus: status,
    }

    Modal.confirm({
      title: '确认修改吗?',
      onOk: async () => {
        batchUpdateOrderStatus(obj).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            message.success("修改成功")
          }
        });
      },
    });
  }

  //刷新1688订单
  const refreshAlibabaOrder = (values: PurchaseOrder[]) => {
    if (values.length <= 0) {
      message.error("请选择需要刷新的采购单!")
      return null;
    }

    const array: string[] = [];
    let errorMessage = "";

    values.map((item) => {
      if (item.platformOrderCode == null) {
        errorMessage += "采购单" + item.orderCode + "不存在1688订单号!<br>";
      } else {
        array.push(item.id);
      }
    })

    if (errorMessage != "") {
      notification.error({
        duration: null,
        placement: "top",
        description: <Space direction="vertical" size="small">{errorMessage.split("<br>")}</Space>,
        message: "采购单校验",
        style: {
          width: "auto"
        }
      });
      return null;
    }
    setSubmitRefreshLoading(true);
    getTradeOrder(array).then((result) => {
      setSubmitRefreshLoading(false);
      if (result.status.success) {
        actionRef.current?.reload?.();
        message.success("刷新成功")
      } else {
        notification.error({
          duration: null,
          placement: "top",
          description: <Space direction="vertical" size="small">{result.body.split("<br>")}</Space>,
          message: "异常订单",
          style: {
            width: "auto"
          }
        });
      }
    });
  }

  /**
   * 修改采购计划需求数量
   * @param record
   */
  const changeOrderMemoOrRemark = (record: PurchaseOrder) => {
    Modal.confirm({
      icon: false,
      width: "35%",
      content: (
        <Form form={form}>
          <ProFormText label="标题" name="title" initialValue={record.title} />
        </Form>
      ),
      onOk: function () {
        const params = {
          title: form.getFieldValue('title'),
          id: record.id,
        };

        updateOrderTitle(params).then((result) => {
          if (result.status.success) {
            $('.title' + record?.id).text(form.getFieldValue('title'));
            // actionRef.current?.reloadAndRest?.();
            notification.success({ message: '修改成功' });
          }
        });
      },
    });
    form.resetFields();
  };

  //物流轨迹弹框
  const showLogistics = (record: PurchaseOrder) => {
    Modal.confirm({
      icon: false,
      centered: true,
      width: '60%',
      content: (
        <LogisticsTraceList orderId={record?.orderId} trackingNumber={record?.trackingNumber} />
      )
    });
  }

  //标记核对
  const markOrderCheck = (record: any) => {
    const isCheck = $(".checkTags" + record.id).attr("attrIsCheck");
    publicFlagState({ isPayment: isCheck == 1 ? 0 : 1, orderId: record.id }).then((result) => {
      if (result.status.success) {
        if (isCheck == 1) {
          $(".checkTags" + record.id).css("background-color", "#DDDDDD").css("color", "#fff").css("border-color", "#DDDDDD").text("待核对")
          $(".checkTags" + record.id).attr("attrIsCheck", 0);

          $(".checkTagsFlag" + record.id).css("color", "#DDDDDD").css("border-color", "#DDDDDD")
          $(".checkTagsFlag" + record.id).attr("attrIsCheck", 0);

        } else {
          $(".checkTags" + record.id).css("background-color", "#00AA00").text("已核对");
          $(".checkTags" + record.id).attr("attrIsCheck", 1);

          $(".checkTagsFlag" + record.id).css("color", "#00AA00").css("border-color", "#00AA00")
          $(".checkTagsFlag" + record.id).attr("attrIsCheck", 1);
        }
        // actionRef.current?.reload();
      }
    })
  }
  // 导出
  const onExport = () => {
    Modal.confirm({
      icon: '',
      width: 500,
      content: '确认导出采购单订单列表？',
      onOk: async () => {
        const res = await exportOrderAsync(queryParams)
        Modal.confirm({
          icon: '',
          width: 500,
          content: <div>{`文件导出${res?.status?.success ? '成功' : '失败'}`},请前往【系统-设置-任务中心】菜单查看导出详情</div>,
          okText: '立即前往',
          onOk: () => history.push(`/setting/setting/taskList#'EXPORT`)
        });
      },
    });
  }

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '单号',
      dataIndex: 'orderNo',
      hideInTable: true,
      colSize: (6 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [numberKey, numberValue] = value;
          const numberValues = numberValue?.trim()?.split(/[,，\s]/)?.map((v: string) => v?.trim())
          if (numberKey === 'orderCode' || numberKey === 'trackingNumber' || numberKey === 'platformOrderCode') {
            return {
              numberKey,
              numberValues: numberValue?.trim() ? numberValues : []
            }
          }
          return {
            numberKey,
            numberValue
          }
        },
      },
      renderFormItem: (_, __, tableForm) => {
        const numberKey = tableForm?.getFieldValue('orderNo');
        return <Filters.SelectInput options={
          [
            {
              value: 'defaultNumber',
              label: '采购/平台/快递单号',
            }, {
              value: 'orderCode',
              label: '采购单号',
            }, {
              value: 'trackingNumber',
              label: '快递单号',
            }, {
              value: 'platformOrderCode',
              label: '平台单号',
            }, {
              value: 'purchaseEntity',
              label: '采购主体',
            }, {
              value: 'title',
              label: '标题',
            },
          ]
        }
          defaultValue={'defaultNumber'}
          placeholder={numberKey?.length && ['orderCode', 'trackingNumber', 'platformOrderCode'].includes(numberKey[0]) ? '可输入多个单号查询，中/英文逗号或空格隔开' : '请输入'}
        />
      }
    },
    {
      title: '商品',
      dataIndex: 'sku',
      hideInTable: true,
      colSize: (6 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [goodsKey, goodsValue] = value;
          const goodsValues = goodsValue?.trim()?.split(/[,，\s]/)?.map((v: string) => v?.trim())
          if (goodsKey === 'sku') {
            return {
              goodsKey,
              goodsValues: goodsValue?.trim() ? goodsValues : []
            }
          }
          return {
            goodsKey,
            goodsValue
          }
        },
      },
      renderFormItem: (_, __, tableForm) => {
        const goodsKey = tableForm?.getFieldValue('sku');
        return <Filters.SelectInput options={
          [{
            value: 'sku',
            label: 'SKU',
          }, {
            value: 'goodsName',
            label: '名称',
          }]
        }
          defaultValue={'sku'}
          placeholder={goodsKey?.length && ['sku'].includes(goodsKey[0]) ? '可输入多个sku查询，中/英文逗号或空格隔开' : '请输入'}
        />
      }
    },
    {
      dataIndex: 'date',
      title: '时间区间',
      hideInTable: true,
      colSize: (6 / 24),
      valueType: "dateRange",
      width: 100,
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [dateKey, dateValue] = value;
          return {
            dateKey,
            dateValue: [
              dateValue[0] ? moment(dateValue[0]).startOf('day') : null, // 当天最早时间
              dateValue[1] ? moment(dateValue[1]).endOf('day') : null,   // 当天最晚时间
            ],
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectGroup options={
          [{
            value: 'createDate',
            label: '创建',
          }, {
            value: 'orderDate',
            label: '下单',
          }, {
            value: 'paymentDate',
            label: '付款',
          },
          {
            value: 'signDate',
            label: '签收',
          },
          {
            value: 'periodDate',
            label: '账期',
          }
          ]
        }
          defaultValue={'createDate'}
          placeholder={['开始时间', '结束时间']}
          renderFormItem={(props: any) => {
            return <RangePicker {...props} />;
          }}
        />
      }
    },
    {
      title: '操作员',
      dataIndex: 'userName',
      hideInTable: true,
      colSize: (6 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [conductorKey, conductorValue] = value;
          return {
            conductorKey,
            conductorValue
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectInput options={
          [{
            value: 'purchaseUsername',
            label: '采购员',
          }, {
            value: 'merchandiserUsername',
            label: '跟单员',
          }
          ]
        }
          defaultValue={'purchaseUsername'}
        />
      }
    },
    {
      title: '订单类型',
      dataIndex: 'orderType',
      hideInTable: true,
      colSize: (6 / 24),
      valueEnum: {
        "": {
          text: "全部",
          status: 'Success',
        },
        "online": {
          text: "1688",
          status: 'Success',
        },
        "onlineIsManual": {
          text: "1688手工单",
          status: 'Success',
        },
        "isManual": {
          text: "手工单",
          status: 'Success',
        }
      }
    },
    {
      title: '标签',
      dataIndex: 'tagName',
      hideInTable: true,
      colSize: (6 / 24),
      // search: {
      //   transform: (value: any) => {
      //     console.log(value);
      //     if (!value) {
      //       return {};
      //     }
      //     const [tagKey, tagNames] = value;
      //     return {
      //       tagKey,
      //       tagNames,
      //     }
      //   },
      // },
      renderFormItem: () => (
        // renderFormItem: (_, __, tableForm) => {
        // const tagKey = tableForm?.getFieldValue('tagKey');
        // console.log(tableForm.getFieldsValue());
        // return <div style={{ display: 'flex', width: '100%' }}>
        //   <Select
        //     // name="tagKey"
        //     defaultValue="and"
        //     style={{ width: '20%' }}
        //     options={[
        //       { value: 'and', label: '且' },
        //       { value: 'or', label: '或' },
        //     ]}
        //     onChange={(value) => tableForm.setFieldValue('tagKey', value)}
        //   />
        <ProFormSelect
          name="tagNames"
          placeholder="请选择标签"
          mode="multiple" // 支持多选
          // style={{ width: '80%' }} // 保持宽度一致
          options={[
            { value: "isUrgent", label: "加急" },
            { value: "isPayment", label: "已核对" },
            { value: "undistributed", label: "待分配" },
            { value: "notExistLogistics", label: "无物流信息" },
            { value: "hasTrackingNumberNotLogistics", label: "有单号无物流信息" },
            { value: "accountPeriod", label: "账期结算" },
            { value: "periodWaitApply", label: "账期待申请" },
            { value: "afterSalesOrder", label: "售后单" },
            { value: "copyOrder", label: "复制单待审核" },
            { value: "isSign", label: "已签收" },
            { value: "NotSign", label: "未签收" },
            { value: "needQc", label: "需质检" },
          ]}
        />
        // </div>
        // }
      )
    },
    {
      title: '平台状态',
      dataIndex: 'platformStatus',
      hideInTable: true,
      colSize: (6 / 24),
      valueEnum: {
        "waitbuyerpay": {
          text: "等待买家付款",
          status: 'Success',
        },
        "waitsellersend": {
          text: "等待卖家发货",
          status: 'Success',
        },
        "waitbuyerreceive": {
          text: "等待买家收货",
          status: 'Success',
        },
        "confirm_goods": {
          text: "已收货",
          status: 'Success',
        },
        "confirm_goods_but_not_fund": {
          text: "等待买家还款",
          status: 'Success',
        },
        "success": {
          text: "交易成功",
          status: 'Success',
        },
        "cancel": {
          text: "交易取消",
          status: 'Success',
        },
        "terminated": {
          text: "交易终止",
          status: 'Success',
        }
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      hideInTable: true,
      colSize: (6 / 24),
      renderFormItem: () => <SelectVender useVenderName />
    },
    {
      title: '采购单金额',
      dataIndex: '',
      hideInTable: true,
      colSize: (6 / 24),
      renderFormItem: () => {
        return (
          <div style={{ display: 'flex' }}>
            <ProFormDigit label='' name='amountStart' /><span style={{ marginRight: 10 }}>~</span><ProFormDigit label='' name='amountEnd' />
          </div>
        )
      }
    },
    {
      title: '平台交易方式',
      dataIndex: 'platformTradeType',
      hideInTable: true,
      colSize: (6 / 24),
      valueEnum: {
        "fxassure": {
          text: "担保交易(fxassure)",
          status: 'Success',
        },
        "assureTrade": {
          text: "担保交易(assureTrade)",
          status: 'Success',
        },
        "period": {
          text: "供应商账期",
          status: 'Success',
        },
        "credit": {
          text: "诚意赊",
          status: 'Success',
        }
      }
    },
    {
      title: '采购状态',
      dataIndex: 'orderStatus',
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return <Filters.CheckboxButton
          defaultValue={[1]}
          options={[
            { value: 1, label: '全部' },
            { value: 0, label: (<Badge size={"small"} overflowCount={9999} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.draft}>草稿</Badge>) },
            { value: 10, label: (<Badge size={"small"} overflowCount={9999} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.orderError}>异常</Badge>) },
            { value: 20, label: '已下单' },
            { value: 30, label: '待付款' },
            { value: 40, label: '已付款' },
            { value: 45, label: '部分到货' },
            { value: 50, label: '全部到货' },
            { value: 55, label: '不等待剩余' },
            { value: 60, label: '已上架' },
            { value: 70, label: '作废' },
            { value: 999, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.auditReject}>驳回</Badge>) }
          ]}
          value={formInstance.getFieldValue('orderStatus')}
          onChange={(e) => {
            setSelectedRows([]);
            setAmountStore(null);
            if (e.length > 0 && e != formInstance.getFieldValue('orderStatus')) {
              formInstance.setFieldValue('orderStatus', e);
            }
            formInstance.submit();
          }}
        />
      }
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return <Filters.CheckboxButton
          defaultValue={[1]}
          options={[
            { value: 1, label: '全部' },
            { value: 35, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodWaitPay}>账期待付款</Badge>) },
            { value: 36, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.creditWaitPay}>诚意赊账待付款</Badge>) },
            { value: 40, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.paymentInProgress}>付款进行中</Badge>) },
            { value: 45, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodRepay}>账期待还款</Badge>) },
            { value: 46, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.creditRepay}>诚意赊账待还款</Badge>) },
            { value: 70, label: '已付全款' },
            { value: 50, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.payReject}>驳回</Badge>) },
          ]}
          value={formInstance.getFieldValue('payStatus')}
          onChange={(e) => {
            setSelectedRows([]);
            setAmountStore(null);
            if (e.length > 0 && e != formInstance.getFieldValue('payStatus')) {
              formInstance.setFieldValue('payStatus', e);
            }
            formInstance.submit();
          }}
        />
      }
    },
    {
      title: '账期状态',
      dataIndex: 'periodStatus',
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return (<Filters.CheckboxButton
          defaultValue={[""]}
          options={[
            { value: "", label: '全部' },
            { value: "APPLY", label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }}>待申请</Badge>) },
            { value: "WAIT", label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodWait}>待审核</Badge>) },
            { value: "PASS", label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodPass}>通过</Badge>) },
            { value: "REJECT", label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodReject}>驳回</Badge>) },
            { value: "END", label: '已结清' },
          ]}
          onChange={(e) => {
            setSelectedRows([]);
            setAmountStore(null);
            if (e.length > 0 && e != formInstance.getFieldValue('periodStatus')) {
              formInstance.setFieldValue('periodStatus', e);
            }
            formInstance.submit();
          }}
        />)
      }
    },
    {
      title: '采购单号/标题/供应商',
      dataIndex: 'id',
      width: 250,
      hideInSearch: true,
      render: (v, record) => {
        const link = record.aliWangWangLink != null ? (<a onClick={function () {
          if (record.aliWangWangLink != null) {
            window.open(record.aliWangWangLink);
          }
        }}><img src={aliwangwang} /></a>) :
          <span className="grey" style={{ color: "red" }}>暂未绑定供应商</span>

        return (
          <>
            <div style={{ fontSize: 13 }}>
              <span style={{ fontWeight: "bold", color: "#FF6600", marginRight: 5, fontSize: 12 }}>
                {record.isManual == 1 ? <PushpinOutlined style={{ fontSize: 14 }} title={"手工单"} /> : null}
                {record.platform}
              </span>
              <span className={"title" + record?.id}>{record.title}</span>&nbsp;<FormOutlined
                style={{ color: 'blue' }}
                onClick={() => changeOrderMemoOrRemark(record)}
              />
            </div>
            <div style={{ fontSize: 14 }}>
              <span style={{ fontWeight: "bold" }}>{record.orderCode}</span>
              <SnippetsTwoTone onClick={() => copyText(record.orderCode)} />
            </div>
            <div>
              <Link key="show" style={{ fontSize: 12, color: '#009FCC' }} to={`/purchase/supplier/SupplierList/${record.supplierId}`}>
                {record.supplierName}
              </Link>
            </div>
            {record?.platformOrderCode ? (<div style={{ fontSize: 13 }}>
              <span><a href={"https://trade.1688.com/order/new_step_order_detail.htm?orderId=" + record.platformOrderCode} target={"_blank"}>{record.platformOrderCode}</a></span>
              <SnippetsTwoTone onClick={() => copyText(record.platformOrderCode)} />
            </div>) : ''}
            <div>{link}&nbsp;<FlagOutlined className={"checkTagsFlag" + record.id} attrIsCheck={record.isPayment} onClick={() => markOrderCheck(record)} style={{ color: record?.isPayment == 1 ? "#00AA00" : "#DDDDDD", cursor: "pointer", fontSize: 16 }} />
            </div>
          </>
        );
      }
    },
    {
      title: '采购/跟单/仓库/主体/物流',
      dataIndex: 'id',
      width: 180,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div style={{ fontSize: 12 }}><UserOutlined title='采购员' />&nbsp;&nbsp;{record.purchaseUsername ? record.purchaseUsername : "--"}</div>
            <div style={{ fontSize: 12 }}>
              <UserOutlined title='跟单员' />&nbsp;&nbsp;{record.merchandiserUsername ? record.merchandiserUsername : "--"}
            </div>
            <div style={{ fontSize: 12 }}><BankOutlined title='采购仓库' />&nbsp;&nbsp;{record.purchaseWarehouse}</div>
            <div style={{ fontSize: 12 }}>
              <ContactsOutlined title={'采购主体'} />&nbsp;&nbsp;{record?.purchaseEntity || '--'}
            </div>
            <div style={{ display: 'flex', fontSize: 12, alignItems: 'flex-start' }}>
              <RocketOutlined title='快递单号' style={{ marginRight: 5 }} />
              <div style={{ flex: '1' }}>
                {record?.trackingNumbers && record?.trackingNumbers?.length > 0 ? record?.trackingNumbers?.map((trackingNumberItem: any) => (<div style={{ display: 'flex' }} key={trackingNumberItem}>
                  <a onClick={() => showLogistics({ trackingNumber: trackingNumberItem, orderId: record.platformOrderCode })}>{trackingNumberItem}</a>
                  <SnippetsTwoTone onClick={() => copyText(trackingNumberItem)} />
                </div>)) : '--'}
              </div>
            </div>
          </>
        );
      }
    },
    {
      title: '采购金额/运费',
      dataIndex: 'id',
      width: 180,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div style={{ fontSize: "12px" }}>
              平台总金额：{!record?.platformOrderAmount ? (record?.orderStatus === 0 ? '--' : '0.00') : record.platformOrderAmount?.toFixed(2)}&nbsp;&nbsp;
              {(record?.orderStatus === 0 || record?.platformOrderAmount?.toFixed(2) == (record.shippingFee + record.totalPrice + record.applyRefundAmount - record.applyPromotionAmount)?.toFixed(2)) ? '' : <ExclamationCircleOutlined style={{ color: 'red' }} />}
            </div>
            <div style={{ fontSize: "12px" }}>本地总金额：{!record?.shippingFee && !record?.totalPrice ? '--' : (record.shippingFee + record.totalPrice + record.applyRefundAmount - record.applyPromotionAmount)?.toFixed(2)}</div>
            <div style={{ fontSize: "12px" }}>商品金额：{!record?.totalPrice ? '--' : record.totalPrice?.toFixed(2)}</div>
            <div style={{ fontSize: "12px" }}>平台运费：<span style={record?.platformShippingFee > 20 ? { fontWeight: "bold", color: "red" } : {}}>{!record?.platformShippingFee ? '--' : record.platformShippingFee?.toFixed(2)}</span></div>
            <div style={{ fontSize: "12px" }}>本地运费：{!record?.shippingFee ? '--' : record.shippingFee?.toFixed(2)}</div>
            <div style={{ fontSize: "12px" }}>
              优惠：{!record?.applyPromotionAmount ? '--' : record.applyPromotionAmount?.toFixed(2)}&nbsp;&nbsp;
              涨价：{!record?.applyRefundAmount ? '--' : record.applyRefundAmount?.toFixed(2)}
            </div>
          </>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        const orderStatus = OrderStatusEnum[record?.orderStatus];
        const orderStatusTags = OrderStatusTagsEnum[record?.orderStatus];
        const payStatus = payStatusEnum[record?.payStatus];
        const purchaseAuditStatus = PurchaseAuditStatusEnum[record?.purchaseAuditStatus];
        const platformStatus = PlatformStatusEnum[record?.platformStatus];
        const options = [
          orderStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              采购状态：
              <span className={"ant-badge-status-dot ant-badge-status-" + (orderStatusTags != undefined ? orderStatusTags : "success")} />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{orderStatus}</span>
            </span>
          ) : null,
          platformStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              平台状态：
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{platformStatus}</span>
            </span>
          ) : null,
          payStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              付款状态：
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{payStatus}</span>
            </span>
          ) : null,
          purchaseAuditStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              审核状态：
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{purchaseAuditStatus}</span>
            </span>
          ) : null,
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '日期',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      align: 'left',
      render: (v, record) => {
        let days = null;
        if (record?.periodTime != null) {
          const periodTime = Date.parse(new Date(record?.periodTime));
          const curTime = Date.parse(new Date());
          days = Math.abs(parseInt((periodTime - curTime) / 1000 / 3600 / 24));
        }
        return <>
          {record.gmtCreate != null ? (
            <div style={{ fontSize: "12px" }}>生成：{moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm")}</div>) : null}
          {record.gmtModified != null ? (
            <div style={{ fontSize: "12px" }}>修改：{moment(record.gmtModified as number).format("YYYY-MM-DD HH:mm")}</div>) : null}
          {record.platformOrderTime != null ? (
            <div style={{ fontSize: "12px" }}>下单：{moment(record.platformOrderTime as number).format("YYYY-MM-DD HH:mm")}</div>) : null}
          {record.paymentTime && <div style={{ fontSize: "12px" }}>申请付款：{moment(record.paymentTime).format("YYYY-MM-DD HH:mm")}</div>}
          {record.platformPayTime != null ? (
            <div style={{ fontSize: "12px" }}>付款：{moment(record.platformPayTime).format("YYYY-MM-DD HH:mm")}</div>) : null}
          {record?.periodTime != null ? (
            <div style={{ fontSize: "12px" }}>账期：{moment(record?.periodTime)?.format("YYYY-MM-DD HH:mm")}&nbsp;{days != null && record?.payStatus != 70 ? <span style={{ fontSize: "12px", color: "red" }}><ClockCircleOutlined /> {days} </span> : null}</div>) : null}
        </>
      }
    },
    {
      title: '签收入库日期',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      align: 'left',
      render: (v, record) => {
        return <>
          {record.signDate != null ? (
            <div style={{ fontSize: "12px" }}>签收：{moment(record.signDate as number).format("YYYY-MM-DD HH:mm")}</div>) : null}
          {record.warehouseEntryTime && <div style={{ fontSize: "12px" }}>入库：{moment(record.warehouseEntryTime).format("YYYY-MM-DD HH:mm")}</div>}
        </>
      }
    },
    {
      title: '标签',
      dataIndex: 'remark',
      width: "200px",
      align: 'left',
      hideInSearch: true,
      render: (v, record) => {
        const options = [
          record.isPayment == 1 ?
            <Tag className={"checkTags" + record.id} attrIsCheck={record.isPayment} color="green" style={{ cursor: "pointer" }} onClick={() => markOrderCheck(record)} >已核对</Tag> :
            <Tag className={"checkTags" + record.id} attrIsCheck={record.isPayment} color="#DDDDDD" style={{ cursor: "pointer" }} onClick={() => markOrderCheck(record)} >待核对</Tag>,
          (SettleTypeMap[record?.settleType] != null ? (<Tag color='#FF6633'> {SettleTypeMap[record?.settleType]}</Tag>) : null),
          (SettleCircleTypeMap[record?.settleCircle] != null ? <Tag color='#FF6633'> {SettleCircleTypeMap[record?.settleCircle]}</Tag> : null),
          record.isUrgent == 1 ? <Tag color='red'>加急</Tag> : null,
          record.needQc ? <Tag color='red'>需质检</Tag> : null,
          record.isSign == 1 ? <Tag color='#00DDDD'>已签收</Tag> : null,
          record.isAfterOrder == 1 ? <Tag color='red'>售后单</Tag> : null,
          record.purchaseAuditStatus == 'reject' ? (<Tag color='#CC0000'>采购驳回</Tag>) : null,
          record.payStatus == 50 ? (<Tag color='#FF6600'>财务驳回</Tag>) : null,
          record.exceptionMessage != null && record.exceptionMessage != '' ? (<Alert style={{ maxWidth: "200px" }} type={"error"} message={record.exceptionMessage} />) : null,
        ];
        return <Space wrap={true} direction="horizontal" aria-colindex={2}>{options}</Space>;
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <Space wrap={true} direction="horizontal" aria-colindex={3}>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:orderCancel"}>
              <Button type="primary" size={"small"} danger ghost style={{ fontSize: 12, borderRadius: 5 }} onClick={() => updateStatus(record.id, 70, 0)}>
                作废
              </Button>
            </Permission>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:orderCopy"}>
              <Button type="primary" size={"small"} ghost style={{ fontSize: 12, borderRadius: 5 }} onClick={() => copyOrder(record.id)}>
                复制
              </Button>
            </Permission>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:orderCopyAudit"}>
              {record?.copyOrderStatus == "wait" ?
                <Button type="primary" size={"small"} ghost style={{ fontSize: 12, borderRadius: 5 }} onClick={() => orderCopyAudit(record.id)}>
                  复制审核
                </Button>
                : null}
            </Permission>
            <Button type="primary" size={"small"} ghost style={{ fontSize: 12, borderRadius: 5, color: "green", borderColor: "green" }} onClick={() => history.push({ pathname: `/purchase/purchase/purchaseOrder/detail/${record?.id}`, state: { title: record?.orderCode } })}>
              详情
            </Button>
            {/*<Permission permissionKey={"purchase:purchase:purchaseOrder:cancelWaitStock"}>*/}
            {/*  <Button type="primary" size={"small"} ghost style={{fontSize:12,borderRadius:5}}  onClick={() => nonWaitResidueButton(record)}>*/}
            {/*    取消剩余*/}
            {/*  </Button>*/}
            {/*</Permission>*/}
            {record?.payStatus == 35 ?
              <Permission permissionKey={"purchase:purchase:purchaseOrder:periodPurchaseCheck"}>
                <Button type="primary" size={"small"} ghost style={{ fontSize: 12, borderRadius: 5, color: "orange", borderColor: "orange" }} onClick={() => periodCheck(record)}>
                  账期确认
                </Button>
              </Permission>
              : null}
            <Permission permissionKey={"purchase:purchase:purchaseOrder:editPurchaseOrder"}>
              <Button type="primary" size={"small"} ghost onClick={() => {
                setCurrentRecord(record);
                setEditOrderVisible(true);
              }}>
                编辑
              </Button>
            </Permission>
          </Space>
        </>
      }
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [activeStatusKey]);

  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        size="small"
        scroll={{ y: 'calc(100vh - 480px)' }}
        // className={styles.inline_search_table}
        tableAlertRender={false}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <b>选中单数：{selectedRowsState.length} &nbsp;</b>,
              <b>采购总额：{amountStore?.selectLocalAmount == null ? 0.00 : amountStore.selectLocalAmount?.toFixed(2)} &nbsp;</b>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:applyPay"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={accountSettlement}>
                  申请账期结算（高级）
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:applyPay"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={adcancedPayment}>
                  申请付款（高级）
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:applyPay"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => applyPay(selectedRowsState)}>
                  申请付款
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:allocMerchandiser"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => setModalVisible(true)}>
                  分配跟单员
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:allocPurchaser"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => setModalVisible1(true)}>
                  分配采购员
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:manualCreateOrder"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => batchUpdateStatus(selectedRowsState, 20)}>
                  手工下单
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:1688CreateOrder"}>
                <Button loading={submitLoading} size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => createAlibabaOrder(selectedRowsState)}>
                  1688下单
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:refreshPlatformOrder"}>
                <Button loading={submitRefreshLoading} size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => refreshAlibabaOrder(selectedRowsState)}>
                  同步1688订单
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:priceQuantityCheck"}>
                <Badge size={"small"} style={{ backgroundColor: "#FF3333", zIndex: 999 }} count={data?.checkPrice}>
                  <Link key="show" to={`/purchase/purchase/purchaseOrderAudit/priceCheck`}>
                    <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary">采购审核</Button>
                  </Link>
                </Badge>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseOrder:purchaseAnalysis"}>
                <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => setPurchaseAnalysisModal(true)}>
                  采购分析
                </Button>
              </Permission>,
              <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={onExport}>
                导出
              </Button>
            ];
            return [...options, ...dom];
          },
        }}
        pagination={{ pageSize: 30 }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            let selectPlatformAmount = 0;
            let selectLocalAmount = 0;
            let selectGoodsAmount = 0;
            selectedRows.forEach((record) => {
              selectPlatformAmount += !record?.platformOrderAmount ? 0 : record.platformOrderAmount;
              selectLocalAmount += !record?.shippingFee && !record?.totalPrice ? 0 : (record.shippingFee + record.totalPrice + record.applyRefundAmount - record.applyPromotionAmount);
              selectGoodsAmount += !record?.totalPrice ? 0 : record.totalPrice;
            })
            setAmountStore({ selectPlatformAmount: selectPlatformAmount, selectLocalAmount: selectLocalAmount, selectGoodsAmount: selectGoodsAmount });
            setSelectedRows(selectedRows);
          },
        }}
        onRow={(record) => {
          return {
            onDoubleClick: (e) => {
              history.push({ pathname: `/purchase/purchase/purchaseOrder/detail/${record?.id}`, state: { title: record?.orderCode } })
            },
          };
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <EditOrderModal open={editOrderVisible} orderId={currentRecord?.id} onFinish={() => { setEditOrderVisible(false); }} onCancel={() => setEditOrderVisible(false)} />
      <AllotMerchandiserModal open={modalVisible} onCancel={() => setModalVisible(false)}
        onFinish={AssigningPersonInCharge} />
      <AllotPurchaseModal open={modalVisible1} onCancel={() => setModalVisible1(false)}
        onFinish={AssigningPersonInCharge} />
      <PurchaseAnalysisModal open={purchaseAnalysisModal} onCancel={() => setPurchaseAnalysisModal(false)} onFinish={() => setPurchaseAnalysisModal(false)} />
      <AdvancedPayment open={paymentVisible} onCancel={() => setPaymentVisible(false)} queryParams={queryParams} />
      <AccountSettlement open={accountSettlementVisible} onCancel={() => setAccountSettlementVisible(false)} queryParams={queryParams} />
    </>
  );
};

export default TableList;
