import type {ProColumns} from '@ant-design/pro-components';
import {Button, Modal, message, ModalProps, Card, Table, Input, Col, Row, Select, notification, Image} from 'antd';
import React, {useEffect, useState} from 'react';
import {PurchaseOrder, PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";
import {ArrowDownOutlined, ArrowUpOutlined} from "@ant-design/icons";
import ProForm,{ProFormField, ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {applyPurchaseAfterSales} from "@/modules/purchaseOrder/infra/api/purchaseAfterSales";

// 定义参数格式
export type modifyQuantityAndPriceModal = {
  data: PurchaseOrderGoods[];
  orderData: PurchaseOrder;
  onFinish: () => void;
} & ModalProps;

export default (props: modifyQuantityAndPriceModal) => {
  const [form] = ProForm.useForm();
  const { onFinish,data,orderData, ...rest } = props;
  const [dataSource, setDataSource] = useState<PurchaseOrderGoods[]>();
  const [inputHide, setInputHide] = useState<boolean>(false);

  useEffect(
    ()=>{
      setDataSource(props.data);
    },
    [props.data]
  );

  const sumApplyAmount = () =>{
    let amount = Number(form.getFieldValue('applyShippingFee'));
    const applyBalance = Number(form.getFieldValue('applyBalance'));
    dataSource?.forEach(item=>{
      if(item?.afterSalesQuantity > 0){
        amount += item.afterSalesQuantity * Number(item.currentPurchasePrice)
      }
    })
    form.setFieldValue('applyAmount', (amount+applyBalance)?.toFixed(2))
  }

  const onFinishs = async (values: any) => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      params['goodsList'] = values;
      params['orderCode'] = orderData.orderCode;
      params['orderId'] = orderData.id;
      applyPurchaseAfterSales(params).then((res) =>{
        if (res.status.success) {
          notification.success({message: '申请成功'});
          onFinish();
        }
      });
    })

  }

  const columns: ProColumns<PurchaseOrderGoods>[] = [
    {
      title: "图片",
      dataIndex: 'skuImg',
      readonly: true,
      render: (v, record) => {
        return <Image src={record.skuImg} width={80}/>
      }
    },
    {
      title: '商品',
      dataIndex: 'sku',
      readonly: true,
      render: (v, record) => {
        return <><div><b>{record.sku}</b></div><div>{record.name}</div></>
      },
    },
    {
      title: "上次采购价",
      dataIndex: 'lastPurchasePrice',
      render: (v, record) => {
        return Number(record.lastPurchasePrice)?.toFixed(2);
      }
    },
    {
      title: "当前采购价",
      dataIndex: 'currentPurchasePrice',
      render: (v, record) => {
        return (
          <>
            <span>{Number(record.currentPurchasePrice)?.toFixed(2) }&nbsp;</span>
            <span>{record.lastPurchasePrice > record.currentPurchasePrice ? (
              <ArrowDownOutlined style={{ color: 'green' }} />
            ) : record.lastPurchasePrice < record.currentPurchasePrice ? (
              <ArrowUpOutlined style={{ color: 'red' }} />
            ): null}
            </span>
          </>
        )
      }
    },
    {
      title: '平台价格',
      dataIndex: 'platformPurchasePrice',
      render: (v, record) => {
        return record?.platformPurchasePrice ? Number(record.platformPurchasePrice)?.toFixed(2): "--";
      }
    },
    {
      title: '采购数量',
      dataIndex: 'platformPurchasePrice',
      render: (v, record) => {
        return record?.purchaseQuantity;
      }
    },
    {
      title: '实收数量',
      dataIndex: 'platformPurchasePrice',
      render: (v, record) => {
        return record?.arrivalQuantity;
      }
    },
    {
      title: '申请数量',
      dataIndex: 'purchaseQuantity',
      render: (v, record) => {
        return (<Input width={60} onChange={(value)=>{
          record.afterSalesQuantity = Number(value.target.value);
          sumApplyAmount();
        }} defaultValue={0}></Input>);
      }
    },
  ];

  return (
    <>
      <Modal {...rest} title="申请售后" width={"60%"} onOk={()=>onFinishs(dataSource)}>
        <ProForm
          form={form}
          layout="horizontal"
          labelCol={{ span: 6 }}
          submitter={false}
        >
        <Card bordered={false} style={{padding:0,margin:0}}>
          <Row gutter={24}>
            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={"标题"}
                name="title"
                rules={[{ required: true, message: '请输入售后标题' }]}
              />
              <ProFormTextArea
                label={"备注"}
                name="remark"
                rules={[{ required: true, message: '请输入备注' }]}
              />
            </Col>
            <Col lg={8} md={8} sm={24}>
              <ProFormField
                label="售后类型"
                name="afterSalesType"
                rules={[{ required: true, message: '请输入售后类型' }]}
              >
                <Select placeholder={"请选择"}
                  options={[
                    {value: "orderRefund", label: '整单退款'},
                    {value: "multiGoodsRefund", label: '多品退款'},
                    {value: "singleGoodsRefund", label: '单品退款'},
                    {value: "onlyReturn", label: '账期退货（减账期应付）'},
                    {value: "periodReceived", label: '账期实收（加账期应付）'}
                    // {value: "amountHedging", label: '非商品费用退款（数量可为0）'}
                  ]}
                  onChange={(e)=>{
                    if(e == "onlyReturn"){
                      setInputHide(true);
                    }else{
                      setInputHide(false);
                    }
                    form.setFieldValue("afterSalesType", e);
                  }}
                />
              </ProFormField>
              <ProFormText
                label={"申请运费"}
                name="applyShippingFee"
                initialValue={orderData?.platformShippingFee?.toFixed(2) || 0}
                // hidden={inputHide}
                onChange={()=>sumApplyAmount()}
                rules={[{ required: true, message: '请输入申请金额' }]}
              />
              <ProFormText
                label={"申请差额"}
                name="applyBalance"
                initialValue={0}
                onChange={()=>sumApplyAmount()}
                // hidden={inputHide}
                rules={[{ required: true, message: '请输入申请金额' }]}
              />
            </Col>
            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={"申请金额"}
                name="applyAmount"
                initialValue={0}
                disabled={true}
                // hidden={inputHide}
              />
            </Col>
          </Row>
        </Card>
        <Card bordered={false} style={{padding:0,marginTop:-30,marginBottom:-25}}>
          <div>
            <b>平台总金额：{orderData?.platformOrderAmount?.toFixed(2)} &nbsp;&nbsp;平台运费：{orderData?.platformShippingFee?.toFixed(2)}</b>
          </div>
        </Card>
        <Card bordered={false} style={{padding:0,margin:0}}>
          <Table<PurchaseOrderGoods>
            columns={columns}
            rowKey="id"
            size={"small"}
            dataSource={dataSource}
            maxLength={1}
            pagination={false}
          />
        </Card>
        </ProForm>
      </Modal>
    </>
  );
};
