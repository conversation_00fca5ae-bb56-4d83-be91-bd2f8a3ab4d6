import {ProFormSelect} from "@ant-design/pro-form";
import {Form, Modal, ModalProps} from "antd";
import {getWarehouse} from "@/modules/purchasePlan/infra/api/purchasePlan";
import {getUser} from "@/modules/purchaseOrder/infra/api/purchaseOrder";

// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: any) => void;
} & ModalProps;

const AllotMerchandiserModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, ...rest } = props;

  return <Modal {...rest} title="分配跟单员" onOk={()=>form.submit()}>
    <Form form={form}  onFinish={onFinish}>
      <ProFormSelect
        width="md"
        name="merchandiserUsername"
        showSearch={true}
        label="跟单员"
        request={async () =>  {//返回的select网络请求
          const params = await getUser("跟单员");
          const res = [];
          const body = params.body;
          for(let i in body){
            const temp = {};
            temp['label'] = body[i];
            temp['value'] = i;
            res.push(temp)
          }
          return res;
        }}
      />

    </Form>
  </Modal>
}

export default AllotMerchandiserModal;
