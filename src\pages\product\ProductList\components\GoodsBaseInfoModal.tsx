import React, { useEffect, useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Spin,
  Table,
  Image,
  Modal,
  Form,
  notification,
  Space, Row, Col, Select, Checkbox, message,
} from "antd";
import { removeSupplierGoods } from "@/modules/goods/infra/api/goods";
import SkuEditModal from "@/pages/product/CreateProduct/components/SkuEditModal";
import SelfImage from "@/components/SelfImage";
import { Goods, GoodsStatusEnum } from "@/modules/goods/domain/goods";
import { Access } from "umi";
import Permission from "@/components/Permission";
import { ProFormField } from "@ant-design/pro-components";
import { CategorySelect } from "@/components/CategoryTreeModal";
import CustomUploadImageGroup from "@/components/CustomUploadImageGroup";
import ProForm, {
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { executeSave, reqByUrl } from "@/modules/common/infra/api/common";
import { Spu, ProductTypeEnum, PackingMaterialEnum } from "@/modules/product/domain/spu";
import { ColumnProps } from "antd/es/table";
import { removeSku } from "@/modules/product/infra/api/spu";
import CurrencySelect from '@/components/common/CurrencySelect';

export type productrModalParams = {
  code: string,
  onRefresh?: void
};

export default (props: productrModalParams) => {
  const { code, onRefresh } = props;
  const [skuEditModal, setSkuEditModal] = useState<boolean>(false);
  const [skuEditParams, setSkuEditParams] = useState<Goods>();
  const [spuData, setSpuData] = useState<Spu>();
  const [spuForm] = ProForm.useForm();
  const [form] = ProForm.useForm();

  useEffect(() => {
    spuForm.setFieldsValue({});
    loadSpuInfo();
  },
    [code]
  );

  const loadSpuInfo = () => {
    reqByUrl("/purchase-mgmt-biz/pd/spu/get", { code: code }).then((res) => {
      if (res?.status.success) {
        res.body.specialTags = res?.body?.specialTags && res?.body?.specialTags.indexOf('[') >= 0 ? JSON.parse(res?.body?.specialTags) : [];
        setSpuData(res?.body);
        spuForm.setFieldsValue(res?.body);
        spuForm.setFieldValue('category', { 'title': res?.body.categoryInfo.categoryPaths?.map(item => { return item?.name }).join("|"), "key": res?.body.categoryInfo?.categoryId });
        onRefresh(res?.body);
      }
    })
  }


  const removeSkuData = (record: Goods) => {
    Modal.confirm({
      content: "确定移除SKU吗？",
      onOk: function () {
        removeSku(record?.skuId).then((result) => {
          if (result.status.success) {
            notification.success({ message: '删除成功' });
            loadSpuInfo();
          }
        });
      },
    });
    form.resetFields();
  }

  const spuSubmit = () => {
    spuForm.validateFields().then(params => {
      //组装categoryInfo
      params.categoryInfo = {
        category: params?.category,
        categoryId: params?.category?.key,
        customParams: [],
        standardParams: []
      };
      console.log(params)
      executeSave("/purchase-mgmt-biz/pd/spu/update", params).then(res => {
        if (res?.status.success) {
          loadSpuInfo();
          message.success("保存成功！");
        }
      });
    })
  }

  const columns: ColumnProps<Goods>[] = [
    {
      title: "图片",
      dataIndex: 'skuImg',
      align: "center",
      width: 60,
      render: (v, record) => {
        return <SelfImage src={record?.mainSpecInfo?.image?.fileUrl} title={record?.skuName} width={80} />
      }
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      align: 'center',
      width: 150,
    },
    {
      title: '名称',
      key: 'skuName',
      dataIndex: 'skuName',
      align: 'left',
      width: 300,
      render: (v, record) => {
        return <div>{record?.skuName}</div>
      }
    },
    {
      title: '属性',
      dataIndex: 'skuAttr',
      align: 'center',
      width: 150,
      render: (v, record) => {
        return <div>{record?.skuAttrName} : {record?.skuAttrValue}</div>
      }
    },
    {
      title: '状态',
      dataIndex: 'salesStatus',
      align: 'center',
      width: 50,
      render: (_, record, index) => {
        return <div>{GoodsStatusEnum[record?.salesStatus]}</div>;
      },
    },
    {
      title: '商品类型',
      dataIndex: 'productType',
      align: 'center',
      width: 80,
      render: (_, record, index) => {
        return <div>{ProductTypeEnum[record?.productType]}</div>;
      },
    },
    {
      title: '产品材质',
      dataIndex: 'productMaterial',
      align: 'center',
      width: 100,
      render: (v, record) => {
        return <div>{record?.productMaterial}</div>
      }
    },
    {
      title: '包装材料',
      dataIndex: 'packingMaterial',
      align: 'center',
      width: 80,
      render: (_, record, index) => {
        return <div>{PackingMaterialEnum[record?.packingMaterial]}</div>;
      },
    },
    {
      title: '币种',
      dataIndex: 'currency',
      align: 'center',
      width: 50,
      render: (v, record) => {
        return <div>{record?.currency}</div>
      }
    },
    {
      title: '开发价',
      dataIndex: 'referenceCost',
      align: 'center',
      width: 80,
      render: (_, record, index) => {
        return <div>{record?.referenceCost?.toFixed(2) || "--"}</div>;
      },
    },
    {
      title: '首次采购价',
      dataIndex: 'firstPurchasePrice',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return <div>{record?.firstPurchasePrice?.toFixed(2) || "--"}</div>;
      },
    },
    {
      title: '上次采购价',
      dataIndex: 'lastPurchasePrice',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return <div>{record?.lastPurchasePrice?.toFixed(2) || "--"}</div>;
      },
    },
    {
      title: '商品毛重/净重(g)',
      dataIndex: 'weight',
      align: 'center',
      width: 135,
      render: (_, record, index) => {
        return <div>{record?.grossWeight} / {record?.netWeight}</div>;
      },
    },
    {
      title: '商品尺寸(cm)',
      dataIndex: '',
      align: 'center',
      width: 135,
      render: (_, record, index) => {
        return (
          <Space key={index} >
            {record?.sizeLength}/
            {record?.sizeWidth}/
            {record?.sizeHeight}
          </Space>
        );
      },
    },
    {
      title: '最小采购量',
      dataIndex: 'minPurchaseQuantity',
      align: 'center',
      width: 100,
      render: (v, record) => {
        return <div>{record?.minPurchaseQuantity}</div>
      }
    },
    {
      title: "操作",
      align: "center",
      width: 100,
      render: (v, record) => {
        return (<>
          <Space>
            <Permission permissionKey={"purchase:product_manager:sku:skuDelete"}>
              <a type="primary" style={{ fontSize: 12, color: "red" }} onClick={() => removeSkuData(record)}>
                删除
              </a>
            </Permission>
            <Permission permissionKey={"purchase:product_manager:sku:skuEdit"}>
              <a type="primary" style={{ fontSize: 12 }} onClick={() => {
                setSkuEditModal(true);
                setSkuEditParams(record);
              }}>
                编辑
              </a>
            </Permission>
          </Space>
        </>)
      }
    }
  ];
  return (
    <>
      <Spin spinning={false} style={{ paddingTop: 0 }}>
        <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0, minHeight: 710 }} bordered={false}>
          <Form form={spuForm} labelCol={{ flex: '100px' }} >
            <Row>
              <Col span={15}>
                <Row>
                  <Col span={6}>
                    <ProFormField
                      rules={[{ required: true, message: '请输入SPU' }]}
                      label="SPU"
                      name="spu"
                      disabled={true}
                    />
                    <ProFormField
                      name="spuId"
                      hidden={true}
                    />
                  </Col>
                  <Col span={18}>
                    <ProFormField
                      rules={[{ required: true, message: '请输入标题' }]}
                      label="标题"
                      name="title"
                    />
                  </Col>
                </Row>
                <Row>
                  <Col span={6}>
                    <ProFormField
                      label="分类"
                      width={"md"}
                      name="category"
                      rules={[{ required: true, message: '请选择商品分类' }]}
                      onChange={(e) => { console.log(e) }}
                      transform={(value: any, namePath: string, allValues: any) => {
                        return {
                          categoryInfo: {
                            categoryId: allValues.category.key,
                            customParams: allValues?.customParams || [],
                            standardParams: allValues?.standardParams || [],
                          },
                        };
                      }}
                    >
                      <CategorySelect />
                    </ProFormField>
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="品牌"
                      name="brandId"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="单位"
                      name="unit"
                    >
                      <Select
                        allowClear={true}
                        options={[
                          { value: "个", label: '个' },
                          { value: "袋", label: '袋' },
                          { value: "箱", label: '箱' },
                        ]}
                      />
                    </ProFormField>
                  </Col>
                  <Col span={6}>
                    <ProFormDigit
                      label="备货周期"
                      name="purchaseCycle"
                      fieldProps={{
                        precision: 0,
                        min: 1,
                      }}
                    />
                  </Col>
                </Row>
                <Row>
                  <Col span={6}>
                    <ProFormField
                      label="中文申报名"
                      name="zhDeclaredName"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="英文申报名"
                      name="enDeclaredName"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="申报货币"
                      name="declaredCurrency"
                    >
                      <CurrencySelect showSearch />
                    </ProFormField>
                  </Col>
                  <Col span={6}>
                    <ProFormDigit
                      label="申报货值"
                      name="declaredPrice"
                    />
                  </Col>
                </Row>
                <Row>
                  <Col span={6}>
                    <ProFormField
                      label="标签"
                      name="tagss"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="是否过CE"
                      name="isCE"
                    >
                      <Select
                        allowClear={true}
                        options={[
                          { value: "0", label: '否' },
                          { value: "1", label: '是' },
                        ]}
                      />
                    </ProFormField>
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="是否有证书"
                      name="isCertificate"
                    >
                      <Select
                        allowClear={true}
                        options={[
                          { value: "0", label: '否' },
                          { value: "1", label: '是' },
                        ]}
                      />
                    </ProFormField>
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="可开增值税"
                      name="isTax"
                    >
                      <Select
                        allowClear={true}
                        options={[
                          { value: "0", label: '否' },
                          { value: "1", label: '是' },
                        ]}
                      />
                    </ProFormField>
                  </Col>
                </Row>
                <Row>
                  <Col span={6}>
                    <ProFormField
                      label="开发员"
                      name="developer"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="采购员"
                      name="purchaser"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="维护人"
                      name="maintainer"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormField
                      label="文案/美工"
                      name="designer"
                    />
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <ProFormField
                      label="特/普货"
                      name="specialTags"
                    >
                      <Checkbox.Group>
                        <Checkbox style={{ marginLeft: 8 }} key={"纯普货"} value={"纯普货"}>纯普货</Checkbox>
                        <Checkbox style={{ marginLeft: 8 }} key={"普货（羽毛&木质）"} value={"普货（羽毛&木质）"}>普货（羽毛&木质）</Checkbox>
                        <Checkbox key={"需欧盟能效认证"} value={"需欧盟能效认证"}>需欧盟能效认证</Checkbox>
                        <Checkbox key={"需要CE认证"} value={"需要CE认证"}>需要CE认证</Checkbox>
                        <Checkbox key={"成人用品带电池"} value={"成人用品带电池"}>成人用品带电池</Checkbox>
                        <Checkbox key={"成人用品普货"} value={"成人用品普货"}>成人用品普货</Checkbox>
                        <Checkbox key={"粉末"} value={"粉末"}>粉末</Checkbox>
                        <Checkbox key={"纯电池"} value={"纯电池"}>纯电池</Checkbox>
                        <Checkbox key={"内置锂离子电池"} value={"内置锂离子电池"}>内置锂离子电池</Checkbox>
                        <Checkbox key={"外置锂离子电池"} value={"外置锂离子电池"}>外置锂离子电池</Checkbox>
                        <Checkbox key={"内置锂金属电池"} value={"内置锂金属电池"}>内置锂金属电池</Checkbox>
                        <Checkbox key={"外置锂金属电池"} value={"外置锂金属电池"}>外置锂金属电池</Checkbox>
                        <Checkbox key={"内置干电池"} value={"内置干电池"}>内置干电池</Checkbox>
                        <Checkbox key={"外置干电池"} value={"外置干电池"}>外置干电池</Checkbox>
                        <Checkbox key={"内置其它电池"} value={"内置其它电池"}>内置其它电池</Checkbox>
                        <Checkbox key={"外置其它电池"} value={"外置其它电池"}>外置其它电池</Checkbox>
                        <Checkbox key={"电子类"} value={"电子类"}>电子类</Checkbox>
                        <Checkbox key={"蓝牙功能"} value={"蓝牙功能"}>蓝牙功能</Checkbox>
                        <Checkbox key={"原木"} value={"原木"}>原木</Checkbox>
                        <Checkbox key={"加工木质"} value={"加工木质"}>加工木质</Checkbox>
                        <Checkbox key={"眼镜类"} value={"眼镜类"}>眼镜类</Checkbox>
                        <Checkbox key={"家庭刀具类"} value={"家庭刀具类"}>家庭刀具类</Checkbox>
                        <Checkbox key={"非家庭刀具类"} value={"非家庭刀具类"}>非家庭刀具类</Checkbox>
                        <Checkbox key={"弱磁"} value={"弱磁"}>弱磁</Checkbox>
                        <Checkbox key={"强磁"} value={"强磁"}>强磁</Checkbox>
                        <Checkbox key={"带胶水"} value={"带胶水"}>带胶水</Checkbox>
                        <Checkbox key={"太阳能面板"} value={"太阳能面板"}>太阳能面板</Checkbox>
                        <Checkbox key={"LED类"} value={"LED类"}>LED类</Checkbox>
                        <Checkbox key={"纺织品"} value={"纺织品"}>纺织品</Checkbox>
                        <Checkbox key={"液体"} value={"液体"}>液体</Checkbox>
                        <Checkbox key={"防疫用品"} value={"防疫用品"}>防疫用品</Checkbox>
                      </Checkbox.Group>
                    </ProFormField>
                  </Col>
                </Row>
              </Col>
              <Col span={7}>
                <ProFormField
                  label="商品主图"
                  name="mainImages"
                >
                  <CustomUploadImageGroup />
                </ProFormField>
              </Col>
            </Row>
          </Form>

          <Row>
            <Col span={22}>
              <Table
                dataSource={spuData?.skus}
                columns={columns}
                bordered={true}
                rowKey="id"
                size={"small"}
                pagination={false}
              />
            </Col>
          </Row>

          <Row style={{ marginTop: 10, paddingRight: 160 }} justify="space-between">
            <div></div>
            <Space>
              <Permission permissionKey={"purchase:product_manager:sku:skuEdit"}>
                <Button
                  type="primary"
                  style={{ marginRight: 20, fontSize: 13, borderRadius: "5px" }}
                  key="bundledAlibaba"
                  ghost={true}
                  onClick={() => {
                    setSkuEditModal(true);
                    setSkuEditParams({ spuId: spuData?.spuId });
                  }}
                >
                  添加多属性
                </Button>
              </Permission>
              <Permission permissionKey={"purchase:product_manager:sku:spuEdit"}>
                <Button type="primary" onClick={() => spuSubmit()} >
                  保存
                </Button>
              </Permission>
            </Space>
          </Row>
        </Card>
      </Spin>
      <Access accessible={skuEditModal}>
        <SkuEditModal visible={skuEditModal} goodsData={skuEditParams} onFinish={() => {
          setSkuEditModal(false);
          loadSpuInfo();
        }} />
      </Access>
    </>
  );
};
