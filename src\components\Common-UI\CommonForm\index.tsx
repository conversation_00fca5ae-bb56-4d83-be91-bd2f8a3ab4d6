import useLocael from '@/hooks/useLocael';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormCheckbox,
  ProFormDateTimeRangePicker,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import moment from 'moment';
import { memo, useCallback, useImperativeHandle, useRef } from 'react';
import styled from 'styled-components';
import type { CommonFormChild, CommonFormColumn, CommonFormProps } from './type';
import { ProFormDateTimePicker } from '@ant-design/pro-components';

const StyleWrapper = styled.div`
  .common-form {
    .form-item {
      .title {
        font-weight: bold;
        font-size: 16px;
      }
    }
    label {
      white-space: pre-wrap;
    }
  }
`;

// 数字框整数格式化
const formatFn = (value: string | number | undefined) => {
  if (typeof value === 'string' && value.includes(',')) return value;
  return value && `${parseInt(value as string, 10)}`;
};

export const getComponent = (child: CommonFormChild | null, readonly?: boolean) => {
  if (!child) return;
  const { type, label, key, valueEnum, render, wrapper, options, min, max, maxLength, isInteger, fieldProps, ...rest } = child;

  // 1 存在 render，自定义渲染
  if (render) {
    return wrapper === false ? (
      render()
    ) : (
      <ProForm.Item label={label} name={key || undefined} {...rest}>
        {render()}
      </ProForm.Item>
    );
  }
  // 2 存在 valueEnum，自动设置为选择框
  else if (valueEnum || type === 'ProFormSelect')
    return <ProFormSelect label={label} name={key} valueEnum={valueEnum} fieldProps={fieldProps} {...(rest as any)} readonly={readonly} />;
  // 3 存在 options, 自动设置为单选框/多选框
  else if (options) {
    if (type === 'ProFormCheckbox') {
      return <ProFormCheckbox.Group label={label} name={key} options={options} fieldProps={fieldProps} {...(rest as any)} readonly={readonly} />;
    }
    return <ProFormRadio.Group label={label} name={key} options={options} fieldProps={fieldProps} {...(rest as any)} readonly={readonly} />;
  }
  // 4 存在 min/max，自动设置为数字框
  else if (min != undefined || max != undefined || isInteger != undefined) {
    return (
      <ProFormDigit
        label={label}
        name={key}
        min={min}
        max={max}
        fieldProps={isInteger ? { formatter: formatFn as any, ...fieldProps } : fieldProps}
        {...(rest as any)}
        readonly={readonly}
      />
    );
  }

  switch (type) {
    case 'ProFormDateTimePicker':
      return (
        <ProFormDateTimePicker
          readonly={readonly}
          label={label}
          name={key}
          fieldProps={{
            showTime: {
              defaultValue: moment('00:00:00', 'HH:mm:ss'),
            },
            ...fieldProps,
          }}
          {...(rest as any)}
        />
      );
    case 'ProFormDateTimeRangePicker':
      return (
        <ProFormDateTimeRangePicker
          readonly={readonly}
          label={label}
          name={key}
          fieldProps={{
            showTime: {
              defaultValue: [moment('00:00:00', 'HH:mm:ss')],
            },
            ...fieldProps,
          }}
          {...(rest as any)}
        />
      );
    case 'ProFormTextArea':
      return (
        <ProFormTextArea
          label={label}
          name={key}
          {...(rest as any)}
          fieldProps={{ maxLength, showCount: maxLength && true, style: { height: fieldProps?.height || 120 }, ...fieldProps }}
        />
      );
    default:
      return <ProFormText label={label} name={key} {...(rest as any)} fieldProps={{ maxLength, showCount: maxLength && true, ...fieldProps }} />;
  }
};

const CommonForm = memo((props: CommonFormProps) => {
  const { columns = [], commonRef, readonly, showBtn = true, submitter, ...formProps } = props;
  const actionRef = useRef<ProFormInstance>();
  const { isEn } = useLocael();

  // 1) ref 方法
  const getFormatValues = useCallback(() => {
    return actionRef.current?.getFieldsFormatValue?.();
  }, []);

  // 2) 获取渲染组件
  // const getComponent = useCallback(
  //   (child: CommonFormChild) => {
  //     const { type, label, key, valueEnum, render, wrapper, options, min, max, maxLength, isInteger, fieldProps, ...rest } = child;

  //     // 1 存在 render，自定义渲染
  //     if (render) {
  //       return wrapper === false ? (
  //         render()
  //       ) : (
  //         <ProForm.Item label={label} name={key || undefined} {...rest}>
  //           {render()}
  //         </ProForm.Item>
  //       );
  //     }
  //     // 2 存在 valueEnum，自动设置为选择框
  //     else if (valueEnum)
  //       return (
  //         <ProFormSelect label={label} name={key} valueEnum={valueEnum} fieldProps={fieldProps} {...(rest as any)} readonly={readonly} />
  //       );
  //     // 3 存在 options, 自动设置为单选框/多选框
  //     else if (options) {
  //       if (type === 'ProFormCheckbox') {
  //         return (
  //           <ProFormCheckbox.Group
  //             label={label}
  //             name={key}
  //             options={options}
  //             fieldProps={fieldProps}
  //             {...(rest as any)}
  //             readonly={readonly}
  //           />
  //         );
  //       }
  //       return (
  //         <ProFormRadio.Group label={label} name={key} options={options} fieldProps={fieldProps} {...(rest as any)} readonly={readonly} />
  //       );
  //     }
  //     // 4 存在 min/max，自动设置为数字框
  //     else if (min != undefined || max != undefined || isInteger != undefined) {
  //       return (
  //         <ProFormDigit
  //           label={label}
  //           name={key}
  //           min={min}
  //           max={max}
  //           fieldProps={isInteger ? { formatter: formatFn as any, ...fieldProps } : fieldProps}
  //           {...(rest as any)}
  //           readonly={readonly}
  //         />
  //       );
  //     }

  //     switch (type) {
  //       case 'ProFormDateTimeRangePicker':
  //         return (
  //           <ProFormDateTimeRangePicker
  //             readonly={readonly}
  //             label={label}
  //             name={key}
  //             fieldProps={{
  //               showTime: {
  //                 defaultValue: [moment('00:00:00', 'HH:mm:ss')],
  //               },
  //               ...fieldProps,
  //             }}
  //             {...(rest as any)}
  //           />
  //         );
  //       case 'ProFormTextArea':
  //         return (
  //           <ProFormTextArea
  //             label={label}
  //             name={key}
  //             {...(rest as any)}
  //             fieldProps={{ maxLength, showCount: maxLength && true, style: { height: fieldProps?.height || 120 }, ...fieldProps }}
  //           />
  //         );
  //       default:
  //         return (
  //           <ProFormText
  //             label={label}
  //             name={key}
  //             {...(rest as any)}
  //             fieldProps={{ maxLength, showCount: maxLength && true, ...fieldProps }}
  //           />
  //         );
  //     }
  //   },
  //   [readonly],
  // );
  const getChildren = useCallback(
    (title: string, childrens: (CommonFormChild | CommonFormChild[])[]) => {
      return childrens.map((child, childIndex) => {
        return Array.isArray(child) ? (
          <ProForm.Group rowProps={{ gutter: 4 }} colProps={{ span: 20 }} title={childIndex === 0 && title}>
            {child.map((item) => getComponent(item, readonly))}
          </ProForm.Group>
        ) : (
          getComponent(child, readonly)
        );
      });
    },
    [readonly],
  );

  // 3) Export
  useImperativeHandle(commonRef, () => ({
    actionRef: actionRef.current,
    setFieldsValue: (params: Record<string, any>) => {
      actionRef?.current?.setFieldsValue(params);
    },
  }));

  return (
    <StyleWrapper>
      <ProForm
        className="common-form"
        formRef={actionRef}
        autoFocusFirstInput={false}
        {...formProps}
        submitter={{
          resetButtonProps: {
            style: {
              display: readonly ? 'none' : showBtn ? 'block' : 'none',
            },
          },
          submitButtonProps: {
            style: {
              display: readonly ? 'none' : showBtn ? 'block' : 'none',
            },
          },
          ...submitter,
        }}
      // labelCol={isEn ? { span: props.enSpan || 7 } : { span: props.cnSpan || 5 }}
      // wrapperCol={{ span: 18 }
      >
        {props.children
          ? (props.children as React.ReactNode)
          : columns
            .filter((v) => !!v)
            .map((column) => {
              // @ts-ignore
              if (column?.title || column?.childrens) {
                const { title, childrens } = column as CommonFormColumn;
                const hasArrChild = childrens.some((child) => Array.isArray(child));
                return (
                  <div className="form-item" key={title}>
                    {hasArrChild ? getChildren(title, childrens) : childrens.map((child) => getComponent(child, readonly))}
                  </div>
                );
              } else {
                return getComponent(column as CommonFormChild, readonly);
              }
            })}
      </ProForm>
    </StyleWrapper>
  );
});

export default CommonForm;
