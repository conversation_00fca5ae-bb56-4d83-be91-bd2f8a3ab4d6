import React, {useEffect} from 'react';
import {PlusOutlined} from '@ant-design/icons';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import moment from "moment";
import {executeSave, reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import Permission from "@/components/Permission";
import {Button, Form, Modal, notification} from "antd";
import {ProFormText} from "@ant-design/pro-components";

const TableList: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/purchase-mgmt-biz/purchase-center/warehouse/pageQuery', {
      ...params,
    });
  });
  const [form] = Form.useForm();
  const saveWarehouse=(record: any)=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form labelCol={{flex: '100px'}} form={form}>
          <ProFormText label="仓库" name="warehouseName"/>
        </Form>
      ),
      onOk: function () {
        const warehouseName = form.getFieldValue('warehouseName');
        executeSave("/purchase-mgmt-biz/purchase-center/warehouse/saveWarehouse",{warehouseName:warehouseName,id:record?.id}).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '操作成功'});
          }
        });

      },
    });

  }

  const removeWarehouse=(record: any)=>{

    reqByUrl("/purchase-mgmt-biz/purchase-center/warehouse/removeWarehouse",{id:record?.id}).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
        notification.success({message: '删除成功'});
      }
    });

  }


  const columns: ProColumns<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      align:"left",
    },
    {
      title: '仓库名称',
      dataIndex: 'warehouseName',
      align:"left",
    },
    {
      title: "创建时间",
      dataIndex: 'gmtCreate',
      hideInSearch: true,
      render: (v, record) => {
        return moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    {
      title: "修改时间",
      dataIndex: 'gmtModified',
      hideInSearch: true,
      render: (v, record) => {
        return moment(record?.gmtModified as number).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    {
      title: "操作",
      dataIndex: 'gmtCreate',
      align: "center",
      hideInSearch: true,
      render: (v, record) => {
        return (<>
          <Permission permissionKey={"purchase:warehouse:removeWarehouse"}>
            <Button type="primary" size={"small"} danger ghost style={{fontSize:12,borderRadius:5}} onClick={() =>  removeWarehouse(record)}>
              删除
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:warehouse:saveWarehouse"}>
            <Button type="primary" size={"small"} ghost  style={{fontSize:12,borderRadius:5,margin: "0 5px"}} onClick={() =>  saveWarehouse(record)}>
              编辑
            </Button>
          </Permission>
        </>)
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);
  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        size={"small"}
        columns={columns}
        toolBarRender={() => [
          <Permission permissionKey={"purchase:warehouse:saveWarehouse"}>
            <Button
              size={"small"}
              type="primary"
              onClick={() => {saveWarehouse({})}}
            >
              新增
            </Button>
          </Permission>
        ]}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;
