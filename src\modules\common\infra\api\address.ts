import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import type { AddressData, AddressType } from '../../domian/address';

export interface QuerySubAddressesParams {
  parentCode?: string;
  type: AddressType;
}

export async function querySubAddresses(params?: QuerySubAddressesParams) {
  return mallRequest<API.ApiBaseResult<AddressData[]>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/address/querySubAddresses',
    method: 'POST',
    data: params,
  });
}
