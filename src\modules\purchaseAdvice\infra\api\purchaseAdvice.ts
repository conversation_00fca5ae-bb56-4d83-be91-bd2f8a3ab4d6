import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "../../../../../config/mallApiConfig";
import {PurchaseAdviceBatch} from "@/pages/purchaseAdvice/data";

export type QueryAdviceParams ={
  batchCode?: string,
}& API.QueryPageParams;

export async function getPurchaseAdviceBatchList(data: QueryAdviceParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<PurchaseAdviceBatch[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/sales-mgmt-biz/sales-center/purchase-advice/pageQueryBatch',
      method: 'POST',
      data,
    },
  );
}

