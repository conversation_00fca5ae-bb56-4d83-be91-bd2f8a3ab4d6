import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "config/mallApiConfig";
import type { DepartmentTree } from "../domain/department";

export type GetDepartmentDetailParams = {
  deptId: string;
}

// 获取部门详情
export async function getDepartmentDetail(data?: GetDepartmentDetailParams) {
  return mallRequest<API.ApiBaseResult<DepartmentTree[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/organize/dept-biz/getById',
      method: 'POST',
      data,
    },
  );
}


//批量删除
export async function batchDelete(deptIds?: string[]) {
  return mallRequest<API.ApiBaseResult<null>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/organize/dept-biz/batchDelete',
      method: 'POST',
      data: { deptIds }
    },
  );
}

export type BatchUpdateParams = {
  deptIds: string[];
  parentId: string;
}

//批量更新
export async function batchUpdate(data: BatchUpdateParams) {
  return mallRequest<API.ApiBaseResult<null>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/organize/dept-biz/batchUpdate',
      method: 'POST',
      data
    },
  );
}

export enum DepartmentState {
  'ACTIVATED' = 'ACTIVATED',
  'DISABLED' = 'DISABLED',
}

export interface CreateDepartmentParams {
	code?: string;
	description?: string;
	name?: string;
	owner?: string;
	parentId: string | number;
	sort?: number;
	state?: DepartmentState;
}
//创建
export async function createDepartment(data: CreateDepartmentParams) {
  return mallRequest<API.ApiBaseResult<null>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/organize/dept-biz/create',
      method: 'POST',
      data
    },
  );
}


export interface GetDeptTreeByEndpointParams {
	code?: string;
	endpoint?: string;
	name?: string;
	orderBys?: OrderBy[];
	parentId?: string | number;
	state?: string;
}

export interface OrderBy {
	direction: number;
	filed: string;
}

// 获取组织树
export async function getDeptTreeByEndpoint(data: GetDeptTreeByEndpointParams) {
  return mallRequest<API.ApiBaseResult<DepartmentTree[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/organize/dept-biz/getDeptTreeByEndpoint',
      method: 'POST',
      data: {
        ...data,
        orderBys: [
          {
            direction: 1,
            filed: 'sequence',
          },
          // {
          //   direction: 0,
          //   filed: 'gmt_create',
          // },
        ]
      }
    },
  );
}

export interface UpdateDeptParams extends CreateDepartmentParams {
  deptId: string;
}

// 更新组织
export async function update(data: UpdateDeptParams) {
  return mallRequest<API.ApiBaseResult<null>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/organize/dept-biz/update',
      method: 'POST',
      data
    },
  );
}
