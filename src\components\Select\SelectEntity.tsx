import { Select } from 'antd';
import { memo, useEffect, useState } from 'react';
import { queryPurchasingEntity } from './api';

const SelectBrand = memo((props: Record<keyof any, any>) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);
  useEffect(() => {
    queryPurchasingEntity({ pageCondition: { pageNum: 1, pageSize: 500 } }).then((res) => {
      const { body } = res;
      setSelectOption(body?.items?.map((v) => ({ value: v.venderId, label: v.venderName })) || []);
    });
  }, []);

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择采购主体"
      optionFilterProp="children"
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
    />
  );
});

export default SelectBrand;
