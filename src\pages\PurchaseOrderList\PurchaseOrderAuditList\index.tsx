import React, {useEffect, useState} from 'react';
import {useRequestTable} from '@/hooks/useRequestTable';
import type {ProColumns} from '@ant-design/pro-components';
import {ProTable} from '@ant-design/pro-components';
import {Button, Space, Table, Tag} from 'antd';
import {PurchasePlanListItem} from "@/pages/PurchasePlanList/PurchasePlanTableList/data";
import {pageQueryOrderAudit} from "@/modules/purchaseOrder/infra/api/purchaseOrderAudit";
import {
  PurchaseOrderAudit,
  PurchaseOrderAuditRecord,
} from "@/pages/PurchaseOrderList/PurchaseOrderAuditList/data";
import {EyeOutlined, SnippetsTwoTone} from "@ant-design/icons";
import {Link} from "umi";
import {
  PurchaseOrderAuditStatusText,
  purchaseOrderAuditTypeEnum
} from "@/modules/purchaseOrder/domain/purchaseOrderAudit";
import moment from "moment";
import {copyText} from "@/utils/comUtil";
import {PurchaseOrderAuditStatus} from "@/modules/purchaseOrder/domain/purchaseOrder";
import CustomPage from "@/components/CustomPage";

export default () => {
  const [activeStatusKey, setActiveStatusKey] = useState<string>();

  const { fetchList, actionRef } = useRequestTable((params) => {
    return pageQueryOrderAudit({
      ...params,
      auditStatus: activeStatusKey,
    });
  });

  const columnsTab: ProColumns<PurchaseOrderAuditRecord, 'text'>[] = [
    {
      title: '审核流程',
      dataIndex: 'auditType',
      align:"left",
      width:"80px",
      render: (v, record) => {
        const auditType = purchaseOrderAuditTypeEnum[record?.auditType]
        return <Tag color='blue'>{auditType}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'auditStatus',
      align:"left",
      width:"80px",
      render: (v, record) => {
        const auditStatus = PurchaseOrderAuditStatusText[record?.auditStatus]
        const options = [
          auditStatus != undefined ? (
            <span><span className="ant-badge-status-dot ant-badge-status-success"/>
              <span className="ant-badge-status-text">{auditStatus}</span></span>) : null

        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '申请人',
      dataIndex: 'applyUsername',
      align:"left",
      width:"80px",
    },
    {
      title: '审核人',
      dataIndex: 'auditUsername',
      align:"left",
      width:"80px",
      render: (v, record) => {
        return (
          <>
            {record.auditUsername ? record.auditUsername : "/"}
          </>
        );
      }
    },
    {
      title: '申请时间',
      dataIndex: 'auditUsername',
      width:"80px",
      render: (v, record) => {
        return (
          <>
            {record.gmtCreate != null ? (<div>{moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm")}</div>) : '/'}
          </>
        );
      }
    },
    {
      title: '审核时间',
      dataIndex: 'auditUsername',
      width:"80px",
      render: (v, record) => {
        return (
          <>
            {record.gmtModified != null ? (<div>{moment(record.gmtModified as number).format("YYYY-MM-DD HH:mm")}</div>) : '/'}
          </>
        );
      }
    },



  ];
  const columns: ProColumns<PurchaseOrderAudit, 'text'>[] = [
    {
      title: '采购单',
      dataIndex: 'orderCode',
      colSize: (4 / 24),
      render: (v, record) => {
        return (
          <>
            <div> 采购单号：{record.orderCode}<SnippetsTwoTone onClick={() => copyText(record.orderCode)}/></div>
            <div> 标题：{record.title}</div>
            <div> 备注：{record.remark}</div>
            <div> 供应商：{record.supplierName}</div>
            <div> 采购员：{record.purchaseUsername}</div>
            <div> 跟单员：{record.merchandiserUsername}</div>
          </>
        );
      }
    },
    {
      title: '审核流程/状态/申请人/审核人/申请时间/审核时间',
      dataIndex: 'goodsSku',
      hideInSearch: true,
      width:1000,
      align:"center",
      render: (v, record) => {
        return (
          <>
            <Table
              showHeader={false}
              dataSource={record.purchaseOrderAuditRecordInfoList} columns={columnsTab}
              rowKey="id"
              size={'small'}
              pagination = {false}
            />
          </>
        );
      }

    }
  ];


  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [activeStatusKey]);

  return (
    <>
        <CustomPage<PurchasePlanListItem>
          actionRef={actionRef}
          request={fetchList}
          columns={columns}
          size={'small'}
          rowKey="supplierName"
          bordered
          search={{
            filterType: 'query',
            layout: 'horizontal',
            span: 24,
            collapseRender: () => null,
            defaultCollapsed: false,
            optionRender: (_, c, dom) => {
              const options = [
                <Link key="show" to={`/purchase/purchase/purchaseOrderAudit/priceCheck`}>
                  <Button key="level" type="primary" >
                    采购审核
                  </Button>
                </Link>,
                <Link key="show" to={`/purchase/purchase/purchaseOrderAudit/financeCheck`}>
                  <Button key="level" type="primary">
                    财务审核
                  </Button>
                </Link>
              ];
              return [...options, ...dom];
            }}}
          revalidateOnFocus={false}
          recordCreator={false}
          recordDelete={false}
          recordUpdater={false}
        />

    </>
  );
};
