import { useRole } from '@/modules/user-center/application/role';
import { Role } from '@/modules/user-center/domain/user';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { Form } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';

export type CreateOrUpdateRoleActions = {
  create: () => void;
  editRow: (row: Role) => void;
};

type CreateOrUpdateFormProps = {
  onFinish?: () => void;
};

const CreateOrUpdateRoleForm = forwardRef(
  (props: CreateOrUpdateFormProps, ref) => {
    const { onFinish } = props;
    const [modalVisible, setModalVisible] = useState<boolean>(false);
    const [form] = Form.useForm();
    const roleService = useRole();
    const [currentRow, setCurrentRow] = useState<Role>();
    const actions: CreateOrUpdateRoleActions = {
      create: () => {
        setCurrentRow(undefined);
        setModalVisible(true);
      },
      editRow: (row) => {
        setCurrentRow(row);
        setModalVisible(true);
      },
    };
    useImperativeHandle(ref, () => {
      return actions;
    });
    const title = useMemo(() => {
      return currentRow?.roleId ? '编辑角色' : '新建角色';
    }, [currentRow]);
    const handleSubmit = async (values: any) => {
      let success;
      if (currentRow?.roleId) {
        success = await roleService.update({
          ...values,
          roleId: currentRow.roleId,
        });
      } else {
        success = await roleService.create(values);
      }
      if (success && onFinish) {
        onFinish();
      }
      return success;
    };
    useEffect(() => {
      if (!modalVisible) {
        form.resetFields();
      }
      if (currentRow && modalVisible) {
        form.setFieldsValue(currentRow);
      }
    }, [modalVisible, currentRow]);

    return (
      <ModalForm
        title={title}
        visible={modalVisible}
        onVisibleChange={setModalVisible}
        layout="horizontal"
        onFinish={handleSubmit}
        width={500}
        form={form}
      >
        <ProFormText
          label="角色名称"
          name="roleName"
          rules={[{ required: true, message: '请输入角色名称' }]}
        />
        <ProFormTextArea
          label="角色描述"
          name="roleDesc"
          rules={[{ required: true, message: '请输入角色描述' }]}
          fieldProps={{
            maxLength: 200,
            showCount: true,
          }}
        />
      </ModalForm>
    );
  },
);

export default CreateOrUpdateRoleForm;
