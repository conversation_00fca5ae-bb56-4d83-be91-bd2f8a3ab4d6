import { Button, message, Spin } from 'antd'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import CommonForm from '@/components/Common-UI/CommonForm';
import { FooterToolbar } from '@ant-design/pro-components';
import { history, useLocation, useParams } from 'umi';
import BaseInfo from "./components/BaseInfo"
import SettlementInfo from './components/SettlementInfo';
import GoodsInfo from './components/GoodsInfo';
import "./index.less"
import { queryCreate, queryDetail, queryUpdate } from './api';
import type { CSI, QueryCreateParams, QueryUpdateParams } from './api';
import type { CommonFormAction } from '@/components/Common-UI/CommonForm/type';
import { isUndef, getFloat } from '@/utils/utils';

const PurchaseAdd = memo(() => {
  const { id } = useParams<{ id: string }>();
  const { state = {} } = useLocation()
  // @ts-ignore
  const isCheck = useMemo(() => !!state.isCheck, [state.isCheck])
  const [data, setData] = useState<CSI[]>([])
  const [detail, setDetail] = useState<any>({})
  const [submitLoading, setSubmitLoading] = useState(false);
  const formRef = useRef<CommonFormAction>();

  // 1) 保存提交
  const validData = async () => {
    if (data.length === 0) {
      message.warn("请添加至少一个商品")
      return Promise.reject();
    }
    const unhandleIndex = data.findIndex((v) => isUndef(v.purchaseQuantity) || isUndef(v.price));
    if (unhandleIndex !== -1) {
      message.warn(`请填写完成第${unhandleIndex + 1}个商品再进行保存`);
      return Promise.reject();
    }
  };
  const onSubmit = async () => {
    try {
      // 0 校验数据
      await formRef.current?.actionRef?.validateFields();
      await validData();
      setSubmitLoading(true);
      setTimeout(() => setSubmitLoading(false), 2000);
      const formData = formRef.current?.actionRef?.getFieldsValue();
      // 2 构建参数
      const _params1: QueryCreateParams = {
        ...formData,
        orderGoodsDetailDirectCreateRequestList: data.map(v => ({
          sku: v.supplierSkuId,
          price: v.price,
          purchaseQuantity: v.purchaseQuantity
        }))
      };
      const _params2: QueryUpdateParams = {
        ...formData,
        purchaseOrderGoodsDetailUpdateRequestList: data.map(v => ({
          sku: v.supplierSkuId,
          price: v.price,
          purchaseQuantity: v.purchaseQuantity
        }))
      };
      // 3 发送请求
      console.log(formData, data);
      const res = id ? await queryUpdate({ ..._params2, id }) : await queryCreate(_params1);
      if (res.status.success) {
        message.success(id ? '更新成功' : '创建成功');
        id ? updatePage() : history.goBack()
      }
    } catch (error) {
      setSubmitLoading(false);
    }
  };

  // 2) 初始化数据
  const updatePage = useCallback(() => {
    if (id) {
      setSubmitLoading(true);
      queryDetail({ purchaseOrderId: id }).then((res) => {
        console.log("res", res.body);
        // 1 基础信息
        formRef.current?.setFieldsValue(res.body || {})
        // 2 商品信息
        setData(res.body?.purchaseOrderGoodsList.map(v => ({
          saleItemId: v.id,
          mainImages: [v.mainImage],
          supplierSkuId: v.sku,
          title: v.name,
          specs: [{ specName: v.attrName, specValue: v.attrValue }],
          categoryName: v.categoryName,
          purchaseQuantity: v.purchaseQuantity,
          price: v.currentPurchasePrice,
          defaultSupplierItemDetailInfo: { supplyPriceCurrency: res.body.settleCurrency },
          currency: v.currency,
          costPrice: v.costPrice,
          expenses: !res.body.shippingFee ? undefined : getFloat((v.costPrice - v.currentPurchasePrice), 2)
        } as any)) || [])
        // 3 停止刷新
        setDetail(res.body)
        setSubmitLoading(false);
      });
    }
  }, [id])
  useEffect(() => updatePage(), [id, updatePage]);
  // useEffect(() => {
  //   formRef.current?.setFieldsValue({
  //     "supplierId": "VD1066489165809592176640",
  //     "supplierName": "20240717陈志豪供应商",
  //     "purchaseWarehouse": "W6879193844346599116800",
  //     "purchaseEntity": "VD0977193819580383236096",
  //     "detailAddress": "228创意园",
  //     "remark": "123213",
  //     "estimatedTimeArrival": "2024-09-03T16:00:00.318Z",
  //     "settleType": "DELIVERY_ON_ARRIVAL",
  //     "settleCircle": "ONLINE7",
  //     "settleCurrency": "CNY",
  //     "payType": "CROSS_BORDER",
  //     "transportationType": "自提",
  //     "freightBearer": "供应商",
  //     "trackingNumber": "*********"
  //   })
  // }, [])

  // 3) 采购主体改变
  const onCompanyChange = () => {
    setData([])
  }

  return (
    <Spin spinning={submitLoading}>
      <div className='page-purchase-add'>
        <CommonForm layout="horizontal" commonRef={formRef} showBtn={false} disabled={isCheck}>
          <div className="content-box">
            <BaseInfo formRef={formRef} id={id} onCompanyChange={onCompanyChange} orderCode={detail?.orderCode} />
            <SettlementInfo />
            <GoodsInfo data={data} setData={setData} formRef={formRef} isCheck={isCheck} detail={detail} />
          </div>
        </CommonForm>
        <FooterToolbar>
          <Button type="ghost" loading={submitLoading} onClick={() => history.goBack()}>
            取消
          </Button>
          {!isCheck && <Button type="primary" loading={submitLoading} onClick={onSubmit}>
            {id ? '更新' : '保存'}
          </Button>}
        </FooterToolbar>
      </div>
    </Spin>
  )
})

export default PurchaseAdd
