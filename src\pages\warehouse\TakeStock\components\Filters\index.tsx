import type { CheckboxGroupProps } from 'antd/lib/checkbox';
import type { InputProps, SelectProps } from "antd";
import { AutoComplete, Checkbox, Input, Row, Select } from "antd";
import type { CheckboxOptionType } from 'antd/es';
import styles from './styles.less'
import { useEffect } from 'react';
import cn from 'classnames';
import { useControllableValue } from 'ahooks';

const CheckboxButton = (props: CheckboxGroupProps) => {
  const { options, ...rest } = props;
  useEffect(() => {
    if (props?.defaultValue) {
      props?.onChange?.(props.defaultValue)
    }
  }, [])
  return <Checkbox.Group className={styles.checkbox__button} {...rest} defaultValue={[0]}>
    {options?.map((item) => {
      return <Checkbox key={(item as CheckboxOptionType).value?.toString()}
        value={(item as CheckboxOptionType).value}
        className={cn({ current: props.value?.includes((item as CheckboxOptionType).value) })}
      >
        {(item as CheckboxOptionType).label}
      </Checkbox>
    })}
  </Checkbox.Group>
}


const SupplierSelect = (props: InputProps) => {
  const [value, onChange] = useControllableValue(props);

  return <Input.Group>
    <Row align='middle'>
      <AutoComplete style={{ width: 'calc(100% - 32px)' }} value={value} onChange={onChange} />
      <a style={{ marginLeft: 4 }}>选择</a>
    </Row>
  </Input.Group>
}


export type SelectInputProps = {
  options: SelectProps['options'],
  defaultValue: SelectProps['defaultValue'];
  placeholder?: string;
  value?: [string, string];
  onChange?: (value?: [string, string]) => void;
}
const SelectInput = (props: SelectInputProps) => {
  const { placeholder, defaultValue } = props;
  const [value, setValue] = useControllableValue(props);
  useEffect(() => {
    setValue([defaultValue, ''])
  }, [])
  return <Input.Group compact>
    <Select value={value?.[0]} defaultValue={defaultValue} options={props.options} onChange={(v) => setValue([v, value?.[1]])} />
    <Input value={value?.[1]} style={{ width: '50%' }} placeholder={placeholder} onChange={(e) => {
      console.log([value?.[0], e.target.value])
      setValue([value?.[0], e.target.value])
    }} />
  </Input.Group>
}


export default {
  CheckboxButton,
  SupplierSelect,
  SelectInput
};
