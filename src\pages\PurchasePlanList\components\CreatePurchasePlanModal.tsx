import {ProFormSelect, ProFormField, ProFormTextArea} from "@ant-design/pro-form";
import {Form, ModalProps, Space} from "antd";
import { Modal } from "antd";
import {
  CreatePlanParmas,
  downloadPurchasePlanTemplate,
  getWarehouse
} from "@/modules/purchasePlan/infra/api/purchasePlan";
import UploadFile from "@/components/UploadFile";
import mallApiConfig from "../../../../config/mallApiConfig";
import {commonExport} from "@/utils/comUtil";

// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: CreatePlanParmas) => void;
} & ModalProps;


const CreatePurchasePlanModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, ...rest } = props;


  return <Modal {...rest} title="导入采购计划" onOk={()=>form.submit()}>
    <Form form={form} onFinish={onFinish}>
      <ProFormSelect
        width="md"
        name="warehouseName"
        label="仓库"
        request={async () =>  {//返回的select网络请求
          let params = await getWarehouse();
          console.log('获取到的下拉省分为：',params);
          let res = [];
          var body = params.body;
          for(var i in body){
            let temp = {};
            temp['label'] = body[i];
            temp['value'] = body[i];
            res.push(temp)
          }
          console.log('最终组装的数据=',res);
          return res;
        }}
      />
      <ProFormTextArea
        width="md"
        name="remark"
        label="备注"
      />
      <Space>
      <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
        <UploadFile limit={1} />
      </ProFormField>
      <a  style={{marginLeft:10}} onClick={function () {
        downloadPurchasePlanTemplate().then(res=>{
          commonExport(res, '采购计划导入模板');
        });
      }}>下载模板</a>
      </Space>
    </Form>
  </Modal>
}

export default CreatePurchasePlanModal;
