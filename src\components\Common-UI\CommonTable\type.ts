import type { FormInstance, SpinProps, TableProps } from 'antd';
import type { ActionType, ProColumns, ProTableProps, RequestData } from '@ant-design/pro-table';
import type { RowSelectMethod, SortOrder, TablePaginationConfig } from 'antd/lib/table/interface';
import type { ParamsType } from '@ant-design/pro-provider';
import type { PageInfo } from '@ant-design/pro-table/lib/typing';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import type React from 'react';

export type CommonTableAction = {
  tab: string; // 当前 tag
  dataSource: any[]; // 表格数据
  actionRef: ActionType | undefined; // proTable ref
  formRef: FormInstance | undefined; // proTable ref
  reload: () => void; // 表单刷新函数
  setPageInfo: (page: Partial<PageInfo>) => void;
  reloadTable: () => void; // 刷新并清除选择项
  resetAndReloadTable: () => void; // 刷新筛选项并清除选择项
  initSelect: (keys: React.Key[]) => void;
  initTable: () => void; // 初始化表格项，数据设置为空
};

export type CommonTableProps<T> = {
  /* ProTable */
  // 1 分页
  pagination?: false | TablePaginationConfig;
  // 2 选择项（当存在时，初始化可选）
  rowKey?: keyof T;
  onSelection?: (selectedRowKeys: React.Key[], selectedRows: T[], info: { type: RowSelectMethod }) => void;
  showChooseCount?: boolean;
  // 3 搜索栏
  search?: false | SearchConfig;
  labelWidth?: number | string | [number | undefined, number];
  // 4 请求函数
  fetchRequest?: (
    params: T & {
      pageSize: number;
      current: number;
      keyword?: string;
      tab?: string;
    },
    sort: Record<string, SortOrder>,
    filter: Record<string, React.ReactText[] | null>,
  ) => Promise<RequestData<T>>;
  showCount?: boolean; // 显示数量
  // 5 表格列
  columns?: ProColumns<T>[];
  actions?: {
    layout?: 'vertical' | 'tile';
    align?: 'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly';
    lastindex?: number;
    fixed?: 'right' | boolean;
    width?: number;
    items: {
      name: string;
      title?: string | ((row: T) => string); // 显示为气泡卡片
      onAction?: (row: T & { faRow?: T }) => void;
      show?: (row: T & { faRow?: T }) => boolean;
      disabled?: (row: T & { faRow?: T }) => boolean;
      color?: string;
      render?: (row: T & { faRow?: T }, index: number) => React.ReactNode;
    }[];
  }; // 操作列基本每个表格都有，考虑抽出编写
  // 6 toolbar 工具栏（只需要传一个，方便布局）
  toolBarRender?: (action: ActionType | undefined, rows: { selectedRowKeys?: (string | number)[]; selectedRows?: T[] }) => React.ReactNode;
  // 7 数据源，传了不需要传请求，用于直接展示
  dataSource?: ProTableProps<T, ParamsType, 'text'>['dataSource'];
  // 8 加载
  loading?: boolean | SpinProps;
  // 9 固定列（使用插入 row 前后的 DOM 无法使用，会导致异常）
  scrollX?: number; // 固定列
  scrollY?: number | [number, number]; // 固定表头
  fixedTop?: number | boolean; // 固定表头
  flex?: boolean; // 是否弹性布局
  autoScrollX?: boolean; // 是否自动计算可滚动宽度
  defaultPageSize?: string | number; // 默认页码
  // 更多 ProTable 选项
  tableRest?: ProTableProps<T, ParamsType, 'text'>;

  /* 表格样式 */
  bg?: string | false; // 鼠标 hover 时背景颜色
  isUnLabel?: boolean; // 表格搜索栏无 label

  /* Tab（当存在时，初始化 tab 栏） */
  tabOption?: { key: string; tab: string }[];

  /* Component */
  tableRef?: React.Ref<CommonTableAction | undefined> | undefined;

  /* Row insert */
  insertBefore?: (row: T & { faRow: T }) => React.ReactNode;
  insertAfter?: (row: T & { faRow: T }) => React.ReactNode;
  renderProp?: string; // 插入后渲染的表格数据
  rowTableRest?: ProTableProps<T, ParamsType, 'text'>; // 子表格的属性
  actionMerge?: boolean; // 操作合并，操作父row
};

export type UseColumnProps<T> = {
  searchLabel?: string; // 搜索栏的文本
  searchProp?: string; // 搜索栏转换属性
  wrap?: boolean; // 是否换行
  format?: (value: any) => any; // 转换值

  // 是否图片
  isImage?: boolean;
  imgW?: number;
  imgH?: number;
} & ProColumns<T>;

export type UseSearchConfig<T> = {
  isUnLabel?: boolean;
} & ProColumns<T>;
