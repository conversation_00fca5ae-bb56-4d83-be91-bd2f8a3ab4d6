import useTableSelection from '@/hooks/useTableSelection';
import { useRequestSubCategory } from '@/modules/tags/application/tags';
import type { CategoryData } from '@/modules/tags/domain/tags';
import type { ActionType } from '@ant-design/pro-table';
import type { DataNode } from 'antd/lib/tree';
import { useEffect, useRef } from 'react';

const getCategoryKeys = (rows: CategoryData[]) => {
  return rows.map((item) => item?.categoryId);
};

export function useCategoryTable(selectedCategory?: DataNode) {
  const fetchSubCategory = useRequestSubCategory(selectedCategory);
  const actionRef = useRef<ActionType>();
  const {
    selectedRowKeys,
    setSelectedRowKeys,
    selectedRowsState,
    setSelectedRows,
    rowSelection: defaultRowSelection,
  } = useTableSelection<CategoryData>();

  const clearSelect = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const changeSelectRows = (row: CategoryData[]) => {
    setSelectedRows(row);
    setSelectedRowKeys(getCategoryKeys(row));
  };

  // 自定义全选按钮，如果列表存在父级分类，则全选==选择子级分类
  // 如果选择子级分类，则取消父级分类的选中按钮;
  // 如果选择父级分类，则取消子级分类的选中按钮
  const rowSelection = {
    ...defaultRowSelection,
    onSelect: (row: CategoryData, selected: boolean, selectedRows: CategoryData[]) => {
      if (!selected) {
        changeSelectRows(selectedRows);
        return;
      }
      // 如果是存在父级分类
      if (selectedCategory?.key) {
        if (
          row.categoryId === selectedCategory.key ||
          selectedRowKeys.includes(selectedCategory.key)
        ) {
          changeSelectRows([row]);
        } else {
          changeSelectRows(selectedRows);
        }
      } else {
        changeSelectRows(selectedRows);
      }
    },
    onSelectAll: (isSelectAll: boolean, rows: CategoryData[]) => {
      if (isSelectAll) {
        if (selectedCategory?.key) {
          const subCategoryRow = rows.filter((item) => item.categoryId !== selectedCategory.key);
          if (subCategoryRow.length === selectedRowKeys.length) {
            clearSelect();
          } else {
            changeSelectRows(subCategoryRow);
          }
        } else {
          changeSelectRows(rows);
        }
      } else {
        clearSelect();
      }
    },
    onChange: () => {},
  };

  useEffect(() => {
    if (selectedCategory?.key) {
      actionRef.current?.reset?.();
      actionRef.current?.reload?.();
    }
    setSelectedRows([]);
  }, [selectedCategory]);
  return {
    fetchSubCategory,
    actionRef,
    selectedRowsState,
    setSelectedRows,
    rowSelection,
  };
}
