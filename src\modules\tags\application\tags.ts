import type { CategoryData } from '@/modules/tags/domain/tags';
import { updateTreeData } from '@/modules/tags/domain/tags';
import type {
  CreateCategoryParams,
  MoveCategoryParams,
  QueryPageSubCategoryParams,
  UpdateCategoryParams,
} from '@/modules/tags/infra/api/tags';
import {
  createCategory,
  deleteCategory,
  moveCategory,
  pageQuerySubCategory,
  queryCategoryInfo,
  querySubCategory,
  updateCategory,
} from '@/modules/tags/infra/api/tags';

import type { ProTableProps } from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import { useCallback, useEffect, useState } from 'react';

export function useCategory() {
  const update = async (values: UpdateCategoryParams) => {
    try {
      const res = await updateCategory(values);
      if (res.status.success) {
        message.success('操作成功');
      } else {
        message.error(res.status.message);
      }
      return res.status.success;
    } catch (error) {
      message.error('操作失败');
      return false;
    }
  };

  const batchUpdate = async (values: MoveCategoryParams) => {
    try {
      const res = await moveCategory(values);
      if (res.status.success) {
        message.success('操作成功');
      } else {
        message.error(res.status.message);
      }
      return res.status.success;
    } catch (error) {
      message.error('操作失败');
      return false;
    }
  };

  const create = async (params: CreateCategoryParams) => {
    try {
      const res = await createCategory(params);
      if (res.status.success) {
        message.success('操作成功');
      } else {
        message.error(res.status.message);
      }
      return res.status.success;
    } catch (error) {
      message.error('操作失败');
      return false;
    }
  };

  const remove = async (params: string[]) => {
    try {
      const res = await deleteCategory(params);
      if (res.status.success) {
        message.success('操作成功');
      } else {
        message.error('操作失败');
      }
      return res.status.success;
    } catch (error) {
      message.error('操作失败');
      return false;
    }
  };

  return {
    update,
    batchUpdate,
    create,
    remove,
  };
}

export function useRequestSubCategory(selectedCategory?: DataNode) {
  const fetchSubCategory: ProTableProps<CategoryData, any>['request'] = useCallback(
    (params) => {
      const { name } = params;
      const options: QueryPageSubCategoryParams = {
        parentCategoryId: selectedCategory?.key || 0,
        pageCondition: {
          pageNum: params.current || 1,
          pageSize: selectedCategory ? params.pageSize - 1 : params.pageSize,
        },
      };
      if (name) {
        options.name = name;
      }

      return pageQuerySubCategory(options).then((res) => {
        let total = 0;
        const { items, pageMeta } = res.body;
        if (items && items.length === 1 && !pageMeta) {
          return {
            total: items.length,
            data: items,
            success: res.status.success,
          };
        }
        if (!items || !pageMeta) {
          return {
            total: 0,
            data: [],
            success: true,
          };
        }
        total = res.body.pageMeta?.total || res.body.items.length;
        const page = res.body.pageMeta?.pages || 0;
        if (selectedCategory?.key) {
          total = total + page;
        }
        return {
          total: total,
          data: res.body.items || [],
          success: res.status.success,
        };
      });
    },
    [selectedCategory],
  );
  return fetchSubCategory;
}

export function useRootCategory() {
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const initTreeData = () => {
    setLoading(true);
    querySubCategory({ parentCategoryId: 0 })
      .then((res) => {
        setTreeData(
          (res.body || []).map((item) => {
            return {
              key: item.categoryId,
              title: item.name,
              isLeaf: Boolean(item.leaf),
              ...item,
            };
          }),
        );
      })
      .finally(() => setLoading(false));
  };
  const loadDataById = ({ key, children }: any) =>
    new Promise<void>((resolve) => {
      if (children) {
        resolve();
        return;
      }
      querySubCategory({ parentCategoryId: key })
        .then((res) => {
          setTreeData((origin) =>
            updateTreeData(
              origin,
              key,
              res.body?.map((item) => ({
                key: item.categoryId,
                title: item.name,
                isLeaf: Boolean(item.leaf),
                ...item,
              })),
            ),
          );
        })
        .finally(() => {
          resolve();
        });
    });
  useEffect(() => {
    initTreeData();
  }, []);
  return {
    treeData,
    setTreeData,
    loading,
    initTreeData,
    loadDataById,
  };
}

export function useCategoryInfo(category?: CategoryData) {
  const {
    data: categoryInfo,
    loading,
    refresh: loadCategoryInfo,
  } = useRequest(() => queryCategoryInfo(category?.categoryId as string).then((res) => res.body), {
    manual: true,
  });

  useEffect(() => {
    if (category?.categoryId) {
      loadCategoryInfo();
    }
  }, [category?.categoryId]);

  return {
    categoryInfo,
    loading,
    loadCategoryInfo,
  };
}

