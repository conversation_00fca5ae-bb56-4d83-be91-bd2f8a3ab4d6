import { Space, Typography } from 'antd';
import SelectOrCreate from '../../../SelectOrCreate';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import { useState } from 'react';
import { useEffect } from 'react';
import { useMemo } from 'react';
import { find } from 'lodash';
import { useUserCustomSpec } from '@/modules/product/application/categorySpec';
import type { SpecItemData } from '../../../CategorySpecField';
import type { ProductSpecData } from '@/modules/product/domain/categorySpec';

export interface SpecEditData extends SpecItemData {
  disabled?: boolean;
}

export interface SpecItemEditProps {
  value?: SpecItemData;
  onChange?: (data?: SpecItemData) => void;
  specList: ProductSpecData[];
  onReload?: () => Promise<void>;
  disabled?: boolean;
  showCreate?: boolean;
  categoryId?: string;
  required?: boolean;
}

const SpecItemEdit = (props: SpecItemEditProps) => {
  const userCustomSpecDomain = useUserCustomSpec();
  const { specList, onReload, showCreate = true, categoryId } = props;
  const [value, onChange] = useMergedState(undefined, {
    value: props.value,
    onChange: props.onChange,
  });
  const [specValues, setSpecValues] = useState<string[]>([]);
  const options = useMemo(() => {
    const allOptions = specList?.map((item) => {
      return {
        value: item.name,
        label: item.name,
        ...item,
      };
    });
    return allOptions;
  }, [specList]);
  const valuesOptions = useMemo(() => {
    const allOptions = specValues.length>0?specValues.map((item) => {
      return {
        value: item,
        label: item,
      };
    }): null;
    return allOptions;
  }, [value?.specId, value?.specValues, specValues, specList]);

  useEffect(() => {
    if (value?.specId) {
      userCustomSpecDomain.getValues(value?.specId).then((res) => {
        setSpecValues(res.body || []);
      });
    }
  }, [JSON.stringify(value)]);

  const handleCreateName = async (val: any) => {
    const res = await userCustomSpecDomain.addSpec({
      categoryId: categoryId as string,
      name: val,
      values: [],
    });
    if (onReload) {
      await onReload();
    }
    onChange({
      specId: res.body,
      specName: val,
      required: false,
      specValues: [],
    });
    return res.body;
  };

  const handleCreateValues = async (val: any) => {
    if (value?.specId) {
      await userCustomSpecDomain.addValue({ specId: value?.specId, value: val });
      const valueRes = await userCustomSpecDomain.getValues(value.specId);
      setSpecValues(valueRes.body || []);
      onChange({
        ...value,
        specValues: [...(value?.specValues || []), val],
      });
    }
    return val;
  };

  return (
    <Space>
      <div style={{ width: 5 }}>
        {value?.required ? <Typography.Text type="danger">*</Typography.Text> : null}
      </div>
      <SelectOrCreate
        style={{ width: 250 }}
        value={value?.specName}
        options={options}
        onCreate={handleCreateName}
        showCreate={showCreate}
        onSelect={(name: any) => {
          const currentParams = find(options, { name });
          onChange({
            ...currentParams,
            specId: currentParams?.specId,
            specName: currentParams?.label || '',
            specValues: [],
          });
        }}
      />
      <SelectOrCreate
        mode="multiple"
        style={{ minWidth: 350 }}
        value={value?.specValues}
        options={valuesOptions}
        onCreate={handleCreateValues}
        showCreate={showCreate}
        onChange={(val) => {
          onChange({ ...value, specValues: val } as any);
        }}
      />
    </Space>
  );
};

export default SpecItemEdit;
