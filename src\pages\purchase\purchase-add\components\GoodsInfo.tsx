import CommonTable from '@/components/Common-UI/CommonTable';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useColumn } from '@/components/Common-UI/CommonTable/hook';
import { Button, Image, InputNumber, message, Row } from 'antd';
import GoodsAddModal from './GoodsAddModal';
import type { CommonTableAction } from '@/components/Common-UI/CommonTable/type';
import type { CommonModalAction } from '@/components/Common-UI/CommonModal';
import type { CommonFormAction } from '@/components/Common-UI/CommonForm/type';
import type { CSI } from '../api';
import CommonShow from '@/components/Common-UI/CommonShow';
import DetailInfo from './DetailInfo';
import { getFloat, onMultiply } from '@/utils/utils';

const GoodsInfo = memo((props: { data: CSI[]; setData: (data: CSI[]) => void, formRef: React.MutableRefObject<CommonFormAction | undefined>, isCheck: boolean, detail: any }) => {
  const { data, setData, formRef, isCheck, detail } = props;
  const includeIds = useMemo(() => data.map((v) => v.saleItemId), [data]);
  const tableRef = useRef<CommonTableAction>();
  const setColumn = useColumn<CSI>({ align: 'center' });
  const addRef = useRef<CommonModalAction>();
  const [show, setShow] = useState(1)
  const [updating, forceUpdate] = useState({})
  const purchaseCount = useMemo(() => {
    const counts = data.filter(v => v.purchaseQuantity).map(v => v.purchaseQuantity)
    return counts.length ? counts.reduce((v1, v2) => Number(v1) + Number(v2)) : 0
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, updating])
  const purchaseAmount = useMemo(() => {
    const counts = data.filter(v => v.purchaseQuantity && v.price).map(v => getFloat(onMultiply(v.price, v.purchaseQuantity), 2))
    return counts.length ? counts.reduce((v1, v2) => Number(v1) + Number(v2)) : 0
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, updating])

  // 1) Column
  const columns = useMemo(() => {
    return [
      setColumn('商品信息', '', {
        render: (_, row) => {
          return <div className='goods-info-box'>
            <Image src={row?.mainImages?.[0]} width={80} height={80} />
            <div className="info-box">
              <div>{row.title}</div>
              <div>类目：{row.categoryName}</div>
              {/* <div>品牌：{row.brandName}</div> */}
              <div>规格：{row?.specs?.map(v => `${v.specName}/${v.specValue}`).join("、")}</div>
              {/* <div>商家SPU：{row?.supplierSpuId || "-"}</div> */}
              <div>商家SKU：{row?.supplierSkuId || "-"}</div>
            </div>
          </div>
        },
        width: 350
      }),
      setColumn('采购数量', 'purchaseQuantity', {
        render: (_, row) => {
          return (
            <InputNumber
              value={row.purchaseQuantity}
              style={{ width: "150px" }}
              min={1}
              onChange={(val) => {
                if (typeof val === 'number') {
                  row.purchaseQuantity = val;
                  forceUpdate({});
                }
              }}
            />
          );
        },
      }),
      setColumn('采购单价', 'price', {
        render: (_, row) => {
          return (
            <InputNumber
              addonBefore={row?.defaultSupplierItemDetailInfo?.supplyPriceCurrency}
              value={row.price}
              min={0.01}
              precision={2}
              style={{ width: "150px" }}
              onChange={(val) => {
                if (typeof val === 'number') {
                  row.price = val;
                  forceUpdate({});
                }
              }}
            />
          );
        },
      }),
      setColumn('采购总金额', 'allPrice', {
        render: (_, row) => {
          if (!row.price || !row.purchaseQuantity) return "-"
          return `${row?.defaultSupplierItemDetailInfo?.supplyPriceCurrency} ${getFloat(onMultiply(row.price, row.purchaseQuantity), 2)}`
        }
      }),
      setColumn('单个分摊费用', 'expenses', { hideInTable: !isCheck }),
      setColumn('商品成本价', 'costPrice', { hideInTable: !isCheck, render: (_, row) => `${row.currency}${row.costPrice}` }),
    ];
  }, [isCheck, setColumn]);

  // 2) 新增商品
  const onConfirm = (goods: CSI[]) => {
    const newItems: any[] = goods.filter((item) => !includeIds.includes(item.saleItemId));
    setData([...data, ...newItems]);
  }

  // 3) Operate
  const actions = useMemo(() => {
    return {
      items: [
        {
          name: '删除',
          title: "是否确定删除该商品",
          onAction: (row: any) => { setData([...data.filter(item => item !== row)]) },
        },
      ],
      align: "center",
      width: 100
    };
  }, [data, setData]);

  return (
    <div className="goods-info card">
      <div className="title">
        <p>
          <span className={show === 1 ? 'active' : ''} onClick={() => setShow(1)}>商品信息</span>
          {props.isCheck && <span className={show === 2 ? 'active' : ''} onClick={() => setShow(2)}>采购入库明细</span>}
        </p>
        <CommonShow show={!isCheck}>
          <Button type="primary" onClick={() => {
            const purchaseEntity = formRef.current?.actionRef?.getFieldValue("purchaseEntity")
            if (!purchaseEntity) {
              message.error("请先选择采购公司主体")
              return
            }
            addRef.current?.open(purchaseEntity)
          }}>
            新增商品
          </Button>
        </CommonShow>
      </div>
      <CommonShow show={show === 1}>
        <CommonTable<CSI>
          rowKey="saleItemId"
          tableRef={tableRef}
          dataSource={data}
          columns={columns}
          actions={isCheck ? undefined : (actions as any)}
          tableRest={{
            search: false,
            toolBarRender: false,
          }}
        />
        <div className="show">已选商品：{data.length}，采购数量：{purchaseCount}，采购总金额：{purchaseAmount}</div>
        <GoodsAddModal ref={addRef} onConfirm={onConfirm} />
      </CommonShow>
      {props.isCheck && <CommonShow show={show === 2}>
        <DetailInfo purchaseOrderCode={detail?.orderCode} />
      </CommonShow>}
    </div>
  );
});

export default GoodsInfo;
