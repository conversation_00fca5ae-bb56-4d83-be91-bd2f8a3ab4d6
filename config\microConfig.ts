export interface MallApiConfigData {
  purchaseCenterUrl?: string;
  productDevelopmentCenterUrl?: string;
  userCenterUrl?: string;
}
const { REACT_APP_ENV } = process.env;
const mallApiConfig: { [key: string]: MallApiConfigData } = {
  fat: {
    purchaseCenterUrl: 'http://gsp-purchase-fat.newnary.cn',
    productDevelopmentCenterUrl: 'http://gsp-pd-fat.newnary.cn',
    userCenterUrl: 'http://fat.newnary.cn/user/',
  },
  pro: {
    userCenterUrl: 'http://pro.newnary.cn/user/',
    // purchaseCenterUrl: 'http://api-gsp-uc.newnary.cn',
    // productDevelopmentCenterUrl: 'http://api-gsp-op.newnary.cn',
  },
};

export default mallApiConfig[REACT_APP_ENV || 'fat'] || mallApiConfig['fat'];
