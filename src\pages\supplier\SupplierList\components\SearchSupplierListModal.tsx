import type {ProColumns} from '@ant-design/pro-table';
import React from 'react';
import {
  Modal,
  ModalProps,
} from "antd";
import {Vender} from "@/modules/supplier/domain/vender";
import {useVender} from "@/modules/supplier/application/vender";
import {CheckCircleTwoTone} from "@ant-design/icons";
import CustomPage from "@/components/CustomPage";
export type  SearchSupplierListParams = {
  onFinish: () => void;
  onSelected: (values: Vender) => void;
} & ModalProps;
export const SearchSupplierListModal =  (props: SearchSupplierListParams) =>  {
  const { onFinish, onSelected, ...rest } = props;
  const { venderList} = useVender("");
  const { actionRef, fetchList } = venderList;

  const selectSupplier = (supplier: Vender) => {
    onSelected(supplier);
  }
  const columns: ProColumns<Vender>[] = [
    {
      title: '名称',
      dataIndex: 'venderName',
    },
    {
      title: "选择",
      dataIndex: 'gmtCreate',
      align: "center",
      hideInSearch: true,
      render: (v, record) => {
        return <><CheckCircleTwoTone onClick={()=>selectSupplier(record)}/></>
      }
    }
  ];
  return (
    <>
      <Modal width={"35%"} {...rest} title={"选择供应商"} onCancel={onFinish} onOk={onFinish}>
        <CustomPage<Vender>
          columns={columns}
          actionRef={actionRef}
          size={"small"}
          bordered={false}
          request={fetchList}
          rowKey="venderId"
          pagination={{pageSize: 5}}
          onRow={(record) => {
            return {
              onDoubleClick: (e) => {
                selectSupplier(record)
              },
            };
          }}
          recordCreator={false}
          recordDelete={false}
          recordUpdater={false}
        />
      </Modal>
    </>
  );
};

