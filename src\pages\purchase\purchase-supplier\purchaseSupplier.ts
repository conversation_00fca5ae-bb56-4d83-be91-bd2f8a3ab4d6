import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import type { ActionType } from '@ant-design/pro-table';
import { Modal, message } from 'antd';
import { useRef, useState, useEffect } from 'react';
import { useRequest } from 'ahooks';
import { pageQuery, purchaseEntityQuery, pageQueryAll, disableItem, activeItem, itemDetail, editItem, createItem } from './api';
import type { Item, Detail } from './api';
// 供应商 - 列表
export const usePurchaseSupplierList = () => {
  const actionRef = useRef<ActionType>();
  const fetchList = usePageQueryRequest(pageQuery);
  const fetchEntityList = usePageQueryRequest(purchaseEntityQuery);
  return {
    actionRef,
    fetchList,
    fetchEntityList
  };
};

// 供应商  - 下拉配置
export const usePurchaseSupplierOptions = () => {
  const [suppliers, setSuppliers] = useState<Item[]>([]); // 所有供应商

  // 获取所有供应商
  const getAllSuppliers = async () => {
    const res: any = await pageQueryAll();
    setSuppliers(res?.body || []);
  }

  useEffect(() => {
    getAllSuppliers();
  }, []);

  return {
    suppliers
  };
};


// 供应商 - 操作
export const usePurchaseSupplier = () => {
  // 禁用
  const disabledApi = async (venderId: string) => {
    const res = await disableItem({ venderId });
    if (!res?.status?.success) {
      return false;
    }
    message.success(res?.status?.message || '操作成功');
    return res?.status?.success
  }
  // 启用
  const activeApi = async (venderId: string) => {
    const res = await activeItem({ venderId });
    if (!res?.status?.success) {
      return false;
    }
    message.success(res?.status?.message || '操作成功');
    return res?.status?.success
  }
  // 新增
  const addApi = async (params: Detail) => {
    const res = await createItem(params);
    if (!res?.status?.success) {
      return false;
    }
    message.success(res?.status?.message || '操作成功');
    return res?.status?.success
  }
  // 编辑
  const editApi = async (params: Detail) => {
    const res = await editItem(params);
    if (!res?.status?.success) {
      return false;
    }
    message.success(res?.status?.message || '操作成功');
    return res?.status?.success
  }
  return {
    disabledApi,
    activeApi,
    addApi,
    editApi,
  };
};

// 供应商 - 详情
export const usePurchaseSupplierInfo = () => {
  const venderDetail = useRequest(
    async (venderId: string) => {
      if (venderId) {
        const res = await itemDetail({ venderId });
        return res.body;
      }
      return undefined;
    },
    { manual: true },
  );
  return {
    venderDetail,
  };
};
