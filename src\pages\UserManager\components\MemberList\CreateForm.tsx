// import PasswordField from '@/modules/user/infra/components/PasswordField';
import { useUser } from '@/modules/user-center/application/user';
import { EndPointUser } from '@/modules/user-center/domain/user';
import { UpdateUserParams } from '@/modules/user-center/infra/user';
import type { ModalFormProps } from '@ant-design/pro-form';
import { ModalForm, ProFormField, ProFormRadio, ProFormText } from '@ant-design/pro-form';
import { useControllableValue } from 'ahooks';
import { Form } from 'antd';
import { find, omit } from 'lodash';
import React, { useContext, useEffect } from 'react';
import { UserManagerContext } from '../..';
import { DepartmentSelect } from '../SelectDepartmentModal';
import PasswordField from './components/PasswordField';
import { DataNode } from 'antd/es/tree';
import { isEmail, isPhone, userNameReg } from '@/utils/utils';

type CreateFormProps = Pick<ModalFormProps, 'visible' | 'onVisibleChange' | 'trigger' | 'onFinish'> & {
  record?: EndPointUser;
};

const CreateMemberForm: React.FC<CreateFormProps> = (props) => {
  const { record } = props;
  const [form] = Form.useForm();
  const [visible, setVisibleChange] = useControllableValue({
    value: props.visible,
    onChange: props.onVisibleChange
  }, {
    defaultValue: false,
  })
  const { endpoint } = useContext(UserManagerContext);
  const { create, update } = useUser();

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        ...record,
        account: find(record?.accounts, { type: 'USERNAME' })?.account,
        username: record?.userName,
        phoneNumber: record?.phoneNumber,
        depts: record?.depts?.map(item => {
          return {
            key: item.deptId,
            title: item.name,
          }
        })
      });
    } else {
      form.resetFields();
    }
  }, [record, visible])

  const onSubmit = async (value: UpdateUserParams) => {
    let res: boolean = false;
    if (!value.userId) {
      const { username, nickName } = value;
      res = await create({
        ...omit(value, ['userId']),
        account: username,
        nickName: nickName
      });
    } else {
      res = await update(value);
    }
    if (res) {
      form.resetFields();
      props.onFinish?.(value);
    }
    return res;
  }

  return (
    <ModalForm
      title={record ? '编辑成员' : '新建成员'}
      width={600}
      layout={"horizontal"}
      labelCol={{ span: 4 }}
      form={form}

      {...omit(props, ['visible', 'onVisibleChange'])}
      onFinish={onSubmit}
      visible={visible}
      onVisibleChange={(v) => {
        if (!v) {
          form.resetFields();
        }
        setVisibleChange(v);
      }}
    >
      <ProFormText name='userId' hidden></ProFormText>
      <ProFormText name='endpoint' hidden initialValue={endpoint}></ProFormText>
      <ProFormText
        label="用户名"
        disabled={!!record?.userId}
        name="username"
        fieldProps={{
          maxLength: 30,
          autocomplete: 'off',
        } as any}
        rules={[
          { required: true, message: '请输入用户名' },
          {
            pattern: userNameReg,
            message:
              '用户名支持5~30个字符，支持数字，英文字母，英文下划线，英文句号,至少一个英文字母',
          },
        ]}
        placeholder="请输入用户名"
      />

      <ProFormText
        label="真实姓名"
        name="nickName"
        rules={[{ required: true, message: '请输入真实姓名' }]}
      />
      <ProFormText
        label="登录账号"
        name="account"
        hidden
        disabled={!!record?.userId}
      // rules={[{ required: true, message: '请输入登录账号' }]}
      />
      <ProFormText label="手机号" name="phoneNumber"
        rules={[
          {
            validator: (rule, value, callback) => {
              try {
                if (value && !isPhone(value)) {
                  throw new Error('请填写正确的手机号码');
                }
                callback();
              } catch (e: any) {
                callback(e);
              }
            },
          },
        ]} />
      <ProFormText label="邮箱" name="email" rules={[
        {
          validator: (rule, value, callback) => {
            try {
              if (value && !isEmail(value)) {
                throw new Error('请填写正确的邮箱');
              }
              callback();
            } catch (e: any) {
              callback(e);
            }
          },
        },
      ]} />
      <ProFormRadio.Group
        label="成员状态"
        name="state"
        initialValue='ACTIVATED'
        options={[
          { label: '正常', value: 'ACTIVATED' },
          { label: '禁用', value: 'DISABLED' },
        ]}
      />
      {record?.userId ? null : <PasswordField label="登录密码" name="password" />}
      <ProFormField label="所属部门" name='depts' transform={(value?: DataNode[]) => {
        return {
          deptIds: value?.map(item => item.key)
        }
      }}>
        <DepartmentSelect />
      </ProFormField>
    </ModalForm>
  );
};

export default CreateMemberForm;
