import {Card, Col, Dropdown, Menu, Modal, ModalProps, Row, Table} from "antd";
import React, {Suspense} from "react";
import {useRequest} from "ahooks";
import styles from "@/pages/analysis/style.less";
import {EllipsisOutlined} from "@ant-design/icons";
import {planAnalysis} from "@/modules/purchasePlan/infra/api/purchasePlan";
// 定义参数格式
export type CreateModalProps = {
  planId: string,
  onFinish: (values: any) => void;
} & ModalProps;

const PurchasePlanAnalysisModal = (props: CreateModalProps) => {
  const { onFinish, planId,...rest } = props;

  const { loading, data } = useRequest(() => planAnalysis(planId==null?"0":planId).then(item=>item.body));


  const menu = (
    <Menu>
      <Menu.Item>操作一</Menu.Item>
      <Menu.Item>操作二</Menu.Item>
    </Menu>
  );
  const columns = [
    {
      title: '采购员',
      dataIndex: 'purchaser',
      key: 'purchaser',
    },
    {
      title: 'SKU数量',
      dataIndex: 'skuCount',
      key: 'skuCount',
      render: (text: React.ReactNode) => <a href="/">{text}</a>,
    },
    {
      title: '未生成数量',
      dataIndex: 'notGenerate',
      key: 'notGenerate',
      render: (v, record) => {
        return record.skuCount-record.createdCount-record.abolishCount;
      }
    },
    {
      title: '已生成金额',
      dataIndex: 'createdAmount',
      key: 'createdAmount',
      className: styles.alignRight,
      render: (v, record) => {
        return Number(record.createdAmount)?.toFixed(2);
      }
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      // sorter: (a: { count: number }, b: { count: number }) => a.count - b.count,
      className: styles.alignRight,
      render: (v, record) => {
        return Number(record.totalAmount)?.toFixed(2);
      }
    },
    // {
    //   title: '采购额度',
    //   dataIndex: 'range',
    //   key: 'range',
    //   // sorter: (a: { range: number }, b: { range: number }) => a.range - b.range,
    //   render: (text: React.ReactNode, record: { status: number }) => (
    //     <Trend flag={record.status === 1 ? 'down' : 'up'}>
    //       <span style={{ marginRight: 4 }}>{text}%</span>
    //     </Trend>
    //   ),
    // },
  ];
  const columns1 = [
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      render: (v, record) => {
        return (<div size={"small"}>{record.supplierName}</div>);
      }
    },
    {
      title: 'SKU数量',
      dataIndex: 'skuCount',
      key: 'skuCount',
      render: (text: React.ReactNode) => <a href="/">{text}</a>,
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      // sorter: (a: { count: number }, b: { count: number }) => a.count - b.count,
      className: styles.alignRight,
      render: (v, record) => {
        return Number(record.totalAmount)?.toFixed(2);
      }
    },
  ];

  const dropdownGroup = (
    <span className={styles.iconGroup}>
      <Dropdown overlay={menu} placement="bottomRight">
        <EllipsisOutlined />
      </Dropdown>
    </span>
  );

  return <Modal width={"50%"} {...rest} style={{padding: 0}}>
    <Row style={{padding:0,height:80}}>
      <Col span={4} style={{padding:0}}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>SKU总款数</p>
          <b>{data?.skuCount}</b>
        </Card>
      </Col>
      <Col span={4}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>采购总金额</p>
          <b>{data?.totalAmount}</b>
        </Card>
      </Col>
      <Col span={4}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>待审核数</p>
          <b>{data?.pendingCount}</b>
        </Card>
      </Col>
      <Col span={4}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>通过</p>
          <b>{data?.passCount}</b>
        </Card>
      </Col>
      <Col span={4}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>作废</p>
          <b>{data?.abolishCount}</b>
        </Card>
      </Col>
      <Col span={4}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>已生成数</p>
          <b>{data?.createdCount}</b>
        </Card>
      </Col>
      <Col span={4}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>驳回</p>
          <b>{data?.rejectCount}</b>
        </Card>
      </Col>
    </Row>

    <Row style={{padding:0}}>
      <Col xl={12} lg={24} md={24} sm={24} xs={24}>
        <Suspense fallback={null}>
          <Card
            loading={loading}
            bordered={false}
            style={{
              height: '100%',marginTop: 0
            }}
          >
            <Table<any>
              rowKey={(record) => record.index}
              size="small"
              columns={columns}
              dataSource={data?.purchaserList}
              // pagination={false}
              pagination={{
                style: { marginBottom: 0 },
                pageSize: 5,
              }}
            />
          </Card>
        </Suspense>
      </Col>
      <Col xl={12} lg={24} md={24} sm={24} xs={24}>
        <Suspense fallback={null}>
          <Card
            loading={loading}
            bordered={false}
            style={{
              height: '100%',marginTop: 0
            }}
          >
            <Table<any>
              rowKey={(record) => record.index}
              size="small"
              columns={columns1}
              dataSource={data?.supplierList}
              // pagination={false}
              pagination={{
                style: { marginBottom: 0 },
                pageSize: 5,
              }}
            />
          </Card>
        </Suspense>
      </Col>
    </Row>

  </Modal>
}

export default PurchasePlanAnalysisModal;
