import { querySubAddresses } from '@/services/common';
import type { CascaderProps } from 'antd';
import { Input, Space, Cascader } from 'antd';
import React, { useEffect, useState } from 'react';

export type CityCascaderProps = Omit<CascaderProps<any>, 'options'> & {
  nation?: string;
};

const CityCascader = (props: CityCascaderProps) => {
  const { onChange, nation, ...rest } = props;
  const [options, setOptions] = React.useState<CascaderProps<any>['options']>([]);
  const [needInputAddress, setNeedInputAddress] = useState<boolean>(false);
  const { value } = props;

  useEffect(() => {
    if (nation) {
      querySubAddresses({
        parentCode: nation,
        type: 'PROVINCE',
      }).then((res) => {
        const data = res.body || [];
        setNeedInputAddress(data.length === 0);
        const provinceOption = data.map((item) => {
          return {
            label: item.name,
            value: item.code,
            code: item.code,
            isLeaf: false,
            type: item.type,
          };
        });
        setOptions(provinceOption);
      });
    }
  }, [nation]);

  const loadData = (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    const typeArray = ['PROVINCE', 'CITY', 'AREA'];
    const index = typeArray.indexOf(targetOption.type);
    const type =
      index + 1 > typeArray.length - 1 ? typeArray[typeArray.length - 1] : typeArray[index + 1];
    targetOption.loading = true;
    querySubAddresses({
      parentCode: type === 'PROVINCE' ? nation : targetOption.code,
      type: type as 'PROVINCE' | 'CITY' | 'AREA',
    }).then((res) => {
      const data = res.body || [];
      const children = data.map((item) => {
        return {
          label: item.name,
          value: item.code,
          code: item.code,
          type: item.type,
          isLeaf: item.type === typeArray[typeArray.length - 1],
        };
      });
      targetOption.loading = false;
      targetOption.children = children;
      setOptions([...(options as any)]);
    });
  };

  const handleChange: any = (_values: any, selectedOptions: any[]) => {
    const data = selectedOptions?.map((item: { label: any }) => item.label);
    if (onChange) {
      onChange(data as any[], selectedOptions);
    }
  };

  const changeAddress = (address: string, index: number) => {
    const [province, city, area] = value || new Array(3).fill('');
    const newAddress = [province, city, area];
    newAddress[index] = address;
    onChange?.(newAddress, newAddress);
  };

  if (needInputAddress) {
    return (
      <Space>
        {['', '', ''].map((item, index) => (
          <Input
            key={index.toString()}
            value={value?.[index] as string}
            onChange={(e) => changeAddress(e.target.value, index)}
          />
        ))}
      </Space>
    );
  }

  return (
    <Cascader<any>
      placeholder="请选择省市区"
      options={options}
      loadData={loadData}
      {...rest}
      onChange={handleChange}
    />
  );
};

export default CityCascader;
