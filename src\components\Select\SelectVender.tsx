import { Select } from 'antd';
import { debounce } from 'lodash';
import { memo, useEffect, useState } from 'react';
import { queryVenderList } from './api';

const SelectVender = memo((props: Record<keyof any, any>) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);
  const [origin, setOrigin] = useState<any[]>([]);

  const onQuery = debounce((venderName?: string) => {
    queryVenderList({ venderName, pageCondition: { pageNum: 1, pageSize: 50 } }).then((res) => {
      const { body } = res;
      setOrigin(body?.items || [])
      setSelectOption(body?.items?.map((v) => ({ value: props?.useVenderName ? v?.venderName : v.venderId, label: v.venderName })));
    });
  }, 500);

  useEffect(() => {
    if (props.value && selectOption.length > 0) return
    queryVenderList(props?.useVenderName ? { venderName: props.value, pageCondition: { pageNum: 1, pageSize: 50 } } : { venderId: props.value, pageCondition: { pageNum: 1, pageSize: 50 } }).then((res) => {
      const { body } = res;
      setOrigin(body?.items || []);
      setSelectOption(body?.items?.map((v) => ({ value: props?.useVenderName ? v?.venderName : v.venderId, label: v.venderName })));
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.value])

  const handleSearch = (newValue: string) => {
    if (newValue) {
      onQuery(newValue)
    } else {
      setSelectOption([]);
      onQuery();
    }
  };

  const onChange = (...args: any[]) => {
    props.onChange(...args)
    const [id] = args
    if (id) {
      const curItem = origin.find(val => id === val.venderId)
      props.onChoose && props.onChoose(curItem)
    }
  }

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择采购供应商"
      optionFilterProp="children"
      onSearch={handleSearch}
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
      onChange={onChange}
    />
  );
});

export default SelectVender;


// setSelectOption(body?.map((v) => ({ value: v.venderId, label: v.venderName })));
