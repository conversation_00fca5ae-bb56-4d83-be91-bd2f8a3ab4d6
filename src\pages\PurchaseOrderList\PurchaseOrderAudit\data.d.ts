import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";

export type PurchaseOrderPriceAuditRecord = {
  "id": string,
  "purchaseOrderId": string,
  "applyUid": string,
  "applyUsername": string,
  "serialNumber": string,
  "auditUid": string,
  "auditUsername": string,
  "auditType": string,
  "auditStatus": string,
  "auditData": string,
  "remark": string,
  "gmtCreate": string,
  "gmtModified": string,
  "remakeAudit": string,
  "purchaseOrderGoodsInfoList": PurchaseOrderGoods[],
  "purchaseOrderInfo": PurchaseOrder,
}

export type PurchaseOrderGoods = {
  "goodsRemark": string,
  "id": string,
  "isUrgent": boolean,
  "purchasePlanId": string,
  "salesWarehouse": string,
  "lastPurchasePrice": number,
  "platformPurchasePrice": number;
  "currentPurchasePrice": number,
  "auditPrice": number,
  "auditQuantity": number,
  "purchaseQuantity": number,
  "purchaseRemark": string,
  "sku": string,
  "status": number,
  "supplierId": string,
  "supplierName": string,
  "totalPrice": number,
  "gmtCreate": string,
  "skuImg": string,
  "arrivalQuantity": number,
  "receivingQuantity": number,
  "remark": string,
}
