import { useEffect } from 'react';
import type { TreeProps } from 'antd';
import { Input, Tree } from 'antd';
import type { DataNode } from 'rc-tree/lib/interface';
import { useState } from 'react';

type TreeSearchProps = TreeProps & {
  treeData: DataNode[];
};

const TreeSearch = (props: TreeSearchProps) => {
  const { treeData, ...rest } = props;
  const [treeSearchValue, setTreeSearchValue] = useState<string>();
  const [newTreeData, setTreeData] = useState<DataNode[]>(treeData);

  const onSearchTree = () => {
    if (!treeSearchValue) {
      setTreeData(treeData);
      return;
    }
    const data = treeData.filter((item) => {
      return (item.title as string).includes(treeSearchValue);
    });
    setTreeData(data);
  };

  useEffect(() => {
    setTreeSearchValue(undefined);
    setTreeData(treeData);
  }, [treeData]);
  return (
    <>
      <Input.Search
        value={treeSearchValue}
        style={{ marginBottom: 8 }}
        placeholder="请输入关键字"
        onChange={(e: any) => setTreeSearchValue(e.target.value)}
        onBlur={() => onSearchTree()}
        onSearch={() => onSearchTree()}
      />
      <Tree treeData={newTreeData} {...rest} />
    </>
  );
};

export default TreeSearch;
