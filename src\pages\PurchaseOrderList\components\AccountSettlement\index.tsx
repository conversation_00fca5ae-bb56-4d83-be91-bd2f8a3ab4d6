import {
  Modal,
  Col,
  Row,
  Radio,
  Table,
  Alert,
  InputNumber,
  DatePicker,
  message,
  Space,
  Button
} from 'antd';
import { useState, useEffect } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import type { CreateModalProps } from './BatchChangeTime'
import styles from './index.less';
import {
  useAdvancedPayment,
} from '../AdvancedPayment/api';
import {
  OrderStatusEnum, OrderStatusTagsEnum,
  payStatusEnum,
  PlatformStatusEnum,
  PurchaseAuditStatusEnum
} from "@/modules/purchaseOrder/domain/purchaseOrder";
import {
  batchApplyPeriodSettle
} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import moment from 'moment';
import BatchChangeTimeModal from './BatchChangeTime';
import { isNumber } from 'lodash';
export type Props = {
  open?: boolean;
  queryParams?: any;
  onCancel?: () => void;
};

const Index = (props: Props) => {
  const { open, onCancel, queryParams } = props;
  const {
    tableData,
    total,
    setTableData,
    getTableData,
    loading,
  } = useAdvancedPayment();
  const [params, setParams] = useState({});
  const [selectRowsKeys, setSelectedRowsKeys] = useState<any[]>([]);
  const [changeTimeModalProps, setChangeTimeModalProps] = useState<CreateModalProps>({ open: false });

  const onOk = async () => {

    if (!selectRowsKeys?.length) {
      message.error('请至少勾选一条订单');
      return;
    }

    const selectRows: any = tableData?.filter(v => selectRowsKeys?.includes(v?.id))?.map(vv => ({
      id: vv?.id,
      orderCode: vv?.orderCode,
      periodAmount: vv?.periodAmount,
      periodTime: vv?.periodTime ? moment(vv?.periodTime) : ''
    }));

    if (selectRows?.find((v: any) => !isNumber(v?.periodAmount) || !v?.periodTime)) {
      message.error('勾选订单中存在账期金额或结算时间为空，请确认！');
      return;
    }
    const res = await batchApplyPeriodSettle({ purchaseOrderApplyPeriodSettleReqs: selectRows });
    if (res.status.success) {
      message.success('操作成功');
      onCancel && onCancel();
    } else {
      message.error('操作失败,请稍后重试');
    }
  };

  const onChangeValue = (key: string, value: string, index: number) => {
    const newTableData = tableData?.map((v, vi) => {
      if (vi === index) {
        return {
          ...v,
          [key]: value,
        };
      } else {
        return v;
      }
    });
    setTableData(newTableData);
  };

  const columns: ProColumns<any>[] = [
    {
      title: '采购单号',
      dataIndex: 'orderCode',
      key: 'orderCode',
      fixed: 'left',
      width: 150,
      render: (v, record) => (
        <div>
          <div>采购单号：{record?.orderCode}</div>
          <div>采购账号：{record?.platformAccount || '--'}</div>
        </div>
      ),
    },
    {
      title: '采购单付款金额',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 120,
      // render: (v, record) => `${record?.totalPrice} CNY`,
      render: (v, record) => (
        <>
          <div style={{ fontSize: "12px" }}> 平台总金额：{record?.platformOrderAmount == null ? '--' : record.platformOrderAmount?.toFixed(2)}</div>
          <div style={{ fontSize: "12px" }}>本地总金额：{record?.shippingFee == null && record?.totalPrice == null ? '--' : (record.shippingFee + record.totalPrice + record.applyRefundAmount - record.applyPromotionAmount)?.toFixed(2)}</div>
          <div style={{ fontSize: "12px" }}>商品金额：{record?.totalPrice == null ? '--' : record.totalPrice?.toFixed(2)}</div>
        </>
      )
    },
    {
      title: '状态',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        const orderStatus = OrderStatusEnum[record?.orderStatus];
        const orderStatusTags = OrderStatusTagsEnum[record?.orderStatus];
        const payStatus = payStatusEnum[record?.payStatus];
        const purchaseAuditStatus = PurchaseAuditStatusEnum[record?.purchaseAuditStatus];
        const platformStatus = PlatformStatusEnum[record?.platformStatus];
        const options = [
          orderStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              采购状态：
              <span className={"ant-badge-status-dot ant-badge-status-" + (orderStatusTags != undefined ? orderStatusTags : "success")} />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{orderStatus}</span>
            </span>
          ) : null,
          platformStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              平台状态：
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{platformStatus}</span>
            </span>
          ) : null,
          payStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              付款状态：
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{payStatus}</span>
            </span>
          ) : null,
          purchaseAuditStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              审核状态：
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{purchaseAuditStatus}</span>
            </span>
          ) : null,
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '下单金额',
      dataIndex: 'amountPayable',
      key: 'amountPayable',
      width: 90,
    },
    {
      title: '采购单付款金额',
      dataIndex: 'afterAmount',
      key: 'afterAmount',
      width: 90,
      render: (v, record) => record?.amountPayable && record?.afterAmount ? record?.amountPayable - record?.afterAmount : record?.amountPayable
    },
    {
      title: '账期金额',
      dataIndex: 'periodAmount',
      key: 'periodAmount',
      width: 100,
      render: (v, record, i) => <InputNumber
        value={record?.periodAmount}
        onChange={(value) => onChangeValue('periodAmount', value, i)}
      />
    },
    {
      title: '结算时间',
      dataIndex: 'periodTime',
      key: 'periodTime',
      width: 150,
      render: (v, record, i) => <DatePicker
        onChange={(value) => onChangeValue('periodTime', value, i)}
        value={record?.periodTime ? moment(record?.periodTime) : null}
      />
    },
  ];

  useEffect(() => {
    if (JSON.stringify(params) === '{}') return;
    getTableData({ ...params });
    setSelectedRowsKeys([]);
  }, [params]);

  useEffect(() => {
    if (!open) return;
    setParams({
      ...queryParams, orderStatus: [50, 55], payStatus: [45, 46], pageCondition: { pageNum: 1, pageSize: 30 }
    });
  }, [queryParams, open]);

  return (
    <Modal
      title={`申请账期结算（高级）`}
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      width={1200}
      bodyStyle={{
        minHeight: 500,
        maxHeight: '90vh',
      }}
    >
      <div className={styles.wrapper}>
        <div className={styles.top}>
          <div className={styles.search}>
            <Row>
              <Col span={3}>采购状态:</Col>
              <Col span={21}>
                <Radio.Group
                  options={[
                    { value: "ALL", label: '全部' },
                    { value: 50, label: '全部到货' },
                    { value: 55, label: '不等待剩余' },
                  ]}
                  onChange={(e) => {
                    setParams({
                      ...params,
                      orderStatus: e.target.value === 'ALL' ? [50, 55] : [e.target.value],
                    });
                  }}
                  value={(params?.orderStatus && params?.orderStatus?.length === 1) ? params?.orderStatus[0] : 'ALL'}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Col>
            </Row>
            <Row style={{ margin: '12px 0' }}>
              <Col span={3}>付款状态:</Col>
              <Col span={21}>
                <Radio.Group
                  options={[
                    { value: "ALL", label: '全部' },
                    { value: 45, label: '账期待还款' },
                    { value: 46, label: '诚意赊账待还款' },
                  ]}
                  onChange={(e) => {
                    setParams({
                      ...params,
                      payStatus: e.target.value === 'ALL' ? [45, 46] : [e.target.value],
                    });
                  }}
                  value={(params?.payStatus && params?.payStatus?.length === 1) ? params?.payStatus[0] : 'ALL'}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Col>
            </Row>
            <Row>
              <Col span={3}>账期状态:</Col>
              <Col span={21}>
                <Radio.Group
                  options={[
                    { value: "ALL", label: '全部' },
                    { value: "APPLY", label: '待申请' },
                    { value: "WAIT", label: '待审核' },
                    { value: "PASS", label: '通过' },
                    { value: "REJECT", label: '驳回' },
                    { value: "END", label: '已结清' },
                  ]}
                  onChange={(e) => {
                    setParams({
                      ...params,
                      periodStatus: e.target.value === 'ALL' ? [''] : [e.target.value],
                    });
                  }}
                  value={(params?.periodStatus && params?.periodStatus[0]) ? params?.periodStatus[0] : 'ALL'}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Col>
            </Row>
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.btns}>
            <Space>
              <Button
                type="primary"
                disabled={!selectRowsKeys?.length}
                onClick={() =>{
                  setChangeTimeModalProps({
                    open: true,
                    name: 'periodTime',
                    title: '批量设置结算时间',
                  })
                }
                }
              >
                批量设置结算时间
              </Button>
            </Space>
          </div>
          <div className={styles.info}>
            <Alert
              message={`已选择${selectRowsKeys?.length}项`}
              type="info"
              showIcon
            />
          </div>
          <div className={styles.tableBox}>
            <Table
              rowKey={'id'}
              dataSource={tableData}
              columns={columns}
              pagination={{
                current: params?.pageCondition?.pageNum || 1,
                pageSizeOptions: [30, 50, 100],
                pageSize: params?.pageCondition?.pageSize || 30,
                total: total ?? 0,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total: number): React.ReactNode | undefined => `共 ${total} 条`,
                onChange: (page: number, pageSize: number) => {
                  setSelectedRowsKeys([]);
                  setParams({ ...params, pageCondition: { pageNum: page, pageSize } })
                },
              }}
              rowSelection={{
                selectedRowKeys: selectRowsKeys,
                onChange: (_, selectedRows) => {
                  setSelectedRowsKeys(selectedRows.map((v) => v?.id));
                },
              }}
              scroll={{ y: 300, x: 800 }}
              loading={loading}
            />
          </div>
        </div>
      </div>
      <BatchChangeTimeModal 
        onFinish={(values) => {
          setTableData(tableData?.map(v => ({
            ...v,
            periodTime: selectRowsKeys?.includes(v?.id) ? values?.periodTime : v?.periodTime,
          })));
          setChangeTimeModalProps({ open: false });
        }}
        onCancel={() => setChangeTimeModalProps({ open: false })}
        {...changeTimeModalProps}
      />
    </Modal>
  );
};
export default Index;
