import { Spu, SpuSkuData } from './spu';

export type ImportItemData<T> = {
  checkResult?: {
    cellCheckResult: { [key in keyof T]: string };
    errorMsg?: string;
  };
} & { [key in keyof T]: T[key] };

export type BatchImportProductBaseItem = {
  customBrandName: string;
  customCategoryName: string;
  customSkuCode: string;
  customSpuCode: string;
  detailImageUrls: string[];
  isCharged: string;
  isContainLiquid: string;
  isContainSpecial: boolean;
  mainImageUrls: string[];
  mainSpecImageUrl: string;
  videoUrls: string[];
} & Pick<
  Spu,
  | 'hscode'
  | 'enTitle'
  | 'originCountry'
  | 'keywords'
  | 'textDesc'
  | 'sellingPointDesc'
  | 'zhTitle'
  | 'title'
> &
  Pick<
    SpuSkuData,
    | 'codeEAN'
    | 'codeUPC'
    | 'grossWeight'
    | 'guarantyPeriod'
    | 'deliveryDays'
    | 'sizeHeight'
    | 'sizeLength'
    | 'sizeWidth'
    | 'netWeight'
    | 'packingHeight'
    | 'packingLength'
    | 'packingWidth'
    | 'moq'
    | 'measuringUnit'
    | 'refProductLink'
    | 'retailPriceCurrency'
    | 'specs'
    | 'suggestedRetailPrice'
    | 'lowestRetailPrice'
  >;

export type BatchImportProductPreviewItem = ImportItemData<BatchImportProductBaseItem>;
