import React, {useState} from 'react';
import {Avatar, Button, Card, Col, Input, List, Row, Table} from "antd";
import TextArea from "antd/es/input/TextArea";
import $ from 'jquery'
import {WebSocketC} from "@/components/WebSocket";

export const RobotSendMsg = () => {

  /****************************************************************************************
   ***********************************Web Socket*******************************************
   ****************************************************************************************/

  const getUuid = () => {
    return 'xxxxxxxxxxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0,
        v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  let curTime = 0;
  let showType = "text";

  const onMsg =(res: MessageEvent)=>{
    if(res.data == "this is the heartbeat message"){
      console.log("连接保持");return;
    }
    console.log(res.data)
    const msg = res.data;
    if(typeof ($('#returnStr'+curTime).html()) == 'undefined'){
      if(showType == 'text'){
        $('#contentStr').append('<li class="ant-list-item" id="returnStr'+curTime+'"><div class="ant-list-item-meta"><div class="ant-list-item-meta-avatar">' +
          '<span class="ant-avatar ant-avatar-circle ant-avatar-image"><img src="http://mms0.baidu.com/it/u=1178535658,1438913827&fm=253&app=138&f=JPEG&fmt=auto&q=75?w=260&h=260" style="width: 32px"></span></div>' +
          '<div class="ant-list-item-meta-content"><h4 class="ant-list-item-meta-title"><b>NJRobot</b></h4><div class="ant-list-item-meta-description" style="color:#444444">'+msg+'</div>' +
          '</div></div></li>');
      }else{
        $('#contentStr').append('<li class="ant-list-item" id="returnStr'+curTime+'"><div class="ant-list-item-meta"><div class="ant-list-item-meta-avatar">' +
          '<span class="ant-avatar ant-avatar-circle ant-avatar-image"><img src="http://mms0.baidu.com/it/u=1178535658,1438913827&fm=253&app=138&f=JPEG&fmt=auto&q=75?w=260&h=260" style="width: 32px"></span></div>' +
          '<div class="ant-list-item-meta-content"><h4 class="ant-list-item-meta-title"><b>NJRobot</b></h4><div class="ant-list-item-meta-description"><img src="' + msg + '" alt=""></div>' +
          '</div></div></li>');
      }
    }else{
      $('#returnStr'+curTime).find(".ant-list-item-meta-description").append(res.data)
    }
  }

  const webSocketInit = () => {
    WebSocketC.connect("ws://*************:18080/api/ws/uuid"+getUuid(), onMsg)
  }
  webSocketInit();

  //
  /**
   * 发送信息
   * @param value
   */
  const scanCode = () => {
    curTime = new Date().getSeconds();
    const msg = $("#sendMessageCode").val();
    $('#contentStr').append('<li class="ant-list-item"><div class="ant-list-item-meta"><div class="ant-list-item-meta-avatar">' +
      '<span class="ant-avatar ant-avatar-circle ant-avatar-image"><img src="https://img95.699pic.com/xsj/1k/bl/27.jpg%21/fw/700/watermark/url/L3hzai93YXRlcl9kZXRhaWwyLnBuZw/align/southeast" style="width: 32px">' +
      '</span></div><div class="ant-list-item-meta-content"><h4 class="ant-list-item-meta-title"><b>Me</b></h4><div class="ant-list-item-meta-description" style="color:#444444">'+msg+'</div>' +
      '</div></div></li>');
    let idType = "1";
    if(msg.indexOf("画个") >= 0){
      showType = "pic";
      idType = "2";
    }else{
      showType = "text";
    }
    const data = {
      text: msg, id: idType, apikey: "", keep: "2", keepText: "1",
    }
    WebSocketC.send(data)
    $("#sendMessageCode").val('')
  }
  $("body").keydown(function (e) {
    // 当 keyCode 是13时,是回车操作
    if (e.keyCode === 13) {
      // 查询余额
      scanCode();
    }
  });
  return (
    <>
        <Card  style={{padding:"0", marginBottom:5, maxHeight: 600, overflow:"scroll"}}  headStyle={{height:25,fontWeight:"bold"}}>
          <div id={"contentStr"}>
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={`http://mms0.baidu.com/it/u=1178535658,1438913827&fm=253&app=138&f=JPEG&fmt=auto&q=75?w=260&h=260`} />}
                title={<b>NJRobot</b>}
                description={<span style={{color: "#444444"}}>欢迎光临 OpenAi</span>}
              />
            </List.Item>
          </div>
        </Card>
      <Card style={{padding:"0"}} headStyle={{height:25,fontWeight:"bold"}}>
        <Row>
            <TextArea id={"sendMessageCode"} style={{ width: 500, height: 100, borderColor:"#DDDDDD",boxShadow: "none"}}/>
        </Row>
      </Card>
    </>
  );
};
export default RobotSendMsg;
