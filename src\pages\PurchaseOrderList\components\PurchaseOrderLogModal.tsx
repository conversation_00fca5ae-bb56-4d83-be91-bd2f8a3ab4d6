import type {ProColumns} from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import React from 'react';
import {pageQueryOrderLog} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {useRequestTable} from "@/hooks/useRequestTable";
import {PurchaseOrderLog} from "@/pages/PurchaseOrderList/data";
import moment from "moment";


export type PurchaseOrderLogListProps = {
  orderId: string;
};

export const PurchaseOrderLogList = (props: PurchaseOrderLogListProps) => {
  const { orderId } = props;

  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryOrderLog({
      ...params,
      orderId: orderId === '' ? undefined : orderId,
    });
  });


  const columns: ProColumns<PurchaseOrderLog>[] = [
    {
      title: "操作人",
      dataIndex: 'operatorUsername',
      width: 100
    },
    {
      title: "内容",
      dataIndex: 'content',
    },
    {
      title: "操作时间",
      dataIndex: 'gmtCreate',
      width: 200,
      render: (v, record) => {
        return moment(record.gmtCreate).format("YYYY-MM-DD HH:mm:ss");
      }
    },
  ];
  return (
      <ProTable<PurchaseOrderLog>
        options={false}
        actionRef={actionRef}
        request={fetchList}
        size="small"
        search={false}
        columns={columns}
        style={{marginLeft: -24}}
      />
  );
};


export type PurchaseOrderLogModalProps = {
  orderId: string;
};

export default (props: PurchaseOrderLogModalProps) => {
  const { orderId } = props;
  return (
      <PurchaseOrderLogList orderId={orderId} />
  );
};
