import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Space, InputNumber } from 'antd';
import { isNumber, isString } from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';
import styles from './styles.less';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import type { SpuSkuData } from '@/modules/product/domain/spu';
import UploadImageField from '@/components/UploadImage';
import type { SpecItemData } from '../../../CategorySpecField';
import { ActionType, EditableFormInstance } from '@ant-design/pro-components';

export interface ProductSkuListProps {
  specs: SpecItemData[];
  isShowFirstImage: boolean;
  value?: ProductSpecSkuData[];
  onChange?: (data: ProductSpecSkuData[]) => void;
  disabled?: boolean;
}

export interface ProductSpecSkuData extends SpuSkuData {
  id?: string;
}

const ProductSpecSkuList = (props: ProductSkuListProps) => {
  const { specs, isShowFirstImage, disabled } = props;
  const [dataSource, setDataSource] = useMergedState<ProductSpecSkuData[]>([], {
    value: props.value || [],
    onChange: props.onChange,
  });
  const [editableKeys, setEditableKeys] = useState<string[]>([]);
  const formRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();

  const changeValueByKey = (value: string | number | any, key: string, index?: string | number) => {
    const newDataSource = dataSource.map((item) => ({ ...item }));
    if ((isNumber(index) || isString(index)) && key) {
      newDataSource[index][key] = value;
      setDataSource(newDataSource);
    }
  };

  const renderSpecColumns: ProColumns<ProductSpecSkuData>[] = useMemo(() => {
    return specs.map((item, index) => {
      return {
        title: item.specName,
        key: 'specName',
        dataIndex: 'customCode',
        width: 100,
        align: 'center',
        editable: false,
        render: (val, record, i) => {
          if ((isShowFirstImage || record?.mainSpecInfo?.image?.fileUrl) && !index) {
            return (
              <Space>
                <UploadImageField
                  value={
                    record?.mainSpecInfo?.image
                      ? [
                          {
                            link: record.mainSpecInfo?.image.fileUrl,
                            fileId: record.mainSpecInfo?.image?.fileId,
                          } as any,
                        ]
                      : undefined
                  }
                  maxCount={1}
                  onChange={(value) => {
                    if (value?.[0]) {
                      const data = {
                        image: {
                          externalUrl: '',
                          fileId: value?.[0]?.fileId,
                          fileUrl: value?.[0]?.link,
                          type: 'IMAGE',
                        },
                        spec: {
                          specName: item.specName,
                          specValue: record?.specs?.[index]?.specValue,
                        },
                      };
                      changeValueByKey(data, 'mainSpecInfo', i);
                    } else {
                      changeValueByKey(undefined, 'mainSpecInfo', i);
                    }
                  }}
                />
              </Space>
            );
          }
          return <div>{record?.specs?.[index]?.specValue}</div>;
        },
      };
    });
  }, [specs, isShowFirstImage]);

  const columns: ProColumns<ProductSpecSkuData>[] = [
    {
      title: '名称',
      key: 'skuName',
      dataIndex: 'skuName',
      align: 'center',
      width: 150,
    },
    {
      title: '属性值',
      dataIndex: 'skuAttrName',
      align: 'center',
      width: 100,
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      align: 'center',
      width: 150,
    },

    {
      title: '商品毛重(g)',
      dataIndex: 'grossWeight',
      valueType: 'digit',
      fieldProps: {
        precision: 2,
      },
      formItemProps: {
        required: true,
        rules: [
          {
            validator(rule, value) {
              if (!value) {
                return Promise.reject(new Error('请填写商品毛重'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      align: 'center',
      width: 120,
    },
    {
      title: '商品净重(g)',
      dataIndex: 'netWeight',
      valueType: 'digit',
      fieldProps: {
        precision: 2,
      },
      align: 'center',
      width: 120,
    },
    {
      title: '商品尺寸(cm)',
      dataIndex: '',
      align: 'center',
      width: 200,
      editable: false,
      render: (_, record, index) => {
        return (
          <Space key={index} >
            <InputNumber
              style={{ width: 60 }}
              value={record?.sizeLength}
              placeholder="长"
              disabled={disabled}
              onChange={(val) => changeValueByKey(val, 'sizeLength', index)}
            />
            <InputNumber
              style={{ width: 60 }}
              value={record?.sizeWidth}
              placeholder="宽"
              disabled={disabled}
              onChange={(val) => changeValueByKey(val, 'sizeWidth', index)}
            />
            <InputNumber
              style={{ width: 60 }}
              value={record?.sizeHeight}
              placeholder="高"
              disabled={disabled}
              onChange={(val) => changeValueByKey(val, 'sizeHeight', index)}
            />
          </Space>
        );
      },
    },
    {
      title: '商品效期',
      dataIndex: 'guarantyPeriod',
      valueType: 'digit',
      align: 'center',
      width: 120,
    },
  ];

  useEffect(() => {
    setEditableKeys(dataSource?.map((item) => item?.skuId || item?.id) as string[]);
    formRef.current?.submit();
  }, [dataSource]);

  return (
    <>
      <div style={{ width: '100%' }}>
        <EditableProTable<ProductSpecSkuData>
          columns={[...renderSpecColumns, ...columns]}
          scroll={{ x: 800 }}
          rowKey={(item: any) => item.skuId || item.id}
          value={dataSource}
          className={styles.editTable}
          recordCreatorProps={false}
          bordered
          actionRef={actionRef}
          editableFormRef={formRef}
          editable={
            disabled
              ? {}
              : {
                  type: 'multiple',
                  editableKeys: editableKeys as React.Key[],
                  onValuesChange: (record, recordList) => {
                    setDataSource(recordList);
                    formRef.current?.submit();
                  },
                }
          }
          onChange={() => {
            formRef.current?.submit();
          }}
        />
      </div>
    </>
  );
};

export default ProductSpecSkuList;
