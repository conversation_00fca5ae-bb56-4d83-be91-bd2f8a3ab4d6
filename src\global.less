@import '~antd/es/style/variable.less';

html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// Compatible with IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.ant-modal-content{
  border-radius: 8px !important;
}
.ant-modal-header{
  border-radius: 8px 8px 0 0 !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  background-color: #0d5aa7 !important;

}
.ant-modal-header > div{
  color: #fff !important;
  font-size: 14px !important;
  font-weight: bold !important;
}
.ant-modal-close-x{
  height: 42px !important;
  line-height: 42px !important;
  color: #fff !important;
}
.ant-modal-body {
  padding: 15px !important;
}
.ant-btn{
  border-radius: 5px !important;
  //height: 26px !important;
  padding: 0 8px 0 8px !important;
}
.ant-input {
  //padding: 2px 8px 2px 8px !important;
  border-radius: 5px !important;
}
.ant-form-item-control-input-content{
  //height: 28px !important;
  //min-height: 28px !important;

}
.ant-input-affix-wrapper {
  //height: 28px !important;
  //min-height: 28px !important;
  border-radius: 5px !important;
  //padding: 1px !important;
}
.ant-select > .ant-select-selector{
  //padding: 2px 8px 2px 8px !important;
  //height: 28px !important;
}
.ant-select-selector {
  border-radius: 5px !important;
  //line-height: 28px !important;
}
.ant-select-selection-item {
  //line-height: 26px !important;
}
.ant-select-selection-placeholder {
  //line-height: 26px !important;
}

.ant-picker {
  //height: 28px !important;
  border-radius: 5px !important;
}

.ant-pro-table-search{
  padding: 0 24px 0 0 !important;
  margin-bottom: 5px !important;
}
th{
  padding: 4px 8px 4px 8px !important;
}

.ant-pagination-prev{
  border: 1px solid #AAA;
  border-radius: 5px;
  margin-right: 5px !important;

}
.ant-pagination-next{
  border: 1px solid #AAA;
  border-radius: 5px;
  margin-left: 5px !important;
}
.ant-pagination-item-active{
  border-radius: 5px;
}
.ant-table-tbody td div {
  font-size: 13px;
}
.ant-form .ant-form-item{
  margin-bottom: 10px;
  margin-right: 10px !important;
}


