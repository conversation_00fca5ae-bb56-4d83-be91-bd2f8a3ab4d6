import mallRequest from '@/utils/mallRequest';
import mallApiConfig from '../../../../../config/mallApiConfig';
import mallRequest1 from "@/utils/mallRequest1";

export interface UploadImageResult {
  fileId: string;
  link: string;
}

export async function uploadImage(fileName: string, fileContent: any) {
  return mallRequest<API.ApiBaseResult<UploadImageResult>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/file/uploadImg',
    method: 'POST',
    data: fileContent,
    params: {
      fileName,
    },
  });
}

export async function uploadVideo(fileName: string, fileContent: any) {
  return mallRequest<API.ApiBaseResult<UploadImageResult>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/file/uploadVideo',
    method: 'POST',
    data: fileContent,
    params: {
      fileName,
    },
  });
}

export async function uploadFile(fileName: string, fileContent: any) {
  return mallRequest<API.ApiBaseResult<UploadImageResult>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/file/uploadFile',
    method: 'POST',
    data: fileContent,
    params: {
      fileName,
    },
  });
}


/********************************************************************************
 ********************************** 公共请求函数封装 ******************************
 *******************************************************************************
 *******************************************************************************
 *******************************************************************************/
/**
 * 分页查询
 * @param path
 * @param data
 */
export async function reqByPage(path: string, data: any & API.QueryPageParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<any[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: path,
      method: 'POST',
      data,
    },
  );
}

/**
 * 查询
 * @param path
 * @param data
 */
export async function reqByBody(path: string, data: any) {
  return mallRequest<API.ApiBaseResult<any>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: path,
      method: 'POST',
      data,
    },
  );
}

/***
 * 查询数据
 * @param path
 * @param data
 */
export async function reqByUrl(path: string, data: any) {
  return mallRequest<API.ApiBaseResult<any>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: path,
      method: 'POST',
      params: data,
    },
  );
}

/**
 * 执行保存添加
 * @param path
 * @param data
 */
export async function executeSave(path: string, data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'POST',
    requestPath: path,
    data
  });
}

/**
 * 导出模板
 * @param path
 */
export async function downloadDataTemplate(path: string) {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: path,
      method: 'GET',
      responseType: 'blob',
    },
  );
}

/**
 * 导出数据
 * @param path
 */
export async function downloadData(path: string) {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: path,
      method: 'POST',
      responseType: 'blob',
    },
  );
}

//财务导出
export async function downloadDataByParams(path: string, data?: any) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: path,
    data,
    responseType: 'blob',
  });
}
