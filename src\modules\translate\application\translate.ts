import { isArray } from 'lodash';
import type { GoogleTranslateParams } from '../infra/translate';
import { googleTranslate } from '../infra/translate';

export const LanguageMap = [
  {
    key: 'zh',
    name: '简体中文',
  },
  {
    key: 'en',
    name: '英文',
  },
];

export const useTranslate = () => {
  const checkIsChrome = () => {
    const userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    return userAgent.indexOf('Chrome') > -1;
  };

  const translate = async (params: GoogleTranslateParams) => {
    const res = await googleTranslate(params);
    if (isArray(res)) {
      const result = res[0];
      const string = result?.map((item: string[]) => item[0]).join('');
      return string;
    }
    return res;
  };
  return {
    translate,
    checkIsChrome,
  };
};
