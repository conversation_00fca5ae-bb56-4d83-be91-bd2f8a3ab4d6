import CommonModal from '@/components/Common-UI/CommonModal';
import { forwardRef, memo, useImperativeHandle, useRef, useState } from 'react';
import type { ModalAction, CommonModalProps } from '@/components/Common-UI/CommonModal';

type ModalProps = CommonModalProps<any>;
const AddReamrk = forwardRef((props: { onClose?: () => void }, ref) => {
  const modalRef = useRef<ModalAction<any>>();
  const [record, setRecord] = useState<any>();

  // 1) Export
  useImperativeHandle(ref, () => ({
    open: modalRef?.current?.open,
  }));

  // 2) Anction
  const onOpen: ModalProps['onOpen'] = (row, params) => {
    setRecord(row);
  };
  const onClose: ModalProps['onClose'] = () => {
    // props.onClose && props.onClose();
  };
  const onConfirm: ModalProps['onConfirm'] = (row) => {
    props.onClose && props.onClose();
    modalRef.current?.close();
  };

  return (
    <CommonModal title="添加备注" modalRef={modalRef} onOpen={onOpen} onClose={onClose} onConfirm={onConfirm}>
      DEMO
    </CommonModal>
  );
});

export default memo(AddReamrk);
