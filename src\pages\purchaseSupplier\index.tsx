import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import { But<PERSON>, Row } from 'antd';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { queryOrderList } from './api';
import type { OrderItem } from './api';
import type { CommonTableAction, CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import { formatDate } from '@/utils/utils';

type TableProps = CommonTableProps<OrderItem>;

const ShelfOrder = memo(() => {
  const setColumn = useColumn<OrderItem>({ align: "center" });
  const setSearch = useSearch<OrderItem>();
  const [selecteRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const tableRef = useRef<CommonTableAction>();

  // 1) Request
  const fetchRequest = useMemo<TableProps['fetchRequest']>(() => {
    return async (params: any) => {
      // 1 获取数据
      const { current, pageSize, ...rest } = params;
      const pageCondition = { pageSize, pageNum: current };
      // 2 查询参数
      const { body } = await queryOrderList({ pageCondition, ...rest });
      // 3 返回值
      return {
        data: body?.items || [],
        total: body?.pageMeta?.total || 0,
      };
    };
  }, []);

  // 2) Column
  const columns = useMemo(() => {
    return [
      setColumn('采购单号', 'purchaseOrderCode', { width: 140 }),
      setColumn('供应商名称', 'supplierName'),
      setColumn('采购公司名称', 'purchaseEntity', { width: 140 }),
      setColumn('收货仓库', 'purchaseWarehouse'),
      setColumn('收货地址', 'warehouseDetails'),
      setColumn('采购总金额', 'totalAmount'),
      setColumn('单据状态', 'purchaseOrderStatus'),
      setColumn('审核状态', 'auditStatus', { valueEnum: { 0: "未审核", 1: "已审核" } }),
      setColumn('收货情况', 'receivingInfo'),
      setColumn('预计到货时间', 'estimatedTimeArrival', { format: formatDate, width: 150 }),
      setColumn('备注', 'remark'),
      setColumn('创建人', 'purchaseUserName'),
      setColumn('创建时间', 'gmtCreate', { format: formatDate, width: 150 }),
    ];
  }, [setColumn]);

  // 3) Operate
  const actions = useMemo<TableProps['actions']>(() => {
    return {
      items: [
        {
          name: '编辑',
          onAction: (row) => { },
        },
        {
          name: '审核',
          onAction: (row) => { },
        },
        {
          name: '作废',
          onAction: (row) => { },
        },
        {
          name: '收货完成',
          onAction: (row) => { },
        },
        {
          name: '删除',
          onAction: (row) => { },
        },
      ],
      align: "center",
      width: 240
    };
  }, []);

  // 4) ToolTip
  const toolBarRender = useCallback(() => {
    return (
      <Row justify="end" className="toolbar-box">
        <Button type="primary" disabled={selecteRowKeys.length === 0}>
          导出
        </Button>
        <Button type="primary" disabled={selecteRowKeys.length === 0}>
          新增采购单
        </Button>
      </Row>
    );
  }, [selecteRowKeys]);

  return (
    <div className="page-shelf-order">
      <CommonTable<OrderItem>
        rowKey="purchaseOrderCode"
        autoScrollX
        defaultPageSize={20}
        tableRef={tableRef}
        fetchRequest={fetchRequest}
        columns={columns}
        actions={actions}
        toolBarRender={toolBarRender}
        tableRest={{ size: "small" }}
        onSelection={(selectedRowKeys) => {
          setSelectedRowKeys(selectedRowKeys as string[]);
        }}
      />
    </div>
  );
});

export default ShelfOrder;
