import { Cascader } from 'antd';
import { memo, useEffect, useState } from 'react';
import { queryAllTagGroup, queryTagList } from './api';
import type { Option } from './api';
import type { DefaultOptionType } from 'antd/lib/select';

const { SHOW_CHILD } = Cascader;
const SelectTag = memo((props: Record<keyof any, any>) => {
  const [options, setOptions] = useState<Option[]>([]);

  // 1) 加载子选项
  const loadData = async (selectedOptions: Option[]) => {
    // 1 获取点击项
    const target = selectedOptions[selectedOptions.length - 1];
    if (target.isLoad) return;
    // 2 显示加载状态
    target.loading = true;
    // 3 发送请求
    const { body } = await queryTagList({ pageCondition: { pageNum: 1, pageSize: 50 }, tagGroupId: target.value as string });
    target.loading = false;
    target.isLoad = true;
    target.children = (body.items || []).map((child) => ({ value: child.tagName, label: child.tagName }));
    // 4 设置状态
    setOptions([...options]);
  };

  // 2) 选择事件
  const onChange = (choosed: Option[][]) => {
    let arr: any = choosed;
    if (arr === undefined) {
      props.onChange && props.onChange(arr);
      return;
    }
    if (!props.multiple) {
      arr = choosed.length === 1 ? '' : choosed[choosed.length - 1];
    } else {
      arr = choosed.filter((item) => item.length >= 2); // 长度必须两个以上
    }
    // @ts-ignore
    props.onChange && props.onChange(arr);
  };

  // 3) Mounted
  useEffect(() => {
    queryAllTagGroup().then((res) => {
      const { body = [] } = res;
      setOptions(body.map((v) => ({ value: v.tagGroupId, label: v.groupTitle, isLeaf: false })));
    });
  }, []);
  const filter = (inputValue: string, path: DefaultOptionType[]) => {
    return path.some((option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  };

  return (
    <>
      <Cascader
        className="tag-cascader"
        style={{ width: '100%' }}
        options={options}
        changeOnSelect
        multiple={props.multiple}
        showCheckedStrategy={SHOW_CHILD}
        expandTrigger="hover"
        loadData={loadData as any}
        {...props}
        onChange={onChange as any}
        showSearch={{ filter }}
      />
    </>
  );
});

export default SelectTag;
