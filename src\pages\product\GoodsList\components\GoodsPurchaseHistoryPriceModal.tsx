import type { ModalProps } from 'antd';
import {Modal} from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import React, {useEffect} from 'react';
import { ProTable } from '@ant-design/pro-components';
import {useRequestTable} from "@/hooks/useRequestTable";
import {Goods} from "@/modules/goods/domain/goods";
import {formatDate} from "@/utils/utils";
import {reqByPage} from "@/modules/common/infra/api/common";
import {ArrowDownOutlined, ArrowUpOutlined} from "@ant-design/icons";

// 定义参数格式
export type GoodsHistoryPriceModalProps = {
  reqParams: Goods;
  onFinish: (values: any) => void;
} & ModalProps;

const GoodsPurchaseHistoryPriceModal = (props: GoodsHistoryPriceModalProps) => {
  const { onFinish, reqParams, ...rest } = props;
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage("/purchase-mgmt-biz/purchase-center/order/historyPricePageQuery",{
      ...params,
      sku: reqParams?.sku
    });
  });

  //
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [reqParams]);

  const columns: ProColumns<any>[] = [
    {
      title: '采购单号',
      align: 'left',
      dataIndex: 'purchaseOrderCode',
      render: (_, record) => {
        return <div>{record?.purchaseOrderCode}</div>;
      },
    },
    {
      title: '供应商',
      align: 'left',
      dataIndex: 'supplierName',
      render: (_, record) => {
        return <div>{record?.supplierName}</div>;
      },
    },
    {
      title: 'SKU',
      align: 'left',
      dataIndex: 'sku',
      render: (_, record) => {
        return <div>{record?.sku}</div>;
      },
    },
    {
      title: '上次价格',
      align: 'left',
      dataIndex: 'originalPrice',
      render: (_, record) => {
        return <div>{record?.originalPrice?.toFixed(2)}</div>;
      },
    },
    {
      title: '当前价格',
      align: 'left',
      dataIndex: 'purchasePrice',
      render: (_, record) => {
        return <div>{record?.purchasePrice?.toFixed(2)}</div>;
      },
    },
    {
      title: '趋势',
      align: 'left',
      dataIndex: 'trend',
      render: (_, record) => {
        return <div>{record?.trend == 0 ? '--' : record?.trend == 1 ? <ArrowUpOutlined style={{ color: 'red' }} /> : <ArrowDownOutlined style={{ color: 'green' }} /> }</div>;
      },
    },

    {
      title: '时间',
      align: 'left',
      dataIndex: 'gmtCreate',
      render: (_, record) => {
        return <div>{formatDate(record?.gmtCreate)}</div>;
      },
    },
  ];

  return  <Modal {...rest} title="历史采购价" closable={false} width="80%"  onOk={()=>onFinish}>
      <ProTable<any>
        search={false}
        options={false}
        size={"small"}
        scroll={{y: 550}}
        actionRef={actionRef}
        request={fetchList}
        columns={columns}
        revalidateOnFocus={false}
        rowKey="id"
        bordered
      />
    </Modal>
};

export default GoodsPurchaseHistoryPriceModal;
