import type {ModalProps} from 'antd';
import {Button, message, Modal, notification, Row, Space, Table, Typography} from 'antd';
import type {ProColumns} from '@ant-design/pro-table';
import React, {useEffect, useState} from 'react';
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {aliBatchPayment, financeAudit, financeAuditQuery} from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import {useRequest} from 'ahooks';
import {
  alibabaStatusTextEnum,
  financeErrorTypeEnum,
} from "@/modules/purchaseOrder/domain/purchaseOrder";
import {getTradeOrder} from "@/modules/purchaseOrder/infra/api/purchaseOrder";

const auditData= async (param: any)=>{
  const data=await financeAuditQuery(param);
  return data;
}

const CheckedOrderModal = (props: CheckedOrderModalProps) => {
  const {onFinish, orderData,onCancel, ...rest} = props;

  const param = {
    "orderIdList": orderData.map((item) => item.id),
  }

  const { data, refresh, loading} = useRequest(() => auditData(param).then(item=>item.body));

  const getOrderAmount =()=>{
    let purchaseOrderAmount=0;
    for (const item in data) {
      purchaseOrderAmount+=data[item].shippingFee+data[item].totalPrice+data[item].applyRefundAmount-data[item].applyPromotionAmount;
    }
    return purchaseOrderAmount?.toFixed(2);
  }

  //提交财务付款
  const onOkManage = () =>{

    aliBatchPayment(param).then((res) => {
      if (res.status.success) {
        window.open(res.body);
        refresh();
      }
    })

  }


  //确认支付
  const confirmPayment=()=>{
    getTradeOrder(param.orderIdList).then((result) => {
      if (result.status.success) {
        message.success("刷新成功")
      }else {
        notification.error({
          duration: null,
          placement: "top",
          description: <Space direction="vertical" size="small">{result.body.split("<br>")}</Space>,
          message: "异常订单",
          style: {
            width: "auto"
          }
        });
      }
      onCancel();
    });
  }

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '采购单号',
      dataIndex: 'orderCode'
    },
    {
      title: '标题',
      hideInSearch: true,
      dataIndex: 'title'
    },
    {
      title: '采购员',
      hideInSearch: true,
      dataIndex: 'purchaseUsername'
    },
    {
      title: '1688订单号',
      hideInSearch: true,
      dataIndex: 'platformOrderCode'
    },
    {
      title: '更新时间',
      hideInSearch: true,
      dataIndex: 'platformSyncDataTime'
    },
    {
      title: '1688状态',
      hideInSearch: true,
      dataIndex: 'platformStatus',
      render: (v, record) => {
        const alibabaStatus = alibabaStatusTextEnum[record?.platformStatus];
        const options = [
          alibabaStatus != undefined ? (
            <span>
              <span className="ant-badge-status-dot ant-badge-status-success"></span>
              <span className="ant-badge-status-text">{alibabaStatus}</span>
            </span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '1688总价',
      hideInSearch: true,
      dataIndex: 'platformOrderAmount',
      render: (v, record) => {
        return record.platformOrderAmount?.toFixed(2);
      }
    },
    {
      title: '应付货款',
      hideInSearch: true,
      dataIndex: 'totalPrice',
      render: (v, record) => {
        const amount=record.totalPrice+record.shippingFee+record.applyRefundAmount-record.applyPromotionAmount;
        return amount?.toFixed(2);
      }
    },
    {
      title: '异常',
      hideInSearch: true,
      dataIndex: 'paymentType',
      render: (v, record) => {
        const options = [];
        record.financeErrorList.map((item)=>{
            if(item!=null && item!=""){
              options.push(<span>
              <span className="ant-badge-status-dot ant-badge-status-error"></span>
              <span className="ant-badge-status-text">{financeErrorTypeEnum[item]}</span>
            </span>)
            };
        });
        return <Space direction="vertical">{options}</Space>;
      }
    },


  ];


  return (
    <Modal {...rest} title="财务付款" closable={false} width="90%"  footer={[
      <Button key="back" loading={loading} onClick={onCancel}>
        关闭
      </Button>,
      <Button key="submit" type="primary" loading={loading} onClick={onOkManage}>
        申请支付
      </Button>,
      <Button key="pay" style={{float: "left",backgroundColor:"#FF8800",border:"0"}} type="primary" loading={loading} onClick={confirmPayment}>
        确认支付
      </Button>,
    ]}  destroyOnClose>
      <Row>
        <b>选中单数：{data!=null?data.length:0} &nbsp;</b>
        <b>勾选应付总金额：{getOrderAmount()} &nbsp;</b>
      </Row>
      <Table
        dataSource={data}
        scroll={{ y: 550 }}
        columns={columns}
        rowKey="id"
        pagination = {false}
        loading={loading}
      />
    </Modal>
  );
}

// 定义参数格式
export type CheckedOrderModalProps = {
  orderData: PurchaseOrder[];
  onFinish: (values: PurchaseOrder[]) => void;

} & ModalProps;
export default CheckedOrderModal;
