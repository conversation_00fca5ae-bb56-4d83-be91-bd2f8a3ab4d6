import { MenuItem } from "@/modules/user-center/infra/role";

export const formatTreeData = (menus: MenuItem[]): any => {
  return menus.map((item) => {
    return {
      title: item.title,
      key: item.menuId,
      children: item.childrenMenu ? formatTreeData(item.childrenMenu) : [],
    };
  });
};

export const findMenu = (menus: MenuItem[], id: string): MenuItem | undefined => {
  let currentItem: MenuItem | undefined;
  menus.forEach((item) => {
    if (item.menuId === id) {
      currentItem = item;
    }
    if (!currentItem && item.childrenMenu) {
      currentItem = findMenu(item.childrenMenu, id);
    }
  });
  return currentItem;
};
