import React, {useEffect, useState} from 'react';
import {Modal, ModalProps, Form, message} from 'antd';
import ProForm, {ProFormText} from "@ant-design/pro-form";
import {ProFormSelect} from "@ant-design/pro-components";
import {executeSave} from "@/modules/common/infra/api/common";
import {GoodsComposeEnum, GoodsComposeRelation} from "@/modules/product/domain/goodsSync";

// 定义参数格式
export type editProps = {
  onFinish: () => void;
  goodsCompose: GoodsComposeRelation;
} & ModalProps;

export default (props: editProps) => {
  const [form] = ProForm.useForm();
  const {onFinish, goodsCompose, ...rest} = props;

  useEffect( () => {
      form.resetFields();
      if (goodsCompose) {
        form.setFieldsValue(goodsCompose);
      }
    },[goodsCompose]);

  const onSubmit = () => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      executeSave('/sales-mgmt-biz/sales-center/goods-compose/createGoodsCompose', params).then((result) => {
        if (result.status.success) {
          message.success('操作成功');
          onFinish();
        }
      });
    })
  }

  return (
    <>
      <Modal {...rest} title="组合关系" className={'globalEnterKeySubmit'} onOk={() => onSubmit()} destroyOnClose={true}>
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProFormText name="id" hidden={true}/>
          <ProFormSelect width={"md"} label="销售类型" name="composeType" rules={[{required: true}]} valueEnum={
            GoodsComposeEnum
          }/>
          <ProFormText width={"md"} label="组合关系" name="relation" rules={[{required: true}]}/>
        </Form>
      </Modal>
    </>
  );
};
