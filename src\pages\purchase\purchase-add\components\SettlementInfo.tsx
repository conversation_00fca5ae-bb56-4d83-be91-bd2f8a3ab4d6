import { memo } from 'react';
import { useFormCom, useFormDependency } from '@/components/Common-UI/CommonForm/hook';
import { PayTypeMap, SettleCircleTypeMap, SettleTypeMap } from '../../purchase-supplier/api';

const SettlementInfo = memo(() => {
  const setComp = useFormCom({ required: false });
  const setDependency = useFormDependency();

  return (
    <div className="settlement-info card">
      <div className="title">
        <p>结算资料</p>
      </div>
      <div className="form-items">
        {setComp('结算类型', 'settleType', { disabled: true, valueEnum: SettleTypeMap })}
        {setComp('结算周期', 'settleCircle', { disabled: true, valueEnum: SettleCircleTypeMap })}
        {setComp('结算币种', 'settleCurrency', { disabled: true })}
        {setComp('支付方式', 'payType', { disabled: true, valueEnum: PayTypeMap })}
        {setComp('运输方式', 'transportationType', { valueEnum: { 自提: '自提', 快递: '快递', 物流: '物流', 送货: '送货' } })}
        {setDependency(["transportationType"], ({ transportationType }) => {
          return transportationType && transportationType !== "自提" ? setComp('物流承运商', 'logisticsCarrier', { maxLength: 50, required: true }) : undefined
        })}
        {setComp('运输承担方', 'freightBearer', { valueEnum: { 供应商: '供应商', 采购方: '采购方' } })}
        {setDependency(["freightBearer"], ({ freightBearer }) => {
          return freightBearer && freightBearer === "采购方" ?
            setComp('总运费', '', {
              render: () =>
                <div className="shipping-fee">
                  {setComp('', 'shippingFee', { min: 0, max: 1000000000, fieldProps: { precision: 2 } })}
                  {setComp('', 'shippingRule', { valueEnum: { 按价格分摊: '按价格分摊' }, disabled: true, initialValue: "按价格分摊" })}
                </div>
            })
            : undefined
        })}
        {setComp('跟踪物流单号', 'trackingNumber', { maxLength: 50 })}
      </div>
    </div>
  );
});

export default SettlementInfo;
