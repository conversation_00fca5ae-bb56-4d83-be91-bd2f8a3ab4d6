import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {EndPointUser} from "@/modules/user-center/domain/user";

export interface UserMenuItem {
  bizContext: string;
  endpoint: string;
  menuId: string;
  menuName: string;
  permission: string;
  resourceType: string;
}

export interface UserMenus {
  isSuperAdmin: string;
  menus: UserMenuItem[];
  userId: string;
}

export async function getUserMenus() {
  return mallRequest<API.ApiBaseResult<UserMenus>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/tenant/endpoint/user-biz/getUserMenus',
    method: 'POST',
  });
}

export async function getAllUser() {
  return mallRequest<API.ApiBaseResult<any>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/order/getUser',
    method: 'POST',
  });
}


