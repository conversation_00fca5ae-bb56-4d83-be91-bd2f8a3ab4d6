import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn } from '@/components/Common-UI/CommonTable/hook';
import type { CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import useTableSelection from '@/hooks/useTableSelection';
import { Button, Modal, Row, Space, Tabs, message } from 'antd';
import { map } from 'lodash';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { Task } from './api';
import { TaskStatusEnum, TaskStatusTextEnum, TaskType } from './api';
import styles from './styles.less';
import { useTask } from './useTask';
type TableProps = CommonTableProps<Task>;
const TaskList: React.FC = () => {
  const { taskList, batchDownload } = useTask();
  const { actionRef, fetchList } = taskList;
  const { rowSelection, selectedRowsState } = useTableSelection<Task>();
  const [activeTab, setActiveTab] = useState<TaskType>(TaskType.IMPORT);
  const setColumn = useColumn<Task>({ width: 80 });

  useEffect(() => {
    actionRef.current?.reload?.();
  }, [activeTab]);

  const handleBatch = async () => {
    const resultList = map(selectedRowsState, 'singleResult').filter((row) => row);
    if (activeTab === TaskType.EXPORT) {
      if (resultList.length > 0) {
        await batchDownload(resultList);
        Modal.destroyAll();
      } else {
        message.error('当前选择暂无可导出内容');
        Modal.destroyAll();
      }
      return;
    }
    const errorList = map(selectedRowsState, 'errorResult').filter((row) => row);
    Modal.confirm({
      icon: false,
      closable: true,
      title: '请选择下载的文件类型',
      className: styles.batchModal,
      content: (
        <Space>
          <Button
            ghost
            type="primary"
            onClick={async () => {
              await batchDownload(resultList);
              Modal.destroyAll();
            }}
            disabled={!resultList.length}
          >
            {'下载原始文件'}
          </Button>
          <Button
            type="primary"
            danger
            ghost
            onClick={async () => {
              await batchDownload(errorList, `批量导出异常-${moment().format('YYYYMMDDHHmmss')}`);
              Modal.destroyAll();
            }}
            disabled={!errorList.length}
          >
            {'下载异常文件'}
          </Button>
        </Space>
      ),
    });
  };
  const columns = useMemo(() => {
    return [
      setColumn('序号', 'index', {
        valueType: 'index',
        width: 48,
        fixed: 'left',
      }),
      setColumn(activeTab === TaskType.IMPORT ? '导入类型' : '导出类型', 'title', {
        hideInSearch: false,
      }),
      setColumn(activeTab === TaskType.IMPORT ? '导入行数' : '导出行数', 'totalCount'),
      setColumn('任务状态', 'state', {
        width: 80,
        hideInSearch: false,
        valueEnum: {
          [TaskStatusEnum.FAIL]: { text: TaskStatusTextEnum.FAIL, status: 'Error' },
          [TaskStatusEnum.QUEUE]: { text: TaskStatusTextEnum.QUEUE, status: 'Processing' },
          [TaskStatusEnum.RUNNING]: { text: TaskStatusTextEnum.RUNNING, status: 'Processing' },
          [TaskStatusEnum.SUCCESS]: { text: TaskStatusTextEnum.SUCCESS, status: 'Success' },
          [TaskStatusEnum.WAIT]: { text: TaskStatusTextEnum.WAIT, status: 'Warning' },
          [TaskStatusEnum.SECTION]: { text: TaskStatusTextEnum.SECTION, status: 'Success' },
        },

      }),
      setColumn('进度说明', 'successCount', {
        width: 200,
        // @ts-ignore
        render: (v: number, record: Task) => {
          return `处理成功数量:${record.successCount || 0}, 异常数量:${record.errorCount || 0}, 耗时:${record.implementTime || 0
            }s`;
        },
      }),
      setColumn('操作人', 'submitterBizName'),
      setColumn(activeTab === TaskType.IMPORT ? '导入时间' : '导出时间', 'gmtCreate', {
        width: 150,
        valueType: 'dateTime',
      }),
    ];
  }, [setColumn, activeTab]);
  const toolBarRender = useCallback(() => {
    return (
      <Row justify="end" className="toolbar-box">
        <Button key="batch" onClick={handleBatch} disabled={!selectedRowsState.length}>
          批量下载
        </Button>
      </Row>
    );
  }, [selectedRowsState]);
  // @ts-ignore
  const actions = useMemo<TableProps['actions']>(() => {
    return {
      width: activeTab === TaskType.IMPORT ? 150 : 80,
      align: 'center',
      items: [
        {
          // @ts-ignore
          name: activeTab === TaskType.IMPORT ? '下载原始文件' : '下载文件',
          onAction: (row) => window.open(row?.singleResult),
          show: (row) => row.singleResult
        },
        {
          // @ts-ignore
          name: '下载异常文件',
          onAction: (row) => window.open(row?.errorResult),
          show: (row) => {
            const errorResult = row?.errorResult?.split(',');
            return activeTab === TaskType.IMPORT && row.errorResult && errorResult.length === 1
          }
        },
        {
          // @ts-ignore
          name: '下载异常文件',
          onAction: (row) => {
            const errorResult = row?.errorResult?.split(',');
            batchDownload(errorResult, `异常文件-${moment().format('YYYYMMDDHHmmss')}`)
          },
          show: (row) => {
            const errorResult = row?.errorResult?.split(',');
            return activeTab === TaskType.IMPORT && row.errorResult && errorResult.length > 1
          }
        },
      ],
    };
  }, [activeTab]);

  return (
    <div className={styles.taskWrapper}>
      <Tabs activeKey={activeTab} className={styles.taskTabs} onTabClick={(k: string) => setActiveTab(k as TaskType)}>
        <Tabs.TabPane tab={'文件导入'} key={TaskType.IMPORT} />
        <Tabs.TabPane tab={'文件导出'} key={TaskType.EXPORT} />
      </Tabs>
      <CommonTable
        rowKey="taskId"
        // @ts-ignore
        tableRef={actionRef}
        columns={columns}
        actions={actions}
        toolBarRender={toolBarRender}
        // @ts-ignore
        fetchRequest={(params, ...args) => {
          return fetchList(
            {
              taskType: activeTab,
              ...params,
            },
            ...args,
          );
        }}
        tableRest={{
          options: false,
          rowSelection: {
            ...rowSelection,
            getCheckboxProps: (record: Task) => ({
              disabled: !record.errorResult && !record.singleResult,
            }),
          }
        }}
        scrollX={1300}
        showCount
      />
    </div>
  );
};

export default TaskList;
