import type { CategorySelectProps } from 'newnary-components/dist/components/CategoryTreeModal';
import { CategorySelect as CategorySelectWrapper, CategoryTreeModal } from 'newnary-components';
import { queryCategoryByParent } from '@/modules/category/infra/api/category';

export const CategorySelect = (
  props: Omit<CategorySelectProps, 'queryCategoryByParent' | 'queryRootCategory'>,
) => {
  const querySubCategory = (parentCategoryId: string | number) => {
    return queryCategoryByParent({ parentCategoryId }).then((res) => {
      return {
        body: res?.body?.map((item) => {
          return {
            categoryId: item.categoryId,
            isLeaf: item.leaf,
            level: item.level,
            name: item.name,
            parentCategoryId: item.parentId,
          };
        }),
      };
    });
  };
  return (
    <CategorySelectWrapper
      {...props}
      queryCategoryByParent={({ parentCategoryId }) => {
        return querySubCategory(parentCategoryId);
      }}
      queryRootCategory={() => {
        return querySubCategory(0);
      }}
      allowClear
    />
  );
};

export default CategoryTreeModal;
