import aliwangwang from "@/assets/images/aliwangwang.gif";
import Permission from "@/components/Permission";
import { getSupplierGoods, getTagsPrintInfo } from "@/modules/goods/infra/api/goods";
import {
  deletePurchaseGoods,
  batchDeletePurchaseGoods,
  getOrderGoods,
  insertOrderGoods,
  modifyQuantityAndPrice, updateOrderGoods
} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import { SupplierGoods } from "@/modules/supplier/domain/vender";
import SelfImage from "@/components/SelfImage";
import ApplyAfterSalesModal from "@/pages/PurchaseOrderList/components/ApplyAfterSalesModal";
import ModifyQuantityAndPrice from "@/pages/PurchaseOrderList/components/ModifyQuantityAndPrice";
import PurchaseOrderGoodsCreateModal from "@/pages/PurchaseOrderList/components/PurchaseOrderGoodsCreateModal";
import { PurchaseOrderGoods } from "@/pages/PurchaseOrderList/data";
import BindAliProductModal from "@/pages/supplier/SupplierGoodsList/components/BindAliProductModal";
import { copyText } from "@/utils/comUtil";
import { ArrowDownOutlined, ArrowUpOutlined, SnippetsTwoTone, WarningOutlined } from "@ant-design/icons/lib";
import { ProFormText } from "@ant-design/pro-components";
import type { ProColumns } from '@ant-design/pro-table';
import { useRequest } from "ahooks";
import { Button, Card, Dropdown, Form, Image, Input, Menu, Modal, Space, Spin, Table, Typography, message, notification } from "antd";
import { useState, useRef } from 'react';
import { Access, history } from "umi";

const { Text } = Typography;
export type PurchaseOrderGoodsListProps = {
  orderId: string;
  supplierId: string;
  orderData: any
  aliWangWangLink?: string;
};
export const PurchaseOrderGoodsList = (props: PurchaseOrderGoodsListProps) => {
  const { orderId, supplierId, orderData, aliWangWangLink } = props;
  const { data, refresh, loading } = useRequest(() => getOrderGoods(orderId).then((res) => res.body));
  const [selectedRowsState, setSelectedRows] = useState<PurchaseOrderGoods[]>([]);
  const [modifyQuantityAndPirce, setModifyQuantityAndPirce] = useState<boolean>(false);
  const [orderGoodsCreate, setOrderGoodsCreate] = useState<boolean>(false);
  const [applyAfterSales, setApplyAfterSales] = useState<boolean>(false);
  const [bindModal, setBindModal] = useState<boolean>(false);
  const [supplierGoodsData, setSupplierGoodsData] = useState<SupplierGoods>();
  //修改价格与数量
  const modifyPriceOrQuantity = (values: PurchaseOrderGoods[]) => {

    const obj = {
      "orderId": orderId,
      "orderGoodsList": values
    }

    modifyQuantityAndPrice(obj).then((res) => {
      if (res.status.success) {
        message.success("申请成功");
        refresh();
      }
    })
  }

  const modify = () => {
    if (selectedRowsState.length <= 0) {
      message.error("请选择需要修改的sku");
      return null;
    }
    setModifyQuantityAndPirce(true);
  }


  /**
   * 修改采购单商品
   * @param record
   */
  const [form] = Form.useForm();
  const updateOrderGoodsModal = (record: PurchaseOrderGoods) => {
    Modal.confirm({
      icon: false,
      content: (
        <Form form={form}>
          <ProFormText label="名称" name="name" initialValue={record.name} disabled/>
          <ProFormText label="备注" name="goodsRemark" initialValue={record.goodsRemark} />
        </Form>
      ),
      onOk: function () {
        const params = {
          name: form.getFieldValue('name'),
          goodsRemark: form.getFieldValue('goodsRemark'),
          id: record.id,
        };

        updateOrderGoods(params).then((result) => {
          if (result.status.success) {
            refresh();
            notification.success({ message: '修改成功' });
          }
        });

      },
    });
    form.resetFields();
  };

  const clickAfterSales = () => {
    if (selectedRowsState.length <= 0) {
      message.error("请选择需要申请的sku");
      return null;
    }
    setApplyAfterSales(true);
  }

  const createFinish = () => {
    setOrderGoodsCreate(false);
    refresh();
  }

  /**
   * 打印标签
   * @constructor
   */
  const printTags = (orderGoods: PurchaseOrderGoods) => {
    getTagsPrintInfo({ sku: orderGoods.sku }).then(res => {
      window.open("/purchasing-center/print/goodsTagsPrint?position=" + (res?.body?.position || '') + "&sku=" + orderGoods.sku + "&goodsWeight=" + orderGoods.netWeight + "&goodsName=" + encodeURIComponent(orderGoods.name) + "&orderCode=" + orderData.orderCode + "&printQuantity=" + (orderGoods.printQuantity ? orderGoods.printQuantity : orderGoods.purchaseQuantity));
    })
  }
  /**
   * 单个删除
   * @param id 
   */
  const deleteGoods = (id: string) => {
    Modal.confirm({
      title: "是否确认删除？",
      icon: false,
      centered: true,
      onOk: function () {
        deletePurchaseGoods(id).then((res) => {
          if (res.status.success) {
            refresh();
            message.success("删除成功")
          }
        });

      },
    });
  }
  /**
   * 批量删除
   * @param id 
   */
  const batchDeleteGoods = () => {
    if (selectedRowsState.length <= 0) {
      message.error("请选择需要删除的sku");
      return;
    }
    Modal.confirm({
      title: "是否确认批量删除选中的SKU？",
      icon: false,
      centered: true,
      onOk: function () {
        batchDeletePurchaseGoods({ ids: selectedRowsState?.map(v => v?.id), purchaseOrderId: orderId }).then((res) => {
          if (res.status.success) {
            refresh();
            message.success("删除成功")
          }
        });

      },
    });
  }
  /**
   * 绑定1688商品
   * @param record
   */
  const bindSupplierGoods = (record: PurchaseOrderGoods) => {
    getSupplierGoods({ supplierId: orderData.supplierId, sku: record.sku }).then((res) => {
      if (res.status.success) {
        setSupplierGoodsData(res.body)
        setBindModal(true)
      }
    })
  }

  const columns: ProColumns<PurchaseOrderGoods>[] = [
    {
      title: "图片",
      dataIndex: 'sku',
      align: "center",
      render: (v, record) => <SelfImage src={record.skuImg} title={record?.name}  width={80} />
    },
    {
      title: "SKU/名称",
      dataIndex: "name",
      width: 200,
      render: (v, record) => {
        const link = aliWangWangLink  ? (<a onClick={function () {
          if (aliWangWangLink != null) {
            window.open(aliWangWangLink);
          }
        }}><img src={aliwangwang} /></a>) : ''
        return <>
          <div style={{ fontWeight: "bold", fontSize: 13 }}>
            {/* {record?.aliLink ? <Dropdown
              placement="topRight"
              overlay={
                  <Menu>
                    <Menu.Item key="detail">
                      <a
                        href={`${record?.aliLink}`}
                        target="_blank"
                        rel="noreferrer"
                      >
                        1688商品链接
                      </a>
                    </Menu.Item>
                  </Menu>
              }
            >
              <span style={{ cursor: 'pointer', color: '#1890ff' }} onClick={() => history.push({ pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })}>{record.sku}</span>
            </Dropdown> : <span style={{ cursor: 'pointer', color: '#1890ff' }} onClick={() => history.push({ pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })}>{record.sku}</span>} */}
            <span style={{ cursor: 'pointer', color: '#1890ff' }} onClick={() => history.push({ pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })}>{record.sku}</span>
            <SnippetsTwoTone onClick={() => copyText(record.sku)} />
          </div>
          <div>{record?.name}</div>
          <div>{link}</div>
        </>;
      }
    },
    {
      title: "上次采购价",
      dataIndex: 'lastPurchasePrice',
      render: (v, record) => {
        return Number(record.lastPurchasePrice)?.toFixed(2);
      }
    },
    {
      title: "当前采购价",
      dataIndex: 'currentPurchasePrice',
      render: (v, record) => {
        return (
          <>
            <span>{Number(record.currentPurchasePrice)?.toFixed(2)}&nbsp;</span>
            <span>{record.lastPurchasePrice > record.currentPurchasePrice ? (
              <ArrowDownOutlined style={{ color: 'green' }} />
            ) : record.lastPurchasePrice < record.currentPurchasePrice ? (
              <ArrowUpOutlined style={{ color: 'red' }} />
            ) : null}
            </span>
          </>
        )
      }
    },
    {
      title: "平台价格",
      dataIndex: 'platformPurchasePrice',
      render: (v, record) => {
        return (
          <>
            {record?.platformPurchasePrice && Number(record?.platformPurchasePrice) * 2 < Number(record?.currentPurchasePrice) ? <span><WarningOutlined style={{ color: "red", fontWeight: "bold" }} /></span> : null}
            <span>{record?.platformPurchasePrice ? Number(record.platformPurchasePrice)?.toFixed(2) : "--"}&nbsp;</span>
            <span>{record?.platformPurchasePrice ? (record.platformPurchasePrice > record.currentPurchasePrice ? (
              <ArrowUpOutlined style={{ color: 'red' }} />
            ) : record.platformPurchasePrice < record.currentPurchasePrice ? (
              <ArrowDownOutlined style={{ color: 'green' }} />
            ) : null) : null}
            </span>
          </>
        )
      }
    },
    {
      title: "采购",
      align: 'center',
      dataIndex: 'purchaseQuantity',
    },
    {
      title: "到货",
      dataIndex: 'arrivalQuantity',
      align: 'center',
      render: (v, record) => {
        return <>
          <span>{record.arrivalQuantity || 0}&nbsp;</span>
          {record?.arrivalQuantity != record?.purchaseQuantity ? <WarningOutlined style={{ color: "#EE7700", fontWeight: "bold" }} /> : null}
        </>;
      }
    },
    {
      title: "售后",
      dataIndex: 'afterSalesQuantity',
      align: 'center',
      render: (v, record) => {
        return record.afterSalesQuantity || 0;
      }
    },
    {
      title: "采购额",
      dataIndex: 'sum',
      render: (v, record) => {
        return (Number(record.currentPurchasePrice) * record.purchaseQuantity)?.toFixed(2);
      }
    },
    {
      title: "实收额",
      dataIndex: 'sum',
      render: (v, record) => {
        return (Number(record.currentPurchasePrice) * record.arrivalQuantity)?.toFixed(2);
      }
    },
    {
      title: "是否需要质检",
      dataIndex: 'needQc',
      render: (v, record) => {
        return <div>{record?.needQc ? "是" : "否"}</div>;
      }
    },
    {
      title: "商品备注",
      dataIndex: 'goodsRemark',
      width: 200,
      render: (v, record) => {
        return <div>{record?.goodsRemark}</div>;
      }
    },
    // {
    //   title: "时间",
    //   dataIndex: 'gmtCreate',
    //   render: (v, record) => {
    //     return moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss");
    //   }
    // },
    {
      title: '打印数量',
      dataIndex: 'printQuantity',
      align: "left",
      width: 100,
      render: (v, record) => {
        return (<Input onChange={(value) => {
          record.purchaseQuantity = Number(value.target.value);
        }} defaultValue={record?.purchaseQuantity}></Input>);
      }
    },
    {
      title: "操作",
      dataIndex: 'gmtCreate',
      width: 200,
      render: (v, record) => {
        return (
          <Space wrap={true} direction="horizontal" aria-colindex={3}>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsBind"}>
              <Button type="primary" size={"small"} style={{ fontSize: 12, borderRadius: 5 }} onClick={() => bindSupplierGoods(record)}>
                绑定
              </Button>
            </Permission>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsUpdate"}>
              <Button type="primary" ghost={true} size={"small"} style={{ fontSize: 12, borderRadius: 5 }} onClick={() => updateOrderGoodsModal(record)}>
                编辑
              </Button>
            </Permission>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsDelete"}>
              <Button type="primary" size={"small"} danger ghost style={{ fontSize: 12, borderRadius: 5 }} onClick={() => deleteGoods(record.id)}>
                删除
              </Button>
            </Permission>
            <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsPrint"}>
              <Button type="primary" size={"small"} ghost={true} style={{ fontSize: 12, borderRadius: 5 }} onClick={() => printTags(record)}>
                打印
              </Button>
            </Permission>
            {!!record?.aliLink &&
            <Button type="primary" size={"small"} ghost={true} style={{ fontSize: 12, borderRadius: 5 }} onClick={() => window.open(record.aliLink, "_blank")}>
              商品源链接
            </Button>
            }
          </Space>
        );
      }
    }
  ];
  let totalAmount = 0;
  let arrivalAmount = 0;
  data?.map((item: { currentPurchasePrice: number; purchaseQuantity: number; arrivalQuantity: number }) => {
    totalAmount += Number(item.currentPurchasePrice) * item.purchaseQuantity;
    arrivalAmount += Number(item.currentPurchasePrice) * item.arrivalQuantity;
  });

  return (
    <>
      <Spin spinning={loading} style={{ paddingTop: 0 }}>
        <Card style={{ border: "none", marginBottom: 10, marginTop: 10 }} bodyStyle={{ padding: 0 }}>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsApplyAfterSales"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", float: "right" }}
              key="create"
              onClick={() => clickAfterSales()}
            >
              申请售后
            </Button>
          </Permission>
          {/* <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsAdd"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", float: "right" }}
              key="create"
              onClick={() => setOrderGoodsCreate(true)}
            >
              添加商品
            </Button>
          </Permission> */}
          <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsAdd"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", float: "right" }}
              key="create"
              onClick={() => setOrderGoodsCreate(true)}
            >
              添加商品
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsApplyEdit"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", float: "right" }}
              key="bundledAlibaba"
              onClick={() => modify()}
            >
              申请修改
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:goodsDelete"}>
            <Button
              size={"small"}
              type="primary"
              style={{ marginRight: 20, fontSize: 13, borderRadius: "5px", float: "right" }}
              key="bundledAlibaba"
              onClick={() => batchDeleteGoods()}
            >
              批量删除
            </Button>
          </Permission>
          <div style={{ marginRight: 10, float: "left", fontWeight: "bold", height: 30 }}>采购总价值：{totalAmount?.toFixed(2)}&nbsp;&nbsp;到货货值：{arrivalAmount?.toFixed(2)}</div>
        </Card>
        <Card bodyStyle={{ padding: 0 }} bordered={false}>
          <Table
            dataSource={data}
            columns={columns}
            rowKey="id"
            size={"small"}
            pagination={false}
            scroll={{ y: 550 }}
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectedRows(selectedRows);
              },
            }}
          />
        </Card>
      </Spin>
      <Access accessible={true}>
        <BindAliProductModal width={"80%"} visible={bindModal} supplierData={supplierGoodsData} onCancel={() => setBindModal(false)} onFinish={() => setBindModal(false)} />
      </Access>
      <ModifyQuantityAndPrice visible={modifyQuantityAndPirce} data={selectedRowsState} onCancel={() => setModifyQuantityAndPirce(false)}
        onFinish={function (res) {
          modifyPriceOrQuantity(res);
          setModifyQuantityAndPirce(false);
        }} />
      <ApplyAfterSalesModal visible={applyAfterSales} data={selectedRowsState} orderData={orderData} onCancel={() => setApplyAfterSales(false)}
        onFinish={function () {
          setApplyAfterSales(false);
        }} />
      <PurchaseOrderGoodsCreateModal visible={orderGoodsCreate} supplierId={supplierId} onCancel={() => setOrderGoodsCreate(false)} onFinish={createFinish} orderId={orderId}/>
    </>
  );
};

export type PurchaseOrderGoodsModalProps = {
  orderId: string;
  supplierId: string;
  orderData: any;
  aliWangWangLink?: string;
};

export default (props: PurchaseOrderGoodsModalProps) => {
  const { orderId, supplierId, orderData, aliWangWangLink } = props;
  return (
    <>
      <PurchaseOrderGoodsList orderData={orderData} orderId={orderId} supplierId={supplierId} aliWangWangLink={aliWangWangLink}/>
    </>
  );
};
