import type { FormInstance } from 'antd';
import { Form } from 'antd';
import { useEffect, useState } from 'react';

function useModalForm<T>(
  effect?: T,
): [FormInstance<any>, boolean, React.Dispatch<React.SetStateAction<boolean>>] {
  const [form] = Form.useForm();
  const [modalVisible, handleModalVisible] = useState<boolean>(false);

  useEffect(() => {
    if (modalVisible) {
      form.setFieldsValue(effect);
    } else {
      form.resetFields();
    }
  }, [effect, form, modalVisible]);

  return [form, modalVisible, handleModalVisible];
}

export default useModalForm;
