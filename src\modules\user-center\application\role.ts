import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import type { ActionType } from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { message, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Role } from '../domain/user';
import { addRole, deleteRole, getMenus, getRoles, QueryEndpointRoleParams, queryUserRolePage, updateRole } from '../infra/role';

export function useRoleTableList() {
  const actionRef = useRef<ActionType>();
  const queryList = usePageQueryRequest(queryUserRolePage);
  return {
    actionRef,
    queryList,
  };
}

export function useRole() {
  const create = async (role: Role) => {
    try {
      const res = await addRole(role);
      if (res.status.success) {
        message.success('创建成功');
      } else {
        message.error('创建失败，请稍后重试');
      }
      return res.status.success;
    } catch (error) {
      message.error('创建失败，请稍后重试');
      return false;
    }
  };

  const update = async (role: Role) => {
    try {
      const res = await updateRole(role);
      if (res.status.success) {
        message.success('更改成功');
      } else {
        message.error('更改失败，请稍后重试');
      }
      return res.status.success;
    } catch (error) {
      message.error('更改失败，请稍后重试');
      return false;
    }
  };

  const remove = (roleId: string) => {
    return new Promise<boolean>((resolve, reject) => {
      Modal.confirm({
        title: '确定删除该角色?',
        onOk: async () => {
          try {
            const res = await deleteRole({ roleId });
            if (res.status.success) {
              message.success('操作成功');
            } else {
              message.error('操作失败');
            }
            resolve(res.status.success);
          } catch (error) {
            message.error('操作失败');
            reject(false);
          }
        },
      });
    });
  };

  return {
    create,
    update,
    remove,
  };
}

// 获取系统菜单
export function useUserSystemMenu(manual?: boolean) {
  const {
    data: userSystemMenus,
    loading,
    refresh: loadUserSystemMenus,
  } = useRequest(() => getMenus().then((res) => res.body), { manual });
  return {
    userSystemMenus,
    loading,
    loadUserSystemMenus,
  };
}

// // 数据权限
// export function useRoleDataRule(role: Role) {
//   const {
//     data: dataRules,
//     loading,
//     refresh: loadDataRules,
//   } = useRequest(() => queryDataRuleByRoleId({ roleId: role.roleId }).then((res) => res.body));

//   const save = async (values: any) => {
//     const data: SaveRoleDataRulesParams = {
//       roleId: role.roleId,
//       ...values,
//     };
//     const res = await saveRoleDataRules(data);
//     if (res.status.success) {
//       message.success('操作成功');
//       loadDataRules();
//     } else {
//       message.error('操作失败');
//     }
//   };
//   return {
//     dataRules,
//     loading,
//     loadDataRules,
//     save,
//   };
// }

// 获取用户角色列表
export function useUserRoleList(endpoint: string) {
  const [page, setPage] = useState<number>(1);
  const [keyword, setKeyword] = useState<string>();
  const pageSize = 20;
  const {
    data: rolesRes,
    loading,
    refresh: loadRoles,
  } = useRequest(() =>
    queryUserRolePage({ pageCondition: { pageNum: page, pageSize }, roleName: keyword, endpoint }).then(
      (res) => res.body,
    ),
  );
  const roles = rolesRes?.items || [];
  const pageMeta = rolesRes?.pageMeta || undefined;

  useEffect(() => {
    loadRoles();
  }, [page]);

  return {
    loading,
    loadRoles,
    pageMeta,
    setPage,
    keyword,
    setKeyword,
    roles,
    pageSize,
  };
}

// 获取系统的角色
export function useSystemRoles(manual?: boolean) {
  const {
    data: systemRoles,
    loading,
    run: loadSystemRoles,
  } = useRequest((params: QueryEndpointRoleParams) => getRoles(params).then((res) => res.body), { manual, });
  return {
    systemRoles,
    loading,
    loadSystemRoles,
  };
}
