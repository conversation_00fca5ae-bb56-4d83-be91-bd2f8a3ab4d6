import type { CreateCategoryActions } from '@/modules/tags/infra/components/CreateTagsModal';
import CreateTagsModal from '@/modules/tags/infra/components/CreateTagsModal';
import type { UpdateCategoryActions } from '@/modules/tags/infra/components/UpdateTagsModal';
import UpdateTagsModal from '@/modules/tags/infra/components/UpdateTagsModal';
import { createRef, forwardRef, useImperativeHandle } from 'react';

export type CategoryActionsProps = {
  create: (values: any) => Promise<boolean>;
  update?: (value: any) => Promise<boolean>;
  batchUpdate?: (value: any) => Promise<boolean>; // 是否批量操作
};

export type CategoryAction = {
  createAction?: CreateCategoryActions['create'];
  updateAction?: UpdateCategoryActions;
};

export const CategoryActions = forwardRef((props: CategoryActionsProps, ref) => {
  const { batchUpdate, update, create } = props;
  const createActions = createRef<CreateCategoryActions>();
  const updateActions = createRef<UpdateCategoryActions>();

  useImperativeHandle(ref, (): CategoryAction => {
    return {
      createAction: createActions.current?.create,
      updateAction: updateActions.current as any,
    };
  });

  return (
    <>
      {/* 新增分类 */}
      <CreateTagsModal title="新增分类" ref={createActions} onFinish={create} />
      <UpdateTagsModal
        title="编辑分类"
        ref={updateActions}
        onUpdate={update}
        onBatchUpdate={batchUpdate}
      />
    </>
  );
});
