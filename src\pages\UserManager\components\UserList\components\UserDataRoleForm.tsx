import { EndPointUser } from '@/modules/user-center/domain/user';
import { UpdateUserParams } from '@/modules/user-center/infra/user';
import type { ModalFormProps } from '@ant-design/pro-form';
import {ModalForm, ProFormRadio, ProFormText} from '@ant-design/pro-form';
import { useControllableValue } from 'ahooks';
import {Card, Form, message, Row, Select} from 'antd';
import { find, omit } from 'lodash';
import React, {useContext, useEffect, useState} from 'react';
import {saveUserDataRole} from "@/modules/user-center/infra/role";
import {set} from "husky";
import {loadPurchaseEntityList} from "@/modules/setting/infra/api/PurchaseEntityConfig";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";

type CreateFormProps = Pick<ModalFormProps, 'visible' | 'onVisibleChange' | 'trigger' | 'onFinish'> & {
  record?: EndPointUser;
};

const UserDataRoleForm: React.FC<CreateFormProps> = (props) => {
  const { record } = props;
  const [form] = Form.useForm();
  const [purchaseEntityList, setPurchaseEntityList] = useState<any[]>();
  const [visible, setVisibleChange] = useControllableValue({
    value: props.visible,
    onChange: props.onVisibleChange
  }, {
    defaultValue: false,
  })

  useEffect(() => {
    if(!purchaseEntityList){
      loadPurchaseEntityList({}).then(res=>{
        setPurchaseEntityList(res.body?.map((item: any) => {
          return {label: item?.purchaseEntity, value: item?.purchaseEntity};
        }))
      });

    }



    if (visible) {
      const purcahseOrderFliter = record?.userDataRoleInfo?.purchaseOrderFliter ? JSON.parse(record?.userDataRoleInfo?.purchaseOrderFliter) : null;
      const purcahsePlanFliter = record?.userDataRoleInfo?.purchasePlanFliter ? JSON.parse(record?.userDataRoleInfo?.purchasePlanFliter) : null;
      form.setFieldsValue({
        ...record,
        account: find(record?.accounts, { type: 'USERNAME' })?.account,
        username: record?.userName,
        purchaseEntity: record?.userDataRoleInfo?.purchaseEntity ? JSON.parse(record?.userDataRoleInfo?.purchaseEntity) : [],
        purchaseOrderFliterPurchaser: purcahseOrderFliter?.purchaseName,
        purchaseOrderFliterMerchandiser: purcahseOrderFliter?.merchandiserUsername,
        purchasePlanFliterPurchaser: purcahsePlanFliter?.purchaser,
        purchasePlanFliterAuditor: purcahsePlanFliter?.Auditor,
      });
    } else {
      form.resetFields();
    }
  }, [record, visible])

  const onSubmit = async (value: UpdateUserParams) => {
    const params = form.getFieldsValue();
    params.purchaseOrderFliter = {
      "purchaseName": form.getFieldValue('purchaseOrderFliterPurchaser'),
      "merchandiserUsername": form.getFieldValue('purchaseOrderFliterMerchandiser')
    };
    params.purchasePlanFliter = {
      "purchaser": form.getFieldValue('purchasePlanFliterPurchaser'),
      "Auditor": form.getFieldValue('purchasePlanFliterAuditor')
    };
    const res = await saveUserDataRole(params)
    if (res.status.success) {
      message.success("编辑成功！")
      form.resetFields();
      props.onFinish?.(value);
    }
    return res.status.success;
  }

  return (
    <ModalForm
      title={'用户数据权限'}
      width={600}
      layout={"horizontal"}
      labelCol={{ span: 4 }}
      form={form}
      {...omit(props, ['visible', 'onVisibleChange'])}
      onFinish={onSubmit}
      visible={visible}
      onVisibleChange={(v) => {
        if (!v) {
          form.resetFields();
        }
        setVisibleChange(v);
      }}
    >
      <ProFormText
        label="用户名"
        disabled={true}
        name="username"
      />
      <ProFormText
        label="真实姓名"
        name="nickName"
        disabled={true}
      />
      <ProFormText
        name="userId"
        hidden={true}
      />

      <ProFormText
        label="关联采购主体"
        name="purchaseEntity"
      >
      <Select
        mode="tags"
        placeholder="请选择"
        onChange={(e)=>{
          form.setFieldValue("purchaseEntity", e);
        }}
        style={{ width: '100%' }}
        options={purchaseEntityList}
      />
      </ProFormText>
      <Card title={"采购单"} size={"small"} style={{paddingTop: 2, paddingBottom: 2}}>
        <ProFormRadio.Group
          label="采购员"
          name="purchaseOrderFliterPurchaser"
          initialValue='NONE'
          options={[
            { label: '无权限', value: 'NONE' },
            { label: '仅自己', value: 'SELF' },
            { label: '全部', value: 'ALL' },
          ]}
        />
        <ProFormRadio.Group
          label="跟单员"
          name="purchaseOrderFliterMerchandiser"
          initialValue='NONE'
          options={[
            { label: '无权限', value: 'NONE' },
            { label: '仅自己', value: 'SELF' },
            { label: '全部', value: 'ALL' },
          ]}
        />
      </Card>
      <Card title={"采购计划"} size={"small"} style={{paddingTop: 2, paddingBottom: 2, marginTop: 5}}>
        <ProFormRadio.Group
          label="采购员"
          name="purchasePlanFliterPurchaser"
          initialValue='NONE'
          options={[
            { label: '无权限', value: 'NONE' },
            { label: '仅自己', value: 'SELF' },
            { label: '全部', value: 'ALL' },
          ]}
        />
        <ProFormRadio.Group
          label="审核人"
          name="purchasePlanFliterAuditor"
          initialValue='NONE'
          options={[
            { label: '无权限', value: 'NONE' },
            { label: '仅自己', value: 'SELF' },
            { label: '全部', value: 'ALL' },
          ]}
        />
      </Card>


    </ModalForm>
  );
};

export default UserDataRoleForm;
