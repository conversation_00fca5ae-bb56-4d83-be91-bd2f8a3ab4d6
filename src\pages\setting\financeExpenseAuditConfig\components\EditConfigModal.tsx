import React, {useEffect, useState} from 'react';
import {Modal, ModalProps, Form, message, Radio} from 'antd';
import ProForm,{ProFormText} from "@ant-design/pro-form";
import {ProFormSelect} from "@ant-design/pro-components";
import {getAllUser} from "@/modules/user/infra/user";
import {
  getExpenseAuditConfigDetail,
  seveExpenseAuditConfig
} from "@/modules/setting/infra/api/financeExpenseAuditConfig";
import {auditModelEnum} from "@/modules/financeExpense/domain/expense";

// 定义参数格式
export type editProps = {
  onFinish: () => void;
  configId: string;
} & ModalProps;

export default (props: editProps) => {
  const [form] = ProForm.useForm();
  const {onFinish, configId, ...rest} = props;
  const [userList, setUserList] = useState<any>();


  useEffect( () => {
      if(!userList){
        getAllUser().then(res=>{ setUserList(res.body)})
      }
      form.resetFields();
      if (configId) {
        getExpenseAuditConfigDetail(configId).then((res) => {
          if (res.status.success) {
            form.setFieldsValue(res.body);
            form.setFieldValue("auditRoleId", res.body?.auditRoleId ? JSON.parse(res.body?.auditRoleId) : []);
          }
        })
      }
    },
    [configId]
  );

  const onSubmit = () => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      seveExpenseAuditConfig(params).then((result) => {
        if (result.status.success) {
          message.success('操作成功');
          onFinish();
        }
      });
    })
  }

  return (
    <>
      <Modal {...rest} title="报销审核配置" className={'globalEnterKeySubmit'} onOk={() => onSubmit()} destroyOnClose={true}>
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProFormText name="id" hidden={true}/>
          <ProFormSelect width={"md"} label="单据类型" name="auditModel" rules={[{required: true}]} valueEnum={
            auditModelEnum
          }/>
          <ProFormText width={"md"} label="审核名称" name="auditRole" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="审核步骤" name="step" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="核算审核" name="isCostAudit" initialValue={0}>
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          </ProFormText>
          <ProFormText width={"md"} label="付款处理" name="isPaymentAudit" initialValue={0}>
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          </ProFormText>
          <ProFormSelect width={"md"} label="默认审核人" name="defaultAuditId" showSearch={true} valueEnum={userList}/>
          <ProFormSelect width={"md"} label="审核人" name="auditRoleId" showSearch={true} mode={"multiple"} valueEnum={userList} rules={[{required: true}]}/>
        </Form>
      </Modal>
    </>
  );
};
