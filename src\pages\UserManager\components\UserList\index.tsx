
import { useRequestTable } from '@/hooks/useRequestTable';
import { useUser } from '@/modules/user-center/application/user';
import { EndPointUser } from '@/modules/user-center/domain/user';
import { deleteUser, pageQueryUser } from '@/modules/user-center/infra/user';
import { formatDate } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {Button, Space, Popconfirm, Tag} from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import { UserManagerContext } from '../..';
import CreateMemberForm from '../MemberList/CreateForm';
import UpdatePasswordForm from './components/UpdatePasswordForm';
import UserRoleModal from './components/UserRoleModal';
import styles from './styles.less';
import UserDataRoleForm from "@/pages/UserManager/components/UserList/components/UserDataRoleForm";


export type MemberTableProps = {
  deptId?: string | number
}


const UserList = (props: MemberTableProps) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [userRoleModalVisible, setUserRoleModalVisible] = useState<boolean>(false);
  const [userDataRoleModalVisible, setUserDataRoleModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<EndPointUser>();
  const userService = useUser();
  const { actionRef, fetchList } = useRequestTable(pageQueryUser);
  const { deptId } = props;
  const { endpoint } = useContext(UserManagerContext);

  useEffect(() => {
    actionRef.current?.reload();
  }, [deptId])

  const columns: ProColumns<EndPointUser>[] = [
    {
      title: '用户信息',
      dataIndex: 'username',
      width: '15%',
      render: (v, record) => {
        return <div>
          <div style={{fontSize: 12}}>用户名: {record.userName}</div>
          <div style={{fontSize: 12}}>真实姓名: {record.nickName}</div>
          <div style={{fontSize: 12}}>创建时间: {formatDate(record.gmtCreate)}</div>
          <div style={{fontSize: 12}}>登录时间: {formatDate(record.lastLoginTime)}</div>
        </div>
      }
    },
    {
      title: '联系方式',
      dataIndex: 'account',
      hideInSearch: true,
      width: '15%',
      render: (v, record) => {
        return <div>
          <div style={{fontSize: 12}}>手机号: {record.phoneNumber || '-'}</div>
          <div style={{fontSize: 12}}>邮箱: {record.email || '-'}</div>
        </div>
      }
    },
    {
      title: '所属部门',
      dataIndex: 'depts',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return <div>
          {record.depts.map(item => {
            return <Tag style={{marginTop: 2}} color={"blue"} key={item.deptId}>{item.name}</Tag>
          })}
        </div>
      }
    },
    {
      title: '角色列表',
      dataIndex: 'roles',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return <div>
          {record.roles.map(item => {
            return <Tag style={{marginTop: 2}} color={"green"} key={item.roleId}>{item?.roleName}</Tag>
          })}
        </div>
      }
    },
    {
      title: '采购主体',
      dataIndex: 'roles',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        const purchaseEntitys = record?.userDataRoleInfo?.purchaseEntity ? JSON.parse(record?.userDataRoleInfo?.purchaseEntity) : [];
        return <div>
          {purchaseEntitys.map(item => {
            return <Tag style={{marginTop: 2}} color={"#F4A460"} key={item}>{item}</Tag>
          })}
        </div>
      }
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: '10%',
      valueEnum: {
        ACTIVATED: { text: '启用', status: 'Processing' },
        DISABLED: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '手机号',
      dataIndex: 'phoneNumber',
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 250,
      render: (_, record) => {
        return <><Space wrap={true} direction="horizontal" aria-colindex={3}>
          {/*<Popconfirm title='是否确认删除该用户' onConfirm={async () => {*/}
          {/*  await deleteUser({ userId: record.userId });*/}
          {/*  actionRef.current?.reload();*/}
          {/*}} >*/}
          {/*  <Button type="primary" danger key="delete" size={"small"} ghost >删除</Button>*/}
          {/*</Popconfirm>*/}
          {record.state === 'ACTIVATED' ?
            <Button type="primary" danger key="disable" size={"small"} onClick={async () => {
              await userService.disable(record.userId);
              actionRef.current?.reload();
            }}>
            禁用
          </Button> :
            <Button type="primary" key="active" size={"small"} ghost onClick={async () => {
              await userService.active(record.userId);
              actionRef.current?.reload();
            }}>
              启用
            </Button>
          }
          <Button type="primary" key="update" size={"small"} ghost onClick={() => {
            setCurrentRow(record);
            setModalVisible(true);
          }}>
            编辑
          </Button>
          <UpdatePasswordForm key="password" userId={record.userId} trigger={
            <Button type="primary"  size={"small"} ghost  >
              修改密码
            </Button>} onFinish={async () => actionRef.current?.reload()}>
          </UpdatePasswordForm>
          <Button type="primary" key="role" size={"small"} ghost onClick={() => {
            setCurrentRow(record);
            setUserRoleModalVisible(true);
          }}>
            用户角色
          </Button>
          <Button type="primary" key="dataRole" size={"small"} ghost onClick={() => {
            setCurrentRow(record);
            setUserDataRoleModalVisible(true);
          }}>
            数据权限
          </Button>
        </Space></>;
      },
    },
  ];
  return (
    <div>
      <ProTable<EndPointUser>
        actionRef={actionRef}
        className={styles.table}
        rowKey="userId"
        size='small'
        request={(params, ...args) => {
          return fetchList({ ...params, deptId, endpoint }, ...args)
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
          labelWidth: 90
        }}
        columns={columns}
        headerTitle={
          <Space>
            <CreateMemberForm
              trigger={
                <Button type="primary" icon={<PlusOutlined />}>
                  新增成员
                </Button>
              }
              onFinish={async () => actionRef.current?.reload()}
            />
            {/* <Button>批量导出</Button> */}
          </Space>
        }
      />
      <CreateMemberForm
        visible={modalVisible}
        onVisibleChange={setModalVisible}
        record={currentRow}
        onFinish={async () => actionRef.current?.reload()}
      />
      {currentRow ? (
        <UserRoleModal
          modalVisible={userRoleModalVisible}
          onCancel={() => setUserRoleModalVisible(false)}
          user={currentRow}
          onFinish={() => setUserRoleModalVisible(false)}
        />
      ) : null}
      {currentRow ? (
        <UserDataRoleForm
          visible={userDataRoleModalVisible}
          onVisibleChange={setUserDataRoleModalVisible}
          record={currentRow}
          onFinish={async () => actionRef.current?.reload()}
        />
      ) : null}
    </div>
  );
};

export default UserList;
