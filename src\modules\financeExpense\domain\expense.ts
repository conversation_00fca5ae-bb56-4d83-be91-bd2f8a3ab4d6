
export enum apportionCompanyEnum  {
  "升威" = '升威'
};


export enum apportionTypeEnum  {
  "public" = '公摊',
  'rate' = '比例分摊',
  'amount' = '金额分摊',
  'none' = '不分摊'
};

export enum auditModelEnum {
  "expenseAudit"= '报销单',
  'borrowingAudit'= '借支',
  'imprestAudit'= '备用金',
  'vat'='物流费/VAT税金/社保/公积金'
}

export enum expensePaymentType {
  'BANK'= '银行转账',
  'ONLINE'= '在线支付',
}

export enum expenseStatusEnum {
  '0' = '草稿',
  '10' = '处理中',
  '20' = '已完成',
  '-10' = '作废',
  '-20' = '驳回',
}

export type FinanceExpense = {
  "id": string,
  "title": string,
  "applyUid": string,
  "shareType": string,
  "applyUsername": string,
  "applyOrganizationId": string,
  "applyOrganizationName": string,
  "priority": number,
  "type": number,
  "purpose": string,
  "amount": number,
  "costAmount": number,
  "currency": string,
  "exchangeRate": number,
  "payment": string,
  "paymentAccount": string,
  "accountName": string,
  "accountBank": string,
  "payeeIdNumber": string,
  "paymentBank": string,
  "isPayment": number,
  "paymentTime": number,
  "status": number,
  "remark": string,
  "departmentUid": number,
  "departmentUsername": string,
  "departmentStatus": number,
  "financeUid": number,
  "financeUsername": string,
  "financeStatus": number,
  "financeSubject": string,
  "cashierUid": number,
  "cashierUsername": string,
  "cashierStatus": number,
  "isCost": number,
  "isAlibabaorder": number,
  "isAmount": number,
  "isShare": number,
  "isBook": number,
  "isDelete": number,
  "companyId": number,
  "companyName": number,
  "tenantId": string,
  "gmtCreate": number,
  "gmtModified": number,
  "auditModel": string,
  "financeExpenseAuditInfo": FinanceExpenseAudit[],
  "financeExpenseApportionInfo": FinanceExpenseApportion[],
  "financeExpenseDetailInfo": FinanceExpenseDetail[],
  "financeExpenseRemarkInfo": FinanceExpenseRemark[]
}

export type FinanceExpenseAudit = {
  "id": string,
  "auditType": string,
  "auditRole": string,
  "step": number,
  "auditStatus": string,
  "auditor": string,
  "auditorId": string,
  "remark": string,
  "expenseId": number,
  "tenantId": string,
  "gmtCreate": number,
  "gmtModified": number,
  "auditDate": number,
  "isCostAudit": number,
  "isPaymentAudit": number,
}

export type FinanceExpenseApportion = {
  "id": string,
  "expenseId": number,
  "organizationId": string,
  "organizationName": string,
  "isDelete": number,
  "rate": number,
  "amount": number,
  "remark": string,
  "gmtCreate": number,
  "gmtModified": number,
  "tenantId": string,
}

export type FinanceExpenseDetail = {
  "id": string,
  "expenseId": number,
  "content": string,
  "amount": number,
  "currency": string,
  "exchangeRate": number,
  "invoiceNumber": string,
  "isDelete": number,
  "isCost": number,
  "gmtCreate": number,
  "gmtModified": number,
  "tenantId": string,
}

export type FinanceExpenseRemark = {
  "id": string,
  "expenseId": number,
  "type": number,
  "content": string,
  "userId": string,
  "userName": string,
  "gmtCreate": number,
  "gmtModified": number,
  "tenantId": string,
}


