import type {ProColumns} from '@ant-design/pro-components';
import {EditableProTable} from '@ant-design/pro-components';
import {Button, Modal, message,ModalProps} from 'antd';
import React, {useEffect, useState} from 'react';
import {PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";
import {modifyQuantityAndPrice} from "@/modules/purchaseOrder/infra/api/purchaseOrder";


// 定义参数格式
export type modifyQuantityAndPriceModal = {
  data: PurchaseOrderGoods[];
  onFinish: (values: PurchaseOrderGoods[]) => void;
} & ModalProps;

export default (props: modifyQuantityAndPriceModal) => {
  const { onFinish,data , ...rest } = props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [dataSource, setDataSource] = useState<PurchaseOrderGoods[]>();

  useEffect(
    ()=>{
      setDataSource(props.data);
      setEditableRowKeys(data.map((item) => item.id));
    },
    [props.data]
  );


  const columns: ProColumns<PurchaseOrderGoods>[] = [
    {
      title: "id",
      dataIndex: "id",
      hideInTable: true
    },
    {
      title: 'sku',
      dataIndex: 'sku',
      readonly: true,
    },
    {
      title: '平台价格',
      dataIndex: 'platformPurchasePrice',
      readonly: true,
    },
    {
      title: '价格',
      dataIndex: 'currentPurchasePrice',
    },
    {
      title: '数量',
      dataIndex: 'purchaseQuantity',
    },
  ];

  return (
    <>
      <Modal {...rest} title="修改价格/数量" width={800} onOk={() => onFinish(dataSource || [])}>
      <EditableProTable<PurchaseOrderGoods>
        columns={columns}
        rowKey="id"
        scroll={{
          x: 500,
        }}
        value={dataSource}
        maxLength={1}
        onChange={setDataSource}
        // toolBarRender={() => {
        //   return [
        //     <Button
        //       type="primary"
        //       key="save"
        //       onClick={() => {
        //         // dataSource 就是当前数据，可以调用 api 将其保存
        //         modify(dataSource);
        //       }}
        //     >
        //       保存数据
        //     </Button>,
        //   ];
        // }}
        editable={{
          type: 'multiple',
          editableKeys,
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
          },
          onChange: setEditableRowKeys,
        }}
      />
      </Modal>
    </>
  );
};
