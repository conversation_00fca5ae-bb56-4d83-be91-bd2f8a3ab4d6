import type { ProColumns } from '@ant-design/pro-table';
import { Button, Descriptions, Form, Image, Modal, notification, Space, Tag, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import { useRequestTable } from "@/hooks/useRequestTable";
import { getGoodsList, getTagsPrintInfo, saveSkuTags, exportSkuAsync } from "@/modules/goods/infra/api/goods";
import { Goods, GoodsStatusEnum } from "@/modules/goods/domain/goods";
import GoodsSupplierListModal from "@/pages/product/GoodsList/components/GoodsSupplierListModal";
import styles from "@/pages/product/ProductList/styles.less";
import { formatDate } from "@/utils/utils";
import { history } from "@@/core/history";
import GoodsSalesListModal from "@/pages/product/GoodsList/components/GoodsSalesListModal";
import { ProFormField, ProFormRadio, ProFormSelect } from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import SelectVender from '@/components/Select/SelectVender';
import SelectCategory from '@/components/Select/SelectCategory';
import { CategorySelect } from '@/components/CategoryTreeModal';
import {
  downloadGoodsEditTemplate,
  downloadGoodsTemplate,
  downloadSkuTagsTemplate,
  importGoods,
  importSkuImage,
  importSkuTags
} from "@/modules/product/infra/api/spu";
import SelfImage from "@/components/SelfImage";
import { useHistoryRouterContext } from "@/components/BasicLayout/hooks/useHistoryRouter";
import GoodsSalesStockModal from "@/pages/product/GoodsList/components/GoodsSalesStockModal";
import { Access } from "umi";
import Permission from "@/components/Permission";
import { ProFormText } from "@ant-design/pro-components";
import CustomPage from "@/components/CustomPage";
import { GoldOutlined, InboxOutlined, SnippetsTwoTone } from "@ant-design/icons";
import { commonExport, copyText } from "@/utils/comUtil";
import { queryCategoryList } from "@/modules/tags/infra/api/tags";
import { downloadDataTemplate, reqByUrl } from "@/modules/common/infra/api/common";
import { ProductTypeEnum, PackingMaterialEnum } from "@/modules/product/domain/spu";
import { TableDropdown } from "@ant-design/pro-table";
import PurchaseDetailListModal from "@/pages/PurchasePlanList/components/PurchaseDetailListModal";
import GoodsPurchaseHistoryPriceModal from "@/pages/product/GoodsList/components/GoodsPurchaseHistoryPriceModal";
import SkuEditModal from "@/pages/product/CreateProduct/components/SkuEditModal";
import { ProductSkusLogModal } from "@/pages/product/CreateProduct/components/ProductSkusLogModal";
import { omit } from 'lodash';
import GoodsPhoto, { GoodsPhotoActions } from './components/GoodsPhoto';

const GoodsList: React.FC<Goods> = () => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [purchaseDetailListVisible, setPurchaseDetailListVisible] = useState<boolean>(false);
  const [ProductSkusLogVisible, setProductSkusLogVisible] = useState<boolean>(false);
  const [historyPriceVisible, setHistoryPriceVisible] = useState<boolean>(false);
  const [goodsSupplierParams, setGoodsSupplierParams] = useState<any>();
  const [goodsSales, setGoodsSales] = useState<boolean>();
  const [currentRow, setCurrentRow] = useState<Goods>();
  const [goodsSalesStock, setGoodsSalesStock] = useState<boolean>(false);
  const [goodsSalesStockParam, setGoodsSalesStockParam] = useState<any>();
  const [skuEditModal, setSkuEditModal] = useState<boolean>(false);
  const [skuEditParams, setSkuEditParams] = useState<Goods>();
  const [searchParmas, setSearchParmas] = useState({});
  const historyRouter = useHistoryRouterContext();
  const goodsRef = useRef<GoodsPhotoActions>()

  const { fetchList, actionRef } = useRequestTable((params) => {
    setSearchParmas(params);
    return getGoodsList({
      ...(omit(params, 'category'))
    });
  });

  const alertGoodsSuppliers = (values: Goods) => {
    setModalVisible(true);
    setGoodsSupplierParams({
      goodsSku: values.sku,
      spuId: values.spuId
    })
  }

  const alertGoodsSalesStock = (values: Goods) => {
    setGoodsSalesStockParam([values])
    setGoodsSalesStock(true);
  }

  const saveSkuTagsModal = (values: Goods) => {
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form form={form}>
          <ProFormSelect
            width="md"
            name="tagsName"
            showSearch={true}
            label="标签"
            request={async () => {//返回的select网络请求
              const params = await queryCategoryList();
              const res: any[] = [];
              for (const i in params.body) {
                const temp = {
                  label: params.body[i].name,
                  value: params.body[i].name,
                };
                res.push(temp)
              }
              return res;
            }}
          />
        </Form>
      ),
      onOk: function () {
        const tagsName = form.getFieldValue('tagsName');
        const data = {
          sku: values.sku,
          tagsName: tagsName
        }

        saveSkuTags(data).then(res => {
          if (res.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({ message: '保存成功' });
          }
        });
      },
    });

  }

  const downloadTemplate = () => {
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form form={form} labelCol={{ span: 6 }}>
          <ProFormRadio.Group
            label="是否添加"
            name="flag"
            initialValue='add'
            options={[
              { label: '新增', value: 'add' },
              { label: '编辑', value: 'edit' },
            ]}
          />
          <ProFormText name="12" label="模板">
            <a onClick={function () {
              downloadGoodsTemplate().then(res => {
                commonExport(res, '商品新增模板');
              });
            }}>新增模板</a>
            <a style={{ marginLeft: 10 }} onClick={function () {
              downloadGoodsEditTemplate().then(res => {
                commonExport(res, '商品编辑模板');
              });
            }}>编辑模板</a>
          </ProFormText>
          <ProFormField name="importExcelUrl" label="文件">
            <UploadFile limit={1} />
          </ProFormField>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        const flag = form.getFieldValue('flag');

        importGoods(link, flag).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({ message: '导入成功' });
          }
        });

      },
    });

  }


  const downloadGroupGoodsTemplate = () => {
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form form={form}>
          <Space>
            <ProFormField formItemProps={{ style: { marginBottom: 0 } }} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a style={{ marginLeft: 10 }} onClick={function () {
              downloadDataTemplate("/sales-mgmt-biz/sales-center/goods-compose/downloadComposeTemplate").then(res => {
                commonExport(res, '组合商品导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        reqByUrl("/sales-mgmt-biz/sales-center/goods-compose/importMsku", { link: link });
        actionRef.current?.reload?.();
        notification.success({ message: '后台任务正在执行请稍后查看' });
      },
    });

  }

  const downloadGoodsTagsTemplate = () => {
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form form={form}>
          <ProFormRadio.Group
            label="是否添加"
            name="addOrDel"
            initialValue='add'
            options={[
              { label: '添加', value: 'add' },
              { label: '删除', value: 'del' },
            ]}
          />
          <Space>
            <ProFormField formItemProps={{ style: { marginBottom: 0 } }} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a style={{ marginLeft: 10 }} onClick={function () {
              downloadSkuTagsTemplate().then(res => {
                commonExport(res, '商品标签导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        const addOrDel = form.getFieldValue('addOrDel');
        importSkuTags(link, addOrDel).then(res => {
          if (res.status.success) {
            notification.success({ message: '导入成功' });
            actionRef.current?.reloadAndRest?.();
          }
        });
      },
    });

  }


  const improtGoodsImage = () => {
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form form={form}>
          <ProFormField formItemProps={{ style: { marginBottom: 0 } }} name="importExcelUrl" label="文件">
            <UploadFile limit={1} />
          </ProFormField>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;

        importSkuImage(link).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({ message: '导入成功' });
          }
        });

      },
    });

  }


  /**
   * 打印标签
   * @constructor
   */
  const printTags = (goods: Goods) => {
    Modal.confirm({
      icon: '',
      width: 400,
      className: 'globalEnterKeySubmit',
      content: (
        <Form form={form}>
          <ProFormText label="打印数量" name="printQuantity" initialValue={1} />
        </Form>
      ),
      onOk: function () {
        getTagsPrintInfo({ sku: goods.sku }).then(res => {
          window.open("/purchasing-center/print/goodsTagsPrint?position=" + (res?.body?.position || '') + "&sku=" + goods.sku + "&goodsWeight=" + (goods?.netWeight ? goods?.netWeight : '--') + "&goodsName=" + encodeURIComponent(goods?.title) + "&orderCode=&printQuantity=" + form.getFieldValue("printQuantity"));
        })
      },
    });
  }

  const alertGoodsSales = (values: Goods) => {
    setGoodsSales(true);
    setCurrentRow(values)
  }

  const columns: ProColumns<Goods>[] = [
    {
      title: '标题',
      width: 200,
      dataIndex: 'title',
      colSize: (6 / 24),
      hideInTable: true,
    },
    {
      title: 'SKU',
      width: 200,
      colSize: (6 / 24),
      dataIndex: 'sku',
      hideInTable: true,
    },
    {
      title: '标签',
      width: 200,
      dataIndex: 'tagsName',
      colSize: (6 / 24),
      hideInTable: true,
    },
    {
      title: '供应商',
      dataIndex: 'supplierId',
      hideInTable: true,
      colSize: (6 / 24),
      renderFormItem: () => <SelectVender />
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      hideInTable: true,
      colSize: (12 / 24),
      formItemProps: {
        style: {
          marginTop: 12
        }
      },
      renderFormItem: () => <SelectCategory />
    },
    // {
    //   title: '分类',
    //   dataIndex: 'category',
    //   hideInTable: true,
    //   colSize: (4 / 24),
    //   search: {
    //     transform: (value: any, namePath: string, allValues: any) => {
    //       return {
    //         categoryId: allValues?.category?.key
    //       };
    //     },
    //   },
    //   renderFormItem: () => <CategorySelect />
    // },
    {
      title: '图片',
      width: 100,
      dataIndex: 'skuImage',
      hideInSearch: true,
      render: (_, record) => <SelfImage src={record?.skuImage} title={record?.skuName} width={80} />
    },
    {
      title: '标题/SKU',
      dataIndex: 'skuName',
      width: 300,
      hideInSearch: true,
      render: (_, record) => {
        return (
          <Descriptions className={styles.list} column={1} contentStyle={{ fontSize: 12 }} labelStyle={{ fontSize: 12 }}>
            <Descriptions.Item label="SKU">{record.sku}&nbsp;<SnippetsTwoTone onClick={() => copyText(record.sku)} /></Descriptions.Item>
            <Descriptions.Item label="标题">{record?.skuName}</Descriptions.Item>
            {record?.skuAttrName ? <Descriptions.Item label={record?.skuAttrName}>{record.skuAttrValue}</Descriptions.Item> : null}
            <div>商品类型：<Tag color={record?.productType == "BIND" ? "blue" : record?.productType == "ASSEMBLE" ? "orange" : "green"}>{ProductTypeEnum[record?.productType]}</Tag></div>
            {record?.relation ? <div>组合关系：{record?.relation}</div> : null}
          </Descriptions>
        );
      },
    },
    {
      title: '采购/开发/核价员',
      dataIndex: 'purchaser',
      hideInSearch: true,
      align: "left",
      width: 100,
      render: (v, record) => {
        return (<Descriptions className={styles.list} column={1} contentStyle={{ fontSize: 12 }} labelStyle={{ fontSize: 12 }}>
          <Descriptions.Item label="采购">{record?.purchaser || "--"}</Descriptions.Item>
          <Descriptions.Item label="开发">{record?.developer || '--'}</Descriptions.Item>
          <Descriptions.Item label="核价员">{record?.maintainer || '--'}</Descriptions.Item>
        </Descriptions>)
      }
    },
    {
      title: '分类/状态/申报名/包材',
      dataIndex: 'categoryName',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        const salesStatus = GoodsStatusEnum[record?.salesStatus];
        return (<Descriptions className={styles.list} column={1} contentStyle={{ fontSize: 12 }} labelStyle={{ fontSize: 12 }}>
          <Descriptions.Item label="分类">{record?.categoryName || "--"}</Descriptions.Item>
          <Descriptions.Item label="状态"> <span style={{ fontSize: 12 }}>{salesStatus}</span></Descriptions.Item>
          <Descriptions.Item label="CN">{record?.zhDeclaredName || "--"}</Descriptions.Item>
          <Descriptions.Item label="EN">{record?.enDeclaredName || '--'}</Descriptions.Item>
          <Descriptions.Item label="包材">{PackingMaterialEnum[record?.packingMaterial] || '--'}</Descriptions.Item>
        </Descriptions>)
      }
    },
    {
      title: '首选供应商',
      dataIndex: 'sku',
      align: "left",
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return (<Descriptions className={styles.list} column={1} contentStyle={{ fontSize: 12 }} labelStyle={{ fontSize: 12 }}>
          <Descriptions.Item label="供应商">{record?.supplierName || '--'}</Descriptions.Item>
          <Descriptions.Item label="采购价">{record?.purchasePrice || '0.0'}</Descriptions.Item>
          {/* <Descriptions.Item label="上次采购价">{record?.lastPurchasePrice || '0.0'}</Descriptions.Item> */}
        </Descriptions>)
      }
    },
    {
      title: '体积/重量',
      dataIndex: 'weight',
      hideInSearch: true,
      width: 80,
      render: (v, record) => {
        return <>
          <div><InboxOutlined />&nbsp;{(record.sizeLength || 0) + " * " + (record.sizeWidth || 0) + " * " + (record.sizeHeight || 0)}</div>
          <div><GoldOutlined />&nbsp;{(record.netWeight || 0) + " g"}</div>
        </>
      }
    },
    {
      title: '时间',
      hideInSearch: true,
      align: "left",
      width: 120,
      render: (v, record) => {
        return (
          <Descriptions className={styles.list} column={1} contentStyle={{ fontSize: 12 }} labelStyle={{ fontSize: 12 }}>
            <Descriptions.Item label="生成">{formatDate(record?.gmtCreate)}</Descriptions.Item>
            <Descriptions.Item label="修改">{formatDate(record?.gmtModified)}</Descriptions.Item>
          </Descriptions>
        );
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 150,
      align: 'left',
      hideInSearch: true,
      render: (v, record) => {
        const tags = record?.tagsName ? record.tagsName.split(",") : [];
        const options: {} | null | undefined = [];
        if (tags.length > 0) {
          tags?.map((item: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined) => {
            options.push(<Tag color="green" style={{ cursor: "pointer" }}> {item}</Tag>)
          })
        }
        return <Space wrap={true} direction="horizontal" aria-colindex={2}>{options}</Space>;
      }
    },
    {
      title: '备注',
      hideInSearch: true,
      align: "left", 
      width: 150,
      render: (v, record) => {
        return (
          <Descriptions className={styles.list} column={1} contentStyle={{ fontSize: 12 }} labelStyle={{ fontSize: 12 }}>
            <Descriptions.Item label="运营">
              <Tooltip title={record?.promotionSuggestion}>
                <div style={{ 
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  display: '-webkit-box',
                  wordBreak: 'break-all'
                }}>
                  {record?.promotionSuggestion}
                </div>
              </Tooltip>
            </Descriptions.Item>
            <Descriptions.Item label="文案">
              <Tooltip title={record?.copywritingSuggestion}>
                <div style={{ 
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  display: '-webkit-box',
                  wordBreak: 'break-all'
                }}>
                  {record?.copywritingSuggestion}
                </div>
              </Tooltip>
            </Descriptions.Item>
          </Descriptions>
        );
      },
    },
    {
      title: '操作',
      width: 145,
      // dataIndex: 'option',
      fixed: 'right',
      align: "left",
      hideInSearch: true,
      render: (_, record) => {

        return (
          <Space split={<span style={{ color: "rgb(24, 144, 255)" }}>|</span>}>
            <a style={{ fontSize: 12 }} onClick={() => history.push({ pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })}>
              详情
            </a>
            <a type="primary" style={{ fontSize: 12 }} onClick={() => printTags(record)}>
              打印
            </a>
            <TableDropdown
              key="more"
              menus={[
                {
                  key: 'supplier',
                  name: <Permission permissionKey={"purchase:product_manager:sku:goodsSupplier"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => alertGoodsSuppliers(record)}>
                      供应商
                    </a>
                  </Permission>
                },
                {
                  key: 'edit',
                  name: <Permission permissionKey={"purchase:product_manager:sku:skuEdit"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => {
                      setSkuEditModal(true)
                      setSkuEditParams(record)
                    }}>
                      编辑
                    </a>
                  </Permission>
                },
                {
                  key: 'skuLog',
                  name: <Permission permissionKey={"purchase:product_manager:sku:skuEdit"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => {
                      setCurrentRow(record);
                      setProductSkusLogVisible(true);
                    }}>
                      日志
                    </a>
                  </Permission>
                },
                {
                  key: 'purchaseDetail',
                  name: <Permission permissionKey={"purchase:product_manager:sku:goodsSupplier"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => {
                      setCurrentRow(record);
                      setPurchaseDetailListVisible(true);
                    }}>
                      采购单明细
                    </a>
                  </Permission>
                },
                {
                  key: 'historyPrice',
                  name: <Permission permissionKey={"purchase:product_manager:sku:goodsSupplier"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => {
                      setCurrentRow(record);
                      setHistoryPriceVisible(true);
                    }}>
                      历史采购价
                    </a>
                  </Permission>
                },
                {
                  key: 'sales',
                  name: <Permission permissionKey={"purchase:product_manager:sku:goodsSales"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => alertGoodsSales(record)}>
                      销量
                    </a>
                  </Permission>
                },
                {
                  key: 'stock',
                  name: <Permission permissionKey={"purchase:product_manager:sku:goodsStock"}>
                    <a type="primary" style={{ fontSize: 12, padding: 0 }} onClick={() => alertGoodsSalesStock(record)}>
                      库存
                    </a>
                  </Permission>
                },
                {
                  key: 'tags',
                  name: <Permission permissionKey={"purchase:product_manager:sku:skuTags"}>
                    <a type="primary" style={{ fontSize: 12 }} onClick={() => saveSkuTagsModal(record)}>
                      标签
                    </a>
                  </Permission>
                },
                {
                  key: 'photo',
                  name: <Permission>
                    <a type="primary" style={{ fontSize: 12 }} onClick={() => goodsRef.current?.onOpen(record.sku)}>
                      图片管理
                    </a>
                  </Permission>
                },
              ]}
            >
              <span style={{ fontSize: 12, marginRight: 5 }}>更多</span>
            </TableDropdown>
          </Space>
        );
      },
    },
  ];
  // 导出
  const onExport = () => {
    Modal.confirm({
      icon: '',
      width: 500,
      content: '确认导出商品SKU列表？',
      onOk: async () => {
        const res = await exportSkuAsync(searchParmas)
        Modal.confirm({
          icon: '',
          width: 500,
          content: <div>{`文件导出${res?.status?.success ? '成功' : '失败'}`},请前往【系统-设置-任务中心】菜单查看导出详情</div>,
          okText: '立即前往',
          onOk: () => history.push(`/setting/setting/taskList#'EXPORT`)
        });
      },
    });
  }

  return (
    <>
      <CustomPage<Goods>
        actionRef={actionRef}
        rowKey="id"
        scroll={{ y: 'calc(100vh - 330px)' }}
        tableAlertRender={false}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          labelWidth: 70,
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
                <Button
                  type="primary"
                  size={"small"}
                  onClick={() => improtGoodsImage()}
                  ghost={true}
                >
                  导入图片
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
                <Button
                  type="primary"
                  size={"small"}
                  onClick={() => downloadTemplate()}
                  ghost={true}
                >
                  导入商品
                </Button>
              </Permission>,
              // <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
              //   <Button
              //     type="primary"
              //     size={"small"}
              //     ghost={true}
              //     onClick={() => downloadGroupGoodsTemplate()}
              //   >
              //     导入组合商品
              //   </Button>
              // </Permission>,
              <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
                <Button
                  type="primary"
                  size={"small"}
                  onClick={() => downloadGoodsTagsTemplate()}
                >
                  导入商品标签
                </Button>
              </Permission>,
              <Button
                type="primary"
                size={"small"}
                onClick={() => onExport()}
              >
                导出商品
              </Button>,
              <Permission permissionKey={"purchase:product_manager:sku:spuCreate"}>
                <Button
                  type="primary"
                  size={"small"}
                  onClick={() => history.push('/product-manager/goods/sku/create')}
                >
                  添加
                </Button>
              </Permission>
            ];
            return [...options, ...dom];
          },
        }}
        request={fetchList}
        columns={columns}
        onRow={(record) => {
          return {
            onDoubleClick: (e) => {
              history.push({ pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })
            },
          };
        }}
        recordCreator={false}
        recordDelete={false}
        recordUpdater={false}
      />
      <Access accessible={purchaseDetailListVisible}>
        <PurchaseDetailListModal visible={purchaseDetailListVisible} onCancel={() => setPurchaseDetailListVisible(false)} data={{ sku: currentRow?.sku }} onFinish={() => { setPurchaseDetailListVisible(false) }} />
      </Access>
      <Access accessible={ProductSkusLogVisible}>
        <ProductSkusLogModal visible={ProductSkusLogVisible} onCancel={() => setProductSkusLogVisible(false)} sku={currentRow?.sku} onFinish={() => { setProductSkusLogVisible(false) }} />
      </Access>
      <Access accessible={historyPriceVisible}>
        <GoodsPurchaseHistoryPriceModal visible={historyPriceVisible} onCancel={() => setHistoryPriceVisible(false)} reqParams={currentRow} onFinish={() => { setHistoryPriceVisible(false) }} />
      </Access>
      <GoodsSupplierListModal visible={modalVisible} onCancel={() => setModalVisible(false)} goodsSupplierParam={goodsSupplierParams} onFinish={function () { setModalVisible(false) }} />
      <GoodsSalesListModal visible={goodsSales} onCancel={() => setGoodsSales(false)} goodsSalesParam={currentRow} onFinish={() => { setGoodsSales(false) }} />
      <Access accessible={goodsSalesStock}>
        <GoodsSalesStockModal visible={goodsSalesStock} onCancel={() => setGoodsSalesStock(false)} reqParams={goodsSalesStockParam} onFinish={() => { setGoodsSalesStock(false) }} />
      </Access>
      <Access accessible={skuEditModal}>
        <SkuEditModal visible={skuEditModal} goodsData={skuEditParams} onFinish={() => {
          setSkuEditModal(false);
          actionRef.current?.reload?.();
        }} />
      </Access>
      <GoodsPhoto ref={goodsRef} />
    </>
  );
};

export default GoodsList;
