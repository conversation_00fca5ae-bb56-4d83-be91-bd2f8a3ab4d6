import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Alert, Space, Tag} from "antd";
import {backgroupTask} from "@/pages/setting/task/data";
import {pageQueryTask} from "@/modules/setting/infra/api/task";
import moment from "moment";

const TableList: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryTask({
      ...params,
    });
  });
  const columns: ProColumns<backgroupTask>[] = [
    {
      title: '任务名称',
      dataIndex: 'title',
      colSize: (6 / 24)
    },
    {
      title: '任务简码',
      dataIndex: 'taskCode',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'taskStatus',
      hideInSearch: true,
    },
    {
      title: '日志',
      dataIndex: 'log',
      hideInSearch: true,
      width: 550,
      render: (v, record) => {
        return <div style={{maxWidth: "550px",maxHeight:"200px",overflow:"scroll",color:"red", fontSize: 12}}>{record?.log}</div>;
      }
    },
    {
      title: '操作人',
      dataIndex: 'creater',
      hideInSearch: true,
    },
    {
      title: '时间',
      dataIndex: 'date',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize: 12}}>开始时间：{moment(record.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}</div>
          <div style={{fontSize: 12}}>完成时间：{record.gmtFinish ? moment(record.gmtFinish).format("YYYY-MM-DD HH:mm:ss") : '--'}</div>
        </>
      }
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   align: 'center',
    //   width: 400,
    //   render: (v, record) => {
    //     return (
    //       <Space>
    //         <a >详情</a>
    //         <a >取消任务</a>
    //       </Space>
    //     );
    //   }
    // }
  ];

  return (
    <>
      <CustomPage<backgroupTask>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;
