import { useCallback } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import type { UseColumnPropps } from './type';

export function useEditColumn<T = any>(config: ProColumns<T> = {}) {
  return useCallback(
    (title: string, dataIndex: string, rest: UseColumnPropps<T> = {}) => {
      const { maxLength, required, errTip, fieldProps, formItemProps, ..._rest } = rest;

      return {
        title,
        dataIndex,
        fieldProps: {
          maxLength,
          ...fieldProps,
        },
        formItemProps: {
          rules: required ? [{ required: true, message: errTip || '此项为必填项' }] : [],
          ...formItemProps,
        },
        ...config,
        ..._rest,
      } as ProColumns<T>;
    },
    [config],
  );
}
