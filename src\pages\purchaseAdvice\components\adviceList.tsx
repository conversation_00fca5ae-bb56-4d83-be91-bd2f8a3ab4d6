import type {ProColumns} from '@ant-design/pro-table';
import React, {useEffect, useState} from 'react';
import {useRequestTable} from "@/hooks/useRequestTable";
import CustomPage from "@/components/CustomPage";
import {PurchaseAdvice, PurchaseAdviceResult} from "@/pages/purchaseAdvice/data";
import {purchaseAdvicePageQuery} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {Table, Tag} from "antd";

const AdviceList  = ( props: any) => {

  const { adviceBatch } = props;
  const { fetchList, actionRef } = useRequestTable((params) => {
    return purchaseAdvicePageQuery({
      ...params,
      batchCode: adviceBatch?.batchCode || params?.batchCode
    });
  });

  useEffect(() => {
    actionRef?.current?.reload();
  }, [adviceBatch]);

  const columnsSub: ProColumns<PurchaseAdvice>[] = [
    {
      title: '基本信息',
      width: 100,
      align: 'left',
      dataIndex: 'orderCode',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 需求：<span style={{fontWeight: "bold", color: "red"}}>{record.purchaseQuantity||'0'}</span></div>
          <div style={{fontSize:"12px"}}> 备货：{record.isPurchase==1?<span style={{color: "green"}}>是</span>:<span style={{color: "blue"}}>否</span>}</div>
          <div style={{fontSize:"12px"}}> 小组：{record?.organizationName||"--"}</div>
        </>
      }
    },
    {
      title: '仓库',
      width: 150,
      align: 'left',
      dataIndex: 'orderCode',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 发货仓库：{record?.deliverWarehouseName||"--"}</div>
          <div style={{fontSize:"12px"}}> 销售仓库：{record?.salesWarehouseName||"--"}</div>
          <div style={{fontSize:"12px"}}> 备货类型：{record?.warehouseType||"--"}</div>
        </>
      }
    },    {
      title: '销量',
      width: 200,
      align: 'left',
      dataIndex: 'orderCode',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> <Tag>7D->{record?.sales7Days||"0"}</Tag><Tag>14D->{record?.sales14Days||"0"}</Tag><Tag>15D->{record?.sales15Days||"0"}</Tag></div>
          <div style={{fontSize:"12px",marginTop: 4}}> <Tag>21D->{record?.sales21Days||"0"}</Tag><Tag>30D->{record?.sales30Days||"0"}</Tag></div>
        </>
      }
    },
    {
      title: '库存',
      width: 100,
      align: 'left',
      dataIndex: 'orderCode',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 可用：{record?.availableStockQuantity||"0"}</div>
          <div style={{fontSize:"12px"}}> 在途：{record?.intransitStockQuantity||"0"}</div>
        </>
      }
    },
    {
      title: '趋势',
      width: 200,
      align: 'left',
      dataIndex: 'orderCode',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 日均销量：{record?.salesDayAvgQuantity||"0"}</div>
          <div style={{fontSize:"12px"}}> 趋势：{record?.salesTrend||"--"}</div>
          <div style={{fontSize:"12px"}}> 公式：<span style={{fontSize: 11}}>{record?.purchaseFormula||"--"}</span></div>
        </>
      }
    },
    {
      title: '配置',
      width: 100,
      align: 'left',
      dataIndex: 'orderCode',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 上限：{record?.upDay||"--"}</div>
          <div style={{fontSize:"12px"}}> 下限：{record?.downDay||"--"}</div>
          <div style={{fontSize:"12px"}}> 可卖天数：{record?.availableSalesDay||"0"}</div>
        </>
      }
    }
  ];


  const columns: ProColumns<PurchaseAdviceResult>[] = [
    {
      title: '批次号',
      dataIndex: 'batchCode',
      hideInTable: true,
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      hideInTable: true,
    },
    {
      title: '商品信息',
      dataIndex: 'batchCode',
      align: 'left',
      hideInSearch: true,
      width: 300,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> SKU：{record?.sku||"--"}</div>
          <div style={{fontSize:"12px"}}> 名称：{record?.productName||"--"}</div>
          <div style={{fontSize:"12px"}}> 批次：{record?.batchCode||"--"}</div>
          <div style={{fontSize:"12px"}}> 分类：{record?.productCograte||"--"}</div>
          <div style={{fontSize:"12px"}}> 状态：{record?.statuts||"--"}</div>
          <div style={{fontSize:"12px"}}> 小组：{record?.organizationName||"--"}</div>
        </>
      }
    },
    {
      title: '备货信息',
      dataIndex: 'batchCode',
      align: 'left',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 最终采购：<span style={{fontWeight:"bold",color: "blue"}}>{record?.purchaseQuantity|| 0}</span></div>
          <div style={{fontSize:"12px"}}> 备货：{record?.isPurchase==1?"备": record?.isPurchase==2?"特殊要备":record?.isPurchase==3?"不备":"--"}</div>
          <div style={{fontSize:"12px"}}> 等级：{record?.level||"--"}</div>
          <div style={{fontSize:"12px"}}> 总需求：{record?.needQuantity||0}</div>
          <div style={{fontSize:"12px"}}> 缺货：{record?.gzOutOfStockQuantity||0}</div>
          <div style={{fontSize:"12px"}}> 总库存(广州仓)：{record?.gzStock||0}</div>
          <div style={{fontSize:"12px"}}> 备注：{record?.note||"--"}</div>
        </>
      }
    },
    {
      title: '备货明细',
      dataIndex: 'center',
      align: 'center',
      hideInSearch: true,
      render: (v, record) => {
        return <Table
          dataSource={record?.adviceList}
          columns={columnsSub}
          pagination={false}
          size={"small"}
          style={{width: '100%',margin: 0, padding: 0,  verticalAlign: "top"}}
        />
      }
    }
  ];

  return (
    <CustomPage<PurchaseAdviceResult>
      actionRef={actionRef}
      request={fetchList}
      columns={columns}
      rowKey="id"
      scroll={{y: 690}}
      search={{
        labelWidth: 120,
      }}
      tableAlertRender={false}
      pagination={{pageSize: 30}}
      recordCreator={false}
      recordUpdater={false}
      recordDelete={false}
    />
);
};

export default AdviceList;
