import React, { useEffect, useState } from 'react';
import { ProFormCheckbox } from '@ant-design/pro-components';
import type { ModalProps } from 'antd';
import {Button, Card, Form, Image, Input, Modal, Select, Table} from 'antd';
import { ProForm, ProFormText } from '@ant-design/pro-form';
import type { AliProduct } from '@/pages/PurchaseOrderList/data';
import { getAlibabaProductInfo } from '@/modules/purchaseOrder/infra/api/purchaseOrder';
import type { SupplierGoods } from "@/modules/supplier/domain/vender";
import { message } from "antd/es";
import {ColumnProps} from "antd/es/table";
import {BoundAlibabaProduct} from "@/modules/supplier/infra/api/vender";

// 定义参数格式
export type CreateModalProps = {
  supplierData: SupplierGoods;
  onFinish: () => void;
} & ModalProps;

const BindAlibabaProductModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { supplierData, onFinish, ...rest } = props;
  const [dataSource, setDataSource] = useState<AliProduct[]>();
  const [currentSelectedSpecId, setCurrentSelectedSpecId] = useState<string>();

  const bindAlibabaProduct=()=>{
    const params = dataSource[0];
    params.isCheck = form.getFieldValue('isCheck');
    params.specId = currentSelectedSpecId;
    BoundAlibabaProduct(params).then(res=>{
      if (res.status.success){
        message.success(res.body);
        onFinish();
      }
    })
  }

  const matching = async () => {
    let url = form.getFieldValue('url');
    // 验证供应商信息
    let isCheck = form.getFieldValue('isCheck');
    if (url == null || url == "") {
      message.info("1688链接不可为空!");
      return null;
    }
    if(url.indexOf(".htm") == -1){
      url += ".html";
    }
    isCheck = isCheck == undefined ? false : isCheck
    const data = {
      url: url,
      isCheck: isCheck,
      sku: supplierData.goodsSku,
      supplierId: supplierData.supplierId
    };
    const res = await getAlibabaProductInfo(data);
    if (res.status.success) {
      // message.info("数据同步完成！");
      const data = [];
      res.body.sku = supplierData.goodsSku;
      res.body.supplierId = supplierData.supplierId;
      res.body.aliLink = url;
      res.body.specId = supplierData.platformSpecId;
      if(!res?.body?.productTypeList || res?.body?.productTypeList?.length == 0){
        setCurrentSelectedSpecId("");
      }else{
        setCurrentSelectedSpecId(supplierData.platformSpecId);
      }
      data.push(res.body);
      setDataSource(data);
    }
  };

  useEffect(() => {
    //初始化已绑定数据
    if (supplierData != undefined && supplierData.aliLink != undefined && supplierData.aliLink != "") {
      form.setFieldsValue({
        ['url']: supplierData.aliLink,
        isCheck: true,
      });
      matching();
    } else {
      form.resetFields();
      setDataSource([])
    }
  }, [supplierData?.id])


  const columns: ColumnProps<AliProduct>[] = [
    {
      title: '缩略图',
      dataIndex: 'erpImg',
      render: (v, record) => {
        return <Image style={{ width: 100 }} src={record.erpImg} />;
      }
    },
    {
      title: '信息',
      dataIndex: 'title',
      width: 200,
      render: (v, record) => {
        return <>
          {record.sku}<br/>
          {record.name}
          </>;
      }
    },
    {
      title: '1688商品ID',
      dataIndex: 'productId',
      width: 200,
    },
    {
      title: '属性',
      key: 'specId',
      width: 300,
      dataIndex: 'specId',
      render: (v, record) => {
        const options = record?.productTypeList?.map((item) => {
              return {
                value: item.specId,
                label: item.goodsType,
              };
            });
            return <Select style={{minWidth: 200}} options={options} value={currentSelectedSpecId} onChange={(value)=>{
              console.log(value)
              setCurrentSelectedSpecId(value)}
            }/>;
      }
    },
    {
      title: '最小采购量',
      dataIndex: 'minOrderQuantity',
    },
    {
      title: '1688图片',
      dataIndex: 'img',
      render: (v, record) => {
        return <img style={{ width: 100 }} src={record.img} />;
      },
    },
  ];

  return (
    <Modal {...rest} title="绑定1688链接" onOk={() => bindAlibabaProduct()} width={"65%"}>
      <Form form={form}>
        <Card style={{ height: 100, padding: 0 }}>
          <ProForm.Group>
            <ProFormText
              width={500}
              name="url"
              help={<ProFormCheckbox initialValue={false} name="isCheck">是否验证供应商信息</ProFormCheckbox>}
            />
            <Button type="primary" onClick={matching}>
              匹配
            </Button>
          </ProForm.Group>
        </Card>
        <Card style={{ marginTop: 5, padding: 0 }}>
          <Table<AliProduct>
            columns={columns}
            rowKey="productId"
            size={"small"}
            style={{padding: 0, margin: 0}}
            dataSource={dataSource}
          />
        </Card>
      </Form>
    </Modal>
  );
};

export default BindAlibabaProductModal;
