import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>, Col, message, Row, Spin} from "antd";
import ProForm, {ProFormField} from "@ant-design/pro-form";
import {useRequest} from "ahooks";
import {updateOrder, updateOrderTitle} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {FooterToolbar} from "@ant-design/pro-layout";
import {UploadFileResult} from "@/services/common";
import {map} from "lodash";
import UploadFile from "@/components/UploadFile";
import Permission from "@/components/Permission";

// 定义参数格式
export type CreateModalProps = {
  orderData: any;
  fieldName: 'purchaseImages' | 'purchaseBankSlips'
};

const PurchaseOrderImportFileModal = (props: CreateModalProps) => {
  const { orderData, fieldName = 'purchaseImages' } = props;
  const [form] = ProForm.useForm();
  const { data, loading } = useRequest(() => orderData);
  //保存采购附件
  const onFinish = (values: any) => {
    const obj={
      id: data.id,
      [fieldName]: values[fieldName],
    }
    updateOrderTitle(obj).then((res) => {
      if (res.status.success){
        message.success("保存成功")
      }
    })
  };

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
      });
    }
  }, [data]);
  console.log('props',props);
  return (
    <>
      <Spin spinning={loading}>
      <ProForm
        form={form}
        layout="horizontal"
        onFinish={onFinish}
        labelCol={{ span: 5 }}
        submitter={false}
        initialValues={{
          [fieldName]: [],
        }}
        onValuesChange={(values) => {
          console.log(values);
        }}
      >
        <Card>
          <Row gutter={36}>
            <Col span={12}>
              <ProFormField
                label="附件上传"
                name={fieldName}
                // 格式化图片格式
                transform={(values: UploadFileResult[]) => {
                  return {
                    [fieldName]: map(values, 'fileId'),
                  };
                }}
              >
                <UploadFile/>
              </ProFormField>
            </Col>
          </Row>
        </Card>
        <FooterToolbar>
          <Permission permissionKey={"purchase:purchase:purchaseOrder:purchaseUploadFile"}>
            <Button type="primary" onClick={() => form?.submit()}>
              提交
            </Button>
          </Permission>
        </FooterToolbar>
    </ProForm>
      </Spin>
    </>
  );
};


export default PurchaseOrderImportFileModal;
