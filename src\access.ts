import type { InitialStateData } from './types';
import routes from 'config/routes';

const getAllRoutes = () => {
  const transferDataSource: any[] = [];
  function flatten(list: any[] = []) {
    list.forEach((item) => {
      transferDataSource.push({ ...item });
      flatten([...(item.routes || [])]);
    });
  }
  flatten(routes);
  return transferDataSource.reduce((acc, route) => {
    if (!route.access) {
      return acc;
    }
    return {
      ...acc,
      [route.access]: false,
    };
  }, {});
};

/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: InitialStateData) {
  const { currentMenu, isSuperAdmin } = initialState;
  if (isSuperAdmin) {
    return {};
  }
  // 菜单权限
  const routerMap = getAllRoutes();
  const menuAccess = (currentMenu || []).reduce(
    (prev, item) => {
      return {
        ...prev,
        [item.permission]: true,
      };
    },
    { ...routerMap },
  );

  return { ...menuAccess };
}
