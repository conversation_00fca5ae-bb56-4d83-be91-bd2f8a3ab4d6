import { useRootCategory } from '@/modules/tags/application/tags';
import type { CategoryData } from '@/modules/tags/domain/tags';
import type { TreeProps } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import { useState } from 'react';

export function useCategoryTree() {
  const rootCategory = useRootCategory();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>();
  const [selectedCategory, setSelectedCategory] = useState<DataNode>();
  const [currentCategory, setCurrentCategory] = useState<CategoryData>();
  const onSelected: TreeProps['onSelect'] = async ([selectedKey], { node }) => {
    if (selectedKey) {
      setSelectedCategory(node);
    }
  };

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  return {
    ...rootCategory,
    expandedKeys,
    setExpandedKeys,
    selectedCategory,
    setSelectedCategory,
    currentCategory,
    setCurrentCategory,
    onSelected,
    onExpand,
  };
}
