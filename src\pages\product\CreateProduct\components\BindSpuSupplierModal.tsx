import { Button, Card, Form, Image, message, Modal, ModalProps, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import ProForm, {
  ProFormText,
} from '@ant-design/pro-form';
import { ProFormCheckbox } from "@ant-design/pro-components";
import { EditableProTable, ProColumns } from "@ant-design/pro-table";
import { EditOutlined, SearchOutlined, SnippetsTwoTone } from "@ant-design/icons";
import { SearchSupplierListModal } from "@/pages/supplier/SupplierList/components/SearchSupplierListModal";
import { getAlibabaProductInfoByUrl } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import { AliProduct } from "@/pages/PurchaseOrderList/data";
import { queryGoodsSupplierBindList, saveSupplierGoodsBind } from "@/modules/supplier/infra/api/vender";
import { SupplierGoodsBind } from "@/modules/supplier/domain/vender";
import { Access } from "@@/plugin-access/access";
import { copyText } from "@/utils/comUtil";
export type reqParams = {
  spuId: string,
  supplierId: string;
  onCancel: () => void;
} & ModalProps;
export const BindSpuSupplier = (props: reqParams) => {

  const { spuId, supplierId, onCancel, ...rest } = props;
  const [searchSupplierVisible, setSearchSupplierVisible] = useState<boolean>(false);
  const [aliProduct, setAliProduct] = useState<AliProduct>();
  const [goodsData, setGoodsData] = useState<SupplierGoodsBind[]>([]);
  const editorFormRef = useRef<any>();
  const [form] = ProForm.useForm();

  /**
   * 调用1688接口返回数据
   */
  const loadAliProductInfo = (bindList?: SupplierGoodsBind[]) => {
    getAlibabaProductInfoByUrl(form.getFieldValue("url")).then((res) => {
      if (res.status.success) {
        setAliProduct(res.body);

        form.setFieldValue("productId", res.body?.productId);
        form.setFieldValue("minOrderQuantity", res.body?.minOrderQuantity);
        form.setFieldValue("sellerLoginId", res.body?.sellerLoginId);

        // 更新 dataSource 中的字段
        const updatedGoodsData = (bindList || []).map((item) => {
          const matchingSpec = res.body?.productTypeList?.find(
            (spec) => spec.specId === item.platformSpecId
          );
          return {
            ...item,
            platformPurchasePrice: matchingSpec?.price || item.platformPurchasePrice, // 优先使用接口返回的价格
            platformSkuId: matchingSpec?.skuId || item.platformSkuId, // 优先使用接口返回的 SKU ID
            platformQuantity: matchingSpec?.amountOnSale || item.platformQuantity, // 优先使用接口返回的库存
            minPurchaseQuantity: form.getFieldValue("minOrderQuantity"), // 使用表单中的最小采购量
          };
        });

        if (updatedGoodsData && updatedGoodsData.length > 0) {
          setGoodsData(updatedGoodsData); // 更新数据源
        }
        message.success("采集完成")
      }
    });
  }

  const queryBindList = (spuIdReq: string, supplierIdReq: string) => {
    queryGoodsSupplierBindList(spuIdReq, supplierIdReq).then((res) => {
      if (res.status.success) {
        const bindList = res?.body || [];
        setGoodsData(bindList); // 确保 dataSource 被正确设置
        if (bindList.length > 0) {
          const firstRow = bindList.find((item) => item.aliLink != "" && item.aliLink != null);
          if (firstRow?.aliLink) {
            form.setFieldValue("supplierName", firstRow?.supplierName);
            form.setFieldValue("supplierId", firstRow?.supplierId);
            form.setFieldValue("url", firstRow?.aliLink);
            form.setFieldValue("isCheck", true);

            loadAliProductInfo(bindList);
          }
        }
      } else {
        message.error("请求异常")
      }
    })
  }

  /**
   * 请求后台拉取商品sku数据
   */
  useEffect(() => {
    if (supplierId !== undefined) {
      form.resetFields();
      setGoodsData([]);
      setAliProduct(null);
      queryBindList(spuId, supplierId);
    }
  }, [spuId, supplierId]);

  const submitBindData = () => {
    const params = form.getFieldsValue();
    params.skuList = goodsData;
    saveSupplierGoodsBind(params).then((res) => {
      if (res.status.success) {
        message.success("绑定成功")
        onCancel();
      }
    })
  }

  const columns: ProColumns<SupplierGoodsBind>[] = [
    {
      title: "ERP图片",
      dataIndex: 'skuImage',
      align: "left",
      width: 100,
      editable: false,
      render: (v, record) => {
        return <Image src={record?.skuImage} width={80} />
      }
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      editable: false,
      align: "left",
      width: 150,
    },
    {
      title: 'ERP属性',
      key: 'skuName',
      editable: false,
      dataIndex: 'skuName',
      align: "left",
      width: 250,
    },
    {
      title: '首选',
      key: 'isDefault',
      dataIndex: 'isDefault',
      valueType: 'select',
      align: "left",
      renderFormItem: (p, config) => {
        const { index } = p;
        return <Select defaultValue={0} options={[{ label: "否", value: 0 }, { label: "是", value: 1 }]} onSelect={(value) => {
          editorFormRef.current?.setRowData(index, { 'isDefault': value })
        }} />;
      },
      width: 100,
    },
    {
      title: '1688属性',
      key: 'platformSpecId',
      dataIndex: 'platformSpecId',
      valueType: 'select',
      width: 250,
      align: "left",
      renderFormItem: (p, config) => {
        const { index } = p;
        const { record } = config;
        return <Select key={record?.platformSpecId} value={record?.platformSpecId} options={aliProduct?.productTypeList ? aliProduct?.productTypeList?.map((item) => {
          return {
            value: item.specId,
            label: item.goodsType,
          };
        }) : [{ value: '', label: "无" }]}
          onSelect={(value) => {
            const selected = aliProduct?.productTypeList?.find((item) => {
              return item.specId == value;
            });
            editorFormRef.current?.setRowData(index, {
              'platformSpecId': value,
              // 'platformSpecName': selected?.goodsType,
              'platformPurchasePrice': selected?.price,
              'platformSkuId': selected?.skuId,
              'platformQuantity': selected?.amountOnSale,
              'minPurchaseQuantity': form.getFieldValue("minOrderQuantity")
            })
          }} />;
      },
    },
    // {
    //   title: '选择属性',
    //   dataIndex: 'platformSpecName',
    //   key: 'platformSpecName',
    //   align: "left",
    //   width: 200,
    // },
    {
      title: '1688价格',
      key: 'platformPurchasePrice',
      editable: false,
      width: 100,
      dataIndex: 'platformPurchasePrice',
      align: "left",
      render: (v, record) => {
        return <span>{record?.platformPurchasePrice?.toFixed(2) || "0.00"}&nbsp;{record?.platformPurchasePrice > 0 ? <SnippetsTwoTone onClick={() => copyText(record.platformPurchasePrice)} /> : null}</span>
      }
    },
    {
      title: '采购价',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      align: "left",
      width: 100,
    },
    {
      title: '最小采购量',
      dataIndex: 'minPurchaseQuantity',
      key: 'minPurchaseQuantity',
      align: "left",
      width: 100
    }
  ];

  return (
    <>
      <Modal destroyOnClose={true} {...rest} title="商品SKU" closable={true} width="80%" onOk={() => { submitBindData() }} onCancel={() => {
        onCancel();
      }}>
        <Form form={form} labelCol={{ flex: '120px' }}>
          <Card style={{ height: 160, padding: 0 }} bordered={false}>
            <ProForm.Group >
              <ProFormText
                width={350}
                name="supplierName"
                label={"供应商"}
                disabled={true}
                addonAfter={<EditOutlined onClick={() => setSearchSupplierVisible(true)} />}
              />
              <ProFormText
                name="supplierId"
                hidden={true}
              />
              <ProFormText
                name="productId"
                hidden={true}
              />
              <ProFormText
                name="sellerLoginId"
                hidden={true}
              />
            </ProForm.Group>
            <ProForm.Group >
              <ProFormText
                width={350}
                name="url"
                label={"商品链接"}
                help={<ProFormCheckbox initialValue={false} name="isCheck">是否验证供应商信息</ProFormCheckbox>}
                addonAfter={<SearchOutlined onClick={(e) => { loadAliProductInfo() }} />}
              />
            </ProForm.Group >
          </Card>
          <Card bodyStyle={{ margin: 0, padding: 0 }} bordered={false}>
            <EditableProTable<SupplierGoodsBind>
              rowKey="skuId"
              columns={columns}
              value={goodsData || []}
              editableFormRef={editorFormRef}
              recordCreatorProps={false}
              size={"small"}
              editable={{
                type: 'multiple',
                editableKeys: goodsData?.map((item) => item.skuId) || [],
                actionRender: (row, config, defaultDoms) => {
                  return [defaultDoms.delete];
                },
                onValuesChange: (record, recordList) => {
                  const minQuantity = form.getFieldValue("minOrderQuantity");
                  const selected = aliProduct?.productTypeList?.find((item) => {
                    return item.specId == record?.platformSpecId;
                  });
                  recordList.map((item) => {
                    if (item.skuId == record.skuId && record?.platformSpecName != selected?.goodsType) {
                      item.platformSpecName = selected?.goodsType;
                      // item.purchasePrice = selected?.price;
                      item.platformPurchasePrice = selected?.price;
                      item.platformSkuId = selected?.skuId;
                      item.platformQuantity = selected?.amountOnSale;
                      item.minPurchaseQuantity = minQuantity;
                    }
                  })
                  setGoodsData(recordList)
                },
              }}
            />
          </Card>
        </Form>
      </Modal>
      <Access accessible={searchSupplierVisible}>
        <SearchSupplierListModal visible={searchSupplierVisible} onFinish={() => { setSearchSupplierVisible(false) }}
          onSelected={(supplier) => {
            form.setFieldValue("supplierName", supplier?.venderName)
            form.setFieldValue("supplierId", supplier?.venderId)
            setSearchSupplierVisible(false)
          }} />
      </Access>
    </>
  );
};

export default BindSpuSupplier;
