import mallRequest from '@/utils/mallRequest';
import request from 'umi-request';
import mallApiConfig from 'config/mallApiConfig';

export type Task = {
  taskId: string;
  processorName: string;
  title: string;
  state: string;
  resultType: string;
  submitterBizName: string;
  gmtCreate: number;
  successCount: number;
  errorCount: number;
  implementTime: number;
  totalCount: number;
  singleResult: string;
  errorResult: string;
};
export enum TaskResultTypeTextEnum {
  NONE = '没有返回值',
  'FILE_URI' = '可下载链接',
  'RECORDS' = '多条结果',
  'SINGLE_RECORD' = '单个结果',
  'FILE' = '文件',
}

export enum TaskStatusEnum {
  WAIT = 'WAIT',
  QUEUE = 'QUEUE',
  RUNNING = 'RUNNING',
  FAIL = 'FAIL',
  SUCCESS = 'SUCCESS',
  SECTION = 'SECTION',
}

export enum TaskStatusTextEnum {
  WAIT = '等待执行',
  QUEUE = '队列中',
  RUNNING = '正在执行',
  FAIL = '失败',
  SUCCESS = '成功',
  SECTION = '部分成功',
}

export enum TaskType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT',
}

// 定义任务处理器
export type TaskProcessorInfo = Pick<Task, 'processorName' | 'resultType' | 'title'>;

export type TaskDetail = Task & {
  parameters: string;
  singleResult: string;
  submitterBizId: string;
  submitterBizType: string;
};


// 查询分类下的规格
export async function executeNow(data: { categoryId: string }) {
  return mallRequest<API.ApiQueryPageResult<Task[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/task-center/task/executeNow',
    method: 'POST',
    data,
  });
}

export type QueryTaskListParams = {
  processorName?: string;
  state?: string;
  title?: string;
  taskType?: TaskType;
} & API.QueryPageParams;

// 查询分类下的规格
export async function queryTaskList(params: { categoryId: string }) {
  return mallRequest<API.ApiQueryPageResult<Task[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/task-center/task/pageQuery',
    method: 'POST',
    data: params,
  });
}

// 获取所有任务处理器
export async function queryAllProcessorInfo() {
  return mallRequest<API.ApiBaseResult<TaskProcessorInfo[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/task-center/task/queryAllProcessorInfo',
      method: 'GET',
    },
  );
}

// 获取所有任务处理器
export async function queryTaskInfo(data: { taskId: string }) {
  return mallRequest<API.ApiBaseResult<TaskDetail>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/task-center/task/queryInfo',
    method: 'POST',
    data,
  });
}
// 获取所有任务处理器
export async function downloadFile(params: { urlList: string[] }) {
  return request(
    `${mallApiConfig.purchaseCenterApiGatewayUrl}/purchase-mgmt-biz/task-center/task/downloadFile`,
    {
      method: 'POST',
      data: params,
      headers: {
        Authorization: localStorage.getItem('ocp-token'),
      },
      responseType: 'blob',
    } as any,
  );
}
