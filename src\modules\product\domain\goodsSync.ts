export type GoodsComposeRelation = {
  "id": string,
  "standardSku": string,
  "relation": string,
  "composeType": string,
  "asinkingSku": string,
  "asinkingSyncStatus": number,
  "asinkingSyncLog": string,
  "tongtoolSku": string,
  "tongtoolSyncStatus": number,
  "tongtoolSyncLog": string,
  "gmtCreate": number,
  "gmtModified": number,
  "tenantId": string,
}

export type GoodsMskuBind = {
  isAsinkingBind: number;
  isAsinkingCreate: number;
  "id": string,
  "msku": string,
  "relation": string,
  "composeType": string,
  "asinkingSku": string,
  "asinkingSyncStatus": number,
  "asinkingSyncLog": string,
  "tongtoolSku": string,
  "tongtoolSyncStatus": number,
  "tongtoolSyncLog": string,
  "gmtCreate": number,
  "gmtModified": number,
  "tenantId": string,
}

export enum GoodsComposeEnum  {
  "BIND" = '捆绑',
  "ASSEMBLE" = '组合'
};



