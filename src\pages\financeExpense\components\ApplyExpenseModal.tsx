import {ProFormField, ProFormInstance, ProFormSelect, ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {<PERSON>ton, Card, Col, Form, message, ModalProps, Radio, Row, Table} from "antd";
import { Modal } from "antd";
import React, {useEffect, useRef, useState} from "react";
import {
  applyExpense,
  getExpenseAuditConfig,
  getExpenseDetail,
} from "@/modules/financeExpense/infra/api/expense";
import {EditableProTable} from "@ant-design/pro-table";
import {ProForm, ProFormSwitch} from '@ant-design/pro-components';
import {EditableFormInstance, ProColumns} from "@ant-design/pro-table/es";
import {ColumnProps} from "antd/es/table";
import {getApportionListByCompany} from "@/modules/setting/infra/api/financeExpenseApportionConfig";
import {FinanceExpenseApportionConfig} from "@/pages/setting/financeExpenseApportionConfig/data";
import {
  apportionCompanyEnum,
  auditModelEnum,
  expensePaymentType,
  FinanceExpense
} from "@/modules/financeExpense/domain/expense";
import {UploadFileResult} from "@/services/common";
import {map} from "lodash";
import UploadFile from "@/components/UploadFile";
import {text} from "express";

// 定义参数格式
export type props = {
  onFinish: () => void;
  expense: FinanceExpense;
} & ModalProps;

const ApplyExpenseModal = (props: props) => {
  const [form] = Form.useForm();
  const {onFinish, expense, onCancel,...rest} = props;
  const [auditModelsList, setAuditModelsList] = useState<any>();
  const formRef = useRef<ProFormInstance<any>>();
  const editorFormRef = useRef<EditableFormInstance<any>>();

  const shareFormRef = useRef<ProFormInstance<any>>();
  const shareEditorFormRef = useRef<EditableFormInstance<any>>();

  const [shareType, setShareType] = useState<string>('none');
  const [onlineLinkShow, setOnlineLinkShow] = useState<boolean>();

  const [organizationList, setOrganizationList] = useState<any>({});
  const [apportionRateList, setApportionRateList] = useState<FinanceExpenseApportionConfig[]>();
  const [shareTitle, setShareTitle] = useState<any>({title:"金额", valueType: 'money', dataIndex: 'amount'});
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * 提交
   */
  const handleSubmit = (submitType: string) => {
    form.validateFields().then(()=>{
      setLoading(true);
      const params = form.getFieldsValue();
      let auditorList: { auditType: any; auditorUid: any; }[] = [];
      Object.keys(params)?.map((key,index)=>{
        if(key.indexOf("auditType-")>-1){
          auditorList.push({auditType: key.replaceAll('auditType-', ''), auditorUid: params[key]})
        }
      })
      params.auditorList = auditorList;
      //款项明细
      params.expenseDetailList = editorFormRef.current?.getRowsData?.();
      if(params.expenseDetailList==0 || params.expenseDetailList[0].length==0){
        message.error("请填写申请事项");
        return;
      }
      params.expenseApportionList = shareEditorFormRef.current?.getRowsData?.();
      params.submitType = submitType;
      applyExpense(params).then(res => {
        setLoading(false);
        if (res.status.success) {
          message.success(submitType=='submit' ? "申请成功" : '保存成功');
          onFinish();
        }
      })
    })
  }

  const  loadAuditStep = (auditModel: string) => {
    getExpenseAuditConfig({'auditModel': auditModel}).then(res => {
      if (res.status.success) {
        let auditModels: JSX.Element[] = [];
        res.body?.map((item)=>{
          const options = item?.roleNameList?.map(r=>{
            return {label:r.nickName, value:r.userId};
          });
          auditModels.push(<Col span={8}><ProFormSelect
            width="sm"
            name={"auditType-"+item.auditType}
            label={item.auditRole}
            options={options}
            initialValue={item?.defaultAuditId}
            rules={[{ required: true, message: '请选择审核人' }]}
          /></Col>)
        })
        setAuditModelsList(auditModels);
      }
    })
  }

  /**
   * 初始话数据
   */
  useEffect(() => {

    form.resetFields();
    if(expense?.id){
      loadAuditStep(expense?.auditModel);
      //编辑详情进来填充数据
      getExpenseDetail(expense?.id).then((res)=> {
        form.setFieldsValue(res.body);
        setShareType(res.body?.shareType);
        setOnlineLinkShow(res.body?.payment!='BANK');
        //审核流程数据填充
        res.body?.financeExpenseAuditInfo?.map(item=>{
          form.setFieldValue("auditType-"+item.auditType, item?.auditorId);
        })
        //table数据填充
        formRef.current?.setFieldsValue({table: res.body?.financeExpenseDetailInfo});
        shareFormRef.current?.setFieldsValue({shareTable: res.body?.financeExpenseApportionInfo});
        //附件渲染
        form.setFieldValue("expenseFile", res.body?.expenseFile == null ? [] : JSON.parse(res.body?.expenseFile))
        //分摊
        setApportionData('expenseAudit', res?.body?.companyName);
      });
    }

  }, [expense]);

  const setApportionData = (auditModel: string, companyName: string) => {
    getApportionListByCompany({auditModel: auditModel, 'companyName': companyName}).then(res=>{
      let orgMap: never[] = [];
      res?.body?.map(item=>{
        orgMap[item?.organizationName] = item?.organizationName;
      })
      setOrganizationList(orgMap);
      setApportionRateList(res?.body?.filter(item=>item.rate>0));
    })
  }

  const columns: ProColumns<any>[] = [
    {
      title: '用途',
      dataIndex: 'content',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '用途必填' }],
        };
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 150,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '金额必填'}],
        };
      },
    },
    {
      title: '币种',
      key: 'currency',
      width: 150,
      dataIndex: 'currency',
      valueType: 'select',
      initialValue: 'CNY',
      valueEnum: {
        'CNY': 'CNY',
        'USD': 'USD',
        'GBP': 'GBP',
        'EUR': 'EUR',
        'AUD': 'AUD',
        'HKD': 'HKD',
        'JPY': 'JPY',
        'PLN': 'PLN'
      },
    },
    {
      title: '操作',
      width: 100,
      editable:false,
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <span>&nbsp;&nbsp;</span>,
        <a
          key="delete"
          onClick={() => {
            const tableDataSource = formRef.current?.getFieldValue(
              'table',
            ) as any[];
            formRef.current?.setFieldsValue({
              table: tableDataSource.filter((item) => item.id !== record.id),
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  const columnsShare: ProColumns<any>[] = [
    {
      title: '核算小组',
      dataIndex: 'organizationName',
      valueType: 'select',
      valueEnum: organizationList,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '核算小组必填' }],
        };
      },
    },
    {
      title: shareTitle?.title,
      dataIndex: shareTitle?.dataIndex,
      width: 150,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '金额必填'}],
        };
      },
    },
    // {
    //   title: '备注',
    //   dataIndex: 'remark',
    //   width: 200,
    // },
    {
      title: '操作',
      width: 100,
      editable: false,
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <span>&nbsp;&nbsp;</span>,
        <a
          key="delete"
          onClick={() => {
            const tableDataSource = shareFormRef.current?.getFieldValue(
              'shareTable',
            ) as any[];
            shareFormRef.current?.setFieldsValue({
              shareTable: tableDataSource.filter((item) => item.id !== record.id),
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  const columnsSharePublic: ColumnProps<any>[] = [
    {
      title: '核算小组',
      dataIndex: 'organizationName',
      render: (v, record) => {
        return <>
          <div>{record.organizationName}</div>
        </>
      }
    },
    {
      title: '比例',
      dataIndex: 'rate',
      width: 150,
      render: (v, record) => {
        return <>
          <div>{record.rate}</div>
        </>
      }
    },
  ];

  return <Modal {...rest} title="申请报销" width={"80%"} confirmLoading={loading} maskClosable={false}  footer={[
    <Button key="cancel"  onClick={onCancel}>
      取消
    </Button>,
    <Button key="submit" type="primary" ghost={true} onClick={()=>{handleSubmit('submit')}}>
      提交审核
    </Button>,
    <Button key="save" type="primary" onClick={()=>{handleSubmit('draft')}}>
      保存
    </Button>
  ]} destroyOnClose={true} onCancel={onCancel}>
    <div  style={{maxHeight: 800, overflow: "scroll", overflowX: "hidden"}}>
    <Card bodyStyle={{padding: 0}} bordered={false} >
      <Form form={form} labelCol={{flex: '100px'}} >
        <Row>
          <Col span={24}>
            <ProFormText
              width="75%"
              name="title"
              label="标题"
              rules={[{ required: true, message: '请填写标题' }]}
            />

            <ProFormText
              name="id"
              hidden={true}
              initialValue={expense?.id}
            />
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <ProFormSelect
              name="auditModel"
              width="sm"
              label="单据类型"
              showSearch={true}
              rules={[{ required: true}]}
              valueEnum={auditModelEnum}
              onChange={(e)=>{
                loadAuditStep(e);
              }}
            />
          </Col>
          <Col span={8}>
            <ProFormSelect
              width="sm"
              name="payment"
              label="支付方式"
              valueEnum={expensePaymentType}
              onChange={(e)=>{
                if(e == 'BANK'){
                  setOnlineLinkShow(false);
                }else{
                  setOnlineLinkShow(true);
                }
              }}
              rules={[{ required: true, message: '请选择支付方式' }]}
            />
          </Col>
          <Col span={8}>
            <ProFormSwitch
              name="priority"
              width="sm"
              label="是否加急"
              tooltip={"选择加急需在备注里说明加急原因"}
            />
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <ProFormSelect
              width="sm"
              name="companyName"
              label="分摊公司"
              valueEnum={apportionCompanyEnum}
              rules={[{ required: true}]}
              onChange={(e)=>{
                setApportionData('expenseAudit', e);
              }}
            />
          </Col>
          <Col span={10}>
            <ProFormText
              width="lg"
              name="shareType"
              label="分摊类型"
              // initialValue={"none"}
              rules={[{ required: true, message: '请选择分摊类型' }]}
            >
              <Radio.Group
                onChange={(e) => {
                  form.setFieldValue('shareType', e.target.value);
                  setShareType(e.target.value);
                  if(e.target.value == 'rate'){
                    setShareTitle({title:"比例", valueType: 'percent', dataIndex: 'rate'});
                  }else if (e.target.value == 'amount'){
                    setShareTitle({title:"金额", valueType: 'money', dataIndex: 'amount'});
                  }
                }}
                // defaultValue={"none"}
              >
                {/*<Radio value="none">不分摊</Radio>*/}
                {/*<Radio value="public">公摊</Radio>*/}
                <Radio value="rate">比例分摊</Radio>
                <Radio value="amount">金额分摊</Radio>
              </Radio.Group>
            </ProFormText>
          </Col>
        </Row>
        <Row hidden={onlineLinkShow==null || onlineLinkShow==true}>
          <Col span={8}>
            <ProFormText
              width="sm"
              name="accountName"
              label="收款人"
              rules={[{ required: !onlineLinkShow, message: '请填写收款人' }]}
            />
          </Col>
          <Col span={8}>
            <ProFormText
              width="sm"
              name="paymentAccount"
              label="收款账号"
              rules={[{ required: !onlineLinkShow, message: '请填写收款账号' }]}
            />
          </Col>
          <Col span={8}>
            <ProFormText
              width="sm"
              name="accountBank"
              label="收款银行"
              rules={[{ required: !onlineLinkShow, message: '请填写收款银行' }]}
            />
          </Col>
          <Col span={8}>
            <ProFormText
              width="sm"
              name="payeeIdNumber"
              label="身份证"
              tooltip={"收款人身份证号码"}
              rules={[{ required: !onlineLinkShow}]}
            />
          </Col>
        </Row>
        <Row hidden={onlineLinkShow==null || onlineLinkShow==false}>
          <Col span={12}>
            <ProFormText
              width="sm"
              name="paymentLink"
              label="付款链接"
            />
          </Col>
        </Row>
        <Row>
          {auditModelsList}
        </Row>
        <Row>
          <Col span={18}>
          <ProFormField
            label="附件"
            name="expenseFile"
            transform={(values: UploadFileResult[]) => {
              return {
                purchaseImages: map(values, 'fileId'),
              };
            }}
          >
            <UploadFile/>
          </ProFormField>
          </Col>
        </Row>
        <ProFormTextArea
          width="md"
          name="remark"
          label="备注"
        />
      </Form>
    </Card>
    <Card style={{marginTop: 0}} bodyStyle={{padding: 0}} bordered={false}>
      <Row>
        <Col span={16} style={{marginLeft: 20}}>
          <ProForm<{table: any[]}>
            formRef={formRef}
            initialValues={{
              table: [],
            }}
            submitter={false}
            size={"small"}
            validateTrigger="onBlur"
          >
            <div style={{fontWeight: "bold",marginBottom:4}}><span style={{color: "red"}}>*&nbsp;</span>申请事项</div>
            <EditableProTable<any>
              rowKey="id"
              editableFormRef={editorFormRef}
              maxLength={20}
              name="table"
              bordered
              defaultSize={"small"}
              // defaultExpandAllRows={true}
              recordCreatorProps={
                {
                  position: 'bottom',
                  record: () => ({ id: (Math.random() * 1000000)?.toFixed(0) }),
                }
              }
              columns={columns}
              editable={{
                type: 'multiple',
                actionRender: (row, config, defaultDom) => {
                  return [
                    defaultDom.delete,
                  ];
                },
              }}
            />
            </ProForm>
          </Col>
          <Col span={7} hidden={shareType=='none' || shareType=='public'}>
            <ProForm<{shareTable: any[]}>
              style={{paddingLeft: 10}}
              formRef={shareFormRef}
              initialValues={{
                shareTable: [],
              }}
              submitter={false}
              size={"small"}
              validateTrigger="onBlur"
            >
              <div style={{fontWeight: "bold",marginBottom:4}}>分摊明细</div>
              <EditableProTable<any>
                rowKey="id"
                bordered
                editableFormRef={shareEditorFormRef}
                maxLength={20}
                name="shareTable"
                defaultSize={"small"}
                recordCreatorProps={
                  {
                    position: 'bottom',
                    record: () => ({ id: (Math.random() * 1000000)?.toFixed(0) }),
                  }
                }
                columns={columnsShare}
                editable={{
                  type: 'multiple',
                  actionRender: (row, config, defaultDom) => {
                    return [
                      defaultDom.delete,
                    ];
                  },
                }}
              />
          </ProForm>
        </Col>
        <Col span={7} hidden={shareType=='none' || shareType=='rate' || shareType=='amount'}>
          <div  style={{paddingLeft: 10}}>
            <div style={{fontWeight: "bold",marginBottom:4}}>分摊明细</div>
            <Table
              size={"small"}
              dataSource={apportionRateList}
              bordered
              columns={columnsSharePublic}
              pagination={false}
            />
          </div>
        </Col>
      </Row>
    </Card>
    </div>
  </Modal>
}

export default ApplyExpenseModal;
