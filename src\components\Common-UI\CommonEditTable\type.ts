import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import type { EditableProTableProps } from '@ant-design/pro-table/lib/components/EditableTable';
import type { ProCoreActionType, RowEditableConfig } from '@ant-design/pro-utils/';

export type UseColumnPropps<T> = {
  maxLength?: number; // 限制输入长度
  required?: boolean; // 是否为必填项
  errTip?: string; // 必填提示
} & ProColumns<T>;

export type CommonEditTableProps<T = any> = EditableProTableProps<T, any> & {
  // 1 表格列
  rowKey?: keyof T;
  columns: ProColumns<T>[];
  actions?: {
    title?: string;
    width?: number;
    layout?: 'vertical' | 'tile';
    align?: 'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly';
    fixed?: 'right';
    items: {
      name: string;
      title?: string; // 显示为气泡卡片
      onAction?: (row: T, action: ProCoreActionType, index: number) => void;
      show?: (row: T) => boolean;
      disabled?: (row: T) => boolean;
      color?: string;
      render?: (row: T, index: number, action?: ProCoreActionType) => React.ReactNode;
    }[];
  };
  // 2 滚动区域值
  scrollX?: number; // 固定列
  scrollY?: number | [number, number]; // 固定表头
  // 3 操作方法
  onRequest?: ProTableProps<T, object>['request']; // 设置默认值
  onCreate?: (index: number, dataSource: T[]) => T; // 新建一行
  onSave?: RowEditableConfig<T>['onSave'];
  // 4 其它
  editable?: boolean; // 默认全部可编辑
  tableRef?: React.Ref<CommonEditTableAction | undefined>;
};

export type CommonEditTableAction = {
  actionRef: ActionType | undefined; // proEditTable ref
  setEditableRowKeys: (keys: string[]) => void; // 设置可编辑项
};
