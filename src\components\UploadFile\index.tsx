import {uploadFile, UploadFileResult, UploadImageResult} from '@/services/common';
import type { UploadProps } from 'antd';
import {Button, notification, Upload} from 'antd';
import type { RcFile } from 'antd/lib/upload';
import { useEffect, useState } from 'react';
import {isArray} from "lodash";

export interface UploadImageProps extends Omit<UploadProps, 'customRequest' | 'onChange'> {
  limit?: number;
  onChange?: (imgs: UploadFileResult[]) => void;
  value?: UploadFileResult[];
  children?: React.ReactNode;
}

const UploadFile = (props: UploadImageProps) => {
  const { limit, onChange, value, children, ...rest } = props;
  const [imageList, setImageList] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<any>([]);

  const handleChange = (list: any[]) => {
    const isUploaded = list.every((item) => item.status === 'done');
    const files: UploadFileResult[] = [];
    list.forEach((item) => {
      if (item.status === 'done') {
        files.push({
          fileId: item.uid,
          link: item.url,
          name: item.name,
        });
      }
    });
    if (isUploaded && onChange) {
      if (limit && limit === 1) {
        onChange(files);
      } else {
        onChange(files);
      }
    }
  };

  const handleRemove: UploadProps['onRemove'] = (file) => {
    const newFileList = fileList?.filter((f) => f.uid !== file.uid);
    setFileList(newFileList);
    handleChange(newFileList);
    return true;
  };

  const handleMaterialUpload: UploadProps['customRequest'] = (options) => {
    setLoading(true);
    const { onSuccess } = options;

    const file = options.file as RcFile;

    if (typeof file === 'string') {
      return;
    }

    const uploadFilename = file.name;

    const imgItem = {
      uid: file.uid, // 注意，这个uid一定不能少，否则上传失败
      name: uploadFilename,
      status: 'uploading',
      url: '',
      percent: 20,
      isCanDel: true,
    };

    setFileList([...fileList, imgItem]);

    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = (e) => {
      uploadFile(uploadFilename, e.target?.result)
        .then((res) => {
          if (res.status.success) {
            const updloaedImgItem = {
              uid: res.body.fileId,
              name: uploadFilename,
              status: 'done',
              url: res.body.link,
              imgUrl: res.body.link,
              percent: 100,
              isCanDel: false,
            };
            setFileList([...fileList, updloaedImgItem]);
            setImageList([...imageList, res.body.link]);
            handleChange([...fileList, updloaedImgItem]);
            onSuccess?.call(true, true, true as any);
          }
        })
        .finally(() => setLoading(false));
    };
    reader.onerror = () => {
      setLoading(false);
    };
  };

  const uploadButton = (
    <>
      {children || <Button loading={loading}>上传文件</Button>}
    </>
  );

  useEffect(() => {
    if (isArray(value)) {
      const newFileList = value?.map((item, index) => {
        return {
          uid: item.fileId,
          name: item.name,
          status: 'done',
          url: item.link,
          imgUrl: item.link,
          link: item.link,
          fileId: item.fileId,
          percent: 100,
          isCanDel: true,
        };
      });
      setFileList(newFileList);
    }
  }, [value]);

  return (
    <Upload
      {...rest}
      fileList={fileList}
      customRequest={handleMaterialUpload}
      onRemove={handleRemove}
    >
      {(limit && fileList && fileList.length >= limit) || rest.disabled ? null : uploadButton}
    </Upload>
  );
};

export default UploadFile;
