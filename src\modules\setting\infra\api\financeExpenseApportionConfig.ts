import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {FinanceExpenseAuditConfig} from "@/pages/setting/financeExpenseAuditConfig/data";
import {FinanceExpenseApportionConfig} from "@/pages/setting/financeExpenseApportionConfig/data";

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;


//
export async function pageQueryExpenseApportion(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<FinanceExpenseApportionConfig[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/setting/expense-apportion/pageQuery',
    data,
  });
}


export async function getExpenseApportionConfigDetail(configId?: string) {
  return mallRequest<API.ApiBaseResult<FinanceExpenseAuditConfig>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'POST',
    requestPath:'/sales-mgmt-biz/sales-center/setting/expense-apportion/getApportionById',
    params: {
      id: configId,
    },
  });
}

export async function seveExpenseApportionConfig(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'POST',
    requestPath:'/sales-mgmt-biz/sales-center/setting/expense-apportion/saveApportionConfig',
    data
  });
}

export async function getApportionListByCompany(data: any) {
  return mallRequest<API.ApiBaseResult<FinanceExpenseApportionConfig[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/setting/expense-apportion/getApportionByCompanyName',
    params: data
  });
}
