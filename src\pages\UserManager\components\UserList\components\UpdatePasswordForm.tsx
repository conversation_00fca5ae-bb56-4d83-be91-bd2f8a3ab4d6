
import { useUser } from '@/modules/user-center/application/user';
import { ModalFormProps, ProFormText } from '@ant-design/pro-form';
import ProForm, { ModalForm } from '@ant-design/pro-form';
import { Form } from 'antd';
import React, { useEffect } from 'react';
import PasswordField from '../../MemberList/components/PasswordField';

type CreateFormProps = {
  userId: string;
} & Pick<ModalFormProps, 'onVisibleChange' | 'visible' | 'onFinish' | 'trigger'>;

const UpdatePasswordForm: React.FC<CreateFormProps> = (props) => {
  const { userId, onFinish } = props;
  const userService = useUser();
  const [form] = Form.useForm();
  const handleSubmit = async (values: any) => {
    const success = await userService.updatePassword({
      userId: userId,
      ...values,
    });
    if (success) {
      onFinish?.(values);
    }
    return success;
  };
  return (
    <ModalForm
      title="修改密码"
      {...props}
      width={500}
      layout="horizontal"
      form={form}
      onFinish={handleSubmit}
      modalProps={{
        destroyOnClose: true
      }}
    >
      {/* <ProFormText.Password label='原始密码' name='oldPassword'></ProFormText.Password> */}
      <PasswordField label="登录密码" name="newPassword" />
    </ModalForm>
  );
};

export default UpdatePasswordForm;
