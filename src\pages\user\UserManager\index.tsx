import { SIGN_SECRET_KEY, TOKEN_KEY } from '@/utils/token';
import mallApiConfig from 'config/mallApiConfig';
import React, { useMemo } from 'react';
import { history, MicroAppWithMemoHistory, useLocation } from 'umi';

/***
 * ***************************************************************************
 * ***************************************************************************
 * ***************************************************************************
 * ********************* 此页面功能已弃用 ***************************************
 * ***************************************************************************
 * ***************************************************************************
 * ***************************************************************************
 */
export default () => {
  const location = useLocation();
  const base = useMemo(() => {
    const last = location.pathname.split('/').filter((i) => i);
    return `/${last[last.length - 1]}`;
  }, [location.pathname]);

  return (
    <MicroAppWithMemoHistory
      name="user-center"
      url={base}
      autoSetLoading
      tokenKey={TOKEN_KEY}
      signSecretKey={SIGN_SECRET_KEY}
      endpoint={mallApiConfig.currTerminal}
      onHistoryChange={(pathname: string) => {
        const last = location.pathname.split('/').filter((i) => i);
        last[last.length - 1] = pathname.replace('/', '');
        const url = '/' + last.join('/');
        history.push(url);
      }}
    />
  );
};
