import getServiceToken from '@/hooks/getServiceToken';
import { GlobalWindow } from '@/types/global';
import { Card, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { history, useLocation } from 'umi';
import DepartmentList from './components/DepartmentList';
import MemberList from './components/MemberList';
import UserRolePanel from './components/UserRole';
import mallApiConfig from "../../../config/mallApiConfig";

export type UserManagerTabKeys = '/member' | '/dept' | '/role';

const useUserManager = (endpoint: string) => {
  const [activeKey, setActiveKey] = useState<UserManagerTabKeys>('/member');
  return {
    activeKey,
    setActiveKey,
    endpoint,
  };
};

export const UserManagerContext = getServiceToken(useUserManager);

export type UserManagerProps = {
  endpoint?: string;
};

function UserManager() {
  const useManagerService = useUserManager(
    mallApiConfig.currTerminal,
  );
  const location = useLocation();
  useEffect(() => {
    useManagerService.setActiveKey(location.pathname as any);
  }, [location.pathname]);
  return (
    <div>
      <UserManagerContext.Provider value={useManagerService}>
        <Card>
          <Tabs
            activeKey={useManagerService.activeKey}
            onTabClick={(key) => {
              if ((window as GlobalWindow).onHistoryChange) {
                (window as GlobalWindow).onHistoryChange?.(key);
              } else {
                history.push(key as any);
              }
            }}
          >
            <Tabs.TabPane tab="成员" key="/user-manager/member">
              <MemberList />
            </Tabs.TabPane>
            <Tabs.TabPane tab="部门" key="/user-manager/dept">
              <DepartmentList />
            </Tabs.TabPane>
            <Tabs.TabPane tab="角色" key="/user-manager/role">
              <UserRolePanel />
            </Tabs.TabPane>
          </Tabs>
        </Card>
      </UserManagerContext.Provider>
    </div>
  );
}

export default UserManager;
