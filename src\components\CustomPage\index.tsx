import { PlusOutlined } from '@ant-design/icons';
import type { PopconfirmProps } from 'antd';
import { Button, Popconfirm } from 'antd';
import type { ReactNode } from 'react';
import React, { useState, useMemo, useEffect } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import { FooterToolbar } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProColumnType } from '@ant-design/pro-table';

import { BetaSchemaForm } from '@ant-design/pro-form';

import CustomTable from '@/components/CustomTable';
import useBatchFunction from '@/hooks/useBatchFunction';
import { useTableHooks } from '../CustomTable/tableHooks';

import useModalForm from '@/hooks/useModalForm';
import type {
  CustomPageProps,
  CustomPageRecordCreator,
  CustomPageRecordDeletion,
  CustomPageRecordUpdater,
} from './data.d';
import type { ProCoreActionType } from '@ant-design/pro-utils';

function CustomPage<T>(props: CustomPageProps<T>) {
  const {
    toolBarRender,
    columns: propColumns,
    rowSelection: propsRowSelection = false,
    batchOptionRender,
    recordCreator = false,
    recordUpdater: recordUpdater = false,
    recordDelete: recordDelete = false,
    actionRef: propsActionRef,
    ...rest
  } = props;
  const [createForm, createModalVisible, handleModalVisible] = useModalForm<T>();
  const [currentRow, setCurrentRow] = useState<T>();
  const [updateForm, updateModalVisible, handleUpdateModalVisible] = useModalForm<T>(currentRow);
  const handleBatchDelete = useBatchFunction(
    (record: T) => recordDelete && (recordDelete as CustomPageRecordDeletion<T>).onDelete?.(record),
  );
  const { rowSelection, selectedRows, selectedRowKeys, setSelectedRows, actionRef } = useTableHooks(
    propsRowSelection,
    propsActionRef,
  );

  const intl = useIntl();

  const [tableOptions] = propColumns.filter((item) => item.valueType === 'option');

  const renderColumnsOption: ProColumnType<T, 'text'>['render'] = (
    _,
    record,
    index,
    action,
    schema,
  ) => {
    let updateLink: ReactNode;
    let deleteLink: ReactNode;
    if (recordUpdater) {
      const { renderButton, buttonText } = recordUpdater as CustomPageRecordUpdater<T>;
      if (renderButton) {
        updateLink = renderButton(record);
      } else {
        updateLink = (
          <a
            key="config"
            onClick={() => {
              setCurrentRow(record);
              handleUpdateModalVisible(true);
            }}
          >
            {buttonText || intl.formatMessage({ id: 'component.table.actions.view' })}
          </a>
        );
      }
    }
    if (recordDelete) {
      const { popConfirmProps = {} } = recordDelete as CustomPageRecordDeletion<T>;
      deleteLink = (
        <Popconfirm
          key="disable"
          {...popConfirmProps}
          title={
            (popConfirmProps as PopconfirmProps).title ||
            intl.formatMessage({ id: 'component.table.delete.confirm' })
          }
          onConfirm={async () => {
            if (recordDelete) {
              await (recordDelete as CustomPageRecordDeletion<T>).onDelete?.(record);
              actionRef.current?.reload();
            }
          }}
        >
          <a>{intl.formatMessage({ id: 'component.table.actions.delete' })}</a>
        </Popconfirm>
      );
    }
    return [updateLink, deleteLink, tableOptions?.render?.(_, record, index, action, schema)];
  };
  const columns: ProColumns<T, 'text'>[] = useMemo(() => {
    const [option] = propColumns.filter((item) => item.valueType == 'option');
    if (!option && !recordUpdater && !recordUpdater) {
      return propColumns;
    }
    return [
      ...propColumns.filter((item) => item.valueType !== 'option'),
      {
        title: '操作',
        dataIndex: 'option',
        valueType: 'option',
        render: (
          _:
            | boolean
            | React.ReactChild
            | React.ReactFragment
            | React.ReactPortal
            | null
            | undefined,
          record: T,
          index: number,
          action: ProCoreActionType<{}> | undefined,
          schema: any,
        ) => renderColumnsOption(_, record, index, action, schema),
      },
    ];
  }, [propColumns, tableOptions]);

  const isHideRowSelection = useMemo(() => {
    if (!batchOptionRender && !recordUpdater && !recordDelete) {
      return true;
    }
    return false;
  }, [batchOptionRender, recordUpdater, recordDelete]);

  // 渲染新增modal
  const renderCreateModal = () => {
    if (!recordCreator) {
      return null;
    }
    const {
      createModalRender,
      onSubmit,
      formProps = {},
    } = recordCreator as CustomPageRecordCreator<T>;
    if (!createModalRender) {
      const onFinish = async (values: T) => {
        if (onSubmit) {
          await onSubmit(values);
          handleModalVisible(false);
        }
      };
      return (
        <BetaSchemaForm<T, 'text'>
          visible={createModalVisible}
          form={createForm}
          onVisibleChange={handleModalVisible}
          layoutType="ModalForm"
          layout="horizontal"
          columns={columns as any}
          onFinish={onFinish}
          {...formProps}
        />
      );
    }
    return createModalRender({
      visible: createModalVisible,
      onVisibleChange: handleModalVisible,
    });
  };

  // 渲染编辑modal
  const renderUpdateModal = () => {
    if (!recordUpdater) {
      return null;
    }
    const {
      updateModalRender,
      onSubmit,
      formProps = {},
    } = recordUpdater as CustomPageRecordUpdater<T>;
    if (!updateModalRender) {
      const onFinish = async (values: T) => {
        if (onSubmit) {
          await onSubmit(values);
          handleUpdateModalVisible(false);
        }
      };
      return (
        <BetaSchemaForm<T, 'text'>
          form={updateForm}
          visible={updateModalVisible}
          onVisibleChange={handleUpdateModalVisible}
          layoutType="ModalForm"
          layout="horizontal"
          columns={columns as any}
          onFinish={onFinish}
          {...formProps}
        />
      );
    }
    return updateModalRender({
      visible: updateModalVisible,
      onVisibleChange: handleUpdateModalVisible,
      record: currentRow,
    });
  };

  // 渲染FooterToolBar
  const renderFooterToolbar = () => {
    if (!selectedRows?.length || !batchOptionRender) {
      return null;
    }
    const extarOptions = batchOptionRender(selectedRows);
    if (!extarOptions && !recordDelete) {
      return null;
    }

    return (
      <FooterToolbar
        extra={
          <div>
            {intl.formatHTMLMessage(
              { id: 'component.table.select' },
              {
                count: (<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>) as any,
              },
            )}
          </div>
        }
      >
        {recordDelete ? (
          <Button
            danger
            ghost
            onClick={async () => {
              await handleBatchDelete(selectedRows);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            {intl.formatMessage({ id: 'component.table.actions.batchDelete' })}
          </Button>
        ) : null}

        {extarOptions}
      </FooterToolbar>
    );
  };

  // 渲染toolBarRender
  const renderToolBarRender = () => {
    let toolBarRenderDom: React.ReactNode[] = [];
    if (toolBarRender) {
      toolBarRenderDom = toolBarRender?.(actionRef as unknown as ActionType, {
        selectedRows,
        selectedRowKeys,
      });
    }
    if (!recordCreator) {
      return toolBarRenderDom;
    }
    return [
      <Button
        type="primary"
        key="primary"
        onClick={() => {
          handleModalVisible(true);
        }}
      >
        <PlusOutlined /> <FormattedMessage id="pages.searchTable.new" defaultMessage="New" />
      </Button>,
      ...toolBarRenderDom,
    ];
  };

  useEffect(() => {
    if (propsActionRef) {
      // @ts-ignore
      propsActionRef.current = actionRef.current;
    }
  }, [actionRef.current]);

  if (propsActionRef) {
    // @ts-ignore
    propsActionRef.current = actionRef.current;
  }

  return (
    <>
      <CustomTable<T, API.PageParams>
        actionRef={actionRef}
        toolBarRender={() => renderToolBarRender()}
        {...rest}
        columns={columns}
        rowSelection={isHideRowSelection ? propsRowSelection : rowSelection}
      />
      {/*/!* 渲染批量操作 *!/*/}
      {/*{renderFooterToolbar()}*/}
      {/*/!* 渲染创建modal *!/*/}
      {/*{renderCreateModal()}*/}
      {/*/!* 渲染编辑modal *!/*/}
      {/*{renderUpdateModal()}*/}
    </>
  );
}

export default CustomPage;
