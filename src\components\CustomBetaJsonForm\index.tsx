import { useMemo } from 'react';
import { BetaSchemaForm } from '@ant-design/pro-form';
import type {
  FormSchema,
  ProFormColumnsType,
} from '@ant-design/pro-form/lib/components/SchemaForm';
import UploadImage from '../UploadImage';
import UploadVideo from '../UploadVideo';
import { CategorySelect } from '@/components/CategoryTreeModal';
import type { ProSchema } from '@ant-design/pro-utils';
import PreviewImageList from '../ImageList';
import { isString } from 'lodash';
import NationSelect from './components/NationSelect';
import BraftEditorForm from '../BraftEditorForm';
import CustomUploadImageGroup from '../CustomUploadImageGroup';
import { Card } from 'antd';

export type TableListItem = {
  key: number;
  name: string;
  status: {
    label: string | number;
    value: number;
  }[];
};

export type CustomBetaSchemaFormValueType =
  | 'link'
  | 'formCard'
  | 'uploadImage'
  | 'uploadImageGroup'
  | 'uploadVideo'
  | 'nationSelect'
  | 'brandSelect'
  | 'categorySelect'
  | 'richText';

export type CustomBetaSchemaFormProps<T> = FormSchema<T, CustomBetaSchemaFormValueType> & {
  labelWidth?: any;
};

export type ValueTypeMap<T extends any> = Pick<ProSchema<T, any, any>, 'renderFormItem' | 'render'>;

export default function CustomBetaSchemaForm<T>(props: CustomBetaSchemaFormProps<T>) {
  const { columns = [], labelWidth, ...restProps } = props;
  const formItemGlobalProps = useMemo(() => {
    const formItemProps: any = {};
    if (labelWidth) {
      formItemProps.labelCol = { flex: `${labelWidth}px` };
    }
    return formItemProps;
  }, [labelWidth]);

  const valueTypeMap: Record<string, ValueTypeMap<T>> = useMemo(
    () => ({
      uploadImage: {
        render: (value: any) => {
          return <PreviewImageList imageList={value || []} />;
        },
        renderFormItem: () => {
          return <UploadImage />;
        },
      },
      uploadImageGroup: {
        render: (value: any) => {
          return <PreviewImageList imageList={value || []} />;
        },
        renderFormItem: () => {
          return <CustomUploadImageGroup />;
        },
      },
      uploadVideo: {
        render: (value: any) => {
          return <UploadVideo value={value} disabled />;
        },
        renderFormItem: () => {
          return <UploadVideo />;
        },
      },
      categorySelect: {
        render: () => {
          return <CategorySelect disabled />;
        },
        renderFormItem: () => {
          return <CategorySelect />;
        },
      },
      nationSelect: {
        render: () => {
          return <NationSelect />;
        },
        renderFormItem: () => {
          return <NationSelect />;
        },
      },
      richText: {
        render: () => <BraftEditorForm />,
        renderFormItem: () => <BraftEditorForm />,
      },
    }),
    [],
  );

  const newColumns: ProFormColumnsType<T, CustomBetaSchemaFormValueType>[] = useMemo(() => {
    return columns?.map((item) => {
      const columnsItem = item as ProFormColumnsType<T, CustomBetaSchemaFormValueType>;
      if (columnsItem.valueType === 'formCard') {
        const { columns: cColumns = [], fieldProps = {}, ...rest } = columnsItem;
        return {
          ...rest,
          renderFormItem: () => {
            return (
              <Card {...(fieldProps as any)}  bordered={false}>
                <CustomBetaSchemaForm<T>
                  columns={
                    ((cColumns || []) as any[]).map((c) => {
                      return {
                        ...c,
                        formItemProps: {
                          ...c.formItemProps,
                          ...formItemGlobalProps,
                        },
                      };
                    }) as any
                  }
                  layoutType="Embed"
                />
              </Card>
            );
          },
        };
      }
      return {
        ...columnsItem,
        ...(isString(columnsItem.valueType) ? valueTypeMap[columnsItem.valueType] : null),
      };
    });
  }, [columns, formItemGlobalProps, valueTypeMap]);
  return (
    <BetaSchemaForm<T, CustomBetaSchemaFormValueType>
      columns={newColumns}
      {...(restProps as any)}
    />
  );
}
