import {useCurrency} from '@/modules/currency/application/currency';
import type {SupplierGoods, Vender} from '@/modules/supplier/domain/vender';
import type {ProColumns} from '@ant-design/pro-table';
import {Button, message, Space} from 'antd';
import React, {useEffect, useState} from 'react';
import {useRequestTable} from "@/hooks/useRequestTable";
import {defaultSupplier, getSupplierGoodsList} from "@/modules/supplier/infra/api/vender";
import {Access} from "umi";
import BindAlibabaProductModal from "@/pages/supplier/SupplierGoodsList/components/BindAliProductModal";
import CustomPage from "@/components/CustomPage";

export interface spgListProps {
  supplier: Vender;
}

const SupplierGoodsList  = ( props: spgListProps) => {

  const { supplier } = props;

  const { currencyMap } = useCurrency();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [data, setData] = useState<SupplierGoods>();


  const { fetchList, actionRef } = useRequestTable((params) => {
    return getSupplierGoodsList({
      ...params,
      supplierName: supplier?.venderName
    });
  });

  useEffect(() => {
    actionRef?.current?.reload();
  }, [supplier]);



  //设置首选供应商
  const isDefaultSupplier=(value: SupplierGoods)=>{

    const obj={
      "id":value.id,
      "goodsId":value.goodsId,
      "sku":value.goodsSku ==undefined? undefined : value.goodsSku,
    }

    defaultSupplier(obj).then(res=>{
      if (res.status.success){
        actionRef.current?.reloadAndRest?.();
        message.success(res.body)
      }
    })
  }

  const columns: ProColumns<SupplierGoods>[] = [
    {
      title:"供应商商品id",
      dataIndex:'id',
      hideInTable:true,
      hideInSearch:true
    },
    {
      title: '供应商名称',
      width: 350,
      dataIndex: 'supplierName',
      hideInSearch: true,
      render: (_, record) => {
        return <>
          <span style={{fontSize: 13}}>{record?.supplierName}</span>
        </>;
      },
    },
    {
      title: 'SKU',
      dataIndex: 'goodsSku',
      render: (_, record) => {
        return <>
          {record?.aliLink ? <a href={record?.aliLink} target={"_blank"} rel="noreferrer"><span style={{fontSize: 13}}>{record?.goodsSku}</span></a> : <span style={{fontSize: 13}}>{record?.goodsSku}</span>}
        </>;
      },
    },
    {
      title: '采购价格',
      dataIndex: 'purchasePrice',
      hideInSearch: true,
    },
    // {
    //   title: '上次采购价',
    //   dataIndex: 'lastPurchasePrice',
    //   hideInSearch: true,
    // },
    {
      title: '1688价格',
      dataIndex: 'platformPurchasePrice',
      hideInSearch: true,
    }
    ,
    {
      title: '是否首选',
      dataIndex: 'isDefault',
      hideInSearch:true,
      valueEnum: {
        '1': { text: '首选', status: 'Processing' },
        '0': { text: '非首选', status: 'Error' },
      },
    },
    {
      title: '是否已绑定',
      dataIndex: 'platformProductId',
      hideInSearch:true,
      render: (v, record) => {
        const div=record.aliLink!="" && record.aliLink!=null?
          <span className="ant-badge ant-badge-status ant-badge-not-a-wrapper"><span
            className="ant-badge-status-dot ant-badge-status-processing"></span><span
            className="ant-badge-status-text">已绑定</span></span>:
          <span className="ant-badge ant-badge-status ant-badge-not-a-wrapper"><span
            className="ant-badge-status-dot  ant-badge-status-error"></span><span
            className="ant-badge-status-text">未绑定</span></span>;
        return (
          <>
            {div}
          </>
        );
      }
    },
    {
      title: '结算币种',
      dataIndex: 'currency',
      hideInSearch:true,
      renderText: (val) => {
        return currencyMap ? currencyMap[val] : val;
      },
    },
    {
      title: '操作',
      width:300,
      dataIndex: 'option',
      hideInSearch: true,
      render: (_, record) => {
        return  (
          <Space>
            <Button type="primary" size={"small"} ghost key={record.id} onClick={() => isDefaultSupplier(record) }>设为首选</Button>
            <Access accessible={true}>
              <Button type="primary" size={"small"} ghost onClick={function(){
              setData(record);
              setModalVisible(true);
              }}>绑定</Button>
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <>
    <CustomPage<Vender>
      actionRef={actionRef}
      rowKey="id"
      search={{
        labelWidth: 120,
      }}
      tableAlertRender={false}
      pagination={{pageSize: 30}}
      request={fetchList}
      columns={columns}
      recordCreator={false}
      recordUpdater={false}
      recordDelete={false}

    />
      <Access accessible={true}>
        <BindAlibabaProductModal width={1000} visible={modalVisible} supplierData={data} onCancel={() => setModalVisible(false) } onFinish={()=>{setModalVisible(false)}} />
      </Access>
    </>
);
};

export default SupplierGoodsList;
