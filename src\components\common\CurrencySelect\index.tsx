import { useCurrency } from '@/modules/currency/application/currency';
import type { SelectProps } from 'antd';
import { Select } from 'antd';

const CurrencySelect = (props: SelectProps<any>) => {
  const { currencyList } = useCurrency();
  return (
    <Select
      options={currencyList?.map((item) => ({
        value: item.alphabeticCode,
        label: `${item.nameZh}(${item.alphabeticCode})`,
      }))}
      {...props}
    />
  );
};

export default CurrencySelect;
