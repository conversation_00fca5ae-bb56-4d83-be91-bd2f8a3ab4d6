import type { FormSchema } from '@ant-design/pro-form/lib/components/SchemaForm';
import type { CustomTableProps, ParamsType } from '@/components/CustomTable';
import type { ModalFormProps } from '@ant-design/pro-form';
import type { PopconfirmProps } from 'antd';
export interface CustomPageRecordCreator<T> {
  formProps?: Omit<FormSchema<T, 'text'>, 'columns'>;
  createButtonText?: '新建' | string;
  onSubmit?: (values: T) => Promise<boolean>;
  createModalRender?: (props: ModalFormProps) => React.ReactNode;
}
export interface CustomPageRecordUpdater<T> {
  buttonText?: '编辑' | string;
  onSubmit?: (values: T) => Promise<boolean>;
  formProps?: Omit<FormSchema<T, 'text'>, 'columns'>;
  updateModalRender?: (props: ModalFormProps & { record?: T }) => React.ReactNode;
  renderButton?: (record: T) => React.ReactNode;
}
export interface CustomPageRecordDeletion<T> {
  buttonText?: '删除' | string;
  onSubmit?: (values: any) => Promise<boolean>;
  onDelete?: (record: T) => Promise<boolean>;
  popConfirmProps?: PopconfirmProps;
}

export interface CustomPageProps<T> extends CustomTableProps<T, ParamsType> {
  /** */
  recordCreator?: false | boolean | CustomPageRecordCreator<T>;
  recordUpdater?: false | boolean | CustomPageRecordUpdater<T>;
  recordDelete?: false | boolean | CustomPageRecordDeletion<T>;
  columns: ProColumns<T>[];
  batchOptionRender?: (selectedRows: T[]) => React.ReactNode;
}
