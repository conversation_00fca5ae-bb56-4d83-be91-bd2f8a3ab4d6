import { Select } from 'antd';
import { debounce } from 'lodash';
import { memo, useEffect, useState } from 'react';
import { queryBrandList } from './api';

const SelectBrand = memo((props: Record<keyof any, any>) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);

  const onQuery = debounce((name) => {
    queryBrandList({ name, pageCondition: { pageNum: 1, pageSize: 50 } }).then((res) => {
      const { body } = res;
      setSelectOption(body?.items?.map((v) => ({ value: v.brandId, label: v.brandName })));
    });
  }, 500);

  useEffect(() => {
    if (!props.value) return
    queryBrandList({ brandIds: props.value, pageCondition: { pageNum: 1, pageSize: 50 } }).then((res) => {
      const { body } = res;
      setSelectOption(body?.items?.map((v) => ({ value: v.brandId, label: v.brandName })));
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleSearch = (newValue: string) => {
    if (newValue) {
      onQuery(newValue)
    } else {
      setSelectOption([]);
    }
  };

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择商品品牌"
      optionFilterProp="children"
      onSearch={handleSearch}
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
    />
  );
});

export default SelectBrand;
