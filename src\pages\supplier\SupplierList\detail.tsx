import { <PERSON><PERSON>, <PERSON>, <PERSON>, Divider, <PERSON>, <PERSON>, Spin } from 'antd';
import type { FC } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import { FooterToolbar } from '@ant-design/pro-layout';
import { useParams } from 'umi';
import ProForm, {
  ProFormDependency,
  ProFormDigit,
  ProFormField,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
} from '@ant-design/pro-form';
import { useVenderInfo, useVender } from '@/modules/supplier/application/vender';
import {
  venderFieldLabels,
  CooperateModalMap,
  PayMethodMap,
  SettleCircleTypeMap,
  SettleModeMap, SettleTypeMap, supplierTypeMap, isCrossBorderMap, isCrossMap,supportCreditBuyMap
} from '@/modules/supplier/domain/vender';
import type { UploadImageResult } from '@/services/common';
import { find, map,isNumber } from 'lodash';
import UploadImageField from '@/components/UploadImage';
import { isPhone, isEmail, isQQ } from '@/utils/utils';
import CityCascader from '@/components/CityCascader';
import { useNation } from '@/modules/common/application/nations';
import NationSelect from '@/components/common/NationSelect';
import CurrencySelect from '@/components/common/CurrencySelect';

const SupplierDetail: FC<{}> = () => {
  const { id } = useParams<{ id: string }>();
  const [form] = ProForm.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [errorVisible, handleErrorVisible] = useState<boolean>(false);
  const { venderDetail, reloadVenderInfo, loading } = useVenderInfo(id);
  const { create, update } = useVender('');
  const { nations } = useNation();

  useEffect(() => {
    if (venderDetail) {
      const { provinceName, cityName, areaName } = venderDetail;
      const address = [venderDetail?.provinceName, venderDetail?.cityName, venderDetail?.areaName];
      form.setFieldsValue({
        ...venderDetail,
        supportCreditBuy: isNumber(venderDetail?.supportCreditBuy) ? venderDetail?.supportCreditBuy + '' : '',
        address: provinceName && cityName && areaName ? address : [],
      });
    }
  }, [venderDetail]);

  const onFinish = async (values: any) => {
    handleErrorVisible(false);
    setSubmitting(true);
    const {
      licenseImages: licenseImagesIds = [],
      qualificationImages: qualificationImagesIds = [],
      // address,
      ...rest
    } = values;
    // const [province, city, area] = address;
    const addressObj = {
      // provinceName: province || venderDetail?.provinceName,
      // cityName: city || venderDetail?.cityName,
      // areaName: area || venderDetail?.areaName,
    };
    console.log(id);
    if (id) {
      const success = await update({
        venderId: id,
        ...rest,
        ...addressObj,
        licenseImagesIds,
        qualificationImagesIds,
        level: 'L1',
      });
      if (success) {
        reloadVenderInfo();
      }
    } else {
      await create({
        ...rest,
        ...addressObj,
        licenseImagesIds,
        qualificationImagesIds,
        level: 'L1',
      });
    }
    setSubmitting(false);
  };

  const onFinishFailed = (errorInfo: any) => {
    setSubmitting(false);
    handleErrorVisible(true);
  };

  const renderSelectField = (keyMap: Record<string, string>, key: string, required: boolean) => {
    return (
      <ProFormField
        label={venderFieldLabels[key]}
        name={key}
        rules={[{ required: required, message: `请选择${venderFieldLabels[key]}` }]}
      >
        <Select placeholder={`请选择${venderFieldLabels[key]}`}>
          {Object.keys(keyMap).map((k) => {
            return (
              <Select.Option value={k} key={k}>
                {keyMap[k]}
              </Select.Option>
            );
          })}
        </Select>
      </ProFormField>
    );
  };

  return (
    <Spin spinning={loading}>
      <ProForm
        form={form}
        layout="horizontal"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        labelCol={{ span: 5 }}
        submitter={false}
        initialValues={{
          licenseImages: [],
          qualificationImages: [],
          settleCurrency: 'CNY',
          nationName: '中国',
          settleMode: 'SUPPLY_PRICE',
        }}
      >
        <Card title="基本信息" style={{ fontSize: 13 }}>
          <Row gutter={24}>
            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={venderFieldLabels.venderName}
                name="venderName"
                rules={[{ required: true, message: '请输入供应商名称' }]}
              />
              {/*{renderSelectField(CooperateModalMap, 'cooperateModal')}*/}
              {renderSelectField(PayMethodMap, 'payType', false)}
              {renderSelectField(supplierTypeMap, 'supplierType', false)}
              <ProFormField
                rules={[{ required: false, message: '请选择结算币种' }]}
                label="结算币种"
                name="settleCurrency"
              >
                <CurrencySelect showSearch />
              </ProFormField>
            </Col>

            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={venderFieldLabels.venderCode}
                name="venderCode"
                rules={[{ required: true, message: '请输入供应商编码' }]}
              />
              {renderSelectField(SettleTypeMap, 'settleType', true)}
              {renderSelectField(isCrossBorderMap, 'isCrossBorder', false)}
              {renderSelectField(supportCreditBuyMap, 'supportCreditBuy', false)}
            </Col>
            <Col lg={8} md={8} sm={24}>
              <ProFormText label={venderFieldLabels.foreignName} name="foreignName" />
              {renderSelectField(SettleCircleTypeMap, 'settleCircle', false)}
              {renderSelectField(isCrossMap, 'isCross', false)}
            </Col>
          </Row>
          <Divider />
          <Row gutter={24}>
            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={"开户行"}
                name="bankName"
              />
              <ProFormText
                label={"淘宝ID"}
                name="taobaoAccount"
              />
            </Col>
            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={"开户名称"}
                name="accountName"
              />
              <ProFormText
                label={"支付宝ID"}
                name="aliPayAccount"
              />
            </Col>
            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={"开户卡号"}
                name="account"
              />
            </Col>
          </Row>



          <Divider />
          <Row gutter={24}>
            <Col lg={8} md={8} sm={24}>
              <ProFormText required={true} label={venderFieldLabels.contractor} name="contractor" />
              <ProFormText
                label={venderFieldLabels.qq}
                name="qq"
                rules={[
                  {
                    validator: (rule, value, callback) => {
                      try {
                        if (value && !isQQ(value)) {
                          throw new Error('请填写正确的qq号码');
                        }
                        callback();
                      } catch (e: any) {
                        callback(e);
                      }
                    },
                  },
                ]}
              />
              <ProFormTextArea
                label={"地址"}
                rules={[{ required: true, message: '请输入地址' }]}
                name="detail"
                placeholder="请输入详细地址"
              />
            </Col>

            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={venderFieldLabels.cellphone}
                name="cellphone"
                required={true}
              />
              <ProFormField
                label={venderFieldLabels.nationName}
                name="nationName"
              >
                <NationSelect />
              </ProFormField>
            </Col>

            <Col lg={8} md={8} sm={24}>
              <ProFormText
                label={venderFieldLabels.email}
                name="email"
                rules={[
                  {
                    validator: (rule, value, callback) => {
                      try {
                        if (value && !isEmail(value)) {
                          throw new Error('请填写正确的邮箱');
                        }
                        callback();
                      } catch (e: any) {
                        callback(e);
                      }
                    },
                  },
                ]}
              />
              {/*<ProFormDependency name={['nationName']} required={false}>*/}
              {/*  {({ nationName }) => {*/}
              {/*    const nation = find(nations, { name: nationName });*/}
              {/*    return (*/}
              {/*      <ProFormField*/}
              {/*        label="地址"*/}
              {/*        name="address"*/}
              {/*        rules={[*/}
              {/*          {*/}
              {/*            validator: (rule, value) => {*/}
              {/*              if (!value) {*/}
              {/*                return Promise.reject('请选择省市区');*/}
              {/*              }*/}
              {/*              const haveNullValue = (value || []).some((item: any) => !item);*/}
              {/*              if (haveNullValue) {*/}
              {/*                return Promise.reject('请输入省市区');*/}
              {/*              }*/}
              {/*              return Promise.resolve();*/}
              {/*            },*/}
              {/*          },*/}
              {/*        ]}*/}
              {/*      >*/}
              {/*        <CityCascader*/}
              {/*          key={nationName}*/}
              {/*          nation={nation?.twoLetterCode}*/}
              {/*          placeholder="请选择省市区"*/}
              {/*        />*/}
              {/*      </ProFormField>*/}
              {/*    );*/}
              {/*  }}*/}
              {/*</ProFormDependency>*/}
            </Col>
          </Row>
        </Card>

        <ProFormDependency name={['payType']}>
          {({ payType }) => {
            if (payType === 'BANK-TRANS') {
              return (
                <Card title="账户信息" style={{ marginTop: 10 }}>
                  <Row gutter={16}>
                    <Col lg={12} md={12} sm={24}>
                      <ProFormText label={venderFieldLabels.account} name="account" />
                      <ProFormText label={venderFieldLabels.accountName} name="accountName" />
                      <ProFormText label={venderFieldLabels.bankName} name="bankName" />
                    </Col>
                  </Row>
                </Card>
              );
            }
            return null;
          }}
        </ProFormDependency>

        <Card title="企业资质" style={{ marginTop: 10 }}>
          <Row gutter={36}>
            <Col span={12}>
              <ProFormField
                label="营业执照"
                name="licenseImages"
                // 格式化图片格式
                transform={(values: UploadImageResult[]) => {
                  return {
                    licenseImages: map(values, 'fileId'),
                  };
                }}
              >
                <UploadImageField maxCount={1} />
              </ProFormField>
            </Col>
            <Col span={12}>
              <ProFormField
                label="其他资质"
                name="qualificationImages"
                transform={(values: UploadImageResult[]) => {
                  return {
                    qualificationImages: map(values, 'fileId'),
                  };
                }}
              >
                <UploadImageField maxCount={3} />
              </ProFormField>
            </Col>
          </Row>
        </Card>

        <FooterToolbar>
          <Button type="primary" onClick={() => form?.submit()} loading={submitting}>
            提交
          </Button>
        </FooterToolbar>
      </ProForm>
    </Spin>
  );
};

export default SupplierDetail;
