import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Space, Tag} from "antd";
import {pageQueryOrderAudit} from "@/modules/setting/infra/api/orderAuditConfig";
import {orderAuditConfig} from "@/pages/setting/orderAuditConfig/data";

const TableList: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryOrderAudit({
      ...params,
    });
  });
  const columns: ProColumns<orderAuditConfig>[] = [
    {
      title: '审核名称',
      dataIndex: 'auditRoleName',
      colSize: (6 / 24)
    },
    {
      title: '审核角色',
      dataIndex: 'auditRole',
      hideInSearch: true,
    },
    {
      title: '审核人',
      dataIndex: 'auditUsername',
      hideInSearch: true,
    },
    {
      title: '审核条件',
      dataIndex: 'auditCondition',
      hideInSearch: true,
      render: (v, record) => {
        return <Tag>{record?.auditCondition}</Tag>;
      }
    },
    {
      title: '审核类型',
      dataIndex: 'auditType',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
      render: (v, record) => {
        return <Tag>{record?.remark}</Tag>;
      }
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   align: 'center',
    //   width: 400,
    //   render: (v, record) => {
    //     return (
    //       <Space>
    //         <a >详情</a>
    //         <a >取消任务</a>
    //       </Space>
    //     );
    //   }
    // }
  ];

  return (
    <>
      <CustomPage<orderAuditConfig>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;
