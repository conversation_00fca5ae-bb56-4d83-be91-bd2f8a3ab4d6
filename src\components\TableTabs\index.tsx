import type { TabsProps } from 'antd';
import { Tabs } from 'antd';
import styles from './styles.less';

export interface TabsItem {
  key: string;
  tab: string;
}

export interface TableTabsProps extends TabsProps {
  tabs: TabsItem[];
  children: React.ReactNode;
}

const TableTabs = (props: TableTabsProps) => {
  const { children, tabs, ...rest } = props;
  return (
    <>
      <Tabs type="card" size="small" {...rest} className={styles.tableTabs}>
        {tabs.map((item) => (
          <Tabs.TabPane key={item.key} tab={item.tab} tabKey={item.key} />
        ))}
      </Tabs>
      <div>{children}</div>
    </>
  );
};

export default TableTabs;
