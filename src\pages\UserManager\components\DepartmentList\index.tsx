import useTableSelection from '@/hooks/useTableSelection';
import type { Department, DepartmentTree } from '@/modules/user-center/domain/department';
import { batchDelete, getDeptTreeByEndpoint } from '@/modules/user-center/infra/organize';
import { CaretDownOutlined, CaretUpOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Space, Popconfirm, Modal } from 'antd';
import { omit } from 'lodash';
import { createRef, useContext } from 'react';
import { UserManagerContext } from '../..';
import BatchUpdateDepartmentModal from './BatchUpdateDepartmentModal';
import CreateOrUpdateDepartmentModal from './CreateOrUpdateDepartmentModal';

function DepartmentList() {
  const { rowSelection, selectedRowKeys } = useTableSelection();
  const { endpoint } = useContext(UserManagerContext);
  const actionRef = createRef<ActionType>();
  const request: ProTableProps<DepartmentTree, DepartmentTree>['request'] = async (params) => {
    const res = await getDeptTreeByEndpoint({
      endpoint,
      ...omit(params, ['current', 'pageSize']),
    })
    return {
      data: res?.body || []
    }
  }
  const columns: ProColumns<Department>[] = [
    {
      dataIndex: 'name',
      title: '部门名称',
    },
    {
      dataIndex: 'state',
      title: '部门状态',
      valueEnum: {
        'ACTIVATED': { text: '启用', status: 'Success' },
        'DISABLED': { text: '禁用', status: 'Error' },
      }
    },
    {
      title: '成员数',
      dataIndex: 'memberNumber',
      hideInSearch: true,
    },
    {
      title: '部门描述',
      hideInSearch: true,
      dataIndex: 'description',
      width: '20%',
      render(dom) {
        return <div style={{ wordBreak: 'break-all' }} >{dom}</div>
      },
    },
    {
      title: '操作',
      hideInSearch: true,
      valueType: 'option',
      render: (v, record) => [
        <CreateOrUpdateDepartmentModal title='编辑部门' record={record} trigger={<a>编辑</a>}
          onFinish={async () => {
            actionRef.current?.reload()
          }}></CreateOrUpdateDepartmentModal>,
        <CreateOrUpdateDepartmentModal title='新增子部门' parentId={record.deptId} trigger={<a>新增子部门</a>}
          onFinish={async () => {
            actionRef.current?.reload()
          }}></CreateOrUpdateDepartmentModal>,
        <Popconfirm title='是否确认删除该部门' onConfirm={async () => {
          const res = await batchDelete([record.deptId]);
          actionRef.current?.reload();
        }} >
          <a >删除</a>
        </Popconfirm>
      ]
    },
  ];

  const handleBatchDelete = () => {
    Modal.confirm({
      title: '是否确认删除选中的部门?',
      onOk: async () => {
        const res = await batchDelete(selectedRowKeys as string[]);
        if (res.status.success) {
          actionRef.current?.reload?.();
          actionRef.current?.clearSelected?.();
        }
      }
    })
  }

  return (
    <>
      <ProTable
        rowKey='deptId'
        columns={columns}
        options={false}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        actionRef={actionRef}
        request={request}
        expandable={{
          expandIcon: (expandProps) => {
            const { onExpand, expanded, record, expandable } = expandProps;
            if (!expandable) {
              return null;
            }
            return <span onClick={(e) => onExpand(record, e)}>
              {expanded ? <CaretDownOutlined /> : <CaretUpOutlined />}
            </span>
          }
        }}
        headerTitle={
          <Space>
            <CreateOrUpdateDepartmentModal
              title='新增部门'
              trigger={
                <Button type="primary" icon={<PlusOutlined />}>
                  新增部门
                </Button>
              }
              onFinish={async () => {
                actionRef.current?.reload()
              }}
            />
            <Button danger ghost disabled={!selectedRowKeys.length} onClick={handleBatchDelete}>删除选中部门</Button>
            <BatchUpdateDepartmentModal
              deptIds={selectedRowKeys as string[]}
              trigger={<Button disabled={!selectedRowKeys.length}>编辑选中部门</Button>}
              onFinish={async () => {
                actionRef.current?.reload()
              }}
            ></BatchUpdateDepartmentModal>
          </Space>
        }
        rowSelection={rowSelection}
      />
    </>

  );
}

export default DepartmentList;
