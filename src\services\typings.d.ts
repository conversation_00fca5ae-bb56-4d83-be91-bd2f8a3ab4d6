// @ts-ignore
/* eslint-disable */

declare namespace API {
  type CurrentUser = {
    avatarUrl?: string;
    nickName?: string;
    userId?: string;
  };

  type ApiBaseResultOfStatus = {
    domainCode: string;
    returnCode: string;
    message?: string;
    success: boolean;
  };

  type ApiBaseResult<T> = {
    body: T;
    status: ApiBaseResultOfStatus;
  };

  type ApiBasePageMeta = {
    pageNum: number;
    pageSize: number;
    pages: number;
    total: number;
  };

  type ApiBasePageResult<T> = {
    items: T;
    pageMeta: ApiBasePageMeta;
  };
  type ApiQueryPageResult<T> = ApiBaseResult<ApiBasePageResult<T>>;

  type LoginResult = {
    signSecret: string;
    token: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type PageCondition = {
    pageNum: number;
    pageSize: number;
  };

  type QueryPageParams = {
    pageCondition: PageCondition;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };
}
