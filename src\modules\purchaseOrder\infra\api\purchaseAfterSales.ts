import mallRequest from "@/utils/mallRequest";
import mallApiConfig from 'config/mallApiConfig';
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {PageQueryOrderParams} from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import mallRequest1 from "@/utils/mallRequest1";
import {PurchaseAfterSales} from "@/pages/purchaseAfterSales/data";


export type applyPurchaseAfterSalesParams = {
  goodsList: []
  orderId: string
};


//申请采购售后
export async function applyPurchaseAfterSales(data?: applyPurchaseAfterSalesParams) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/purchaseAfterSales/applyAfterSales',
    data,
  });
}


//分页search参数
export type PageQueryAfterSalesParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
  channelOrderNo?: string;
  orderState?: string;
  distributionOrderId?: string;
} & API.QueryPageParams;
// 采购售后列表
export async function pageQueryAfterSales(data?: PageQueryAfterSalesParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/purchaseAfterSales/pageQuery',
    data,
  });
}


//售后单审核
export async function updateAuditStatus(data?: any) {
  return mallRequest<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/purchaseAfterSales/updateAuditStatus',
    data
  });
}

//下载导入模板
export async function downloadAfterSalesOrder() {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'GET',
    requestPath: '/purchase-mgmt-biz/purchase-center/purchaseAfterSales/downloadAfterSalesOrder',
    responseType: 'blob',
  });
}

//导入售后单
export async function importAfterSalesOrder(link?: string) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/purchaseAfterSales/importAfterSalesOrder',
    params:{
      link
    }
  });
}

// 采购售后单详情
export async function getAfterSalesOrderDetail(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<PurchaseAfterSales>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/purchaseAfterSales/queryOrderDetail',
    params: {
      orderId,
    },
  });
}


