import { Row, Image, Typography, Descriptions, Popover, Tooltip } from 'antd';
import { memo, ReactNode } from 'react';
import styled from 'styled-components';

const StyleWrapper = styled.div`
  .info-box {
    & > div {
      flex-shrink: 0;
    }

    .ant-image {
      margin-right: 12px;
      img {
        object-fit: cover;
      }
    }

    .info {
      flex: 1;
      white-space: wrap;

      .title {
        display: -webkit-box;
        overflow: hidden;
        text-align: left;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      p {
        margin-bottom: 0px;
      }

      p:first-child {
        margin-bottom: 0px;
        color: #1890ff;
        word-break: break-all;
      }
    }

    .ant-descriptions-row > th,
    .ant-descriptions-row > td {
      padding-bottom: 0px;
    }
  }
`;

type GoodsInfoColumnProps = {
  img?: string;
  title?: string | ReactNode;
  info?: [string, string, boolean?][];
  size?: number;
};
const GoodsInfoColumn = memo((props: GoodsInfoColumnProps) => {
  return (
    <StyleWrapper>
      <Row className="info-box" wrap={false} align="middle">
        {props.img && (
          <Image
            width={props.size || 80}
            height={props.size || 80}
            fallback="https://static.elephantpal.com/common/mall/static/load-img-icon.png"
            src={props.img || 'https://static.elephantpal.com/common/mall/static/load-img-icon.png'}
          />
        )}
        <div className="info">
          {typeof props.title === 'string' ? (
            <Tooltip title={props.title} trigger="hover">
              <p className="title">{props.title}</p>
            </Tooltip>
          ) : (
            props.title
          )}
          {props?.info?.map((item) => (
            <p key={item[0]}>
              <Descriptions column={1}>
                <Descriptions.Item label={item[0]}>
                  {item[2] === undefined ? (
                    <Typography.Paragraph copyable={!!item[1] && item[1] != '-'} style={{ marginBottom: 0 }}>
                      {item[1] || '-'}
                    </Typography.Paragraph>
                  ) : (
                    item[1] || '-'
                  )}
                </Descriptions.Item>
              </Descriptions>
            </p>
          ))}
        </div>
      </Row>
    </StyleWrapper>
  );
});

export default GoodsInfoColumn;
