import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "config/mallApiConfig";
import type { DepartmentTree } from "../domain/department";
import { EndPointUser, Role } from "../domain/user";


export interface PageQueryUser extends API.QueryPageParams {
  account: string;
  bizContext: string;
  deptId: string;
  email: string;
  endpoint: string;
  nickName: string;
}

// 用户分页查询
export async function pageQueryUser(data?: PageQueryUser) {
  return mallRequest<API.ApiQueryPageResult<EndPointUser[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/tenant/endpoint/user-biz/pageQuery',
      method: 'POST',
      data,
    },
  );
}

// 启用用户
export async function activeUser(data?: {userId: string }) {
  return mallRequest<API.ApiBaseResult<DepartmentTree[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/tenant/endpoint/user-biz/active',
      method: 'POST',
      data,
    },
  );
}

// 禁用用户
export async function disableUser(data?: {userId: string }) {
  return mallRequest<API.ApiBaseResult<DepartmentTree[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/tenant/endpoint/user-biz/disable',
      method: 'POST',
      data,
    },
  );
}

// 禁用用户
export async function deleteUser(data?: {userId: string }) {
  return mallRequest<API.ApiBaseResult<DepartmentTree[]>>(
    mallApiConfig.userCenterApiGatewayUrl,
    {
      requestPath: '/user-center/tenant/endpoint/user-biz/delete',
      method: 'POST',
      data,
    },
  );
}




export interface CreateUserParams {
	cellphone: string;
	deptIds: any[];
	email: string;
	endpoint: any[];
	nickName: string;
	password: string;
	username: string;
  account: string;
}

// 创建用户
export async function createUser(data: CreateUserParams) {
  return mallRequest<API.ApiBaseResult<API.CurrentUser>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/tenant/endpoint/user-biz/create',
    method: 'POST',
    data,
  });
}

export type UpdateUserParams = CreateUserParams & {
  userId: string;
}

// 创建用户
export async function updateUser(data: UpdateUserParams) {
  return mallRequest<API.ApiBaseResult<API.CurrentUser>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/tenant/endpoint/user-biz/updateBaseInfoByOperation',
    method: 'POST',
    data,
  });
}


export interface BindUserRoleOpt {
  opt: 0 | 1;
  roleId: string;
}

export interface BindUserRoleParams {
  userId: string;
  roleOpts?: BindUserRoleOpt[];
}

//绑定用户角色
export async function bingUserRole(params: BindUserRoleParams) {
  return mallRequest<API.ApiBaseResult<API.CurrentUser>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/tenant/endpoint/user-biz/bingUserRole',
    method: 'POST',
    data: params,
  });
}




export interface UserRole {
	isSuperAdmin: boolean;
	roles: Role[];
	superAdmin: boolean;
	userId: string;
}

// 获取用户角色
export async function getUserRole(params: BindUserRoleParams) {
  return mallRequest<API.ApiBaseResult<UserRole>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/tenant/endpoint/user-biz/getUserRole',
    method: 'POST',
    data: params,
  });
}




export type UpdateUserPasswordParams = {
  newPassword: string;
  oldPassword: string;
  userId: string;
};

export async function updateUserPassword(params: UpdateUserPasswordParams) {
  return mallRequest<API.ApiBaseResult<API.CurrentUser>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/tenant/endpoint/user-biz/updatePasswordDirect',
    method: 'POST',
    data: params,
  });
}
