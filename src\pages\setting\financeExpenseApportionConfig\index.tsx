import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Space} from "antd";
import {FinanceExpenseAuditConfig} from "@/pages/setting/financeExpenseAuditConfig/data";
import Permission from "@/components/Permission";
import {FinanceExpenseApportionConfig} from "@/pages/setting/financeExpenseApportionConfig/data";
import EditConfigModal from "@/pages/setting/financeExpenseApportionConfig/components/EditConfigModal";
import {apportionCompanyEnum, auditModelEnum} from "@/modules/financeExpense/domain/expense";
import {reqByPage} from "@/modules/common/infra/api/common";

const TableList: React.FC = () => {

  const [editConfigModal, setEditConfigModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<FinanceExpenseAuditConfig>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/setting/expense-apportion/pageQuery',{
      ...params,
    });
  });
  const columns: ProColumns<FinanceExpenseApportionConfig>[] = [

    {
      title: '单据类型',
      dataIndex: 'auditModel',
      valueType: 'select',
      valueEnum: auditModelEnum,
      colSize: (6 / 24)
    },
    {
      title: '公司',
      dataIndex: 'companyName',
      valueType: 'select',
      valueEnum: apportionCompanyEnum,
      colSize: (6 / 24)
    },
    {
      title: '小组',
      dataIndex: 'organizationName',
      hideInSearch: true,
    },
    {
      title: '比例',
      dataIndex: 'rate',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.remark}</div>;
      }
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 400,
      render: (v, record) => {
        return (
          <Space>
            <Button type="primary" size={"small"} ghost onClick={()=>{setEditConfigModal(true);setCurrentRow(record)}}>
              编辑
            </Button>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<FinanceExpenseAuditConfig>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Permission permissionKey={"purchase:purchase"}>
                <Button size={"small"} key="level" type="primary" onClick={() => {
                  setEditConfigModal(true);
                  setCurrentRow({id:null});
                }}>
                  添加
                </Button>
              </Permission>
            ]
            return [...options, ...dom];
          }
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <EditConfigModal visible={editConfigModal} configId={currentRow?.id} onCancel={()=>setEditConfigModal(false)} onFinish={()=>{setEditConfigModal(false);actionRef.current?.reload()}}/>
    </>
  );
};

export default TableList;
