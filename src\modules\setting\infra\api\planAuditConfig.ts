import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {planAuditConfig} from "@/pages/setting/planAuditConfig/data";

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;

//
export async function pageQueryPlanAudit(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<planAuditConfig[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/planAudit/pageQuery',
    data,
  });
}
