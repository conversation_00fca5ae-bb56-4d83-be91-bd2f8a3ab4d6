import { getLocale, useIntl } from 'umi';
import { useCallback } from 'react';
// import type ids from '@/locales/zh-CN/pages';
import type commonIds from '@/locales/zh-CN';

// 多语言使用，类似于 Vue 的 i8n => $t
export default function useLocael() {
  const { formatMessage } = useIntl();
  const locale = getLocale();

  return {
    $t: useCallback((id: keyof typeof commonIds) => formatMessage({ id }), [formatMessage]),
    locale,
    isEn: locale === 'en-US',
  };
}
