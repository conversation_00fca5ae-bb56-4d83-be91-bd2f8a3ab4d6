import { querySupplyCategoryParamsByCategoryId } from '../infra/api/categoryParams';

// 分类参数
export const useCategoryParams = () => {
  const getParamsByCategoryId = (categoryId: string) => {
    return querySupplyCategoryParamsByCategoryId({ categoryId });
  };

  // 获取对应分类的参数列表
  const getStandardParamsList = async (categoryId: string) => {
    const res = await getParamsByCategoryId(categoryId);
    const categoryStandardParams = [...(res?.body?.current || []), ...(res?.body?.parent || [])];
    return categoryStandardParams;
  };

  return {
    getParamsByCategoryId,
    getStandardParamsList,
  };
};
