import React, {useEffect, useState} from 'react';
import {Modal, ModalProps, Form, message, Input, Radio, Row, Col} from 'antd';
import {executeSave} from "@/modules/common/infra/api/common";
import ProForm, {ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {ProFormSelect} from "@ant-design/pro-components";
import {DailyDinnerMenu} from "@/pages/dailyDinner/data";

// 定义参数格式
export type editProps = {
  onFinish: () => void;
  menuData: DailyDinnerMenu;
} & ModalProps;

export default (props: editProps) => {
  const {onFinish, menuData, ...rest} = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = ProForm.useForm();

  useEffect(() => {
    console.log(menuData)
    form.setFieldsValue(menuData);
  }, [menuData]);

  // @ts-ignore
  const onSubmit = () => {
    form.validateFields().then(res=>{
      setLoading(true);
      const params = form.getFieldsValue();
      executeSave('/sales-mgmt-biz/sales-center/setting/daily-dinner/saveDailyDinnerMenu', params).then((result) => {
        setLoading(false);
        if (result.status.success) {
          message.success('保存成功');
          onFinish();
        }
      });
    })
  }

  return (
    <>
      <Modal {...rest} title="菜单维护" className={'globalEnterKeySubmit'} confirmLoading={loading} onOk={() => onSubmit()} maskClosable={false}>
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProFormText hidden={true} name="id"/>
          <ProFormSelect width={"md"} label="餐厅" name="canteenName" rules={[{required: true}]} valueEnum={{
            '海创科技园饭堂': '海创科技园饭堂'
          }}/>
          <ProFormText width={"md"} label="菜名" name="name" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="价格" name="price" rules={[{required: true}]}/>
          <ProFormSelect width={"md"} label="类别" name="type" valueEnum={{
            '普通餐': '普通餐',
            '蒸饭': '蒸饭',
            '靓粥': '靓粥',
            '靓面': '靓面',
            '甜品': '甜品',
            '小吃': '小吃',
            '套餐': '套餐',
            '其它': '其它',
          }}/>
          <ProFormText width={"md"} label="供应时间" name="provisionTime" initialValue={"全天候"}/>
          <ProFormTextArea width={"md"}  label={"备注"} name={"remark"}/>
        </Form>
      </Modal>
    </>
  );
};
