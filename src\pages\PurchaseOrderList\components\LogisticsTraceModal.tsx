import type { ExchangeRageHistoryListItem } from '@/modules/currency/infra/currency';
import { queryLogisticsTrace } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type { ProColumns } from '@ant-design/pro-table';
import { useRequest } from "ahooks";
import { Table } from "antd";

export type LogisticsTraceListProps = {
  orderId: string;
  trackingNumber: string;
};

export const LogisticsTraceList = (props: LogisticsTraceListProps) => {
  const { orderId, trackingNumber } = props;

  const { data } = useRequest(() => queryLogisticsTrace({ orderId: orderId || '0', trackingNumber }).then((res) => res.body));

  const columns: ProColumns<ExchangeRageHistoryListItem>[] = [
    {
      title: "扫描时间",
      width: 200,
      dataIndex: 'acceptTime',
    },
    {
      title: "轨迹信息",
      dataIndex: 'remark',
    },
  ];
  return (
    <Table
      dataSource={data}
      columns={columns}
      rowKey="id"
      size="small"
      pagination = {false}
    />
  );
};

export type LogisticsTraceModalProps = {
  orderId: string;
};

export default (props: LogisticsTraceModalProps) => {
  const { orderId } = props;
  return (
    <>
      <LogisticsTraceList orderId={orderId} />
    </>
  );
};
