import { useUserCustomSpec } from '@/modules/product/application/categorySpec';
import useRequest from '@ahooksjs/use-request';
import { DeleteOutlined } from '@ant-design/icons';
import { Button, Space } from 'antd';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import { useEffect } from 'react';
import type { CategorySpecFieldProps, SpuSpecValue } from '..';
import SpecItemEdit from '../../ProductSpecsManagePanel/components/SpecItemEdit';

export default (props: CategorySpecFieldProps) => {
  const { categoryId } = props;
  const [value, setValue] = useMergedState<SpuSpecValue>([], {
    value: props.value,
    onChange: props.onChange,
  });
  const userCustomSpecDomain = useUserCustomSpec();
  const { data: specList, run } = useRequest(async () => {
    const res = await userCustomSpecDomain.getAll(categoryId);
    if (res.body) {
      const body = res.body;
      return [...(body.current || []), ...(body.parent || [])];
    }
  });
  useEffect(() => {
    if (categoryId) {
      run();
    }
  }, [categoryId]);

  const onDelete = (index: number) => {
    const newSpecList = value.map((item) => ({ ...item }));
    newSpecList.splice(index, 1);
    setValue(newSpecList);
  };

  const handleCreate = async () => {
    setValue([...value, { specName: '', specValues: [] }]);
  };
  return (
    <div>
      <Button style={{ marginLeft: 8 }} type="primary" ghost onClick={handleCreate}>
        添加规格
      </Button>
      <div style={{ marginTop: 10 }}>
        <Space direction="vertical">
          {value?.map((item, index) => {
            const filterInValueData = (specList || []).filter((d) => {
              const valuesKey = value.map((v) => v.specName);
              return item.specName == d.name || !valuesKey.includes(d.name);
            });
            return (
              <Space key={index}>
                <SpecItemEdit
                  categoryId={categoryId}
                  value={item}
                  specList={filterInValueData}
                  onChange={(v) => {
                    const newValues = [...value];
                    newValues[index] = v as any;
                    setValue(newValues);
                  }}
                />
                <DeleteOutlined onClick={() => onDelete(index)} />
              </Space>
            );
          })}
        </Space>
      </div>
    </div>
  );
};
