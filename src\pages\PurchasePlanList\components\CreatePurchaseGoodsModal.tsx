import {ProFormSelect, ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {Form, message, Modal, ModalProps} from "antd";
import {createPlanGoods} from "@/modules/purchasePlan/infra/api/purchasePlan";
import React from "react";

// 定义参数格式
export type CreateModalProps = {
  planId: string,
  onFinish: () => void;
} & ModalProps;


const CreatePurchasePlanGoodsModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, planId,...rest } = props;

  const onSubmit = () => {
    const params = form.getFieldsValue();
    params.purchasePlanId=planId==null?0:planId;
    createPlanGoods(params).then((result) => {
      if (result.status.success) {
        message.success('创建成功');
        onFinish();
      }
    });
  }

  return <Modal {...rest} title="创建计划商品" onOk={()=>onSubmit()}>
    <Form form={form} labelCol={{flex: '80px'}}>
      <ProFormText
        width="md"
        name="sku"
        label="SKU"
      />
      <ProFormText
        width="md"
        name="purchaseQuantity"
        label="数量"
      />
      <ProFormText
        width="md"
        name="salesWarehouse"
        label="销售仓库"
      />
      <ProFormText
        width="md"
        name="goodsRemark"
        label="商品备注"
      />
      <ProFormText
        width="md"
        name="purchaseEntity"
        label="采购主体"
      />
      <ProFormText
        width="md"
        name="merchandiserUsername"
        label="跟单员"
      />
      <ProFormSelect
        width="md"
        name="isUrgent"
        label="是否紧急"
        valueEnum={{
          '1': '是',
          '0': '否'
        }}
      />
    </Form>
  </Modal>
}

export default CreatePurchasePlanGoodsModal;
