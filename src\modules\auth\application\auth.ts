import { loginAccount as loginAccountApi } from '@/modules/auth/infra/api/login';
import { getToken, removeSignSecret, removeToken } from '@/utils/token';
import { useEffect, useMemo } from 'react';
import { history, useModel } from 'umi';
import { queryCurrentUser } from '../infra/api/auth';
import { useTenant } from './tenant';

export const logOut = () => {
  removeToken();
  removeSignSecret();
};

export function useAuthenticate() {
  const { initialState, setInitialState, refresh } = useModel('@@initialState');
  const tenantService = useTenant();

  // 判断当前用户是否已登录
  const isLogin = useMemo(() => {
    const token = getToken();
    return !!(token && initialState?.currentUser?.nickName);
  }, [initialState?.currentUser]);

  useEffect(() => {
    refresh();
  }, [location.pathname]);

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    const userRes = await queryCurrentUser();
    if (userRes && userRes?.status.success) {
      setInitialState({
        ...initialState,
        currentUser: userRes?.body,
      } as any);
    }
  };

  const loginAccount = async (account: API.LoginParams) => {
    const response = await loginAccountApi(account);
    return response.status.success;
  };

  // 登录
  const login = async (account: API.LoginParams) => {
    try {
      const loginAccountSuccess = await loginAccount(account);
      if (loginAccountSuccess) {
        const success = await tenantService.login();
        if (!success) {
          return success;
        }
        await fetchCurrentUser();
      }
      return loginAccountSuccess;
    } catch (error) {
      return false;
    }
  };

  const loginOut = () => {
    logOut();
    history.push('/user/login');
  };

  return {
    currentUser: initialState?.currentUser,
    login,
    isLogin,
    fetchCurrentUser,
    loginOut,
  };
}
