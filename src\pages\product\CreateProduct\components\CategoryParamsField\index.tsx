import { useCategoryParams } from '@/modules/product/application/categoryParams';
import type { CategoryParamsItemData } from '@/modules/product/infra/api/categoryParams';
import useRequest from '@ahooksjs/use-request';
import { ProFormField } from '@ant-design/pro-components';
import { Col, Input, Row, Select, Tag, Typography } from 'antd';
import { find } from 'lodash';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import { useEffect } from 'react';
import { Access } from 'umi';
import styles from './styles.less';

export type CategoryParamsValue = CategoryParamsValueItem[];

export type CategoryParamsValueItem = {
  paramName: string;
  paramId?: string;
  required?: boolean;
  values: string[] | string;
};

export type CategoryParamsFieldProps = {
  categoryId: string;
  value?: CategoryParamsValue;
  onChange?: (value: CategoryParamsValue) => void;
};

//  分类参数校验
const CategoryParamsField = (props: CategoryParamsFieldProps) => {
  const { categoryId } = props;
  const [value, setValue] = useMergedState<CategoryParamsValue>([], {
    value: props.value,
    onChange: props.onChange,
  });
  const categoryParamsDomain = useCategoryParams();
  const { data: paramsList, run } = useRequest(async () => {
    const res = await categoryParamsDomain.getParamsByCategoryId(categoryId);
    if (res.body) {
      const body = res.body;

      return [...(body.current || []), ...(body.parent || [])];
    }
    return [];
  });

  useEffect(() => {
    if (categoryId) {
      run();
    }
  }, [categoryId]);

  // useEffect(() => {
  //   if (!paramsList?.length) {
  //     setValue([]);
  //   } else {
  //     const requiredParams = paramsList?.filter((item) => item.required);
  //     const newValue = [...value];
  //     requiredParams?.map((item) => {
  //       const currentValue = find(newValue, { paramName: item.name });
  //       if (currentValue) {
  //         currentValue.required = item.required;
  //       } else {
  //         newValue.push({
  //           paramName: item.name,
  //           required: item.required,
  //           values: item.inputType === 'INPUT' ? '' : [],
  //         });
  //       }
  //     });
  //     setValue([...newValue]);
  //   }
  // }, [paramsList, categoryId]);

  const handleChange = (v: string | string[], item: CategoryParamsItemData) => {
    const findCurrent = find(value, { paramName: item.name });
    if (findCurrent) {
      findCurrent.values = v;
      findCurrent.required = item.required;
      setValue([...value]);
    } else {
      setValue([
        ...value,
        {
          paramName: item.name,
          required: item.required,
          values: v,
        },
      ]);
    }
  };

  if (!paramsList?.length) {
    return null;
  }

  return (
    <>
      <Row className={styles.category_params}>
        {paramsList?.map((item) => {
          return (
            <Col span={8} key={item.paramId} style={{ display: 'flex' }}>
              <ProFormField
                required={item.required}
                width="md"
                label={item.name}
                style={{ width: '100%' }}
              >
                <Access
                  accessible={item.inputType !== 'INPUT'}
                  fallback={
                    <Input
                      style={{ width: '100%' }}
                      value={find(value, { paramName: item.name })?.values}
                      onChange={(e) => handleChange(e.target.value, item)}
                    />
                  }
                >
                  <Select
                    mode="tags"
                    style={{ width: '100%' }}
                    value={find(value, { paramName: item.name })?.values}
                    allowClear={false}
                    options={item.values.map((v) => ({ label: v, value: v }))}
                    onSelect={(v: string[] | string) => handleChange(v, item)}
                    tagRender={(tagProps) => {
                      return (
                        <Tag closable={false} style={{ marginRight: 3 }}>
                          {tagProps.value}
                        </Tag>
                      );
                    }}
                  />
                </Access>
              </ProFormField>
            </Col>
          );
        })}
      </Row>
      <div style={{ marginBottom: 10 }}>
        <Typography.Text type="secondary">
          请认真准确填写属性，有利于用户在购买的过程中提高下单率
        </Typography.Text>
      </div>
    </>
  );
};

export default CategoryParamsField;
