import { useCategoryParams } from '@/modules/product/application/category';
import type { CategoryData } from '@/modules/product/domain/category';
import type { CategoryParamsItemData } from '@/modules/product/infra/api/categoryParams';
import { PlusOutlined } from '@ant-design/icons';
import type { ModalFormProps, ProFormInstance } from '@ant-design/pro-form';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Button, Modal, Row, Select, Tag } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

export type CategoryParamsProps = {
  categoryId: string;
};

export type CreateCategoryParamsModalProps<T> = Pick<ModalFormProps<T>, 'onFinish'>;

export type CategoryParamsActions = {
  showCategoryParams: (row: CategoryData) => void;
};

const CategoryParamsList = forwardRef((props: any, ref) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [category, setCategory] = useState<CategoryData>();
  const formRef = useRef<ProFormInstance<any>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const actionRef = useRef<ActionType>();
  const { categoryParams, createParams, updateParams } = useCategoryParams(category);
  const actions: CategoryParamsActions = {
    showCategoryParams: (row: CategoryData) => {
      setCategory(row);
      setModalVisible(true);
    },
  };
  useImperativeHandle(ref, () => actions);
  useEffect(() => {
    setEditableRowKeys([]);
  }, [modalVisible]);
  const columns: ProColumnType<CategoryParamsItemData>[] = [
    {
      title: '参数名称',
      dataIndex: 'name',
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入参数名称',
          },
        ],
      },
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      align: 'center',
      render: (v, record) => {
        return (
          <Tag color={record?.required ? 'success' : 'error'}>{record?.required ? '是' : '否'}</Tag>
        );
      },
      fieldProps: {
        options: [
          {
            value: true,
            label: '是',
          },
          {
            value: false,
            label: '否',
          },
        ],
      },
      valueType: 'select',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择是否必填',
          },
        ],
      },
    },
    {
      title: '参数类型',
      dataIndex: 'inputType',
      valueType: 'select',
      align: 'center',
      fieldProps: {
        options: [
          {
            label: '单选',
            value: 'SELECT',
          },
          {
            label: '多选',
            value: 'CHECKBOX',
          },
          {
            label: '文本输入',
            value: 'INPUT',
          },
        ],
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入参数类型',
          },
        ],
      },
    },
    {
      title: '参数值',
      dataIndex: 'values',
      align: 'center',
      fieldProps: {
        mode: 'tags',
        style: {
          marginBottom: 0,
        },
        placeholder: '请输入参数值',
      },
      formItemProps: {
        rules: [
          ({ getFieldsValue }) => ({
            validator() {
              const row = getFieldsValue();
              const keys = Object.keys(row);
              if (keys.length) {
                const value = row[keys[0]] as CategoryParamsItemData;
                const { inputType, values } = value;
                if (inputType === 'INPUT') {
                  return Promise.resolve();
                }
                if (!values) {
                  return Promise.reject(new Error('请输入参数值'));
                }
                if (!values.length) {
                  return Promise.reject(new Error('请输入参数值,至少一个参数值'));
                }
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      valueType: 'select',
      render: (v, record) => {
        if (record.inputType === 'INPUT') {
          return '-';
        }
        return (
          <>
            {record.values?.map((item) => {
              return <Tag>{item}</Tag>;
            })}
          </>
        );
      },
      renderFormItem: (p, config) => {
        const { record } = config;
        if (record?.inputType === 'INPUT' || !record?.inputType) {
          return null;
        }
        return <Select mode="tags" />;
      },
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      width: 120,
      render: (_, row) => [
        <a
          key="edit"
          onClick={() => {
            actionRef.current?.startEditable(row.paramId);
          }}
        >
          编辑
        </a>,
      ],
    },
  ];
  return (
    <Modal
      title="设置自定义参数"
      width={1000}
      bodyStyle={{ minHeight: 400 }}
      visible={modalVisible}
      onCancel={() => setModalVisible(false)}
      footer={
        <Row justify="space-between">
          <a
            onClick={() => {
              actionRef.current?.addEditRecord({
                paramId: 'new-id',
              });
            }}
          >
            <PlusOutlined /> 添加参数
          </a>
          <Button onClick={() => setModalVisible?.(false)}>关闭</Button>
        </Row>
      }
    >
      <EditableProTable<CategoryParamsItemData>
        formRef={formRef}
        actionRef={actionRef}
        bordered
        key={category?.categoryId}
        rowKey="paramId"
        columns={columns}
        value={categoryParams?.current}
        recordCreatorProps={false}
        editable={{
          actionRender: (row, config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
          editableKeys,
          onSave: async (key, row) => {
            let res;
            if (row.paramId !== 'new-id') {
              res = await updateParams(row);
            } else {
              res = await createParams(row, category?.categoryId as string);
            }
            return res;
          },
          onChange: setEditableRowKeys,
        }}
      />
    </Modal>
  );
});

export default CategoryParamsList;
