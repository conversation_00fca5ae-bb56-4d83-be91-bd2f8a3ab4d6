import React from 'react';
import {useParams} from "umi";
import {useRequest} from "ahooks";
import {printOrderDeliveryLis} from "@/modules/purchaseOrder/infra/api/purchaseOrder";

const Print: React.FC = () => {
  const {id: orderId} = useParams<{ id: string }>();
  const {data} = useRequest(() => printOrderDeliveryLis(orderId).then((res) => res.body));

  return (
    <>
      <div style={{backgroundColor: "#fff", minHeight: 1500}} dangerouslySetInnerHTML={{__html: data}}></div>
    </>
  );
};
export default Print;
