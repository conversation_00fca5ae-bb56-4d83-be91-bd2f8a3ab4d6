import type {ProColumns} from '@ant-design/pro-table';
import React from 'react';
import {useRequest} from "ahooks";
import {Modal, Table,} from "antd";
import {querySkuLogBySku} from "@/modules/goods/infra/api/goods";
import {Goods} from "@/modules/goods/domain/goods";
import moment from "moment";
import {PurchasePlan} from "@/pages/PurchasePlanList/data";
import PurchaseDetailListModal from "@/pages/PurchasePlanList/components/PurchaseDetailListModal";

export type  productSkuLogModalParams = {
  sku: string,
  onFinish: (values: PurchasePlan) => void;
};
export const ProductSkusLogModal = (props: productSkuLogModalParams) => {
  const {sku,onFinish, ...rest} = props;
  const {data, refresh, loading} = useRequest(() => querySkuLogBySku(sku).then((res) => res?.body));

  const columns: ProColumns<Goods>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      hideInSearch: true,
      align: 'center',
      width: 50,
    },
    {
      title: '内容',
      dataIndex: 'content',
      hideInSearch: true,
      align: 'center',
      width: 300,
    },
    {
      title: '操作人',
      dataIndex: 'operatorUsername',
      hideInSearch: true,
      align: 'center',
      width: 50,
    },
    {
      title: "创建时间",
      dataIndex: 'gmtCreate',
      hideInSearch: true,
      width: 50,
      render: (v, record) => {
        return moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss");
      }
    }
  ];
  return (
    <>
      <Modal {...rest} title="sku日志"  width="80%" onOk={onFinish}>
        <Table<any>
          rowKey="id"
          size="small"
          dataSource={data}
          columns={columns}
          pagination={{
            style: { marginBottom: 0 },
            pageSize: 10,
          }}
        />
      </Modal>
    </>
  );
};

export default PurchaseDetailListModal;


