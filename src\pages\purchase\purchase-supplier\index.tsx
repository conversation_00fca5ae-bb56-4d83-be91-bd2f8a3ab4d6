import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import type { CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import { useCurrency } from '@/modules/currency/application/currency';
import { formatDate } from '@/utils/utils';
import { Button, Row } from 'antd';
import { memo, useCallback, useMemo, useRef } from 'react';
import { history } from 'umi';
import type { Item } from './api';
import { PayTypeMap, SettleCircleTypeMap, SettleTypeMap, VenderStatusMap } from './api';
import Confirm from './confirm';
import styles from './index.less';
import { usePurchaseSupplierList, usePurchaseSupplierOptions } from './purchaseSupplier';

type TableProps = CommonTableProps<Item>;

const ShelfOrder = memo(() => {
  const setColumn = useColumn<Item>({ align: "center" });
  const setSearch = useSearch<Item>();
  const { actionRef, fetchList } = usePurchaseSupplierList();
  const { suppliers } = usePurchaseSupplierOptions();
  const { currencyMap } = useCurrency();
  const confirmRef = useRef()
  // reload
  const reload = () => {
    // @ts-ignore
    actionRef?.current?.reloadTable();
  }
  // column
  const columns = useMemo(() => {
    return [
      setSearch('供应商名称', 'supplierId', {
        valueEnum: Object.fromEntries(suppliers.map(item => [item.venderId, item?.venderName])),
        fieldProps: {
          showSearch: true,
        }
      }),
      setSearch('供应商状态', 'status', {
        valueEnum: VenderStatusMap
      }),
      setColumn('序号', 'index', {
        width: 48,
        fixed: 'left',
        render: (v, row, i) => i + 1
      }),
      setColumn('供应商编码', 'venderCode', {
        width: 200,
      }),
      setColumn('供应商名称', 'venderName', {
        width: 200,
      }),
      setColumn('状态', 'status', {
        valueEnum: VenderStatusMap,
      }),
      setColumn('联系人', 'contractor'),
      setColumn('联系电话', 'cellphone'),
      setColumn('国家', 'nationName', {
        render: (v, row) => row?.supplierAddressDTO?.nationName
      }),
      setColumn('地址', 'purchaseOrderStatus', {
        render: (v, row) => `${row?.supplierAddressDTO?.detail}` || '-'
      }),
      setColumn('结算类型', 'settleType', {
        valueEnum: SettleTypeMap,
      }),
      setColumn('结算周期', 'settleCircle', {
        valueEnum: SettleCircleTypeMap,
      }),
      setColumn('结算币种', 'settleCurrency', {
        valueEnum: currencyMap,
      }),
      setColumn('支付方式', 'payType', {
        valueEnum: PayTypeMap,
      }),
      setColumn('创建人', 'creator'),
      setColumn('创建时间', 'gmtCreate', {
        format: formatDate,
        sorter: true,
        width: 150
      }),
    ];
  }, [setColumn, suppliers, currencyMap]);
  // actions
  const actions = useMemo<TableProps['actions']>(() => {
    return {
      width: 120,
      align: "center",
      items: [
        {
          name: '编辑',
          onAction: (row) => history?.push(`/purchase-supplier/edit/${row?.venderId}`),
        },
        {
          name: '启用',
          onAction: (row) => confirmRef?.current?.open({ venderId: row?.venderId, title: '启用供应商', operation: 'active', tips: `确认启用${row?.venderName}?` }),
          show: (row) => row?.status === 'DISABLED',
        },
        {
          name: '禁用',
          onAction: (row) => confirmRef?.current?.open({ venderId: row?.venderId, title: '禁用供应商', operation: 'disabled', tips: `确认禁用${row?.venderName}?` }),
          show: (row) => row?.status === 'ACTIVATED',
          color: 'red',
        },
      ],
    };
  }, []);

  // toolbar
  const toolBarRender = useCallback(() => {
    return (
      <Row justify="end">
        <Button type="primary" onClick={() => history?.push('/purchase-supplier/create')}>
          新增
        </Button>
      </Row>
    );
  }, []);

  return (
    <div className={styles.tableWrapper}>
      <CommonTable<Item>
        rowKey="venderId"
        autoScrollX
        defaultPageSize={20}
        tableRef={actionRef as any}
        // @ts-ignore
        fetchRequest={(params, sort, filter) => {
          const requestParams = { ...params, orderBy: { filed: 'GMT_CREATE', direction: sort?.gmtCreate === "ascend" ? 1 : 0 } };
          return fetchList(requestParams, sort, filter)
        }}
        columns={columns}
        actions={actions}
        toolBarRender={toolBarRender}
        tableRest={{ size: "small" }}
      />
      <Confirm ref={confirmRef} reload={reload} />
    </div>
  );
});

export default ShelfOrder;
