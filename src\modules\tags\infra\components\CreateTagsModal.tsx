import type { CustomModalFormProps } from '@/components/CustomModalForm';
import CustomModalForm from '@/components/CustomModalForm';
import { useCategoryInfo } from '@/modules/tags/application/tags';
import type { CategoryData } from '@/modules/tags/domain/tags';
import type { CategoryCascaderActions } from '@/modules/tags/infra/components/CategoryCascader';
import CategoryCascader from '@/modules/tags/infra/components/CategoryCascader';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ProFormDigit, ProFormField, ProFormText } from '@ant-design/pro-form';
import { Form, Space, Tooltip } from 'antd';
import { omit } from 'lodash';
import { createRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';

export interface UpdateCategoryParams {
  parentCategoryId: string;
  name: string;
  sameLevelSequence: number;
}

export type CreateCategoryActions = {
  create: (parentCategory?: CategoryData) => void;
};

export type UpdateCategoryModalProps = CustomModalFormProps<UpdateCategoryParams>;

const CreateTagsModal = forwardRef((props: UpdateCategoryModalProps, ref) => {
  const [form] = Form.useForm();
  const categoryCascaderActionRef = createRef<CategoryCascaderActions>();
  const [parentCategory, setParentCategory] = useState<CategoryData>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const { categoryInfo } = useCategoryInfo(parentCategory);

  const actions: CreateCategoryActions = {
    create: (category) => {
      setParentCategory(category);
      setModalVisible(true);
    },
  };

  useImperativeHandle(ref, () => actions);

  useEffect(() => {
    if (modalVisible) {
      categoryCascaderActionRef.current?.reload();
    }
  }, [modalVisible]);

  useEffect(() => {
    if (categoryInfo) {
      const formData = {
        parentCategoryIds: categoryInfo?.paths?.map((item) => item.name),
        parentCategoryId: categoryInfo.categoryId,
      };
      form.setFieldsValue({
        ...formData,
        name: '',
        sameLevelSequence: 1,
      });
    } else {
      form.resetFields();
    }
  }, [form, categoryInfo, modalVisible]);

  return (
    <CustomModalForm<UpdateCategoryParams>
      {...omit(props, ['record'])}
      visible={modalVisible}
      onVisibleChange={setModalVisible}
      form={form}
    >
      <ProFormField
        name="parentCategoryIds"
        label={
          <Space>
            父级标签
            <Tooltip title="此项不填表示一级">
              <QuestionCircleOutlined />
            </Tooltip>
          </Space>
        }
        transform={(values) => {
          const categoryId = values[values.length - 1] || 0;
          const initialParentCategoryNames = categoryInfo?.paths.map((item) => item.name);
          if (initialParentCategoryNames?.includes(categoryId)) {
            return {
              parentCategoryId: categoryInfo?.categoryId,
            };
          }
          return {
            parentCategoryId: categoryId || '0',
          };
        }}
      >
        <CategoryCascader ref={categoryCascaderActionRef} />
      </ProFormField>
      <ProFormText
        name="name"
        label="标签名称"
        rules={[{ required: true, message: '请输入标签名称' }]}
      />
      <ProFormDigit
        name="sameLevelSequence"
        label={
          <Space>
            排序
            <Tooltip title="此项不填表示一级">
              <QuestionCircleOutlined />
            </Tooltip>
          </Space>
        }
      />
    </CustomModalForm>
  );
});

export default CreateTagsModal;
