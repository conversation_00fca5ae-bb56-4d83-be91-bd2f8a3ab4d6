import React from 'react';
import {useParams} from "umi";
import {useRequest} from "ahooks";
import {printPurchaseContract} from "@/modules/purchaseOrder/infra/api/purchaseOrder";

const Print: React.FC = () => {

  const {id: orderId} = useParams<{ id: string }>();
  const {data} = useRequest(() => printPurchaseContract(orderId).then((res) => res.body));

  return <div style={{backgroundColor: "#fff", float: "left"}} dangerouslySetInnerHTML={{__html: data}}></div>;
};
export default Print;
