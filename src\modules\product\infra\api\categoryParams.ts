import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

export type CategoryParamsInputType = 'SELECT' | 'CHECKBOX' | 'INPUT';

export type CategoryParamsItemData = {
  inputType: CategoryParamsInputType;
  name: string;
  paramId: string;
  required: true;
  values: string[];
};

export type QueryCategoryParamsResult = {
  current: CategoryParamsItemData[];
  parent: CategoryParamsItemData[];
};

// 根据类目查询运营平台分类参数
export async function querySupplyCategoryParamsByCategoryId(params: { categoryId: string }) {
  return mallRequest<API.ApiBaseResult<QueryCategoryParamsResult>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/param/queryByCategoryId',
      method: 'GET',
      params,
    },
  );
}

// 供应商自定义类目参数-创建
export async function createCategoryParams(data: CategoryParamsItemData & { categoryId: string }) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/param/create',
    method: 'POST',
    data,
  });
}

export type AddValueByCategoryParamsIdParams = {
  paramId: string;
  value: string;
};

// 供应商自定义类目参数-添加参数值
export async function addValueByCategoryParamsId(data: AddValueByCategoryParamsIdParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/category/param/addValue',
    method: 'POST',
    data,
  });
}

// 供应商自定义类目参数-添加参数值
export async function updateCategoryParams(
  data: CategoryParamsItemData & { categoryId: string } & { paramId: string },
) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/product-center/param/update',
    method: 'POST',
    data,
  });
}

