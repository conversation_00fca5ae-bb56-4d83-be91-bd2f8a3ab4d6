import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Alert, Button, Space, Tag} from "antd";
import Permission from "@/components/Permission";
import {reqByPage} from "@/modules/common/infra/api/common";
import {VatNumber} from "@/pages/setting/vatNumber/data";
import {copyText} from "@/utils/comUtil";
import {LoadingOutlined, SnippetsTwoTone} from "@ant-design/icons";
import moment from "moment";
import EditVatNumberModal from "@/pages/setting/vatNumber/components/EditVatNumberModal";

const TableList: React.FC = () => {

  const [editModal, setEditModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<VatNumber>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/setting/vat-account/pageQuery',{
      ...params,
    });
  });
  const columns: ProColumns<VatNumber>[] = [
    {
      title: 'VAT',
      dataIndex: 'vat',
      width: 100,
      hideInTable: true,
      colSize: (4 / 24),
    },
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      hideInTable: true,
      colSize: (4 / 24)
    },
    {
      title: '申请公司',
      dataIndex: 'applicant',
      hideInTable: true,
      colSize: (4 / 24),
    },
    {
      title: '国家',
      dataIndex: 'nation',
      hideInTable: true,
      colSize: (4 / 24),
    },
    {
      title: '名称/VAT/关联账号',
      dataIndex: 'name',
      width: 250,
      hideInSearch: true,
      colSize: (4 / 24),
      render: (v, record) => {
        return <>
          <div>名称：{record?.name}</div>
          <div>VAT：{record?.vat}<SnippetsTwoTone onClick={() => copyText(record.vat)}/></div>
          <div>关联账号：{record?.relationAccount ? JSON.parse(record?.relationAccount).map(item=>{
            return <Tag>{item}</Tag>;
          }) : '--'}
          </div>
        </>;
      }
    },
    {
      title: '申请公司/申请人地址',
      dataIndex: 'applicant',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{maxWidth: 200}}>申请公司：{record?.applicant}</div>
          <div style={{maxWidth: 200}}>申请人地址：{record?.address}</div>
        </>;
      }
    },
    {
      title: '申请人/电话/邮箱',
      dataIndex: 'contactFirst',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>申请人：{record?.contactFirst}</div>
          <div>电话：{record?.phoneFirst}</div>
          <div>邮箱：{record?.emailFirst}</div>
          <div>国家：<Tag color={"blue"}>{record?.nation}</Tag></div>
        </>;
      }
    },
    {
      title: '备用联系人/电话/邮箱',
      dataIndex: 'contactFirst',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>联系人：{record?.contactSecond || '--'}</div>
          <div>电话：{record?.phoneSecond || '--'}</div>
          <div>邮箱：{record?.emailSecond || '--'}</div>
        </>;
      }
    },
    {
      title: '时间',
      dataIndex: 'date',
      width: 150,
      hideInSearch: true,
      align: 'left',
      render: (v, record) => {
        return <>
          {record.gmtCreate != null ? (
            <div style={{fontSize:"12px"}}>创建：{moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm")}</div>) : null}
          {record.gmtModified != null ? (
            <div style={{fontSize:"12px"}}>修改：{moment(record.gmtModified as number).format("YYYY-MM-DD HH:mm")}</div>) : null}
        </>
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 350,
      hideInSearch: true,
      render: (v, record) => {
        return <div style={{maxWidth: 420}}><Alert message={record?.remark}/></div>;
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <Space>
            <a onClick={()=>{setEditModal(true);setCurrentRow(record)}}>
              编辑
            </a>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<VatNumber>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Permission permissionKey={"purchase:purchase"}>
                <Button size={"small"} key="level" type="primary" onClick={() => {
                  setEditModal(true);
                  setCurrentRow({id:null});
                }}>
                  添加
                </Button>
              </Permission>
            ]
            return [...options, ...dom];
          }
        }}
      />
      <EditVatNumberModal visible={editModal} rowData={currentRow} onCancel={()=>setEditModal(false)} onFinish={()=>{setEditModal(false);actionRef.current?.reload()}}/>
    </>
  );
};

export default TableList;
