import React, {useEffect, useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import {Button, Form, Modal, notification, Space} from "antd";
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {ExclamationCircleOutlined, SnippetsTwoTone} from "@ant-design/icons";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import {commonExport, copyText} from "@/utils/comUtil";
import {
  downloadAfterSalesOrder,
  importAfterSalesOrder,
  pageQueryAfterSales, updateAuditStatus
} from "@/modules/purchaseOrder/infra/api/purchaseAfterSales";
import {PurchaseAfterSales} from "@/pages/purchaseAfterSales/data";
import {ProFormField} from '@ant-design/pro-form';
import UploadFile from '@/components/UploadFile';
import AfterSalesDetailModal from "@/pages/purchaseAfterSales/components/AfterSalesDetailModal";
import moment from "moment";
import {AfterSalesStatusEnum, AfterSalesTypeEnum} from "@/modules/purchaseOrder/domain/purchaseAfterSales";
import {Access, Link, history} from "umi";
import Permission from "@/components/Permission";


const TableList: React.FC = () => {
  const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const [afterSalesDetailModal, setAfterSalesDetailModal] = useState<boolean>();
  const [selectedRowsState, setSelectedRows] = useState<PurchaseAfterSales[]>([]);
  const [afterSalesData, setAfterSalesData] = useState<PurchaseAfterSales>();

  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryAfterSales({
      ...params,
      status: activeStatusKey === '' ? undefined : activeStatusKey,
    });
  });

  const auditAfterSales=(auditStatus: string)=>{

    if (selectedRowsState.length<=0){
      notification.error({message: '请选择需要审核的数据'});
      return;
    }

    //通过实收金额和申请金额要一致

    Modal.confirm({
      title: "确认审核吗",
      icon: false,
      centered: true,
      onOk: function () {
        const params={
          "afterSalesIdList":selectedRowsState.map(item=>item.afterSalesOrderId),
          "auditStatus":auditStatus
        }

        updateAuditStatus(params).then(res=>{
          if (res.status.success){
            notification.success({message: '审核成功'});
            actionRef.current?.reloadAndRest?.();
          }

        })

      },
    });
  }


  const [form] = Form.useForm();
  const importAmountReceived=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadAfterSalesOrder().then(res=>{
                commonExport(res, '售后单导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;

        importAfterSalesOrder(link).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '导入成功'});
          }else {
            notification.error({
              duration: null,
              placement: "top",
              description: <Space direction="vertical" size="small">{result.body.split("<br>")}</Space>,
              message: "异常订单",
              style: {
                width: "auto"
              }
            });
          }
        });

      },
    });

  }

  const columns: ProColumns<PurchaseAfterSales>[] = [
    {
      title: '采购单号',
      dataIndex: 'purchaseOrderCode',
      hideInTable: true,
      colSize: (4 / 24),
    },
    {
      title: '平台单号',
      dataIndex: 'platformOrderCode',
      hideInTable: true,
      colSize: (4  / 24),
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      hideInTable: true,
      colSize: (4  / 24),
    },
    {
      title: '售后类型',
      dataIndex: 'afterSalesType',
      hideInTable: true,
      colSize: (4  / 24),
      valueEnum: AfterSalesTypeEnum
    },
    {
      title: '实收金额',
      dataIndex: 'amountReceived',
      hideInTable: true,
      colSize: (4 / 24),
      valueEnum: {
        "1": {
          text: "大于0",
        },
        "0": {
          text: "等于0",
        }
      }
    },
    {
      title: '售后状态',
      dataIndex: 'afterSalesStatus',
      hideInTable: true,
      renderFormItem: (t,config,formInstance) => {
        return(<Filters.CheckboxButton
          defaultValue={[""]}
          options={[
            { value: "", label: '全部' },
            { value: "WAIT", label: '待审核' },
            { value: "PASS", label: '通过' },
            { value: "REJECT", label: '驳回' },
            // { value: "END", label: '完结' }
          ]}
          onChange={(e) => {
            setSelectedRows([]);
            if(e.length>0 && e!=formInstance.getFieldValue('afterSalesStatus')){
              formInstance.setFieldValue('afterSalesStatus', e);
            }
            formInstance.submit();
          }}
        />)
      }
    },
    {
      title: '标题/单号',
      dataIndex: 'title',
      hideInSearch: true,
      width: 180,
      render: (v, record) => {
        return (
          <>
            <div style={{fontSize: 12}}>{record?.title}</div>
            <div style={{fontSize: 12}}>{record?.code}</div>
          </>
        );
      }
    },
    {
      title: '采购/平台单号',
      dataIndex: 'purchaseOrderCode',
      hideInSearch: true,
      width: 180,
      render: (v, record) => {
        return (
          <>
            <div style={{fontSize: 13}}> {record?.purchaseOrderCode}<SnippetsTwoTone onClick={() => copyText(record.purchaseOrderCode)} /></div>
            <div style={{fontSize: 13}}> {record?.platformOrderCode}<SnippetsTwoTone onClick={() => copyText(record.platformOrderCode)} /></div>
          </>
        );
      }
    },
    {
      title: '售后类型',
      dataIndex: 'afterSalesType',
      hideInSearch: true,
      width: 150,
      render: (v,record)=>{
        return <><span style={{fontSize: 12}}>{AfterSalesTypeEnum[record?.afterSalesType]}</span></>
      }
    },

    {
      title: '申请人',
      dataIndex: 'applyUsername',
      hideInSearch: true,
      render: (v,record)=>{
        return <><span style={{fontSize: 13}}>{record?.applyUsername}</span></>
      }
    },
    {
      title: '实收金额',
      dataIndex: 'amountReceived',
      hideInSearch: true,
      align:'center',
      render: (v,record)=>{
        const amountReceived=record.amountReceived==null?0:record.amountReceived;
        return amountReceived?.toFixed(2)
      }
    },
    {
      title: '申请金额',
      dataIndex: 'applyAmount',
      hideInSearch: true,
      align:'center',
      render: (v,record)=>{
        const amountReceived=record.amountReceived==null?0:record.amountReceived;
        return (<>
           <span> {record?.applyAmount?.toFixed(2) != amountReceived?.toFixed(2) ? (
             <ExclamationCircleOutlined style={{color:'red',fontSize:10,float:"right"}}/>) : null}</span>
          <span>{record?.applyAmount?.toFixed(2)}</span>
          </>)
      }
    },
    {
      title: '商品金额',
      dataIndex: 'applyGoodsAmount',
      hideInSearch: true,
      render: (v,record)=>{
        return record?.applyGoodsAmount?.toFixed(2)
      }
    },
    {
      title: '申请运费',
      dataIndex: 'applyShippingFee',
      hideInSearch: true,
      render: (v,record)=>{
        return record?.applyShippingFee?.toFixed(2)
      }
    },
    {
      title: '申请差额',
      dataIndex: 'applyBalance',
      hideInSearch: true,
      render: (v,record)=>{
        return record?.applyBalance?.toFixed(2) || 0
      }
    },
    {
      title: '金额变动',
      dataIndex: 'afterAmount',
      hideInSearch: true,
      align:'center',
      render: (v,record)=>{
        return <span>{record?.afterAmount?.toFixed(2)}</span>;
      }
    },
    {
      title: '状态',
      dataIndex: 'afterSalesStatus',
      align: 'left',
      hideInSearch: true,
      render: (v, record) => {
        const status = AfterSalesStatusEnum[record?.afterSalesStatus];
        const options = [
          status != undefined ? (
            <span>
              <span className="ant-badge-status-dot ant-badge-status-success"></span>
              <span className="ant-badge-status-text" style={{fontSize: 13}}>{status}</span>
            </span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return <div style={{fontSize: 12}}>{record?.remark}</div>
      }
    },
    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      hideInSearch: true,
      width: 160,
      render: (v, record) => {
        return <div style={{fontSize: 13}}>{moment(record?.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}</div>
      }
    },
    {
      title: '操作',
      align: 'left',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <Space>
              <Button type="primary" size={"small"}   style={{fontSize:12,borderRadius:5}} onClick={() => history.push({ pathname: `/purchase/purchase/purchaseOrder/detail/${record?.purchaseOrderId}`, state: { title: record?.purchaseOrderCode } })}>
                采购单
              </Button>
            <Button onClick={() =>{
              setAfterSalesDetailModal(true)
              setAfterSalesData(record)
            }} type="primary" size={"small"}  ghost style={{fontSize:12,borderRadius:5,color:"green",borderColor:"green"}}>
              详情
            </Button>
          </Space>
        );
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [activeStatusKey]);

  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        tableAlertRender={false}
        scroll={{ y: 'calc(100vh - 320px)' }}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Permission permissionKey={"purchase:purchase:purchaseAfterSales:pass"}>
                <Button key="level" type="primary" onClick={()=>auditAfterSales("PASS")}>
                  通过
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseAfterSales:reject"}>
                <Button key="level" type="primary" onClick={()=>auditAfterSales("REJECT")}>
                  驳回
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:purchase:purchaseAfterSales:importReceiveAmount"}>
                <Button key="level" type="primary" onClick={()=>importAmountReceived()}>
                  导入实收金额
                </Button>
              </Permission>
            ];
            return [...options, ...dom];
          },
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <Access accessible={afterSalesDetailModal}>
        <AfterSalesDetailModal visible={afterSalesDetailModal} order={afterSalesData} onCancel={() => setAfterSalesDetailModal(false)}
                             onFinish={function (){ setAfterSalesDetailModal(false)}}/>
      </Access>
    </>
  );
};

export default TableList;
