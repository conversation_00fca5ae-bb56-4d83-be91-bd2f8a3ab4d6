import { useCategoryParams } from '@/modules/product/application/categoryParams';
import { useCategorySpec } from '@/modules/product/application/categorySpec';
import { useSpu } from '@/modules/product/application/spu';
import type { SpuDetailInfo } from '@/modules/product/domain/spu';
import type { ProFormProps } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { message } from 'antd';
import { debounce, find, union } from 'lodash';
import { useState, useEffect } from 'react';
import { useParams } from 'umi';
import type { CategoryParamsValueItem } from '../components/CategoryParamsField';
import type { SpecItemData } from '../components/CategorySpecField';
import { createOrUpdateSkuList } from './utils';
export function useSpuFormService() {
  const { spuId, skuId } = useParams<{ spuId: string, skuId: string }>();
  const spuDomain = useSpu();
  const categorySpecDomain = useCategorySpec();
  const categoryParamsDomain = useCategoryParams();
  const [form] = ProForm.useForm();
  const [loading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // 记录商品的Spu值
  const { productLoad } = spuDomain;

  const spuDetail = productLoad.data;

  useEffect(() => {
    if (spuId) {
      productLoad?.run?.(spuId);
    }
  }, [spuId]);

  // 把商品详情转成创建/更改参数
  const formateSpuDetailToFormParams = async (data: SpuDetailInfo) => {
    const {
      categoryInfo,
      skus,
      logisticsAttr,
      keywords = [],
      sellingPointDescs = [],
      textDescs = [],
      packingDescs = [],
      titles,
      ...rest
    } = data;
    const spuI18ns: any[] = [];
    // titles.map((item) => {
    //   spuI18ns.push({
    //     language: item.locale,
    //     packingDesc: find(packingDescs, { locale: item.locale })?.packingDesc,
    //     sellingPointDesc: find(sellingPointDescs, { locale: item.locale })?.sellingPointDesc,
    //     textDesc: find(textDescs, { locale: item.locale })?.textDesc,
    //     keywords: find(keywords, { locale: item.locale })?.keywords,
    //     ...item,
    //   });
    // });
    const categorySpecList = await categorySpecDomain.getCategorySpec(categoryInfo.categoryId);
    const requiredCategorySpecs: SpecItemData[] = categorySpecList
      .filter((item) => item.required)
      .map((item) => {
        return {
          specName: item.name,
          specValues: [],
          required: item.required,
        };
      });

    const specs: SpecItemData[] = skus.reduce((specList: SpecItemData[], sku) => {
      const skuSpec = sku.specs;
      const newSpecList: SpecItemData[] = [...specList];
      skuSpec.map((spec) => {
        const currentSpec = find(newSpecList, { specName: spec.specName });
        if (currentSpec) {
          currentSpec.specValues = union([...currentSpec.specValues], [spec.specValue]);
        } else {
          newSpecList.push({
            specName: spec.specName,
            specValues: [spec.specValue],
          });
        }
      });
      return newSpecList;
    }, requiredCategorySpecs);
    const categoryStandardParams = await categoryParamsDomain.getStandardParamsList(
      categoryInfo.categoryId,
    );
    const standardParams: CategoryParamsValueItem[] = categoryStandardParams.reduce(
      (prev: CategoryParamsValueItem[], item) => {
        const currentStandardParams = find(categoryInfo.standardParams, { paramName: item.name });
        if (currentStandardParams) {
          return [
            ...prev,
            { ...currentStandardParams, paramId: item.paramId, required: item.required },
          ];
        }
        if (item.required) {
          prev.push({
            paramName: item.name,
            values: [],
            required: item.required,
            paramId: item.paramId,
          });
        }
        return prev;
      },
      [],
    );

    const useMultiSpec = skus.every((item) => item.specs.length);

    form.setFieldsValue({
      categoryInfo: {
        standardParams: standardParams,
        category: {
          key: categoryInfo?.categoryId,
          title: categoryInfo?.categoryPaths?.map((item) => item.name).join('|'),
        },
      },
      specs,
      skus,
      ...rest,
      useMultiSpec,
      spuI18ns: spuI18ns,
      spuLogisticsAttr: logisticsAttr,
      isShowFirstImage: skus?.some((item) => item.mainSpecInfo?.image?.fileUrl),
    });
  };

  useEffect(() => {
    if (spuDetail) {
      formateSpuDetailToFormParams(spuDetail);
    }
  }, [spuDetail]);

  const onValuesChange: ProFormProps['onValuesChange'] = async (changeValue, values) => {
    // // 如果表单的规格发生变化的话，则重置Sku信息
    if (changeValue?.specs) {
      const skuList = createOrUpdateSkuList(values.specs, values.skuList || []);
      form.setFieldsValue({ skus: skuList });
    }

    // // 当表单的分类发生变化的时候，重置规格、商品信息

    if (changeValue?.categoryInfo?.category?.key) {
      form.setFieldValue('useMultiSpec', true);
    }
    if (changeValue?.categoryInfo?.category?.key) {
      const skuList = createOrUpdateSkuList([], values?.skuList || []);
      const categoryId = values?.categoryInfo?.category?.key;
      let specs: SpecItemData[] = values.categoryInfo.standardParams || [];
      if (categoryId) {
        const categorySpecList = await categorySpecDomain.getCategorySpec(categoryId);
        const requiredCategorySpecs: SpecItemData[] = categorySpecList
          .filter((item) => item.required)
          .map((item) => {
            return {
              specName: item.name,
              specValues: [],
              specId: item.specId,
              required: item.required,
            };
          }, []);
        specs = requiredCategorySpecs;
      }
      const categoryStandardParams = await categoryParamsDomain.getStandardParamsList(categoryId);
      const standardParams: CategoryParamsValueItem[] = categoryStandardParams.reduce(
        (prev: CategoryParamsValueItem[], item) => {
          if (item.required) {
            prev.push({
              paramName: item.name,
              values: [],
              required: item.required,
              paramId: item.paramId,
            });
          }
          return prev;
        },
        [],
      );
      form.setFieldsValue({
        specs: specs,
        skus: skuList,
        categoryInfo: {
          standardParams: standardParams,
          category: values.categoryInfo?.category,
        },
      });
    }
  };

  const onFinishFailed: ProFormProps['onFinishFailed'] = async ({ errorFields }) => {
    if (errorFields.length) {
      message.error('请填写商品必填信息');
      form.scrollToField(errorFields[0].name);
    }
  };

  const transformFormValues = (values: any) => {
    const { spuI18ns = [], ...rest } = values;
    const needFormattedKeys = [
      'keywords',
      'packingDescs',
      'sellingPointDescs',
      'textDescs',
      'titles',
    ];
    const newSpuI8ns = spuI18ns.reduce(
      (
        prev: { [x: string]: { [x: string]: any; locale: any }[] },
        item: { [x: string]: any; language?: any },
      ) => {
        const locale = item.language;
        const keys = Object.keys(item);

        keys.map((key) => {
          const name = find(needFormattedKeys, (k) => {
            return k.includes(key);
          }) as string;
          prev[name] = prev[name] || [];
          prev[name].push({
            [key]: item[key],
            locale,
          });
        });
        return prev;
      },
      {},
    );

    return {
      ...rest,
      ...newSpuI8ns,
      locale: spuI18ns[0].language,
    };
  };

  const onFinish = debounce(
    async (values: any) => {
      setSubmitting(true);
      // console.log(values);
      try {
        if (spuId) {
          await spuDomain.update(
            {
              ...values,
              spuId: spuId,
            },
            () => productLoad.run(spuId),
          );
        } else {
          await spuDomain.create(values);
        }
        setSubmitting(false);
      } catch (error) {
        // console.log(error);
        setSubmitting(false);
      }
    },
    1000,
    { leading: true },
  );

  return {
    spuId,
    skuId,
    spuDetail,
    form,
    loading,
    onValuesChange,
    onFinishFailed,
    onFinish,
    submitting,
  };
}
