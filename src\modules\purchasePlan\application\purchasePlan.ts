import { useRequestTable } from '@/hooks/useRequestTable';
import {pageQuery, pageQueryPlanDetail} from '../infra/api/purchasePlan';

/**
 * 采购计划
 */
export const usePurchasePlanList = () => {
  return useRequestTable(pageQuery);
};

export const usePurchasePlan = () => {
  const purchasePlanList = usePurchasePlanList();
  return {purchasePlanList};
};

/**
 * 采购计划商品
 */
export const usePurchasePlanGoodsList = () => {
  return useRequestTable(pageQueryPlanDetail);
};

export const usePurchasePlanGoods = () => {
  const purchasePlanGoodsList = usePurchasePlanGoodsList();
  return {purchasePlanGoodsList};
};
