import { RouteContext } from '@ant-design/pro-layout';
import { Tabs } from 'antd';
import { useContext, useEffect } from 'react';
import { history, useModel } from 'umi';
import './styles.less';

const HeaderContent = () => {
  const { tabs, onRemove, onAdd, setCurrentMenu, activeKey, setActiveKey } = useModel('headerTabs');
  const { currentMenu, isMobile } = useContext(RouteContext);

  useEffect(() => {
    setActiveKey(currentMenu?.path);
    setCurrentMenu(setCurrentMenu);
    if (currentMenu?.path && currentMenu?.name) {
      onAdd({
        key: currentMenu?.path,
        name: currentMenu?.name,
      });
    }
  }, [currentMenu?.path]);
  if (isMobile) {
    return null;
  }

  return (
    <Tabs
      className="headerTabs"
      activeKey={activeKey}
      hideAdd
      onTabClick={(key) => {
        history.push(key);
        setActiveKey(key);
      }}
      onEdit={(targetKey) => onRemove(targetKey as string)}
      type="editable-card"
    >
      {tabs.map((item) => {
        return <Tabs.TabPane key={item.key} tab={item.name} />;
      })}
    </Tabs>
  );
};
export default HeaderContent;
