import { history } from 'umi';
import type { MenuDataItem } from '@ant-design/pro-layout';
import { RouteContext } from '@ant-design/pro-layout';
import { Space } from 'antd';
import { find } from 'lodash';
import type { ReactChild, ReactFragment, ReactNode, ReactPortal } from 'react';
import { useCallback, useContext, useMemo } from 'react';
import HeaderDropdown from '../HeaderDropdown';
import styles from './styles.less';

export interface HeaderMenuDropdownProps {
  menu: MenuDataItem;
  children: ReactNode;
}

const filterMenuChildren = (item: MenuDataItem) => {
  return (
    item.children?.filter(
      (c: { path: any; redirect: any; hideInMenu: any }) => c.path && !c.redirect && !c.hideInMenu,
    ) || []
  );
};

export interface MenuItemRenderProps {
  titleMenu: MenuDataItem;
  links: MenuDataItem;
}
const MenuItemRender = (props: MenuItemRenderProps) => {
  const { links, titleMenu } = props;
  return (
    <div className={styles.item}>
      <h4 onClick={() => titleMenu.path && history.push(titleMenu.path)}>
        <Space>
          {titleMenu.icon}
          {titleMenu.name}
        </Space>
      </h4>
      <ul className={styles.links}>
        {filterMenuChildren(links).map(
          (item: {
            key: any;
            path: string;
            name: boolean | ReactChild | ReactFragment | ReactPortal | null | undefined;
          }) => {
            const currentRouter = find(links.routes, { path: item.path });
            return (
              currentRouter?.name || item.name ? <>
              <div
                key={`menu-item-${item.name}-${item.key}`}
                className={styles.link}
                onClick={() => item.path && history.push(item.path)}
              >
                {currentRouter?.name || item.name}
              </div></> : null
            );
          },
        )}
      </ul>
    </div>
  );
};

const HeaderMenuDropdown = (props: HeaderMenuDropdownProps) => {
  const { menu, children } = props;
  const { menuData } = useContext(RouteContext);
  const currentMenu = useMemo(() => {
    const current = find(menuData, { path: menu.path }) || {};
    return current;
  }, [menuData, menu]);
  const isLinkMenu = useMemo(() => {
    return currentMenu.children
      ?.filter((item: { path: any; redirect: any }) => item.path && !item.redirect)
      .every((item: { children: string | any[] }) => {
        return !item.children || item.children.length !== 0;
      });
  }, [currentMenu]);

  const renderHeaderDropdownOverlay = useCallback(
    (data: MenuDataItem) => {
      if (isLinkMenu) {
        return  <div className={styles.menu} key={`menu-item-${data.name}-${data.key}`}>
          <div>
            {/*二级循环*/}
            {data?.children?.map(item => {
              if(!item?.hideInMenu && item?.name){
                return <div className={styles.menu1}>
                    <div
                      style={{cursor:"pointer",fontWeight: "bold"}}
                      key={`menu-item-${item.name}-${item.key}`}
                      onClick={() => item.path && history.push(item.path)}
                    >
                      {item?.icon}&nbsp;
                      {item?.name}
                    </div>

                    {/*三级循环*/}
                    {item?.children?.map(item1=> {
                      if(item1?.hideInMenu != true && item1?.name){
                        return <div className={styles.menu2}>
                          <div
                            style={{cursor:"pointer"}}
                            key={`menu-item-${item1.name}-${item1.key}`}
                            onClick={() => item1.path && history.push(item1.path)}
                          >{item1?.name}</div>
                        </div>
                      }
                    })}
                  </div>
              }
            })}
            {/*<MenuItemRender links={data} titleMenu={data} />*/}
          </div>
        </div>
      }
      return ;
    },
    [currentMenu],
  );

  return (
    <HeaderDropdown overlay={renderHeaderDropdownOverlay(currentMenu)}>{children}</HeaderDropdown>
  );
};

export default HeaderMenuDropdown;
