export type DailyDinner = {
  remark: string;
  status: string;
  "id": string,
  "creator": string,
  "creatorId": string,
  "name": string,
  "varietyId": string,
  "price": number,
  "canteenName": string,
  "quantity": number,
  "gmtCreate": number,
  "gmtModified": number,
}

export type DailyDinnerMenu = {
  "id": string,
  "img": string,
  "type": string,
  "price": number,
  "canteenName": string,
  "provisionTime": string,
  "remark": string,
  "name": string,
  "quantity": number,
  "gmtCreate": number,
  "gmtModified": number,
}





