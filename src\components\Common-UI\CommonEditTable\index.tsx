import type { ActionType } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Popconfirm, Row } from 'antd';
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import type { CommonEditTableProps } from './type';

const StyleWrapper = styled.div`
  .common-edit-table {
    .row-hide-table {
      td {
        border: unset;
      }
    }

    .common-table-actions-vertical {
      flex-direction: column;
      gap: 8px;
      align-items: center;

      a {
        padding: 2px 0;
      }
    }

    .common-table-actions-tile {
      gap: 8px;
      align-items: center;

      a {
        /* padding-top: 4px; */
        /* padding-right: 8px; */
      }
    }
  }
`;

function CommonEditTable<Data extends Record<string, any> = any>(props: CommonEditTableProps<Data>) {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);
  const actionRef = useRef<ActionType>();

  // 1) 提取属性
  const { rowKey, columns, actions, recordCreatorProps, tableRef, ..._props } = props;

  // 2) 设置列
  const _columns = useMemo(() => {
    if (actions && !columns.some((v) => v.valueType === 'option')) {
      const AlignMap = {
        start: 'left',
        end: 'right',
        center: 'center',
        'space-around': 'center',
        'space-between': 'center',
        'space-evenly': 'center',
      };
      columns.push({
        title: actions.title || '操作',
        width: actions.width || 150,
        fixed: actions.fixed || 'right',
        align: AlignMap[actions.align as string],
        valueType: 'option',
        render: (text, row, index, action) => {
          return (
            <Row justify={actions.align} className={`common-table-actions-${actions.layout || 'tile'}`}>
              {actions.items.map((item) => {
                const { show, name, title, color, onAction, disabled, render } = item;
                const _disabled = disabled && disabled(row);
                // 如果存在 title，渲染为气泡窗提示
                if (title) {
                  return show === undefined || show(row) ? (
                    <Popconfirm
                      title={title}
                      disabled={_disabled}
                      onConfirm={async () => {
                        if (onAction) {
                          // @ts-ignore
                          await onAction(row, action);
                        }
                      }}
                    >
                      <a style={_disabled ? { color: '#bbbbc1' } : {}}>{name}</a>
                    </Popconfirm>
                  ) : null;
                }
                // 如果存在 render, 自定义渲染
                if (render) {
                  // @ts-ignore
                  return show === undefined || show(row) ? render(row, index, action) : null;
                }
                // 无则正常判断返回
                return show === undefined || show(row) ? (
                  <a
                    key={name}
                    style={{ color: _disabled ? '#bbbbbb' : color }}
                    // @ts-ignore
                    onClick={() => !_disabled && onAction && onAction(row, action)}
                  >
                    {name}
                  </a>
                ) : null;
              })}
            </Row>
          );
        },
      });
    }
    return columns || [];
  }, [actions, columns]);

  // 3) 获取滚动值
  const windowH = document.documentElement.offsetHeight;
  const _scrollX = useMemo(() => columns.map((v) => v.width || 100).reduce((pre, next) => Number(pre) + Number(next)), [columns]);
  const _scroll = useMemo(() => {
    // 1 设置初始值
    const scrollVal: any = {};
    // 2 初始化
    if (props.scrollY) {
      // 数组
      if (Array.isArray(props.scrollY)) {
        scrollVal.y = windowH <= 800 ? props.scrollY[0] : props.scrollY[1];
      }
      // 数值，默认两倍
      else {
        scrollVal.y = props.scrollY;
      }
    }
    if (props.actions) scrollVal.x = props.scrollX || _scrollX;
    // 4 返回值
    return scrollVal;
  }, [_scrollX, props.actions, props.scrollX, props.scrollY, windowH]);

  // 4) 暴露方法
  useImperativeHandle(tableRef, () => ({
    actionRef: actionRef?.current,
    setEditableRowKeys,
  }));

  return (
    <StyleWrapper>
      <EditableProTable<Data>
        tableClassName="common-edit-table"
        rowKey={rowKey}
        scroll={_scroll}
        columns={_columns}
        loading={loading}
        actionRef={actionRef}
        recordCreatorProps={
          recordCreatorProps !== false
            ? {
                position: 'bottom',
                record: props.onCreate || (() => ({} as Data)),
                ...recordCreatorProps,
              }
            : false
        }
        request={
          props.onRequest
            ? async (...parmas) => {
                if (props.onRequest) {
                  setLoading(true);
                  const res = await props.onRequest(...parmas);
                  setLoading(false);
                  return res;
                }
                return {
                  data: [],
                  total: 0,
                };
              }
            : undefined
        }
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
          onSave: async (...params) => {
            if (props.onSave) {
              setLoading(true);
              await props.onSave(...params).finally(() => {
                setLoading(false);
              });
            }
          },
          onChange: setEditableRowKeys,
        }}
        {..._props}
      />
      {/* <ProCard title="表格数据" headerBordered collapsible defaultCollapsed>
        <ProFormField
          ignoreFormItem
          fieldProps={{
            style: {
              width: '100%',
            },
          }}
          mode="read"
          valueType="jsonCode"
          text={JSON.stringify(props.value)}
        />
      </ProCard> */}
    </StyleWrapper>
  );
}

export default CommonEditTable;
