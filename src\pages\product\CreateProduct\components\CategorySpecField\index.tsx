import { usePlatformCategorySpec } from '@/modules/product/application/categorySpec';
import useRequest from '@ahooksjs/use-request';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import { useEffect } from 'react';
import PlatformSpecField from './PlatformSpecField';

import UserCustomSpecField from './UserCustomSpecField';

export interface SpecItemData {
  specName: string;
  specId?: string;
  specValues: string[];
  required?: boolean;
}

export type SpuSpecValue = SpecItemData[];

export type CategorySpecFieldProps = {
  categoryId: string;
  value?: SpuSpecValue;
  onChange?: (value: SpuSpecValue) => void;
};

const CategorySpecField = (props: CategorySpecFieldProps) => {
  const { categoryId } = props;
  const [value, setValue] = useMergedState<SpuSpecValue>([], {
    value: props.value,
    onChange: props.onChange,
  });
  const categorySpecDomain = usePlatformCategorySpec();
  const { data: paramsList, run } = useRequest(async () => {
    const res = await categorySpecDomain.getSpecListByCategoryId(categoryId);
    if (res.body) {
      const body = res.body;
      return [...(body.current || []), ...(body.parent || [])];
    }
    return [];
  });

  useEffect(() => {
    if (categoryId) {
      run();
    }
  }, [categoryId]);

  // useEffect(() => {
  //   if (paramsList?.length) {
  //     const requiredParams = paramsList?.filter((item) => item.required);
  //     const newValue = [...value];
  //     requiredParams?.map((item) => {
  //       const currentValue = find(newValue, { specName: item.name });
  //       if (currentValue) {
  //         currentValue.required = item.required;
  //       } else {
  //         newValue.push({
  //           specName: item.name,
  //           specId: item.specId,
  //           specValues: [],
  //           required: item.required,
  //         });
  //       }
  //     });
  //     setValue([...newValue]);
  //   } else {
  //     setValue([]);
  //   }
  // }, [paramsList, categoryId]);

  if (!paramsList?.length) {
    return (
      <>
        <UserCustomSpecField {...props} />
      </>
    );
  }
  return (
    <PlatformSpecField
      categoryId={categoryId}
      dataSource={paramsList}
      value={value}
      onChange={setValue}
    />
  );
};

export default CategorySpecField;
