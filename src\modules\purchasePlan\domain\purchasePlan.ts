/**
 * 状态：0未处理，10部分生成，20全部生成 30 作废
 */

export enum PlanState {
  'WAIT' = '0',
  'PART_CREATED' = '10',
  'ALL_CREATED' = '20',
  'CANCEL' = '30',
  'REJECT' = '50',
}

export enum PlanStateText {
  'WAIT' = '待处理',
  'PART_CREATED' = '部分生成',
  'ALL_CREATED' = '全部生成',
  'CANCEL' = '作废',
  'REJECT' = '驳回',
}

export enum PlanGoodsStatus {
  'PENDING'='0',
  'WAIT' = '10',
  'ABOLISH' = '20',
  'PASS' = '30',
  'CREATED' = '40',
  'REJECT' = '50',
  'LEADER_AUDIT'="60",
}


export enum PlanGoodsStatusText {
  'PENDING'="待处理",
  'WAIT'="挂起",
  'ABOLISH'= "作废",
  'PASS'= "通过",
  'CREATED'= "已生成",
  'REJECT' = "驳回",
  'LEADER_AUDIT'="组长待审核",

}


export enum PlanGoodsText {
  '0'="待处理",
  '10'="挂起",
  '20'="作废",
  '30'= "通过",
  '40'= "已生成",
  '50' = "驳回",
}
