import type { PlanState } from "@/modules/purchasePlan/domain/purchasePlan";


export type PurchasePlan = {
  id: string,
  creatorName: string,
  warehouseName: string,
  skuCount: number,
  amount: number,
  remark: string,
  status: PlanState,
  gmtCreate: number
}


export type PurchasePlanGoods = {
  "goodsRemark": string,
  "id": string,
  "isUrgent": boolean,
  "purchasePlanId": string,
  "salesWarehouse": string,
  "purchasePrice": number,
  "purchaseQuantity": number,
  "purchaseRemark": string,
  "sku": string,
  "status": number,
  "supplierId": string,
  "supplierName": string,
  "totalPrice": number
}
/**
 * 各销售仓库需求表item
 */
export type SalesQuanityListItem = {

}

export type PurchasePlanLog = {
  "content": string,
  "gmtCreate": number,
  "operatorUsername": string,
  "purchasePlanId": string
}
