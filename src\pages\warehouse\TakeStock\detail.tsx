import React from 'react';
import {Alert, Card, Descriptions, Row, Spin, Tabs} from "antd";
import {useParams} from "umi";
import styles from "@/pages/PurchaseOrderList/styles.less";
import TabPane from "@ant-design/pro-card/es/components/TabPane";
import {useRequest} from "ahooks";
import {getOrderDetail} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import PurchaseOrderGoodsModal from "@/pages/warehouse/TakeStock/components/PurchaseOrderGoodsModal";
import PurchaseOrderLogModal from "@/pages/PurchaseOrderList/components/PurchaseOrderLogModal";
import {PlatformStatusEnum} from "@/modules/purchaseOrder/domain/purchaseOrder";
import moment from "moment";


const TableList: React.FC = () => {
  const {id: orderId} = useParams<{ id: string }>();
  //请求这个数据怎么覆盖  重新赋值 再调一次接口然后在回调里赋值不可以吗 赋给这个data
  const {data, loading, refresh} = useRequest(() => getOrderDetail(orderId).then((res) => res.body));

  const onChange = (key: string) => {
    console.log(key);
  };

  const platformStatus = PlatformStatusEnum[data?.platformStatus];

  //转换下单时间格式
  const platformOrderTime=data?.platformOrderTime==null?"":moment(data?.platformOrderTime as number).format("YYYY-MM-DD HH:mm:ss");
  return (
    <>
      <Spin spinning={loading}>
        <Card>
          <Descriptions column={4}>
            <Descriptions.Item label="标题">{data?.title}</Descriptions.Item>
            <Descriptions.Item label="采购单号">{data?.orderCode}</Descriptions.Item>
            <Descriptions.Item label="采购仓库">{data?.purchaseWarehouse}</Descriptions.Item>
            <Descriptions.Item label="采购员">{data?.purchaseUsername}</Descriptions.Item>
            <Descriptions.Item label="跟单员">{data?.merchandiserUsername}</Descriptions.Item>
            <Descriptions.Item label="平台单号">{data?.platformOrderCode}</Descriptions.Item>
            <Descriptions.Item label="采购渠道">{data?.platform}</Descriptions.Item>
            <Descriptions.Item label="采购账号">{data?.platformAccount}</Descriptions.Item>
            <Descriptions.Item label="平台订单状态">{platformStatus}</Descriptions.Item>
            <Descriptions.Item label="快递单号">{data?.trackingNumber}</Descriptions.Item>
            <Descriptions.Item label="平台订单总金额">{data?.platformOrderAmount}</Descriptions.Item>
            <Descriptions.Item label="平台运费">{data?.platformShippingFee}</Descriptions.Item>
            <Descriptions.Item label="实付运费">{data?.shippingFee}</Descriptions.Item>
            <Descriptions.Item label="下单时间">{platformOrderTime}</Descriptions.Item>
            <Descriptions.Item label="付款时间">{data?.platformPayTime}</Descriptions.Item>
          </Descriptions>
          <Row>
            <Descriptions column={4}>
              <Descriptions.Item label="留言">
                <Alert message={data?.memo} type="success" style={{width: 200, height: 100}}/>
              </Descriptions.Item>
              <Descriptions.Item label='备注'>
                <Alert message={data?.remark} type="success" style={{width: 200, height: 100}}/>
              </Descriptions.Item>
              {data?.exceptionMessage ? (
                <Descriptions.Item label='异常信息'>
                  <Alert message={data?.exceptionMessage} type="error" style={{width: 300}}/>
                </Descriptions.Item>
              ) : null}
            </Descriptions>
          </Row>
        </Card>
        <Card className={styles.card}>
          <Tabs onChange={onChange} type="card">
            <TabPane tab="商品信息" key="1">
              <>
                <PurchaseOrderGoodsModal orderId={orderId} supplierId={data?.supplierId}/>
              </>
            </TabPane>
            <TabPane tab="操作日志" key="6">
              <>
                <PurchaseOrderLogModal orderId={orderId}/>
              </>
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </>
  );
};

export default TableList;
