import {ProFormText} from "@ant-design/pro-form";
import {Form, Modal, ModalProps} from "antd";
import {useEffect, useState} from "react";
import {Vender} from "@/modules/supplier/domain/vender";

// 定义参数格式
export type CreateModalProps = {
  data: Vender,
  onFinish: (values: Vender) => void;
} & ModalProps;

const AddSupplierGoodsModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish,data,...rest } = props;

  useEffect(
    ()=>{
      form.setFieldsValue({
        ['venderName']: data?.venderName,
        ["venderId"]: data?.venderId,
        ["sku"]: null,
        ["purchasePrice"]: null,
      });

    },
    [data?.venderId]
  );

  return <Modal {...rest} title="导入采购计划" onOk={()=>form.submit()}>
    <Form form={form} onFinish={onFinish}>
      <ProFormText
        width="md"
        disabled
        name="venderName"
        label="供应商名称"
      />
      <ProFormText
        width="md"
        hidden
        name="venderId"
        label="供应商名称"
      />
      <ProFormText
        width="md"
        name="sku"
        label="商品sku"
      />
      <ProFormText
        width="md"
        name="purchasePrice"
        label="商品价格"
      />

    </Form>
  </Modal>
}

export default AddSupplierGoodsModal;
