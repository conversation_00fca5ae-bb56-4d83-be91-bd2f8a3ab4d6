import { BetaSchemaForm } from '@ant-design/pro-form';
import type { ActionType, ProTableProps, RequestData } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { FormInstance } from 'antd';
import { Button, Card, Form, Row, Space } from 'antd';
import type { SortOrder } from 'antd/es/table/interface';
import { isBoolean, isUndefined, omitBy } from 'lodash';
import type { ReactText } from 'react';
import { useMemo, useState } from 'react';
import { useEffect } from 'react';
import { createRef } from 'react';
import type { TableTabsProps } from '../TableTabs';
import styles from './styles.less';
import { useTableHooks } from './tableHooks';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';

export type ParamsType = Record<string, any>;
export type CustomTableProps<T, U extends ParamsType, ValueType = 'text'> = {
  tab?: TableTabsProps;
} & ProTableProps<T, U, ValueType>;

function CustomTable<T, Params extends ParamsType = ParamsType, ValueType = 'text'>(
  props: CustomTableProps<T, Params, ValueType>,
) {
  const {
    tab,
    search = false,
    pagination,
    actionRef: propsActionRef,
    toolBarRender: propsToolBarRender,
    tableAlertRender: propsTableAlertRender,
    request: propsRequest,
    rowSelection: propsRowSelection = false,
    ...rest
  } = props;
  let [searchForm] = Form.useForm();
  const ref = createRef<HTMLDivElement>();

  const [loading, setLoading] = useState<boolean>(false);
  const { rowSelection, selectedRows, selectedRowKeys, actionRef } = useTableHooks(
    propsRowSelection,
    propsActionRef,
  );
  if(search && search?.className == null){
    search.className=styles.inline_search_btn
  }

  useEffect(() => {
    if (search && (search as SearchConfig).form) {
      searchForm = (search as SearchConfig).form as FormInstance<any>;
    }
  }, [search]);

  const fetchData = useMemo(() => {
    if (!propsRequest) return undefined;
    return async (
      pageParams: Record<string, any>,
      proSort: Record<string, SortOrder>,
      proFilter: Record<string, ReactText[] | null>,
    ) => {
      const searchValues = omitBy(searchForm.getFieldsValue(), isUndefined);
      const actionParams = {
        ...(pageParams || {}),
        ...searchValues,
      };
      // eslint-disable-next-line no-underscore-dangle
      delete (actionParams as any)._timestamp;
      setLoading(true);
      try {
        const response = await propsRequest(actionParams as any, proSort, proFilter);
        setLoading(false);
        return response as RequestData<T>;
      } catch (error) {
        setLoading(false);
        return {} as RequestData<T>;
      }
    };
  }, [searchForm, propsRequest]);

  const searchRender = () => {
    return (
          <>
            <BetaSchemaForm
              onKeyDown={(e)=>{e.keyCode === 13?actionRef.current?.reload():null}}
              requiredMark={false}
              className={"customSearchFormArea"}
              style={{marginTop: -5}}
              form={searchForm}
              // size={"small"}
              // onFieldsChange={() => {
              //   actionRef.current?.reload();
              // }}
              layout="inline"
              layoutType="Form"
              submitter={{
                render: () => {
                  return null;
                },
              }}
              columns={[
                ...(props.columns?.map((item) => {
                  const { title, fieldProps = {} } = item;
                  return {
                    ...item,
                    title: fieldProps?.searchTitle,
                    fieldProps: {
                      ...fieldProps,
                      placeholder: fieldProps?.placeholder || title,
                    },
                    valueType: item.valueType === 'textarea' ? 'input' : item.valueType,
                    hideInForm: item.hideInSearch,
                  };
                }) as any),
              ]}
            />
        <Row justify="space-between" align="middle" style={{paddingBottom: 5}}>
          <div></div>
          <div>
            {toolBarRender()?.map(item=>{
              return <span style={{marginLeft: 5}}>{item}</span>
            })}
            <Space align="start" style={{marginLeft: 5}}>
              <Button
                onClick={() => {
                  searchForm.resetFields();
                  actionRef.current?.reload();
                }}
                size={"small"}
              >
                重置
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  actionRef.current?.reload();
                }}
                loading={loading}
                size={"small"}
              >
                查询
              </Button>
            </Space>
          </div>
        </Row>
</>
    );
  };

  const toolBarRender = () => {
    if (isBoolean(search)) {
      return propsToolBarRender
        ? propsToolBarRender(actionRef as unknown as ActionType, {
          selectedRowKeys,
          selectedRows: selectedRows || [],
        })
        : [];
    }
    return [];
  }

  // const tableAlertRender = () => {
  //   if (search) {
  //     return propsToolBarRender
  //       ? propsToolBarRender(actionRef as unknown as ActionType, {
  //         selectedRowKeys,
  //         selectedRows: selectedRows || [],
  //       })
  //       : [];
  //   }
  //   return [];
  // }

  return (
    <div ref={ref}>
      <Card bodyStyle={{marginBottom: -10}}>
        {isBoolean(search) ? searchRender() : null}
        <ProTable<T, Params, ValueType>
          style={{margin:-24, marginTop:0}}
          className={styles.customTable}
          defaultSize="small"
          // eslint-disable-next-line @typescript-eslint/no-shadow
          actionRef={(ref: ActionType) => {
            actionRef.current = ref;
            if (propsActionRef as React.MutableRefObject<ActionType | undefined>) {
              (propsActionRef as any).current = ref;
            }
          }}
          pagination={{
            pageSizeOptions: ['10', '20', '30', '50'],
            defaultPageSize: 50,
            ...(pagination ? pagination : {}),
          }}
          search={isBoolean(search)
            ? search
            : {
              layout: 'inline',
              filterType: 'query',
              labelWidth: 100,
              defaultCollapsed: false,
              split: true,
              ...(search ? search : {}),
            }}
          tableAlertRender={search ? propsTableAlertRender : false}
          options={false}
          request={fetchData}
          tableRender={(tableProps, defaultDom) => {
            if (tab) {
              return <TableTabs {...tab}>{defaultDom}</TableTabs>;
            }
            return defaultDom;
          }}
          toolBarRender={isBoolean(search) ? null : toolBarRender}
          bordered
          rowSelection={propsRowSelection ? rowSelection : propsRowSelection}
          {...rest}
        />
      </Card>
    </div>
  );
}

export default CustomTable;
