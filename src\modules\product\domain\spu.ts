import type { UploadImageResult } from '@/modules/common/infra/api/common';

export interface ImageListItem extends Partial<UploadImageResult> {
  externalUrl?: string;
  type?: 'IMAGE' | 'VIDEO';
  fileUrl?: string;
  fileId?: string;
}

export enum SpuDevelopState {
  'ABANDONED' = 'ABANDONED',
  'DEVELOPING' = 'DEVELOPING',
  'DEVELOP_FINISHED' = 'DEVELOP_FINISHED',
}

// SPU
export interface CustomParam {
  paramName: string;
  values: any[];
}

export interface StandardParam {
  paramName: string;
  values: any[];
}
export interface SkuMainSpecInfo {
  image: ImageListItem;
  spec: Spec;
}

export interface SkuI18ns {
  language: string;
  measuringUnit: string;
  skuMainSpecInfo: SkuMainSpecInfo;
  specs: Spec[];
}

export interface SpuSkuData {
  codeEAN: string;
  codeUPC: string;
  customCode: string;
  grossWeight: number;
  guarantyPeriod: number;
  mainSpecInfo?: MainSpecInfo;
  measuringUnit: string;
  netWeight: number;
  refProductLink: string;
  sizeHeight: number;
  sizeLength: number;
  sizeWidth: number;
  specs: Spec[];
  skuId?: string;
}

export interface SpuCategoryInfo {
  categoryId: string;
  customParams: CustomParam[];
  standardParams: StandardParam[];
}

export interface SpuI18ns {
  keywords: string[];
  language: string;
  originCountry: string;
  packingDesc: string;
  sellingPointDesc: string;
  spuCategoryInfo: SpuCategoryInfo;
  textDesc: string;
  title: string;
}

export interface SpuLogisticsAttr {
  isCharged: boolean;
  isContainLiquid: boolean;
  isContainSpecial: boolean;
}

export interface Video {
  externalUrl: string;
  fileId: string;
  fileUrl: string;
  type: string;
}

export interface CategoryPath {
  id: string;
  name: string;
}

export interface CustomParam {
  paramName: string;
  values: any[];
}

export interface StandardParam {
  paramName: string;
  values: any[];
}

export interface CategoryInfo {
  categoryId: string;
  categoryName: string;
  categoryPaths: CategoryPath[];
  customParams: CustomParam[];
  standardParams: StandardParam[];
}

export interface DetailImage {
  externalUrl: string;
  fileId: string;
  fileUrl: string;
  type: string;
}

export interface Keyword {
  keywords: any[];
  locale: string;
}

export interface LogisticsAttr {
  enDeclaredName: string;
  isCharged: boolean;
  isContainLiquid: boolean;
  isContainSpecial: boolean;
  zhDeclaredName: string;
}

export interface MainImage {
  externalUrl: string;
  fileId: string;
  fileUrl: string;
  type: string;
}

export interface PackingDesc {
  locale: string;
  packingDesc: string;
}

export interface SellingPointDesc {
  locale: string;
  sellingPointDesc: string;
}

export interface Image {
  externalUrl: string;
  fileId: string;
  fileUrl: string;
  type: string;
}

export interface MainSpecInfo {
  image: Image;
  spec: Spec;
}

export interface Sku {
  codeEAN: string;
  codeUPC: string;
  customCode: string;
  gmtCreate: number;
  gmtModified: number;
  grossWeight: number;
  guarantyPeriod: number;
  mainSpecInfo: MainSpecInfo;
  measuringUnit: string;
  netWeight: number;
  packingHeight: number;
  packingLength: number;
  packingWidth: number;
  refProductLink: string;
  sizeHeight: number;
  sizeLength: number;
  sizeWidth: number;
  skuId: string;
  specs: Spec[];
  spuId: string;
}

export interface TextDesc {
  locale: string;
  textDesc: string;
}

export interface Video {
  externalUrl: string;
  fileId: string;
  fileUrl: string;
  type: string;
}

export interface Spu {
  brandId: string;
  brandName: string;
  categoryInfo: CategoryInfo;
  customCode: string;
  detailImages: DetailImage[];
  keywords: Keyword[];
  locale: string;
  locales: any[];
  logisticsAttr: LogisticsAttr;
  mainImages: MainImage[];
  originCountry: string;
  packingDescs: PackingDesc[];
  sellingPointDescs: SellingPointDesc[];
  skus: Sku[];
  spuId: string;
  textDescs: TextDesc[];
  titles: Title[];
  videos: Video[];
}

export type SpuDetailInfo = {
  logisticsAttr: SpuLogisticsAttr;
} & Omit<Spu, 'spuLogisticsAttr'>;

// SPU LIST
export interface CategoryPath {
  id: string;
  name: string;
}

export interface RefProductLink {
  refLink: string;
  sourceSite: string;
}

export interface PurchaseSupplier {
  supplierId: string;
  supplierName: string;
  supplierSkuId: string;
}

export interface SkuSaleChannelInfo {
  channelId: string;
  channelName: string;
}

export interface SupplierInfo {
  supplierId: string;
  supplierName: string;
  supplierSkuId: string;
}

export interface SpuListSku {
  codeEAN: string;
  codeUPC: string;
  customCode: string;
  gmtCreate: number;
  gmtModified: number;
  grossWeight: number;
  guarantyPeriod: number;
  mainSpecInfo: MainSpecInfo;
  measuringUnit: string;
  netWeight: number;
  packingHeight: number;
  packingLength: number;
  packingWidth: number;
  purchaseSuppliers: PurchaseSupplier[];
  refProductLink: string;
  sizeHeight: number;
  sizeLength: number;
  sizeWidth: number;
  skuId: string;
  skuSaleChannelInfos: SkuSaleChannelInfo[];
  specs: Spec[];
  spuId: string;
  supplierInfos: SupplierInfo[];
}

export interface CategoryPath {
  id: string;
  name: string;
}

export interface RefProductLink {
  refLink: string;
  sourceSite: string;
}

export interface Spec {
  specName: string;
  specValue: string;
}

export interface MainSpecInfo {
  image: Image;
  spec: Spec;
}

export interface PurchaseSupplier {
  supplierId: string;
  supplierName: string;
  supplierSkuId: string;
}

export interface SkuSaleChannelInfo {
  channelId: string;
  channelName: string;
}
export interface SupplierInfo {
  supplierId: string;
  supplierName: string;
  supplierSkuId: string;
}

export interface Title {
  locale: string;
  title: string;
}

export interface SpuListItem {
  relation: string;
  productType: string;
  spu: string;
  title: string;
  salesStatus: string;
  unit: string;
  purchaseCycle: string;
  isCE: string;
  isCertificate: string;
  isTax: string;
  specialTags: string;
  purchaser: string;
  maintainer: string;
  designer: string;
  isCharged: boolean;
  isContainSpecial: boolean;
  isContainLiquid: boolean;
  zhDeclaredName: string;
  enDeclaredName: string;

  artDesigner: string;
  brandId: string;
  brandName: string;
  categoryId: string;
  categoryName: string;
  categoryPaths: CategoryPath[];
  creator: string;
  customCode: string;
  dataSource: string;
  dataSourceMethod: string;
  developState: string;
  developer: string;
  gmtCreate: number;
  gmtDevelopFinished: number;
  gmtModified: number;
  locale: string;
  locales: any[];
  mainImage: string;
  refProductLinks: RefProductLink[];
  skus: Sku[];
  specNum: number;
  spuId: string;
  tags: any[];
  titles: Title[];
}

export enum ProductTypeEnum  {
  'NORMAL' = '普通',
  'BIND' = '捆绑',
  'ASSEMBLE' = '组装'
};

export enum PackingMaterialEnum {
    'PLASTIC_BAG' = '塑料袋',
    'BUBBLE_BAG' = '泡泡袋',
    'NEUTRAL_BOX' = '中性纸盒',
    'COLOR_BOX' = '彩盒'
}
