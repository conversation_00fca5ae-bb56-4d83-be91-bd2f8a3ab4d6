import type { TableRowSelection } from 'antd/lib/table/interface';
import type { Key } from 'react';
import { useRef, useState, useCallback } from 'react';
import { useMountMergeState } from '@ant-design/pro-utils';
import type { ActionType } from '@ant-design/pro-table';

export function useTableHooks<T>(
  propsRowSelection:
    | false
    | (TableRowSelection<T> & {
        alwaysShowAlert?: boolean | undefined;
      })
    | undefined,
  propsActionRef?:
    | React.MutableRefObject<ActionType | undefined>
    | ((actionRef: ActionType) => void)
    | undefined,
) {
  const actionRef = useRef<ActionType>();
  const selectedRowsRef = useRef<T[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useMountMergeState<React.ReactText[]>([], {
    value: propsRowSelection ? propsRowSelection.selectedRowKeys : undefined,
  });
  const [selectedRows, setSelectedRows] = useState<T[]>([]);

  const setSelectedRowsAndKey = useCallback(
    (keys: React.ReactText[], rows: T[]) => {
      setSelectedRowKeys(keys);
      if (!propsRowSelection || !propsRowSelection?.selectedRowKeys) {
        selectedRowsRef.current = rows;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setSelectedRowKeys],
  );

  const rowSelection: TableRowSelection<T> = {
    selectedRowKeys,
    ...propsRowSelection,
    onChange: (keys: Key[], rows: T[]) => {
      if (propsRowSelection && propsRowSelection.onChange) {
        propsRowSelection.onChange(keys, rows);
      }
      setSelectedRows(rows);
      setSelectedRowsAndKey(keys, rows);
    },
  };

  if (propsActionRef) {
    // @ts-ignore
    propsActionRef.current = actionRef.current;
  }

  return {
    actionRef,
    rowSelection,
    setSelectedRows,
    selectedRows,
    selectedRowKeys,
  };
}
