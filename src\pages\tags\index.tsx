import CustomTable from '@/components/CustomTable';
import { useCategory } from '@/modules/tags/application/tags';
import type {
  CreateCategoryParams,
  UpdateCategoryParams,
} from '@/modules/tags/infra/api/tags';
import { DownOutlined, RedoOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import type { ProColumns } from '@ant-design/pro-table';
import { Button, Dropdown, Menu, Modal, Popconfirm, Space, Typography } from 'antd';
import { createRef } from 'react';
import { useTagsUi } from './application/tagsUI';
import type { CategoryAction } from './components/CategoryActions';
import { CategoryActions } from './components/CategoryActions';
import TreeSearch from './components/TreeSearch';
import './styles.less';
import {CategoryData} from "@/modules/tags/domain/tags";

export default () => {
  const categoryService = useCategory();
  const { refreshData, categoryTable, categoryTree, refreshTreeAndTable } = useTagsUi();
  const actions = createRef<CategoryAction>();
  const { treeData, loadDataById, loading } = categoryTree;
  const {
    expandedKeys,
    onSelected,
    onExpand,
    selectedCategory,
    currentCategory,
    setCurrentCategory,
  } = categoryTree;
  const { fetchSubCategory, selectedRowsState, setSelectedRows, rowSelection, actionRef } =
    categoryTable;

  const handleUpdate = async (values: UpdateCategoryParams) => {
    const params = {
      ...values,
      categoryId: currentCategory?.categoryId as string,
    };
    const success = await categoryService.update(params);
    if (success) {
      refreshData(params, 'update');
    }
    return success;
  };

  const handleBatchUpdate = async (values: UpdateCategoryParams) => {
    const ids = selectedRowsState.map((item) => item.categoryId);
    const params = {
      categoryIds: ids,
      moveTo: values.parentCategoryId || '0',
    };
    const success = await categoryService.batchUpdate(params);
    refreshData(ids, 'batchMove');
    return success;
  };

  const handleCreate = async (values: CreateCategoryParams) => {
    const params = {
      ...values,
      parentCategoryId: values.parentCategoryId || '0',
    };
    const success = await categoryService.create(params);
    if (success) {
      refreshData(params, 'create');
    }
    return success;
  };

  const handleDelete = async (ids: string[]) => {
    const success = await categoryService.remove(ids);
    if (success) {
      if (ids.length) {
        setSelectedRows([]);
      }
      refreshData(ids, 'delete');
    }
  };

  const handleMenuClick = (e: any) => {
    if (e.key === 'delete') {
      Modal.confirm({
        title: '确认删除已选择的标签?',
        onOk: () => {
          handleDelete(selectedRowsState.map((item) => item.categoryId));
        },
      });
    } else {
      actions.current?.updateAction?.batchUpdate?.(selectedCategory as any);
    }
  };

  const menu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="1">批量编辑</Menu.Item>
      <Menu.Item key="delete">批量删除</Menu.Item>
    </Menu>
  );

  const columns: ProColumns<CategoryData>[] = [
    {
      title: '上级标签',
      dataIndex: 'parentName',
    },
    {
      title: '标签名称',
      dataIndex: 'name',
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'title',
      valueType: 'option',
      width: 250,
      render: (v, record) => [
        <a
          key="update"
          onClick={() => {
            setCurrentCategory(record);
            actions.current?.updateAction?.update?.(record);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="delete"
          title="是否确认删除该标签?"
          onConfirm={() => handleDelete([record.categoryId])}
        >
          <a>删除</a>
        </Popconfirm>
      ],
    },
  ];

  const headerTitle = (
    <Space>
      <Button
        type="primary"
        onClick={() => {
          setCurrentCategory({
            ...selectedCategory,
            name: '',
          } as any);
          actions.current?.createAction?.(
            ({
              ...selectedCategory,
              name: '',
            } as any) || undefined,
          );
        }}
      >
        添加标签
      </Button>
      <Dropdown overlay={menu} disabled={!selectedRowsState.length}>
        <Button>
          批量操作 <DownOutlined />
        </Button>
      </Dropdown>
      {selectedRowsState.length ? (
        <Typography.Text disabled type="secondary" style={{ fontSize: 14, fontWeight: 'normal' }}>
          已选 <a>{selectedRowsState.length}</a> 条数据
        </Typography.Text>
      ) : null}
    </Space>
  );

  return (
    <>
      <ProCard
        title={headerTitle}
        extra={
          <Space>
            <Button icon={<RedoOutlined />} onClick={refreshTreeAndTable} />
          </Space>
        }
        split="vertical"
        bordered
        headerBordered
        className="categoryPanel"
      >
        <ProCard className="categoryPanel-tree" colSpan="250px" loading={loading}>
          <TreeSearch
            treeData={treeData}
            loadData={loadDataById}
            onSelect={onSelected}
            expandedKeys={expandedKeys}
            onExpand={onExpand}
          />
        </ProCard>
        <ProCard className="categoryPanel-tree" bodyStyle={{ padding: 0 }}>
          <CustomTable
            actionRef={actionRef}
            style={{ width: '100%' }}
            className="category-table"
            search={false}
            request={fetchSubCategory}
            columns={columns}
            pagination={{
              pageSize: 30,
            }}
            rowKey="categoryId"
            rowSelection={rowSelection}
          />
        </ProCard>
      </ProCard>
      {/* 标签操作 */}
      <CategoryActions
        ref={actions}
        create={handleCreate}
        update={handleUpdate}
        batchUpdate={handleBatchUpdate}
      />
    </>
  );
};
