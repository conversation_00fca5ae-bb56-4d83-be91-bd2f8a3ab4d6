import {message} from "antd";

/**
 * 复制
 * @param value
 */
export const copyText = (value: any) => {
  const oInput = document.createElement('input');
  oInput.value = value;
  document.body.appendChild(oInput);
  oInput.select(); // 选择对象
  const tag = document.execCommand("Copy");
  if (tag) {
    message.success("复制成功!")
  }
  oInput.remove();
}

/**
 * 公共导出方法
 * @param res
 * @param title
 */
export const commonExport = (res: any, title: string) => {
  if(!res?.status){
    const link=document.createElement("a");
    let blob = new Blob([res],{type:'application/vnd.ms-excel'})
    link.style.display = 'none';
    link.href=URL.createObjectURL(blob);
    link.download= title;
    document.body.appendChild(link);
    link.click();
  }
}


