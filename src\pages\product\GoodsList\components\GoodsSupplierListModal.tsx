import aliwangwang from "@/assets/images/aliwangwang.gif";
import Permission from "@/components/Permission";
import SelectVender from '@/components/Select/SelectVender';
import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import { queryGoodsSupplier, queryGoodsSupplierParams } from "@/modules/goods/infra/api/goods";
import { SupplierGoods } from "@/modules/supplier/domain/vender";
import BindSpuSupplierModal from "@/pages/product/CreateProduct/components/BindSpuSupplierModal";
import type { PurchasePlan } from '@/pages/PurchasePlanList/data';
import { ProForm, ProFormItem, ProFormSelect, ProFormText, ProTable } from "@ant-design/pro-components";
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { createSupplierGoods, removeSupplierGoods, updateSupplierGoods } from "@/modules/goods/infra/api/goods";
import type { ModalProps } from 'antd';
import { Button, Form, Modal, notification,  Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Access } from "umi";

// 定义参数格式
export type GoodsSupplierModalProps = {
  goodsSupplierParam: queryGoodsSupplierParams;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const GoodsSupplierListModal = (props: GoodsSupplierModalProps) => {
  const { onFinish, goodsSupplierParam, ...rest } = props;
  const actionRef = useRef<ActionType>();
  const [bindSupplierModal, setBindSupplierModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<SupplierGoods>();
  const [form] = Form.useForm();

  const fetchList = usePageQueryRequest((params) => {
    return queryGoodsSupplier({
      ...params,
      goodsSku: goodsSupplierParam.goodsSku
    });
  });

  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [goodsSupplierParam]);

  const editSupplierGoods = (record: any) => {
    Modal.confirm({
      icon: false,
      width: 500,
      centered: true,
      content: (
        <Form labelCol={{ flex: '120px' }} form={form}>
          <ProForm.Group>
            <ProFormItem label="供应商" name="supplierName" initialValue={record?.supplierName} rules={[{ required: true, message: '请输入供应商' }]} >
              <SelectVender disabled={record?.supplierName} useVenderName style={{ minWidth: 300 }} />
            </ProFormItem>
            <ProFormText width={300} label="SKU" name="goodsSku" disabled={record?.goodsSku} initialValue={record?.goodsSku} rules={[{ required: true, message: '请输入SKU' }]} />
            {/* <ProFormText width={300} label="上次采购价" name="lastPurchasePrice" initialValue={record?.lastPurchasePrice} /> */}
            <ProFormText width={300} label="采购价格" name="purchasePrice" initialValue={record?.purchasePrice} rules={[{ required: true, message: '请输入采购价格' }]} />
            <ProFormText width={300} label="最小采购量" name="minPurchaseQuantity" initialValue={record?.minPurchaseQuantity} />
            <ProFormText width={300} label="备货周期" name="purchaseCycle" initialValue={record?.purchaseCycle} />
            <ProFormSelect
              width={300}
              name="currency"
              label="币种"
              initialValue={record?.currency}
              options={[
                { label: 'CNY', value: "CNY" },
                { label: 'USD', value: "USD" }
              ]}
            />
            <ProFormSelect
              width={300}
              name="isDefault"
              label="是否首选"
              initialValue={record?.isDefault?.toString() || '0'}
              options={[
                { label: '否', value: "0" },
                { label: '是', value: "1" }
              ]}
              allowClear={false}
            />
            <ProFormText hidden={true} name="supplierGoodsId" initialValue={record?.id} />
          </ProForm.Group>
        </Form>
      ),
      onOk: function () {
        const params = form.getFieldsValue();
        if (record?.id) {
          updateSupplierGoods(params).then((result) => {
            if (result.status.success) {
              notification.success({ message: '修改成功' });
              actionRef.current?.reloadAndRest?.()
            }
          });
        } else {
          createSupplierGoods(params).then((result) => {
            if (result.status.success) {
              notification.success({ message: '修改成功' });
              actionRef.current?.reloadAndRest?.()
            }
          });
        }

      },
    });
    form.resetFields();
  }

  const columns: ProColumns<SupplierGoods>[] = [
    {
      title: '供应商',
      width: 300,
      align: 'left',
      dataIndex: 'supplierName',
      render: (_, record) => {
        return <>
          {record?.aliWangWangLink ? <><a href={record?.aliWangWangLink} target={"_blank"} rel="noreferrer"><img src={aliwangwang} /></a></> : null}&nbsp;&nbsp;
          <span style={{ fontSize: 12 }}>{record?.supplierName}</span>
        </>;
      },
    },
    {
      title: '1688链接',
      width: 300,
      align: 'left',
      dataIndex: 'supplierName',
      render: (_, record) => {
        return <>
          {record?.aliLink ? <a href={record?.aliLink} target={"_blank"} rel="noreferrer"><span style={{ fontSize: 13 }}>{record?.goodsSku}</span></a> : <span style={{ fontSize: 13 }}>{record?.goodsSku}</span>}
        </>;
      },
    },
    {
      title: '采购价',
      align: 'center',
      dataIndex: 'purchasePrice',
    },
    // {
    //   title: '上次采购价',
    //   align: 'center',
    //   dataIndex: 'lastPurchasePrice',
    // },
    {
      title: '最小采购量',
      align: 'center',
      dataIndex: 'minPurchaseQuantity',
    },
    {
      title: '交期',
      align: 'center',
      dataIndex: 'purchaseCycle',
    },
    {
      title: '是否首选',
      align: 'center',
      dataIndex: 'isDefault',
      render: (_, record) => {
        return record.isDefault == "1" ? "首选" : '-';
      },
    },
    // {
    //   title: '时间',
    //   align: 'center',
    //   dataIndex: 'gmtCreate',
    //   valueType: 'dateTime',
    // },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'option',
      render: (_, record) => {
        return <Space>
          <Permission permissionKey={"purchase:product_manager:sku:supplierEdit"}>
            <a onClick={() => editSupplierGoods(record)}>
              编辑
            </a>
          </Permission>
          <Permission permissionKey={"purchase:product_manager:sku:bindGoodsSupplier"}>
            <a onClick={() => {
              setCurrentRow(record); setBindSupplierModal(true)
            }}>
              绑定供应商
            </a>
          </Permission>
        </Space>;
      },
    },
  ];

  return <>
    <Modal {...rest} title="供应商列表" closable={false} width="80%" onOk={onFinish}>
      <ProTable<any>
        search={false}
        options={false}
        size={"small"}
        actionRef={actionRef}
        request={fetchList}
        columns={columns}
        revalidateOnFocus={false}
        rowKey="id"
        bordered
      />
    </Modal>
    <Access accessible={bindSupplierModal}>
      <BindSpuSupplierModal visible={bindSupplierModal} spuId={goodsSupplierParam?.spuId} supplierId={currentRow?.supplierId} onCancel={() => setBindSupplierModal(false)} onFinish={() => setBindSupplierModal(false)} />
    </Access>
  </>
};

export default GoodsSupplierListModal;
