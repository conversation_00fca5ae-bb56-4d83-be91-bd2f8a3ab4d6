import React, { useEffect, useState } from 'react';
import { Modal, ModalProps, Col, Row, notification, Form, message } from 'antd';
import { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import ProForm, { ProFormText } from "@ant-design/pro-form";
import { ProFormSelect, RequestOptionsType } from "@ant-design/pro-components";
import { getOrderDetail, updateOrder } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import { loadAliAccountList } from "@/modules/setting/infra/api/AlibabaAccount";
import { getWarehouse } from "@/modules/purchasePlan/infra/api/purchasePlan";
import SelectVender from '@/components/Select/SelectVender';
// 定义参数格式
export type editOrderModal = {
  onFinish: () => void;
  orderId: string;
} & ModalProps;

export default (props: editOrderModal) => {
  const [form] = ProForm.useForm();
  const { onFinish, orderId, ...rest } = props;
  const [orderData, setOrderData] = useState<PurchaseOrder>();

  useEffect(
    () => {
      if (orderId) {
        getOrderDetail(orderId).then((res) => {
          if (res.status.success) {
            setOrderData(res.body);
            form.setFieldsValue(res.body);
          }
        })
      }
    },
    [orderId]
  );

  const onSubmit = () => {
    const params = form.getFieldsValue();
    updateOrder(params).then((result) => {
      if (result.status.success) {
        message.success('修改成功');
        onFinish();
      }
    });
  }

  return (
    <>
      <Modal {...rest} title="采购单编辑" width={"60%"} className={'globalEnterKeySubmit'} onOk={() => onSubmit()} destroyOnClose={true}>
        <Form labelCol={{ flex: '80px' }} form={form}>
          <Row>
            <Col style={{ padding: "0 10px" }}>
              {/*<ProFormText width={230} label="标题" name="title" initialValue={orderData?.title}/>*/}
              <ProFormText name="id" hidden={true} initialValue={orderData?.id} />
              <ProFormSelect
                width={230} label="账号" name="platformAccount"
                initialValue={orderData?.platformAccount}
                request={async () => {
                  const res = await loadAliAccountList();
                  const v: RequestOptionsType[] | PromiseLike<RequestOptionsType[]> | { label: any; value: any; }[] = [];
                  res.body?.map((item: any) => {
                    v.push({ label: item?.accountName, value: item?.accountName });
                  })
                  return v;
                }}
              />
              <ProFormText width={230} label="平台总金额" name="platformOrderAmount" disabled={orderData?.platform == "1688"}
                initialValue={orderData?.platformOrderAmount} />
              <ProFormText width={230} label="收货人" name="platformFullName" disabled={orderData?.platform == "1688"}
                initialValue={orderData?.platformFullName} />
              <ProFormText width={230} label="快递单号" name="trackingNumber" disabled={orderData?.platform == "1688"} initialValue={orderData?.trackingNumber} />
              <ProFormSelect
                width={230}
                name="isManual"
                label="手工单"
                initialValue={orderData?.isManual == 0 ? 0 : 1}
                options={[
                  { label: '否', value: 0 },
                  { label: '是', value: 1 }
                ]}
              />
            </Col>
            <Col style={{ padding: "0 10px" }}>
              <ProFormSelect
                width={230} label="渠道" name="platform"
                initialValue={orderData?.platform}
                valueEnum={{
                  '1688': '1688',
                  'taobao': '淘宝',
                  'pdd': '拼多多',
                  'jd': '京东',
                  'other': '其他'
                }}
              />
              <ProFormSelect
                width={230}
                name="purchaseWarehouse"
                label="仓库"
                request={async () => {//返回的select网络请求
                  let params = await getWarehouse();
                  let res = [];
                  var body = params.body;
                  for (var i in body) {
                    let temp = {};
                    temp['label'] = body[i];
                    temp['value'] = body[i];
                    res.push(temp)
                  }
                  return res;
                }}
              />
              <ProFormText width={230} label="平台运费" name="platformShippingFee" disabled={orderData?.platform == "1688"}
                initialValue={orderData?.platformShippingFee} />
              <ProFormText width={230} label="收货电话" name="platformMobile" disabled={orderData?.platform == "1688"}
                initialValue={orderData?.platformMobile} />
              <ProFormSelect
                width={230}
                name="isUrgent"
                label="加急"
                initialValue={orderData?.isUrgent == 0 ? 0 : 1}
                options={[
                  { label: '否', value: 0 },
                  { label: '是', value: 1 }
                ]}
              />
            </Col>
            <Col style={{ padding: "0 10px" }}>
              {/* <ProForm width={230} label="供应商" name="supplierName" initialValue={orderData?.supplierName} /> */}
              <ProForm.Item label='供应商' name='supplierName' initialValue={orderData?.supplierName} >
                <SelectVender useVenderName />
              </ProForm.Item>
              <ProFormSelect
                width={230} label="交易方式" name="platformTradeType"
                initialValue={orderData?.platformTradeType}
                disabled={orderData?.platform == "1688"}
                valueEnum={{
                  'fxassure': '担保交易(fxassure)',
                  'assureTrade': '担保交易(assureTrade)',
                  'period': '供应商账期',
                  'credit': '诚意赊'
                }}
              />
              <ProFormText width={230} label="ERP运费" name="shippingFee" disabled={orderData?.platform == "1688"}
                initialValue={orderData?.shippingFee} />
              <ProFormText width={230} label="收货地址" name="platformAddress" disabled={orderData?.platform == "1688"}
                initialValue={orderData?.platformAddress} />
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};
