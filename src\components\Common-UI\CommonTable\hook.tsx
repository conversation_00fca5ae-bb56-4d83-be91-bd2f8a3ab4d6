import type { ProColumns } from '@ant-design/pro-table';
import { Image } from 'antd';
import React, { useCallback } from 'react';
import type { UseColumnProps, UseSearchConfig } from './type';

const getValue = (dataIndex: string | string[], record: any) => {
  let value = '';
  let row = record;
  if (Array.isArray(dataIndex)) {
    dataIndex.forEach((key) => {
      row = row[key];
      value = row;
    });
  } else {
    value = record[dataIndex];
  }
  return value;
};

export function useColumn<T = any>(config: ProColumns<T> = {}) {
  return useCallback(
    (title: string | React.ReactNode, dataIndex: string | string[], rest: UseColumnProps<T> = {}) => {
      const { searchLabel, formItemProps = {}, renderFormItem } = rest;
      const target = {
        title,
        dataIndex,
        ellipsis: true,
        hideInSearch: renderFormItem ? false : true,
        ...config,
        ...rest,
        formItemProps: {
          ...formItemProps,
          name: rest.searchProp || dataIndex,
          label: title,
        },
        renderFormItem,
      };

      // searchLabel 为在搜索栏时显示
      if (searchLabel) {
        target.hideInSearch = false;
        target.formItemProps.label = searchLabel;
      }

      // wrap 存在判断显示是否换行
      if (rest.wrap) {
        const renderFn = target.render;
        target.render = (_, record, index, action, schema) => {
          return <div style={{ whiteSpace: 'pre-wrap' }}>{renderFn ? renderFn(_, record, index, action, schema) : getValue(dataIndex, record) || '-'}</div>;
        };
      }

      // format 存在转换值
      if (rest.format) {
        target.render = (_, record, index, action, schema) => {
          const val = getValue(dataIndex, record)
          return rest.format && val ? rest.format(val) : '-';
        };
      }

      // isImage 存在转换为图片显示
      if (rest.isImage) {
        target.render = (_, record, index, action, schema) => {
          return (
            <Image
              src={getValue(dataIndex, record) || 'https://static.elephantpal.com/common/mall/static/load-img-icon.png'}
              width={rest.imgW || 100}
              height={rest.imgH || 100}
            />
          );
        };
      }

      // 默认无 label，将内容放到 placeholder 中进行展示

      return target as ProColumns<T>;
    },
    [config],
  );
}
export function useSearch<T = any>(config: UseSearchConfig<T> = {}) {
  const { isUnLabel } = config;

  return useCallback(
    (title: string, dataIndex: string, rest: ProColumns<T> & { placeholder?: string } = {}) => {
      const { placeholder, fieldProps = {}, valueType, ...other } = rest;
      let _placeholder: any = isUnLabel ? title : placeholder;
      if (valueType === 'dateRange') {
        _placeholder = isUnLabel ? [_placeholder, _placeholder] : placeholder;
      }
      const target = {
        title: isUnLabel ? undefined : title,
        dataIndex,
        hideInSearch: false,
        hideInTable: true,
        valueType,
        fieldProps: {
          placeholder: _placeholder,
          ...fieldProps,
        },
        ...config,
        ...other,
      };

      // target.fieldProps.placeholder = placeholder || `请输入${title}`;
      // delete target.title;

      return target as ProColumns<T>;
    },
    [config],
  );
}
