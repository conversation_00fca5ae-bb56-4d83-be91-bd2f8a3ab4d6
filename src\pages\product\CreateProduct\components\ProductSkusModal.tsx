import type {ProColumns} from '@ant-design/pro-table';
import React, {useState} from 'react';
import {useRequest} from "ahooks";
import {
  Button,
  Card,
  Spin,
  Table,
  Image,
  Modal,
  Form,
  notification,
  Space,
} from "antd";
import SelfImage from "@/components/SelfImage";
import {querySkuListBySpuId, removeSupplierGoods} from "@/modules/goods/infra/api/goods";
import SkuEditModal from "@/pages/product/CreateProduct/components/SkuEditModal";
import {Goods, GoodsStatusEnum, GoodsStatusTagsEnum} from "@/modules/goods/domain/goods";
import {ProductTypeEnum, PackingMaterialEnum} from "@/modules/product/domain/spu";
import {Access} from "umi";
import Permission from "@/components/Permission";
import {OrderStatusTagsEnum} from "@/modules/purchaseOrder/domain/purchaseOrder";
export type  productSupplierModalParams = {
  spuId: string,
  skuId: string
};
export const ProductSkusModal =  (props: productSupplierModalParams) =>  {
  const { spuId, skuId } = props;
  const { data, refresh, loading } = useRequest(() => querySkuListBySpuId(spuId).then((res) => res?.body));
  const [skuEditModal, setSkuEditModal] = useState<boolean>(false);
  const [skuEditParams, setSkuEditParams] = useState<Goods>();

  const [form] = Form.useForm();

  const removeSku = (record: Goods)=>{
    Modal.confirm({
      content: "确定移除SKU吗？",
      onOk: function () {
        removeSupplierGoods(record?.id).then((result) => {
          if (result.status.success) {
            notification.success({message: '删除成功'});
            refresh();
          }
        });
      },
    });
    form.resetFields();
  }

  const editSku = (record: Goods)=>{
    setSkuEditModal(true)
    setSkuEditParams(record)
  }


  const columns: ProColumns<Goods>[] = [
    {
      title: "图片",
      dataIndex: 'skuImg',
      align: "center",
      width: 100,
      render: (v, record) => {
        return <SelfImage src={record?.mainSpecInfo?.image?.fileUrl} title={record?.skuName} width={80} />
      }
    },
    {
      title: 'SKU',
      key: 'sku',
      dataIndex: 'sku',
      align: 'center',
      width: 150,
    },
    {
      title: '名称',
      key: 'skuName',
      dataIndex: 'skuName',
      align: 'left',
      width: 250,
    },
    {
      title: '属性',
      dataIndex: 'skuAttr',
      align: 'center',
      width: 150,
      render: (v, record) => {
        return <div>{record?.skuAttrName} : {record?.skuAttrValue}</div>
      }
    },
    {
      title: '状态',
      dataIndex: 'salesStatus',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return GoodsStatusEnum[record?.salesStatus];
      },
    },
    {
      title: '商品类型',
      dataIndex: 'productType',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return ProductTypeEnum[record?.productType];
      },
    },
    {
      title: '包装材料',
      dataIndex: 'packingMaterial',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return PackingMaterialEnum[record?.packingMaterial];
      },
    },
    {
      title: '开发价',
      dataIndex: 'referenceCost',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return record?.referenceCost?.toFixed(2) || "--";
      },
    },
    {
      title: '首次采购价',
      dataIndex: 'firstPurchasePrice',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return <div>{record?.firstPurchasePrice?.toFixed(2) || "--"}</div>;
      },
    },
    {
      title: '上次采购价',
      dataIndex: 'lastPurchasePrice',
      align: 'center',
      width: 100,
      render: (_, record, index) => {
        return record?.lastPurchasePrice?.toFixed(2) || "--";
      },
    },
    {
      title: '商品毛重/净重(g)',
      dataIndex: 'weight',
      align: 'center',
      width: 120,
      render: (_, record, index) => {
        return (
          <Space key={index} >
            {record?.grossWeight} / {record?.netWeight}
          </Space>
        );
      },
    },
    {
      title: '商品尺寸(cm)',
      dataIndex: '',
      align: 'center',
      width: 150,
      editable: false,
      render: (_, record, index) => {
        return (
          <Space key={index} >
            {record?.sizeLength}/
            {record?.sizeWidth}/
            {record?.sizeHeight}
          </Space>
        );
      },
    },
    {
      title: "操作",
      dataIndex: 'gmtCreate',
      align: "center",
      render: (v, record) => {
        return (<>
          <Permission permissionKey={"purchase:product_manager:sku:skuDelete"}>
            <Button type="primary" size={"small"} danger ghost style={{fontSize:12,borderRadius:5}} onClick={() =>  removeSku(record)}>
              删除
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:product_manager:sku:skuEdit"}>
            <Button type="primary" size={"small"} ghost  style={{fontSize:12,borderRadius:5,margin: "0 5px"}} onClick={() =>  editSku(record)}>
              编辑
            </Button>
          </Permission>
          </>)
      }
    }
  ];
  return (
    <>
      <Spin spinning={loading} style={{paddingTop:0}}>
        <Card style={{border:"none",marginBottom:10}} bodyStyle={{padding: 0}}>
          <Permission permissionKey={"purchase:product_manager:sku:skuEdit"}>
            <Button
                size={"small"}
                type="primary"
                style={{marginRight:20,fontSize: 13,borderRadius:"5px"}}
                key="bundledAlibaba"
                onClick={()=>editSku({spuId: spuId})}
              >
                添加
            </Button>
          </Permission>
        </Card>
        <Card bodyStyle={{padding: 0}}>
          <Table
            dataSource={data}
            columns={columns}
            rowKey="id"
            size={"small"}
            pagination = {false}
          />
        </Card>
      </Spin>
      <Access accessible={true}>
        <SkuEditModal visible={skuEditModal} goodsData={skuEditParams} onFinish={() => {
          setSkuEditModal(false);
          refresh();
        }} />
      </Access>
    </>
  );
};

export default (props: productSupplierModalParams) => {
  const { spuId, skuId } = props;
  return (
    <>
      <ProductSkusModal spuId={spuId} skuId={skuId}/>
    </>
  );
};

