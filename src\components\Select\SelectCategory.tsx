import { Select } from 'antd';
import { debounce } from 'lodash';
import { memo, useEffect, useState } from 'react';
import { queryCategoryList } from './api';

const SelectCategory = memo((props: Record<keyof any, any>) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);
  const [origin, setOrigin] = useState<any[]>([]);

  const onQuery = debounce((categoryName?: string) => {
    queryCategoryList({ categoryName, pageCondition: { pageNum: 1, pageSize: 50 } }).then((res) => {
      const { body } = res;
      setOrigin(body?.items || [])
      setSelectOption(body?.items?.map((v) => ({ value: v?.categoryId, label:  `${v?.allCategoryName}` })));
    });
  }, 500);

  useEffect(() => {
    if (props.value && selectOption.length > 0) return
    queryCategoryList({ categoryId: props.value, pageCondition: { pageNum: 1, pageSize: 50 } }).then((res) => {
      const { body } = res;
      setOrigin(body?.items || []);
      setSelectOption(body?.items?.map((v) => ({ value: v?.categoryId, label:  `${v?.allCategoryName}` })));
    });
  }, [props.value])

  const handleSearch = (newValue: string) => {
    if (newValue) {
      onQuery(newValue)
    } else {
      setSelectOption([]);
      onQuery();
    }
  };

  const onChange = (...args: any[]) => {
    props.onChange(...args)
    const [id] = args
    if (id) {
      const curItem = origin.find(val => id === val.categoryId)
      props.onChoose && props.onChoose(curItem)
    }
  }

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择商品分类"
      optionFilterProp="children"
      onSearch={handleSearch}
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
      onChange={onChange}
    />
  );
});

export default SelectCategory;
