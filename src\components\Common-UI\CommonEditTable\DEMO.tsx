import CommonEditTable from '@/components/Common-UI/CommonEditTable';
import { memo, useCallback, useMemo, useState } from 'react';
import type { CommonEditTableProps } from '@/components/Common-UI/CommonEditTable/type';
import { useEditColumn } from '@/components/Common-UI/CommonEditTable/hook';

type TableProps = CommonEditTableProps<any>;

const PackageInfo = memo(() => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const setColumn = useEditColumn<any>({ align: 'center' });

  // 1) columns
  const columns: TableProps['columns'] = useMemo(() => {
    return [
      setColumn('包裹号', 'a', { readonly: true }),
      setColumn('包裹重量(g)', 'b'),
      setColumn('包裹长(cm)', 'c'),
      setColumn('包裹宽(cm)', 'd'),
      setColumn('包裹宽(cm)', 'e'),
      setColumn('包裹高(cm)', 'a'),
      setColumn('商品编码及数量', 'f', {
        readonly: true,
        render: (dom, row) => <a className="nowarp-txt">{row?.f?.join(',')}</a>,
        renderFormItem: (_, { record }) => <div className="nowarp-txt">{record?.f?.join(',') || '-'}</div>,
      }),
      setColumn('跟踪号', 'g', {
        readonly: true,
        render: (dom, row) => <a>{row.g}</a>,
      }),
    ];
  }, [setColumn]);

  // 2) actions
  const actions: TableProps['actions'] = useMemo(() => {
    return {
      align: 'center',
      items: [
        {
          name: '编辑',
          onAction: (row, action) => {
            console.log(row.id, action);

            action?.startEditable?.(row.id);
          },
        },
        {
          name: '删除',
          onAction: (row) => {
            setDataSource(dataSource.filter((item) => item.id !== row.id));
          },
        },
      ],
    };
  }, [dataSource]);

  // 3) 默认值
  const onRequest: TableProps['request'] = async () => {
    return {
      data: [
        { id: 12132, a: 1, b: 2, c: 3, d: 4, e: 5, f: [66666, 66666, 66666, 66666, 66666, 66666], g: 77777777 },
        { id: 34444, a: 2, b: 2, c: 3, d: 4, e: 5, f: [66666], g: 77777777 },
      ],
      total: 0,
    };
  };

  // 4) 新建一行
  const onCreate = useCallback<NonNullable<TableProps['onCreate']>>((index: number) => {
    return {
      id: (Math.random() * 1000000)?.toFixed(0),
      a: index + 1,
    };
  }, []);

  // 5) 新建保存
  const onSave = useCallback<NonNullable<TableProps['onSave']>>(async (rowKey, row) => {
    console.log('onSave', row);
  }, []);

  return (
    <CommonEditTable
      rowKey="id"
      columns={columns}
      actions={actions}
      value={dataSource}
      onChange={setDataSource}
      onRequest={onRequest}
      onCreate={onCreate}
      onSave={onSave}
    />
  );
});

export default PackageInfo;
