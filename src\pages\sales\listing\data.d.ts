export type AsinkingMskuListing = {
  "afnFulfillableQuantity": string,
  "afnInboundReceivingQuantity": number,
  "afnInboundShippedQuantity": number,
  "afnInboundWorkingQuantity": number,
  "afnUnsellableQuantity": number,
  "asin": string,
  "createDate": string,
  "currencyCode": string,
  "fnsku": string,
  "fulfillmentChannelType": string,
  "id": string,
  "isDelete": number,
  "itemName": string,
  "jsonData": string,
  "landedPrice": number,
  "lastStar": string,
  "listingId": string,
  "listingPrice": number,
  "listingUpdateDate": string,
  "localName": string,
  "localSku": string,
  "openDate": string,
  "parentAsin": string,
  "points": string,
  "price": number,
  "quantity": number,
  "reservedCustomerorders": number,
  "reservedFcProcessing": number,
  "reservedFcTransfers": number,
  "reviewNum": number,
  "sellerCategory": string,
  "sellerRank": number,
  "sellerSku": string,
  "shipping": number,
  "smallImageUrl": string,
  "status": number,
}
