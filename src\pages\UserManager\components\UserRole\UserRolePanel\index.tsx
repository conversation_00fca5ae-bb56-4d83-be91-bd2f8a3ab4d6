
import { useRole, useUserRoleList } from '@/modules/user-center/application/role';
import { Role } from '@/modules/user-center/domain/user';
import UserManager, { UserManagerContext } from '@/pages/UserManager';
import { MoreOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import TabPane from '@ant-design/pro-card/lib/components/TabPane';
import {
  Button,
  Dropdown,
  Input,
  List,
  Menu,
  Pagination,
  Row,
  Space,
  Tabs,
  Typography,
} from 'antd';
import { find } from 'lodash';
import { createRef, useContext, useEffect, useState } from 'react';
import CreateOrUpdateRoleForm, { CreateOrUpdateRoleActions } from '../components/CreateOrUpdateRoleForm';
import DatabasePermissionModal from '../components/DatabasePermissionModal';
import RolePermissionList from '../components/RolePermissionList';
import styles from './styles.less';

const UserRolePanel = () => {
  const editForm = createRef<CreateOrUpdateRoleActions>();
  const [activeKey, setActiveKey] = useState<string>();
  const [currentRow, setCurrentRow] = useState<Role>();
  const { remove } = useRole();
  const {endpoint} = useContext(UserManagerContext);
  const userRole = useUserRoleList(endpoint);
  useEffect(() => {
    if (!currentRow && userRole.roles && userRole.roles.length) {
      setCurrentRow(userRole.roles[0]);
    }
    if (currentRow && userRole.roles && userRole.roles.length) {
      const item = find(userRole.roles, { roleId: currentRow.roleId });
      if (item?.roleDesc !== currentRow.roleDesc || item.roleName !== currentRow.roleName) {
        setCurrentRow(item);
      }
    }
  }, [userRole.roles, currentRow]);

  const handleDelete = async (row: Role) => {
    const success = await remove(row.roleId);
    if (success) {
      userRole.loadRoles();
    }
  };

  return (
    <>
      <Button
        style={{ marginBottom: 10 }}
        type="primary"
        onClick={() => editForm.current?.create()}
      >
        新建角色
      </Button>
      <CreateOrUpdateRoleForm ref={editForm} onFinish={() => userRole.loadRoles()} />
      <ProCard
        headerBordered
        bodyStyle={{ padding: 0, height: 'calc(100vh - 48px - 42px - 48px - 42px - 2px )' }}
        bordered
      >
        <ProCard
          colSpan="240px"
          bordered
          bodyStyle={{
            padding: 0,
            overflowY: 'auto',
            height: 'calc(100vh - 48px - 42px - 48px - 42px - 2px - 48px - 48px)',
          }}
          title={
            <Space direction="vertical">
              <Input.Search
                placeholder="根据角色名查找"
                value={userRole.keyword}
                onChange={(e) => userRole.setKeyword(e.target.value)}
                onSearch={() => userRole.loadRoles()}
              />
            </Space>
          }
          actions={[
            <Pagination
              key="pagination"
              defaultCurrent={1}
              total={userRole?.pageMeta?.total || 0}
              current={userRole?.pageMeta?.pageNum || 1}
              pageSize={userRole.pageSize}
              simple
              onChange={(p) => userRole.setPage(p)}
            />,
          ]}
        >
          <List
            style={{ borderTop: '1px solid #f0f0f0', marginTop: 24 }}
            loading={userRole.loading}
            pagination={false}
            dataSource={userRole.roles || []}
            renderItem={(item) => {
              return (
                <List.Item
                  actions={[
                    <Dropdown
                      key="dropdown"
                      overlay={
                        <Menu>
                          <Menu.Item onClick={async () => handleDelete(item)}>删除</Menu.Item>
                        </Menu>
                      }
                    >
                      <MoreOutlined />
                    </Dropdown>,
                  ]}
                  key={item.roleId}
                  style={{
                    padding: '8px 0 8px 24px',
                    backgroundColor: item.roleId === currentRow?.roleId ? 'rgb(230, 247, 255)' : '',
                  }}
                  onClick={() => setCurrentRow(item)}
                >
                  {item.roleName}
                </List.Item>
              );
            }}
          />
        </ProCard>
        <ProCard bodyStyle={{ padding: 0 }}>
          {!!currentRow &&   <ProCard
              headerBordered
              headStyle={{ width: '100%', display: 'block', paddingBottom: 0 }}
              title={
                <div>
                  <Row justify="space-between">
                    <Typography.Title level={5}>{currentRow?.roleName}</Typography.Title>
                    <Space>
                      <Button
                        ghost
                        type="primary"
                        onClick={() => currentRow && editForm.current?.editRow(currentRow)}
                      >
                        编辑
                      </Button>
                      <Button
                        danger
                        ghost
                        onClick={async () => currentRow && handleDelete(currentRow)}
                      >
                        删除
                      </Button>
                    </Space>
                  </Row>
                  <Typography.Text style={{ fontSize: 14, fontWeight: 'normal' }}>
                    {currentRow?.roleDesc}
                  </Typography.Text>
                  <Tabs
                    activeKey={activeKey}
                    onTabClick={setActiveKey}
                    className={styles.tabs}
                    size="small"
                  >
                    <TabPane key="menu" tab="菜单权限" />
                    {/* <TabPane key="data" tab="数据权限" /> */}
                  </Tabs>
                </div>
              }
              bodyStyle={{
                padding: 0,
                overflowY: 'auto',
                height: 'calc(100vh - 48px - 42px - 48px - 2px - 42px - 120px)',
              }}
            >
              <Tabs
                activeKey={activeKey}
                tabBarStyle={{ paddingLeft: 16 }}
                renderTabBar={() => <div />}
              >
                <TabPane key="menu" tab="菜单权限">
                  <RolePermissionList role={currentRow as Role} />
                </TabPane>
                {/* <TabPane key="data" tab="数据权限"> */}
                  {/* <DatabasePermissionModal role={currentRow as Role} /> */}
                {/* </TabPane> */}
              </Tabs>
            </ProCard>}
        </ProCard>
      </ProCard>
    </>
  );
};

export default UserRolePanel;
