import type {ModalProps} from 'antd';
import {Modal} from 'antd';
import type {ProColumns} from '@ant-design/pro-table';
import type {PurchasePlan} from '@/pages/PurchasePlanList/data';
import React, {useEffect} from 'react';
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {ProTable} from '@ant-design/pro-components';
import {useRequestTable} from "@/hooks/useRequestTable";
import {reqByPage} from "@/modules/common/infra/api/common";

// 定义参数格式
export type PurchaseDetailListModalProps = {
  data: any;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const GoodsDevelopDetailModal = (props: PurchaseDetailListModalProps) => {
  const { onFinish, data, ...rest } = props;
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/goods-develop/pageQueryDetail',{
      ...params,
      goodsDevelopId: data?.id,
    });
  });
  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [data]);

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '采购单号',
      width: 150,
      align: 'left',
      dataIndex: 'sku',
    },
    {
      title: '采购认领人',
      width: 150,
      align: 'left',
      dataIndex: 'purchaseUsername',
    },
    {
      title: '供应商链接',
      width: 150,
      align: 'left',
      dataIndex: 'supplierLink',
    },
    {
      title: '采购价格',
      width: 150,
      align: 'left',
      dataIndex: 'purchasePrice',
    },
    {
      title: '毛重',
      width: 150,
      align: 'left',
      dataIndex: 'weight',
    },
    {
      title: '长',
      width: 150,
      align: 'left',
      dataIndex: 'length',
    },
    {
      title: '宽',
      width: 150,
      align: 'left',
      dataIndex: 'width',
    },
    {
      title: '高',
      width: 150,
      align: 'left',
      dataIndex: 'height',
    },
    {
      title: '产品优势',
      width: 150,
      align: 'left',
      dataIndex: 'advantage',
    },
    {
      title: '产品劣势',
      width: 150,
      align: 'left',
      dataIndex: 'inferiority',
    },
    {
      title: '采购备注',
      width: 150,
      align: 'left',
      dataIndex: 'purchaseRemark',
    },
    {
      title: '采购完成时间',
      width: 150,
      align: 'left',
      dataIndex: 'purchaseFinishTime',
    },
  ];

  return (
    <Modal {...rest} title="开发SKU明细" closable={false} width="80%" onOk={onFinish}>
      <ProTable<any>
        search={false}
        options={false}
        size="small"
        actionRef={actionRef}
        request={fetchList}
        columns={columns}
        rowKey="id"
        bordered
      />
    </Modal>
  );
};

export default GoodsDevelopDetailModal;
