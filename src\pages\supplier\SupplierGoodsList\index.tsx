import ProCard from '@ant-design/pro-card';
import React, {useEffect, useState} from "react";
import {useRequest} from "ahooks";
import {getVenderList} from "@/modules/supplier/infra/api/vender";
import {Vender} from "@/modules/supplier/domain/vender";
import aliwangwang from "@/assets/images/aliwangwang.gif";
import { Input, List, Pagination, Space} from 'antd';
import SupplierGoodsList from "@/pages/supplier/SupplierGoodsList/components/supplierGoodsList";

const UserRolePanel = () => {
  const [currentRow, setCurrentRow] = useState<Vender>();
  const [page, setPage] = useState<number>(1);
  const [supplierSearchName, setSupplierSearchName] = useState<string>();
  const pageSize = 25;

  const { data: supplierRes, loading, refresh} = useRequest(() =>
    getVenderList({ pageCondition: { pageNum: page, pageSize: pageSize }, venderName: supplierSearchName }).then(
      (res) => res.body,
    ),
  );

  useEffect(() => {
    refresh();
  }, [page]);

  return (
    <>
      <ProCard
        headerBordered={false}
        bodyStyle={{ padding: 0,  }}
        bordered={false}
      >
        <ProCard
          colSpan="350px"
          bordered
          bodyStyle={{
            padding: 0,
            overflowY: 'auto',
            // height: 'calc(100vh - 48px - 42px - 48px - 42px - 2px - 48px - 48px)',
          }}
          title={
            <Space direction="vertical">
              <Input.Search
                placeholder="供应商名称"
                value={supplierSearchName}
                onChange={(e) => setSupplierSearchName(e.target.value)}
                onSearch={() => refresh()}
              />
            </Space>
          }
          actions={[
            <Pagination
              key="pagination"
              defaultCurrent={1}
              total={supplierRes?.pageMeta?.total || 0}
              current={supplierRes?.pageMeta?.pageNum || 1}
              pageSize={pageSize}
              simple
              onChange={(p) => setPage(p)}
            />,
          ]}
        >
          <List
            style={{ borderTop: '1px solid #f0f0f0', marginTop: 24 }}
            loading={loading}
            pagination={false}
            dataSource={supplierRes?.items || []}
            bordered={false}
            renderItem={(item) => {
              return (
                <List.Item
                  key={item.venderId}
                  style={{
                    padding: 3,
                    backgroundColor: item.venderId === currentRow?.venderId ? 'rgb(230, 247, 255)' : '',
                  }}
                  onClick={() => setCurrentRow(item)}
                >
                  {item?.loginId ? <a onClick={function () { window.open("https://amos.alicdn.com/getcid.aw?v=3&uid="+item?.loginId+"&site=cnalichn&groupid=0&s=1&charset=UTF-8&fromid='") }}><img src={aliwangwang}/>&nbsp;&nbsp;</a> : null}
                  {item?.venderName}
                </List.Item>
              );
            }}
          />
        </ProCard>
          <ProCard
            headerBordered={false}
            bordered={false}
            headStyle={{ width: '100%', display: 'block', paddingBottom: 0 }}
            bodyStyle={{
              padding: 0,
              overflowY: 'auto',
              height: 'calc(150vh - 48px - 42px - 48px - 2px - 42px - 120px)',
            }}
          >
          <SupplierGoodsList supplier={currentRow}/>
        </ProCard>
      </ProCard>
    </>
  );
};

export default UserRolePanel;
