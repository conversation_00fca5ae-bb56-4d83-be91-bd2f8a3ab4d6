import type {ExchangeRageHistoryListItem} from '@/modules/currency/infra/currency';
import {useRequestTable} from '@/hooks/useRequestTable';
import type {ProColumns} from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {useEffect} from 'react';
import {useIntl} from 'umi';
import { getOrderGoods} from "@/modules/purchaseOrder/infra/api/purchaseOrder";


export type PurchaseOrderGoodsListProps = {
  orderId: string;
};

export const PurchaseOrderGoodsList = (props: PurchaseOrderGoodsListProps) => {
  const { orderId } = props;
  const intl = useIntl();
  const { actionRef, fetchList } = useRequestTable((params) => {
    return getOrderGoods({
      ...params,
      orderId: orderId,
    });
  });
  useEffect(() => {
    actionRef.current?.reload();
  }, [orderId]);
  const columns: ProColumns<ExchangeRageHistoryListItem>[] = [
    {
      title: intl.formatMessage({ id: 'currency.rate' }),
      dataIndex: 'sku',
    },
    {
      title: intl.formatMessage({ id: 'currency,operator' }),
      dataIndex: 'operator',
    },
    {
      title: intl.formatMessage({ id: 'currency.gmtEffectStart' }),
      valueType: 'dateTime',
      width: 180,
      dataIndex: 'gmtEffectStart',
    },
    {
      title: intl.formatMessage({ id: 'currency.gmtEffectEnd' }),
      valueType: 'dateTime',
      width: 180,
      dataIndex: 'gmtEffectEnd',
    },
  ];
  return (
      <ProTable<ExchangeRageHistoryListItem>
        options={false}
        actionRef={actionRef}
        request={fetchList}
        search={false}
        columns={columns}
        scroll={{ y: 300 }}
      />
  );
};

export type PurchaseOrderGoodsModalProps = {
  orderId: string;
};

export default (props: PurchaseOrderGoodsModalProps) => {
  const { orderId } = props;
  return (
    <>
        <PurchaseOrderGoodsList orderId={orderId} />
    </>
  );
};
