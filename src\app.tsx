import Footer from '@/components/Footer';
import RightContent from '@/components/RightContent';
import { MenuDataItem, PageLoading, SettingDrawer } from '@ant-design/pro-components';
import mallApiConfig from 'config/mallApiConfig';
import type { RunTimeLayoutConfig } from 'umi';
import { getLocale, setLocale } from 'umi';
import { history } from 'umi';
import defaultSettings from '../config/defaultSettings';
import { queryCurrentUser } from './modules/auth/infra/api/auth';
import { loginEndpoint } from './modules/auth/infra/api/login';
import { getUserMenus } from './modules/user/infra/user';
import type { InitialStateData } from './types';
import { getToken, setSignSecret, setToken } from './utils/token';
import HeaderMenuDropdown from "@/components/HeaderMenuDropdown";
import Link from "antd/lib/typography/Link";

const loginPath = '/user/login';

/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <PageLoading />,
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<InitialStateData> {
  const fetchUserInfo = async () => {

    try {
      const msg = await queryCurrentUser();
      return msg.body;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // 如果不是登录页面，执行
  // console.log("getInitialState", getToken());
  // const currentMenu = await getUserMenus();
  // return {
  //   fetchUserInfo,
  //   currentUser: {},
  //   settings: defaultSettings,
  //   currentMenu: currentMenu?.body?.menus,
  //   isSuperAdmin: currentMenu?.body?.isSuperAdmin === '1',
  // }
  if (history.location.pathname !== loginPath && getToken()) {
    const currentUser = await fetchUserInfo();
    try {
      const currentMenu = await getUserMenus();
      return {
        fetchUserInfo,
        currentUser,
        settings: defaultSettings,
        currentMenu: currentMenu?.body?.menus,
        isSuperAdmin: currentMenu?.body?.isSuperAdmin === '1',
      };
    } catch (error) {
      return {
        fetchUserInfo,
        currentUser,
        settings: defaultSettings,
      };
    }
  }
  return {
    fetchUserInfo,
    settings: defaultSettings,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    waterMarkProps: {
      content: '',
    },
    footerRender: false,
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    menuHeaderRender: undefined,
    // menuItemRender: (itemProps: MenuDataItem, defaultDom: React.ReactNode)  => {
    //   return null;
    // },
    subMenuItemRender: (itemProps: MenuDataItem, defaultDom: React.ReactNode) => {
      if (itemProps.name === '首页') {
        return (
          <Link to={itemProps.path}>
            首页
          </Link>
        );
      }
      return (
        <HeaderMenuDropdown menu={itemProps}>
          <div style={{ height: '48px', fontWeight: "bold" }}>{defaultDom}</div>
        </HeaderMenuDropdown>
      );
    },
    menuProps: {
      openKeys: []
    },
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children: any, props: { location: { pathname: string | string[] } }) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {!props.location?.pathname?.includes('/login') && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
  };
};

export const qiankun = {
  // 应用加载之前
  async bootstrap(props: any) {
    // if (props && props.tokenKey) {
    //   // console.log(props)
    //   const global: GlobalWindow = window;
    //   global.tokenKey = props.tokenKey
    //   global.signSecretKey = props.signSecretKey;
    //   global.endpoint = props.endpoint;
    //   global.onHistoryChange = props.onHistoryChange
    // }
    if ((window as any).__INJECTED_PUBLIC_PATH_BY_QIANKUN__) {
      const tokenKey = 'ocp-token';
      const secretKey = 'ocp-sign-secret';
      // const tenantToken = localStorage.getItem('TENANT-TOKEN');
      const tenantToken = localStorage.getItem(tokenKey);
      // const tenantSignSecret = localStorage.getItem('TENANT-SIGN-SECRET');
      const tenantSignSecret = localStorage.getItem(secretKey);
      if (tenantToken && tenantSignSecret && !getToken()) {
        setToken(tenantToken);
        setSignSecret(tenantSignSecret);
        await loginEndpoint({ endpoint: mallApiConfig.currTerminal });
      }
      //   // const { locale } = props;
      //   // const currentLocale = getLocale();
      //   // if (locale && locale !== currentLocale) {
      //   //   setLocale(locale, false);
      //   // }

      // } else {
      //   // const currentLocale = window.localStorage.getItem('umi_locale');
      //   // if (!currentLocale) {
      //   //   setLocale('zh-CN');
      //   // }
    }
    // setLocale('zh-CN');
  },
  // 应用 render 之前触发
  async mount() {
    document.title = defaultSettings.title || '采购平台';
  },
};
