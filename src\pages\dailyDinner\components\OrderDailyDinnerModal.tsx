import React, {useState} from 'react';
import {Modal, ModalProps, message, Input} from 'antd';
import {executeSave, reqByPage} from "@/modules/common/infra/api/common";
import {ProColumns} from "@ant-design/pro-table";
import {useRequestTable} from "@/hooks/useRequestTable";
import {DailyDinnerMenu} from "@/pages/dailyDinner/data";
import ProTable from '@ant-design/pro-table';

// 定义参数格式
export type editProps = {
  onFinish: () => void;
} & ModalProps;

export default (props: editProps) => {
  const {onFinish,  ...rest} = props;
  const [selectedRows, setSelectedRows] = useState<DailyDinnerMenu[]>();
  const [loading, setLoading] = useState<boolean>(false);

  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/setting/daily-dinner/menuPageQuery',{
      ...params,
    });
  });

  // @ts-ignore
  const onSubmit = () => {
    const ids = selectedRows?.map(item=>{return item?.id});
    if(ids?.length == 0 || !ids){
      message.error("请勾选订餐菜单！");
      return false;
    }
    const list = selectedRows?.map(item=>{
      item.menuId = item?.id;
      item.quantity = item?.quantity || 1;
      item.remark = item?.remark || "";
      return item;
    })
    const params = {dinnerList: list};
    setLoading(true);
    executeSave('/sales-mgmt-biz/sales-center/setting/daily-dinner/saveDailyDinner', params).then((result) => {
      setLoading(false);
      if (result.status.success) {
        message.success('订餐成功！');
        onFinish();
      }
    });
  }

  const columns: ProColumns<DailyDinnerMenu>[] = [
    // {
    //   title: "图片",
    //   dataIndex: 'name',
    //   align: "left",
    //   hideInSearch: true,
    //   width: 80,
    //   render: (v, record) => {
    //     return <Image src={record?.img} width={80}/>
    //   }
    // },
    {
      title: "菜名",
      dataIndex: 'name',
      align: "left",
      render: (v, record) => {
        return <div>{record?.name}</div>
      }
    },
    {
      title: '价格',
      key: 'price',
      dataIndex: 'price',
      align: "left",
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.price?.toFixed(2)}</div>
      }
    },
    {
      title: '类别',
      key: 'type',
      dataIndex: 'type',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.type}</div>
      }
    },
    {
      title: '餐厅',
      key: 'canteenName',
      dataIndex: 'canteenName',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.canteenName}</div>
      }
    },
    {
      title: '供应时间',
      key: 'provisionTime',
      dataIndex: 'provisionTime',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.provisionTime}</div>
      }
    },
    {
      title: '数量',
      key: 'quantity',
      dataIndex: 'quantity',
      hideInSearch: true,
      align: "left",
      width: 100,
      render: (v, record) => {
        return <Input type={"number"} defaultValue={1} onChange={(value)=>{
          record.quantity = Number(value.target.value);
        }}/>;
      }
    },
    {
      title: '备注',
      key: 'remark',
      dataIndex: 'remark',
      hideInSearch: true,
      align: "left",
      width: 300,
      render: (v, record) => {
        return <Input  onChange={(value)=>{
          record.remark = value.target.value;
        }}/>;
      }
    },
  ];

  return (
    <>
      <Modal {...rest} title="订餐" className={'globalEnterKeySubmit'} width={"80%"}  onOk={() => onSubmit()} destroyOnClose={true} maskClosable={false}>
        <ProTable<DailyDinnerMenu>
          options={false}
          loading={loading}
          rowKey="id"
          columns={columns}
          request={fetchList}
          actionRef={actionRef}
          bordered={true}
          scroll={{y: 550}}
          pagination={{pageSize: 50}}
          size={"small"}
          tableAlertRender={false}
          cardProps={{bodyStyle: {padding: 0}}}
          rowSelection={{onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            }
          }}
        />
      </Modal>
    </>
  );
};
