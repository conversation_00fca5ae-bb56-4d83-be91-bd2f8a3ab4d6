import type { UploadImageResult } from '@/modules/common/infra/api/common';
import { uploadVideo } from '@/modules/common/infra/api/common';
import type { UploadProps } from 'antd';
import { Button, Upload } from 'antd';
import { useEffect, useRef, useState } from 'react';
import type { RcFile } from 'antd/lib/upload';
import useMergedState from 'rc-util/es/hooks/useMergedState';

export interface UploadImageProps extends Omit<UploadProps, 'customRequest' | 'onChange'> {
  limit?: number;
  onChange?: (imgs: UploadImageResult[]) => void;
  value?: UploadImageResult[];
}

const UploadVideo = (props: UploadImageProps) => {
  const { limit, onChange, value, ...rest } = props;
  const [imageList, setImageList] = useMergedState<UploadImageResult[]>([], {
    value,
    onChange,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<any>([]);

  const fileRef = useRef({ fileIds: {} });

  const handleMaterialUpload: UploadProps['customRequest'] = (options) => {
    setLoading(true);
    const { onSuccess } = options;

    const file = options.file as RcFile;

    if (typeof file === 'string') {
      return;
    }

    const uploadFilename = file.name;

    const imgItem = {
      uid: file.uid, // 注意，这个uid一定不能少，否则上传失败
      name: uploadFilename,
      status: 'uploading',
      url: '',
      percent: 20,
      isCanDel: true,
    };

    setFileList([...fileList, imgItem]);

    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = (e) => {
      uploadVideo(uploadFilename, e.target?.result)
        .then((res) => {
          if (res.status.success) {
            const updloaedImgItem = {
              uid: res.body.fileId,
              name: uploadFilename,
              status: 'done',
              url: res.body.link,
              imgUrl: res.body.link,
              percent: 100,
              isCanDel: false,
            };
            if (res.body.fileId) {
              const fileIds = fileRef?.current?.fileIds || {};
              fileIds[res.body.link] = res.body.fileId;
            }
            setFileList([...fileList, updloaedImgItem]);
            setImageList([
              ...imageList,
              {
                ...res.body,
              },
            ]);
            onSuccess?.call(true, true, true as any);
          }
        })
        .finally(() => setLoading(false));
    };
    reader.onerror = () => {
      setLoading(false);
    };
  };

  useEffect(() => {
    if (props.value) {
      const newLinks = (props.value || []).map((item) => {
        return {
          uid: item.fileId, // 注意，这个uid一定不能少，否则上传失败
          name: item.fileId,
          status: 'done',
          url: item.link,
          isCanDel: true,
        };
      });
      setFileList(newLinks);
    }
  }, [props.value]);

  return (
    <Upload {...rest} fileList={fileList} customRequest={handleMaterialUpload} accept=".mp4">
      <Button loading={loading}>上传文件</Button>
    </Upload>
  );
};

export default UploadVideo;
