import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Form, Image, Modal, notification, Space, Tag} from "antd";
import {FinanceExpenseAuditConfig} from "@/pages/setting/financeExpenseAuditConfig/data";
import Permission from "@/components/Permission";
import {downloadDataTemplate, reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import {GoodsDevelop} from "@/modules/product/domain/goodsDevelop";
import {CopyOutlined, LoadingOutlined} from "@ant-design/icons";
import {commonExport, copyText} from "@/utils/comUtil";
import moment from "moment";
import {ProFormField} from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import {Access} from "@@/plugin-access/access";
import GoodsDevelopDetailModal from "@/pages/product/goodsDevelop/components/GoodsDevelopDetailModal";

const TableList: React.FC = () => {

  const [goodsDevelopDetailListModal, setGoodsDevelopDetailListModal] = useState<boolean>(false);
  const [goodsDevelopDetailParams, setGoodsDevelopDetailParams] = useState<any>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/goods-develop/pageQuery',{
      ...params,
    });
  });


  const [form] = Form.useForm();
  const downloadTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadDataTemplate("/sales-mgmt-biz/pd/goods-develop/downloadGoodsDevelopTemplate").then(res=>{
                commonExport(res, '商品开发导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        reqByUrl("/sales-mgmt-biz/pd/goods-develop/importSkuTags", {link:link}).then((result) => {
          if (result.status.success) {
            actionRef.current?.reload?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }


  const columns: ProColumns<GoodsDevelop>[] = [
    {
      title: '需求编码',
      dataIndex: 'developCode',
      colSize: (6 / 24),
      hideInTable: true
    },
    {
      title: '图片',
      width:100,
      dataIndex: 'skuImage',
      hideInSearch: true,
      render: (_, record) => {
        return  (
          <div><Image src={record.developImage} width={120} height={120} /></div>
        );
      },
    },
    {
      title: '开发需求',
      dataIndex: 'auditRole',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>编码：<a href={record?.developLink} target={"_blank"} rel="noreferrer">{record?.developCode}</a><CopyOutlined style={{color: "blue"}} onClick={()=>{copyText(record?.developCode)}}/></div>
          <div>需求发起人：{record?.developUsername}</div>
          <div>发起时间：{moment(record?.gmtCreate)?.format("YYYY-MM-DD HH:mm:ss")}</div>
          <div>站点：{record?.site}</div>
          <div>运输方式：{record?.shippingType}</div>
          <div>参考售价：{record?.referencePrice?.toFixed(2)}</div>
        </>;
      }
    },
    {
      title: '销量/运营模式/评级',
      dataIndex: 'auditRole',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div>30天销量：{record?.salesQuantity30}</div>
          <div>30天销售额：{record?.salesAmount30}</div>
          <div>产品等级：{record?.level}</div>
          <div>运营模式：{record?.operationMode}</div>
          <div>关键词：{record?.keywordCn}/{record?.keywordEn}</div>
          <div>大类排名：{record?.categoryRanking} &nbsp;&nbsp;Ratings评分：{record?.ratingsPoint}&nbsp;&nbsp;Ratings数量：{record?.ratingsQuantity}</div>
        </>;
      }
    },
    {
      title: '开发流程',
      dataIndex: 'auditRole',
      hideInSearch: true,
      width: 300,
      render: (v, record) => {
        return <>
          <Tag style={{margin: 2}} color={"blue"}>需求填写</Tag>
          <Tag style={{margin: 2}} color={"blue"}> <LoadingOutlined /> 采购确认</Tag>
          <Tag style={{margin: 2}} color={"default"}>商品创建</Tag>
          <Tag style={{margin: 2}} color={"default"}>文案编辑</Tag>
          <Tag style={{margin: 2}} color={"default"}>销售上架</Tag>
        </>;
      }
    },
    {
      title: '采购板块',
      dataIndex: 'auditRole',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.isCostAudit==1?<span style={{color: "green"}}>是</span>: '--'}</div>;
      }
    },
    {
      title: '开发环节',
      dataIndex: 'auditRole',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.isCostAudit==1?<span style={{color: "green"}}>是</span>: '--'}</div>;
      }
    },
    {
      title: '美工/文案编辑',
      dataIndex: 'auditRole',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.isCostAudit==1?<span style={{color: "green"}}>是</span>: '--'}</div>;
      }
    },
    {
      title: 'listing上新分析',
      dataIndex: 'auditRole',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.isCostAudit==1?<span style={{color: "green"}}>是</span>: '--'}</div>;
      }
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 400,
      render: (v, record) => {
        return (
          <Space>
            <Button type="primary" size={"small"} ghost onClick={()=>{setGoodsDevelopDetailListModal(true);setGoodsDevelopDetailParams(record)}}>
              明细
            </Button>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<FinanceExpenseAuditConfig>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Permission permissionKey={"purchase:purchase"}>
                <Button size={"small"} key="level" type="primary" onClick={() => downloadTemplate()}>
                  需求创建
                </Button>
              </Permission>,
              // <Permission permissionKey={"purchase:purchase"}>
              //   <Button size={"small"} key="level" type="primary" onClick={() => {
              //     setEditConfigModal(true);
              //     setCurrentRow({id:null});
              //   }}>
              //     采购导入
              //   </Button>
              // </Permission>,
              // <Permission permissionKey={"purchase:purchase"}>
              //   <Button size={"small"} key="level" type="primary" onClick={() => {
              //     setEditConfigModal(true);
              //     setCurrentRow({id:null});
              //   }}>
              //     美工编辑
              //   </Button>
              // </Permission>,
              // <Permission permissionKey={"purchase:purchase"}>
              //   <Button size={"small"} key="level" type="primary" onClick={() => {
              //     setEditConfigModal(true);
              //     setCurrentRow({id:null});
              //   }}>
              //     数据分析
              //   </Button>
              // </Permission>,
              <Permission permissionKey={"purchase:warehouse:importGoods"}>
                <Button
                  type="primary"
                  size={"small"}
                  onClick={() => downloadTemplate()}
                >
                  导入商品库存
                </Button>
              </Permission>
            ]
            return [...options, ...dom];
          }
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <Access accessible={goodsDevelopDetailListModal}>
        <GoodsDevelopDetailModal visible={goodsDevelopDetailListModal} data={goodsDevelopDetailParams} onCancel={()=>setGoodsDevelopDetailListModal(false)} onFinish={()=>{setGoodsDevelopDetailListModal(false);actionRef.current?.reload()}}/>
      </Access>
    </>
  );
};

export default TableList;
