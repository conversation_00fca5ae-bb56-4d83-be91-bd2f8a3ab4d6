import { payTypeEnum } from "@/modules/purchaseOrder/domain/purchaseOrder";
import { ProFormSelect } from '@ant-design/pro-form';
import { Form, Modal, ModalProps } from 'antd';
type Item = {
  value?: string;
  label?: string;
};
// 定义参数格式
export type CreateModalProps = {
  open?: boolean;
  title?: string;
  onFinish?: (values: any) => void;
} & ModalProps;

const Index = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { title, onFinish, ...rest } = props;

  return (
    <Modal title={title} width={380} onOk={() => form.submit()} {...rest}>
      {/* @ts-ignore */}
      <Form form={form} onFinish={onFinish}>
        {/* @ts-ignore */}
        <ProFormSelect
          width="md"
          name='payType'
          showSearch={true}
          label={'支付方式'}
          options={Object.entries(payTypeEnum).map(([key, value]) => ({
            value: key,
            label: value
          }))}
        />
      </Form>
    </Modal>
  );
};

export default Index;
