import {ProFormSelect} from "@ant-design/pro-form";
import {Form, Modal, ModalProps} from "antd";
import {getWarehouse} from "@/modules/purchasePlan/infra/api/purchasePlan";
import {getUser} from "@/modules/purchaseOrder/infra/api/purchaseOrder";

// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: any) => void;
} & ModalProps;

const AllotPurchaseModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, ...rest } = props;

  return <Modal {...rest} title="分配采购员" onOk={()=>form.submit()}>
    <Form form={form}  onFinish={onFinish}>
      <ProFormSelect
        width="md"
        name="purchaseUsername"
        showSearch={true}
        label="采购员"
        request={async () =>  {//返回的select网络请求
          const params = await getUser("采购员");
          const res = [];
          const body = params.body;
          for(let i in body){
            const temp = {};
            temp['label'] = body[i];
            temp['value'] = i;
            res.push(temp)
          }
          return res;
        }}
        valueEnum={{
          '测试1': '测试1',
          '测试2': '测试2',
          '测试3': '测试3',
          '测试4': '测试4',
        }}
      />

    </Form>
  </Modal>
}

export default AllotPurchaseModal;
