import React, { useEffect, useRef, useState } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import { Badge, Button, Form, message, Modal, notification, Space, Tag, DatePicker } from "antd";
import CustomPage from "@/components/CustomPage";
import { useRequestTable } from "@/hooks/useRequestTable";
import type { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import { ExclamationCircleOutlined, SnippetsTwoTone } from "@ant-design/icons";
import {
  exportFinance,
  exportFinanceAsync,
  pageQueryFinanceOrder,
  payHandle,
  periodPayHandle
} from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import { OrderStatusEnum, PaymentStatusEnum, payTypeEnum } from "@/modules/purchaseOrder/domain/purchaseOrder";
import CheckedOrderModal from "@/pages/PurchaseFinanceList/components/CheckedOrderModal";
import CheckedOrderPayModal from "@/pages/PurchaseFinanceList/components/CheckedOrderPayModal";
import CheckedPeriodOrderModal from "@/pages/PurchaseFinanceList/components/CheckedPeriodOrderModal";
import CheckedPeriodOrderPayModal from "@/pages/PurchaseFinanceList/components/CheckedPeriodOrderPayModal";
import BatchUpdatePayMethods from './components/BatchUpdatePayMethods';
import BatchPayOanualOrder from './components/BatchPayOanualOrder';

import moment from "moment";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import styles from "@/pages/PurchaseOrderList/styles.less";
import { Link } from "@umijs/preset-dumi/lib/theme";
import { Access } from "@@/plugin-access/access";
import { ProFormDateRangePicker, ProFormItem, ProFormText, RequestOptionsType } from "@ant-design/pro-components";
import { ProFormRadio, ProFormSelect, ProFormTextArea } from "@ant-design/pro-form";
import { useRequest } from "ahooks";
import {
  confirmThePayment,
  purchaseDataCount,
} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import { commonExport, copyText } from "@/utils/comUtil";
import Permission from "@/components/Permission";
import PurchaseFinanceAnalysisModal from "@/pages/PurchaseFinanceList/components/PurchaseFinanceAnalysisModal";
import { loadAliAccountList } from "@/modules/setting/infra/api/AlibabaAccount";
import { getPlanSupplier, updateSupplier } from "@/modules/purchasePlan/infra/api/purchasePlan";
import { loadPurchaseEntityList } from "@/modules/setting/infra/api/PurchaseEntityConfig";
import { history } from 'umi';
const RangePicker = DatePicker.RangePicker as any;
const TableList: React.FC = () => {
  const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const { fetchList, actionRef } = useRequestTable((params) => {
    return pageQueryFinanceOrder({
      ...params,
      status: activeStatusKey === '' ? undefined : activeStatusKey,
    });
  });
  const [selectedRowsState, setSelectedRows] = useState<PurchaseOrder[]>([]);
  //财务审核modal
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  //财务付款modal
  const [payModalVisible, setPayModalVisible] = useState<boolean>(false);

  //账期财务审核modal
  const [periodModalVisible, setPeriodModalVisible] = useState<boolean>(false);
  //账期财务付款modal
  const [periodPayModalVisible, setPeriodPayModalVisible] = useState<boolean>(false);
  const [purchaseFinanceAnalysisModal, setPurchaseFinanceAnalysisModal] = useState<boolean>(false);
  const [amountStore, setAmountStore] = useState<any>();
  const [searchParams, setSearchFormValue] = useState<any>();
  const [curPeriodStatus, setCurPeriodStatus] = useState<string>();

  const { data, refresh } = useRequest(() => purchaseDataCount({ type: "finance,periodStatus" }).then((res) => res.body));
  const batchUpdatePayMethodsRef = useRef();
  const batchPayOanualOrderRef = useRef();
  /**
   * 审核订单弹框对话
   */
  const orderCheckDialog = () => {
    if (selectedRowsState.length > 0) {
      setModalVisible(true)
    } else {
      notification.error({ message: "请勾选采购单!" });
    }
  }
  // 批量设置支付方式
  const batchUpdatePayMethods = () => {
    if (selectedRowsState.length <= 0) {
      notification.error({ message: "请勾选采购单!" });
      return;
    } else {
      // @ts-ignore
      batchUpdatePayMethodsRef?.current?.open({
        orderData: selectedRowsState,
      })
    }
  }
  /**
   * 账期付款
   */
  const periodPay = () => {
    if (selectedRowsState.length <= 0) {
      notification.error({ message: "请勾选采购单!" });
      return;
    } else {
      setPeriodPayModalVisible(true);
    }
  }
  // 批量手工单付款
  const batchConfirmOanualOrder = () => {
    if (selectedRowsState.length <= 0) {
      notification.error({ message: "请勾选采购单!" });
      return;
    } else {
      // @ts-ignore
      batchPayOanualOrderRef?.current?.open({
        orderData: selectedRowsState,
      })
    }
  }

  /**
   * 账期审核
   */
  const periodAudit = () => {
    if (selectedRowsState.length <= 0) {
      notification.error({ message: "请勾选采购单!" });
      return;
    } else {
      setPeriodModalVisible(true)
    }
  }

  /**
   * 线下单付款
   */
  const [form] = Form.useForm();
  const [exportForm] = Form.useForm();
  const offlineOrderPay = (data: PurchaseOrder) => {
    if (data.platform == "1688") {
      notification.error({ message: "该订单不属于线下单" });
      return;
    }
    let purchaseOrderAmount = data.shippingFee + data.totalPrice + data.applyRefundAmount - data.applyPromotionAmount;
    let platformOrderAmount = data.platformOrderAmount;
    Modal.confirm({
      icon: false,
      centered: true,
      okText: "确认支付",
      content: (
        <Form labelCol={{ flex: '100px' }} form={form}>
          <ProFormText label="开户行" name="bankName" disabled={true} initialValue={data.bankName} />
          <ProFormText label="开户名" name="accountName" disabled={true} initialValue={data.accountName} />
          <ProFormText label="开户账号" name="account" disabled={true} initialValue={data.account} />
          <ProFormText label="采购总额" name="bb" disabled={true} initialValue={purchaseOrderAmount} />
          {data?.platformTradeType == 'period' ?
            <ProFormText label="账期应付" name="cc" disabled={true} initialValue={data?.periodAmount?.toFixed(2)} />
            : <ProFormText label="申请付款总额" name="cc" disabled={true} initialValue={platformOrderAmount?.toFixed(2)} />}
        </Form>
      ),
      onOk: function () {
        const params = {
          id: data.id,
        };
        confirmThePayment(params).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({ message: '修改成功' });
          }
        });
      },
    });
    form.resetFields();
  }

  /**
   * 付款处理
   */
  const payHandleModal = (values: PurchaseOrder[], flag: boolean) => {
    if (values.length == 0) {
      notification.error({ message: "请勾选采购单!" });
      return;
    }
    let purchaseOrderAmount = 0;
    let platformOrderAmount = 0;
    //账期
    if (flag) {
      for (const item in values) {
        purchaseOrderAmount += values[item]?.amountPayable - values[item]?.afterSalesPrice;
        platformOrderAmount += values[item]?.periodAmount;
      }
    } else {
      for (const item in values) {
        purchaseOrderAmount += values[item].shippingFee + values[item].totalPrice + values[item].applyRefundAmount - values[item].applyPromotionAmount;
        platformOrderAmount += values[item].platformOrderAmount;
      }
    }

    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form labelCol={{ flex: '100px' }} form={form}>
          <ProFormText label="选中单数" name="aa" disabled={true} initialValue={values.length} />
          <ProFormText label="采购总额" name="bb" disabled={true} initialValue={purchaseOrderAmount} />
          <ProFormText label="申请付款总额" name="cc" disabled={true} initialValue={platformOrderAmount} />
          <ProFormRadio.Group
            name="auditStatus"
            label="审核状态"
            options={[
              { label: "通过", value: "pass" },
              { label: "驳回", value: "reject" }
            ]}
          />
          <ProFormTextArea
            width="md"
            name="remark"
            label="备注"
          />
        </Form>
      ),
      onOk: function () {
        const params = {
          orderIdList: values.map(item => item.id),
          remark: form.getFieldValue('remark'),
          auditStatus: form.getFieldValue('auditStatus'),
        };
        if (params.auditStatus == null || params.auditStatus == '') {
          notification.error({ message: '请选择审核状态' });
          return null;
        }
        if (flag) {
          periodPayHandle(params).then((result) => {
            if (result.status.success) {
              notification.success({ message: '修改成功' });
              actionRef.current?.reloadAndRest?.();
              refresh();
            }
          });
        } else {
          payHandle(params).then((result) => {
            if (result.status.success) {
              notification.success({ message: '修改成功' });
              actionRef.current?.reloadAndRest?.();
              refresh();
            }
          });
        }
      },
    });
  }

  const refreshAlibabaOrder = () => {
    if (selectedRowsState.length > 0) {
      setPayModalVisible(true)
    } else {
      notification.error({ message: "请勾选采购单!" });
    }
  }

  const exportFinanceData = () => {
    Modal.confirm({
      icon: '',
      width: 500,
      content: (
        <Form form={exportForm}>
          <ProFormDateRangePicker name={"date"} label={"时间"} />
        </Form>
      ),
      onOk: async () => {
        const formValues = exportForm.getFieldsValue();
        if (!formValues?.date) {
          message.error("请选择时间区间")
          return;
        }
        const [startDate, endDate] = formValues.date;
        const params = {
          dateKey: 'createDate',
          // 'dateValue': formValues?.date?.map((item) => { return item.toISOString() })
          dateValue: [
            startDate ? moment(startDate).startOf('day').toISOString() : null, // 开始时间取当天最早时间
            endDate ? moment(endDate).endOf('day').toISOString() : null,   // 结束时间取当天最晚时间
          ],
        };
        const res = await exportFinanceAsync(params);
        Modal.confirm({
          icon: '',
          width: 500,
          content: <div>{`文件导出${res?.status?.success ? '成功' : '失败'}`},请前往【系统-设置-任务中心】菜单查看导出详情</div>,
          okText: '立即前往',
          onOk: () => history.push(`/setting/setting/taskList#'EXPORT`)
        });
      },
    });
  }

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '单号',
      dataIndex: 'orderNo',
      hideInTable: true,
      colSize: (6 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [numberKey, numberValue] = value;
          const numberValues = numberValue?.trim()?.split(/[,，\s]/)?.map((v: string) => v?.trim())
          return {
            numberKey,
            numberValues: numberValue?.trim() ? numberValues : []
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectInput options={
          [{
            value: 'orderCode',
            label: '采购单号',
          }, {
            value: 'trackingNumber',
            label: '快递单号',
          }, {
            value: 'platformOrderCode',
            label: '平台单号',
          }, {
            value: 'purchaseEntity',
            label: '采购主体',
          }]
        }
          defaultValue={'orderCode'}
          placeholder='输入多个，中/英文逗号或空格隔开'
        />
      }
    },
    {
      title: '商品',
      dataIndex: 'sku',
      hideInTable: true,
      colSize: (6 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [goodsKey, goodsValue] = value;
          return {
            goodsKey,
            goodsValue
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectInput options={
          [{
            value: 'sku',
            label: 'SKU',
          }, {
            value: 'goodsName',
            label: '名称',
          }]
        }
          defaultValue={'sku'}
        />
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      hideInTable: true,
      colSize: (6 / 24),
    },
    {
      dataIndex: 'date',
      title: '时间区间',
      hideInTable: true,
      colSize: (6 / 24),
      valueType: "dateRange",
      width: 100,
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [dateKey, dateValue] = value;
          return {
            dateKey,
            dateValue: [
              dateValue[0] ? moment(dateValue[0]).startOf('day') : null, // 当天最早时间
              dateValue[1] ? moment(dateValue[1]).endOf('day') : null,   // 当天最晚时间
            ],
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectGroup options={
          [{
            value: 'createDate',
            label: '创建',
          }, {
            value: 'orderDate',
            label: '下单',
          }, {
            value: 'paymentDate',
            label: '付款',
          }, {
            value: 'signDate',
            label: '签收',
          }, {
            value: 'periodDate',
            label: '账期',
          }]
        }
          defaultValue={'createDate'}
          placeholder={['开始时间', '结束时间']}
          renderFormItem={(props: any) => {
            return <RangePicker {...props} />;
          }}
        />
      }
    },
    {
      title: '加急',
      dataIndex: 'isUrgent',
      colSize: (6 / 24),
      hideInTable: true,
      valueEnum: {
        [1]: {
          text: "是",
          status: 'Error',
        }, [0]: {
          text: "否",
          status: 'Success',
        }
      },
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      colSize: (6 / 24),
      hideInTable: true,
      valueEnum: {
        [1]: {
          text: "银行转账",
          status: 'Success',
        },
        [2]: {
          text: "现金支付",
          status: 'Success',
        },
        [3]: {
          text: "支付宝",
          status: 'Success',
        },
        [4]: {
          text: "余额抵充",
          status: 'Success',
        },
        [5]: {
          text: "跨境宝",
          status: 'Success',
        },
        [6]: {
          text: "超级支付宝",
          status: 'Success',
        }
      }
    },
    {
      title: '账号',
      dataIndex: 'platformAccount',
      colSize: (6 / 24),
      hideInTable: true,
      request: async () => {
        const res = await loadAliAccountList();
        return res.body?.map((item: any) => {
          return { label: item?.accountName, value: item?.accountName };
        })
      }
    },
    {
      title: '采购主体',
      dataIndex: 'purchaseEntity',
      colSize: (6 / 24),
      hideInTable: true,
      request: async () => {
        const res = await loadPurchaseEntityList({});
        return res.body?.map((item: any) => {
          return { label: item?.purchaseEntity, value: item?.purchaseEntity };
        })
      }
    },
    {
      title: '用户',
      dataIndex: 'userName',
      hideInTable: true,
      colSize: (6 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [conductorKey, conductorValue] = value;
          return {
            conductorKey,
            conductorValue
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectInput options={
          [{
            value: 'bankAccountName',
            label: '收款人',
          }, {
            value: 'purchaseUsername',
            label: '采购员',
          }, {
            value: 'merchandiserUsername',
            label: '跟单员',
          }]
        }
          defaultValue={'bankAccountName'}
        />
      }
    },
    {
      title: '标签',
      dataIndex: 'tagName',
      hideInTable: true,
      colSize: (6 / 24),
      valueEnum: {
        "": {
          text: "全部",
          status: 'Success',
        },
        "periodTrade": {
          text: "账期",
          status: 'Success',
        },
        "isManual": {
          text: "手工单",
          status: 'Success',
        }
      }
    },
    {
      title: '平台交易方式',
      dataIndex: 'platformTradeType',
      hideInTable: true,
      colSize: (6 / 24),
      valueEnum: {
        "fxassure": {
          text: "担保交易(fxassure)",
          status: 'Success',
        },
        "assureTrade": {
          text: "担保交易(assureTrade)",
          status: 'Success',
        },
        "period": {
          text: "供应商账期",
          status: 'Success',
        },
        "credit": {
          text: "诚意赊",
          status: 'Success',
        }
      }
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return (<Filters.CheckboxButton
          defaultValue={[1]}
          options={[
            { value: 1, label: '全部' },
            { value: -10, label: '无需付款' },
            { value: 10, label: '申请付定金' },
            { value: 20, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.payTheBalance}>申请付余额</Badge>) },
            { value: 40, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.paymentInProgress}>付款进行中</Badge>) },
            { value: 35, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodWaitPay}>账期待付款</Badge>) },
            { value: 36, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.creditWaitPay}>诚意赊账待付款</Badge>) },
            {
              value: 45, label: (
                <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodRepay}>账期待还款</Badge>
              )
            },
            { value: 46, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.creditRepay}>诚意赊账待还款</Badge>) },
            { value: 70, label: '已付全款' },
            { value: 50, label: (<Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.reject}>驳回</Badge>) },
          ]}
          onChange={(e) => {
            setSelectedRows([])
            setAmountStore(null);
            if (e.length > 0 && e != formInstance.getFieldValue('payStatus')) {
              setActiveStatusKey(e[0].toString())
              setSelectedRows([]);
              actionRef?.current && actionRef?.current?.clearSelected();
              formInstance.setFieldValue('payStatus', e);
            }
            formInstance.submit();
          }}
        />)
      }
    },
    {
      title: '账期状态',
      dataIndex: 'periodStatus',
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return (<Filters.CheckboxButton
          defaultValue={[""]}
          options={[
            { value: "", label: '全部' },
            {
              value: "WAIT", label: (
                <div style={{ margin: '0 16px' }}>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#1890ff", marginTop: -6, marginRight: 18 }} count={data?.periodWait_46}> 待</Badge>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodWait_45}>审核 </Badge>
                </div>
              )
            },
            {
              value: "PASS", label: (
                <div style={{ margin: '0 16px' }}>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#1890ff", marginTop: -6, marginRight: 18 }} count={data?.periodPass_46}> 通</Badge>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodPass_45}>过 </Badge>
                </div>
              )
            },
            {
              value: "PERIOD_PASS", label: (
                <div style={{ margin: '0 16px' }}>
                  {/* <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#1890ff", marginTop: -6, marginRight: 18 }} count={data?.periodPass_46}> 通</Badge> */}
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodPass_45}> 账期通过 </Badge>
                </div>
              )
            },
            {
              value: "CREDIT_PASS", label: (
                <div style={{ margin: '0 16px' }}>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#1890ff", marginTop: -6, marginRight: 18 }} count={data?.periodPass_46}> 诚意赊通过 </Badge>
                  {/* <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodPass_45}>过 </Badge> */}
                </div>
              )
            },
            {
              value: "REJECT", label: (
                <div style={{ margin: '0 16px' }}>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#1890ff", marginTop: -6, marginRight: 18 }} count={data?.periodReject_46}> 驳</Badge>
                  <Badge overflowCount={9999} size={"small"} style={{ backgroundColor: "#FF3333", marginTop: -6, marginRight: -6 }} count={data?.periodReject_45}>回 </Badge>
                </div>
              )
            },
            { value: "END", label: '已结清' },
          ]}
          onChange={(e) => {
            setSelectedRows([])
            setAmountStore(null);
            if (e.length > 0 && e != formInstance.getFieldValue('periodStatus')) {
              formInstance.setFieldValue('periodStatus', e);
              setCurPeriodStatus(e[0])
            }
            formInstance.submit();
          }}
        />)
      }
    },
    {
      title: '标题/采购单号',
      dataIndex: 'title',
      hideInSearch: true,
      width: 180,
      render: (v, record) => {
        return <>
          <div style={{ fontSize: 12, fontWeight: "bold" }}>{record?.title}</div>
          <div style={{ fontSize: 13 }}> {record.orderCode}<SnippetsTwoTone onClick={() => copyText(record.orderCode)} /></div>
          <div style={{ fontSize: 12 }}> {<a href={"https://trade.1688.com/order/new_step_order_detail.htm?orderId=" + record.platformOrderCode} target={"_blank"}>{record?.platformOrderCode}</a> || "--"}<SnippetsTwoTone onClick={() => copyText(record?.platformOrderCode)} /></div>
        </>;
      }
    },
    {
      title: '渠道/账号',
      dataIndex: 'platformAccount',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{ fontSize: 13 }}> {record?.platform || "--"}</div>
          <div style={{ fontSize: 13 }}> {record?.platformAccount}</div>
        </>;
      }
    },
    {
      title: '采购主体/收款人',
      dataIndex: 'purchaseEntity',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{ fontSize: 12 }}>{record?.purchaseEntity}</div>
          <div style={{ fontSize: 12 }}>{record?.accountName || "--"}</div>
        </>;
      }
    },
    {
      title: '采购/跟单',
      dataIndex: 'purchaseUsername',
      hideInSearch: true,
      render: (v, record) => {
        return <><div style={{ fontSize: 12 }}>{record?.purchaseUsername}</div>
          <div style={{ fontSize: 12 }}>{record?.merchandiserUsername}</div></>;
      }
    },
    {
      title: '采购/付款状态',
      dataIndex: 'status',
      align: 'left',
      hideInSearch: true,
      render: (v, record) => {
        const orderStatus = OrderStatusEnum[record?.orderStatus];
        const payStatus = PaymentStatusEnum[record?.payStatus];
        return <>
          {orderStatus != undefined ?
            <div>
              <span>
                <span className="ant-badge-status-dot ant-badge-status-success"></span>
                <span style={{ fontSize: 12 }} className="ant-badge-status-text">{orderStatus}</span>
              </span>
            </div> : null}
          {payStatus != undefined ?
            <div>
              <span>
                <span className="ant-badge-status-dot ant-badge-status-success"></span>
                <span style={{ fontSize: 12 }} className="ant-badge-status-text">{payStatus}</span>
              </span>
            </div> : null}
        </>;
      }
    },
    {
      title: '支付方式/平台交易方式',
      dataIndex: 'status',
      align: 'left',
      hideInSearch: true,
      render: (v, record) => {
        const payTypeStatus = payTypeEnum[record?.payType];
        let platformTradeType = '';
        if (record?.platformTradeType == "fxassure") {
          platformTradeType = "担保交易(fxassure)";
        } else if (record?.platformTradeType == "assureTrade") {
          platformTradeType = "担保交易(assureTrade)";
        } else if (record?.platformTradeType == "period") {
          platformTradeType = "供应商账期";
        } else if (record?.platformTradeType == "credit") {
          platformTradeType = "诚意赊";
        } else {
          platformTradeType == null ? "" : record?.platformTradeType;
        }
        return <>
          {payTypeStatus != undefined ?
            <div>
              <span>
                <span className="ant-badge-status-dot ant-badge-status-success"></span>
                <span style={{ fontSize: 12 }} className="ant-badge-status-text">{payTypeStatus}</span>
              </span>
            </div> : null}
          {platformTradeType != undefined ?
            <div>
              <span>
                <span className="ant-badge-status-dot ant-badge-status-success"></span>
                <span style={{ fontSize: 12 }} className="ant-badge-status-text">{platformTradeType}</span>
              </span>
            </div> : null}
        </>;
      }
    },
    {
      title: '平台/账期金额',
      dataIndex: 'totalPrice',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{ fontSize: 12 }}>
            {record.platformOrderAmount?.toFixed(2) != (record.totalPrice + record.shippingFee + record.applyRefundAmount - record?.applyPromotionAmount)?.toFixed(2) ? (
              <ExclamationCircleOutlined style={{ color: 'red', fontSize: 10, float: "right" }} />
            ) : null}
            平台金额：{record?.platformOrderAmount?.toFixed(2)}
          </div>
          {record?.periodAmount != null ? (<>
            <div style={{ color: "#FF6600", fontSize: 12 }}>
              <span style={{ fontSize: 12, color: "#FF6600" }}>
                <div>
                  账期应付：{record?.periodAmount?.toFixed(2)}
                  {record?.periodAmount?.toFixed(2) != (record?.amountPayable - record?.afterAmount)?.toFixed(2) ? (
                    <ExclamationCircleOutlined style={{ color: 'red', fontSize: 10, float: "right" }} />
                  ) : null}
                </div>
              </span>
            </div>
          </>) : null}
        </>;
      }
    },
    {
      title: '应付货款',
      dataIndex: 'totalPrice',
      width: 250,
      hideInSearch: true,
      render: (v, record) => {
        return <>
          <div style={{ fontSize: 12 }}><span style={{ fontWeight: "bold" }}>{(record?.totalPrice + record?.shippingFee + record?.applyRefundAmount - record?.applyPromotionAmount)?.toFixed(2)}</span> = {record?.totalPrice?.toFixed(2) || 0.00}(商品金额)+{record?.shippingFee?.toFixed(2)}(ERP运费)<br />
            +{record?.applyRefundAmount?.toFixed(2)}(涨价)-{record?.applyPromotionAmount?.toFixed(2)}(优惠)
          </div>
          {record?.afterAmount != null && record?.afterAmount > 0 ? (<>
            <div style={{ fontSize: 12, color: "#FF6600" }}>
              {(record?.amountPayable - record?.afterAmount)?.toFixed(2)}(账期应付) =  {record?.amountPayable?.toFixed(2) || 0.00}(下单金额) - {record?.afterAmount?.toFixed(2) || 0.00}(售后金额)
            </div>
          </>) : null}
        </>;
      }
    },
    {
      title: '标签',
      dataIndex: 'remark',
      width: 200,
      align: 'left',
      hideInSearch: true,
      render: (v, record) => {
        return <>
          {record?.platformTradeType == 'period' ? <Tag color='#FF6600'>账期</Tag> : null}
          {record?.isManual == 1 ? <Tag color='red'>手工单</Tag> : null}
        </>
      }
    },
    {
      title: '时间',
      dataIndex: 'paymentTime',
      align: 'left',
      width: 180,
      hideInSearch: true,
      render: (v, record) => {
        return <>
          {record.paymentTime != null ? (
            <div style={{ fontSize: 12 }}>申请：{moment(Number(record.paymentTime)).format("YYYY-MM-DD HH:mm:ss")}</div>) : null}
          {record.platformPayTime != null ? (
            <div style={{ fontSize: "12px" }}>付款：{moment(record.platformPayTime).format("YYYY-MM-DD HH:mm:ss")}</div>) : null}
          {record?.periodTime != null ? (
            <div style={{ fontSize: 12, color: "#FF6600" }}>账期：{moment(record?.periodTime)?.format("YYYY-MM-DD HH:mm:ss")}</div>) : null}
        </>
      }
    },
    {
      title: '操作',
      align: 'left',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <Space>
            <Button type="primary" size={"small"} ghost style={{ fontSize: 12, borderRadius: 5, color: "green", borderColor: "green" }} onClick={() => history.push({ pathname: `/purchase/purchase/purchaseOrder/detail/${record?.id}`, state: { title: record?.orderCode } })}>
              详情
            </Button>
            {record.platform != "1688" && record?.payStatus != 70 ?
              <Permission permissionKey={"purchase:finance:purchaseOrder:manualPayment"}>
                <Button type="primary" size={"small"} onClick={() => offlineOrderPay(record)} ghost style={{ fontSize: 12, borderRadius: 5, color: "blue", borderColor: "blue" }}>
                  手工单付款
                </Button>
              </Permission>
              : null}
          </Space>
        );
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [activeStatusKey]);

  const reload = () => {
    if (actionRef?.current?.clearSelected) {
      actionRef?.current?.clearSelected();
      actionRef.current?.reload();
    }
  }
  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={(params) => {
          setSearchFormValue(params);
          return fetchList(params);
        }}
        rowKey="id"
        //可以隐藏选择多少项提示
        tableAlertRender={false}
        scroll={{ y: 'calc(100vh - 430px)' }}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <b>选中单数：{selectedRowsState.length} &nbsp;</b>,
              <b>应付总额：{amountStore?.selectAmount == null ? 0.00 : amountStore.selectAmount?.toFixed(2)} &nbsp;</b>,
              curPeriodStatus ? (<b style={{ color: "#FF6600" }}>账期应付：{amountStore?.selectPeriodAmount?.toFixed(2) || 0} &nbsp;</b>) : null,
              <Permission permissionKey={"purchase:finance:purchaseOrder:batchCheck"}>
                <Button key="level" disabled={activeStatusKey == "1"} type="primary" onClick={() => orderCheckDialog()}>
                  批量审核
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:manualCheck"}>
                <Button key="level" disabled={activeStatusKey == "1"} type="primary" onClick={() => payHandleModal(selectedRowsState, false)}>
                  人工审核
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:batchPayment"}>
                <Button key="level" type="primary" onClick={() => batchUpdatePayMethods()} disabled={activeStatusKey == "1"}>
                  批量修改支付方式
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:batchPayment"}>
                <Button key="level" disabled={activeStatusKey == "1"} type="primary" onClick={function () { refreshAlibabaOrder() }}>
                  批量付款
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:batchPayment"}>
                <Button key="level" disabled={activeStatusKey == "1"} type="primary" onClick={() => batchConfirmOanualOrder()}>
                  批量手工单付款
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:periodCheck"}>
                <Button key="level" ghost={true} type="primary" onClick={() => periodAudit()}>
                  账期审核
                </Button>
              </Permission>,
              // <Permission permissionKey={"purchase:finance:purchaseOrder:periodManualCheck"}>
              //   <Button key="level" ghost={true} type="primary" onClick={() => payHandleModal(selectedRowsState,true)}>
              //     账期人工审核
              //   </Button>
              // </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:periodPayment"}>
                <Button key="level" ghost={true} type="primary" onClick={() => periodPay()}>
                  账期付款
                </Button>
              </Permission>,
              <Permission permissionKey={"purchase:finance:purchaseOrder:exportOrder"}>
                <Button key="level" type="primary" onClick={() => exportFinanceData()}>
                  导出
                </Button>
              </Permission>,
              <Button size={"small"} style={{ fontSize: 13, borderRadius: "5px" }} key="level" type="primary" onClick={() => setPurchaseFinanceAnalysisModal(true)}>
                财务分析
              </Button>
            ];
            return [...options, ...dom];
          },
        }}
        columns={columns}
        // className={styles.inline_search_table}
        toolBarRender={() => []}
        rowSelection={{
          onChange: (_, selectedRows) => {
            let selectAmount = 0;
            let selectPeriodAmount = 0;
            selectedRows.forEach((record) => {
              selectAmount += record.totalPrice + record.shippingFee + record.applyRefundAmount - record?.applyPromotionAmount;
              selectPeriodAmount += record?.periodAmount;
            })
            setAmountStore({ selectAmount: selectAmount, selectPeriodAmount: selectPeriodAmount });
            setSelectedRows(selectedRows);
          },
        }}
        pagination={{ pageSize: 30 }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <Access accessible={modalVisible}>
        <CheckedOrderModal visible={modalVisible} onCancel={() => {
          setModalVisible(false);
          actionRef.current?.reloadAndRest?.();
          refresh();
        }} orderData={selectedRowsState} onFinish={function () { }} />
      </Access>
      <Access accessible={payModalVisible}>
        <CheckedOrderPayModal visible={payModalVisible} onCancel={() => {
          setPayModalVisible(false);
          actionRef.current?.reloadAndRest?.();
          refresh();
        }} orderData={selectedRowsState} onFinish={function () { }} />
      </Access>
      <Access accessible={periodModalVisible}>
        <CheckedPeriodOrderModal visible={periodModalVisible} onCancel={() => {
          setPeriodModalVisible(false);
          actionRef.current?.reloadAndRest?.();
          refresh();
        }} orderData={selectedRowsState} onFinish={() => {
          setPeriodModalVisible(false);
          actionRef.current?.reloadAndRest?.();
          refresh();
        }
        } />
      </Access>
      <Access accessible={periodPayModalVisible}>
        <CheckedPeriodOrderPayModal visible={periodPayModalVisible} onCancel={() => {
          setPeriodPayModalVisible(false);
          actionRef.current?.reloadAndRest?.();
          refresh();
        }} orderData={selectedRowsState} onFinish={function () { }} />
      </Access>
      <Access accessible={purchaseFinanceAnalysisModal}>
        <PurchaseFinanceAnalysisModal visible={purchaseFinanceAnalysisModal} onCancel={() => setPurchaseFinanceAnalysisModal(false)} onFinish={() => setPurchaseFinanceAnalysisModal(false)} />
      </Access>
      {/* @ts-ignore */}
      <BatchUpdatePayMethods ref={batchUpdatePayMethodsRef} reload={reload} />
      {/* @ts-ignore */}
      <BatchPayOanualOrder ref={batchPayOanualOrderRef} reload={reload} />
    </>
  );
};

export default TableList;
