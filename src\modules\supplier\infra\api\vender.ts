import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import type {SupplierGoods, SupplierGoodsBind, Vender} from '../../domain/vender';
import mallRequest1 from "@/utils/mallRequest1";

export interface CreateVenderParams {
  areaName?: string;
  cellphone?: string;
  cityName?: string;
  contractor?: string;
  cooperateModal?: string;
  detail?: string;
  fax?: string;
  foreignName: string;
  level?: string;
  nation?: string;
  payMethod?: string;
  provinceName?: string;
  qq?: string;
  settleCircle?: string;
  settleCurrency?: string;
  settleType?: string;
  venderCode: string;
  venderName: string;
}

export async function createVender(params: CreateVenderParams) {
  return mallRequest<API.ApiBaseResult<API.CurrentUser>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/create',
    method: 'POST',
    data: params,
  });
}

export async function updateVender(params: CreateVenderParams) {
  return mallRequest<API.ApiBaseResult<API.CurrentUser>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/update',
    method: 'POST',
    data: params,
  });
}

export interface QueryVenderParams {
  cooperateModal?: string;
  foreignName?: string;
  level?: string;
  pageCondition: API.PageCondition;
  status?: string;
  venderCode?: string;
  venderName?: string;
}

export async function getVenderList(params: QueryVenderParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Vender[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/pageQuery',
      method: 'POST',
      data: params,
    },
  );
}

export async function getAll() {
  return mallRequest<API.ApiBaseResult<Vender[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/getAll',
    method: 'GET',
  });
}

// 启用供应商
export async function venderActive(data: { venderId: string }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/active',
      method: 'POST',
      data,
    },
  );
}

// 禁用供应商
export async function venderDisable(data: { venderId: string }) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/disable',
    method: 'POST',
    data,
  });
}

// 获取供应商信息
export async function getVenderInfo(params: { venderId: string }) {
  return mallRequest<API.ApiBaseResult<Vender>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/render',
    method: 'GET',
    params,
  });

}




export type QuerySupplierGoodsParams ={
  sku?: string,
  supplierName?: string,
}& API.QueryPageParams;



// 供应商商品分页查询
export async function getSupplierGoodsList(data: QuerySupplierGoodsParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<SupplierGoods[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/pageQuery',
      method: 'POST',
      data,
    },
  );
}


//设置首选供应商
export async function defaultSupplier(data: any) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<SupplierGoods[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/defaultSupplier',
      method: 'POST',
      data,
    },
  );
}


//获取供应商商品信息
export async function getSupplierGoodsBySupplierId(supplierId?: string) {
  return mallRequest<API.ApiQueryPageResult<SupplierGoods[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/supplier/goods/get',
    params:{
      supplierId
    },
  });
}


//绑定供应商链接
export async function bindingSupplierUrl(data: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/bindingSupplierUrl',
    method: 'POST',
    data,
  });
}


//绑定供应商商品链接
export async function BoundAlibabaProduct(data: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/BoundAlibabaProduct',
    method: 'POST',
    data,
  });
}

//供应商添加商品
export async function addSupplierGoods(data: Vender) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/addSupplierGoods',
    method: 'POST',
    data,
  });
}

//下载供应商模板
//下载商品模板
export async function downloadSupplierTemplate() {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/downloadSupplierTemplate',
      method: 'GET',
      responseType: 'blob',
    },
  );
}

//导入供应商
export async function importSupplier(link?: string) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/importSupplier',
    params:{
      link
    }
  });
}


export async function queryGoodsSupplierBindList(spuId: string, supplierId: string) {
  return mallRequest<API.ApiBaseResult<SupplierGoodsBind[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/supplier/goods/queryGoodsSupplierBindList',
    params: {spuId: spuId, supplierId: supplierId}
  });
}

export async function saveSupplierGoodsBind(data: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/batchBindSupplierGoods',
    method: 'POST',
    data,
  });
}





