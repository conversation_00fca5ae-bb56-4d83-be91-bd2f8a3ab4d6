import umiRequest from 'umi-request';

export type GoogleTranslateParams = {
  translateText: string;
  languages: 'en';
};

export async function googleTranslate(params: GoogleTranslateParams) {
  return umiRequest<string>('https://translate.googleapis.com/translate_a/single', {
    method: 'GET',
    params: {
      client: 'gtx',
      dt: 't',
      sl: 'auto',
      tl: params.languages,
      q: params.translateText,
    },
  });
}
