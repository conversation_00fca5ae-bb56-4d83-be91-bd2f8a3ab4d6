import type {ProColumns} from '@ant-design/pro-components';
import {EditableProTable} from '@ant-design/pro-components';
import {Button, Modal, message,ModalProps} from 'antd';
import React, {useEffect, useState} from 'react';
import {PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";
import {batchTakeStock} from "@/modules/purchaseOrder/infra/api/purchaseOrder";


// 定义参数格式
export type modifyQuantityAndPrice = {
  data: PurchaseOrderGoods[];
  onFinish: (values: any) => void;
} & ModalProps;

export default (props: modifyQuantityAndPrice) => {
  const { onFinish,data , ...rest } = props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [dataSource, setDataSource] = useState<PurchaseOrderGoods[]>();

  useEffect(
    ()=>{
      setDataSource(props.data);
      setEditableRowKeys(data.map((item) => item.id));
    },
    [props.data]
  );


  //修改价格与数量
  const modify=(values: PurchaseOrderGoods[])=>{

    const obj={
      "orderGoodsList":values
    }

    batchTakeStock(obj).then((res)=>{
      if (res.status.success){
        message.success("修改成功");
      }
    })
  }


  const columns: ProColumns<PurchaseOrderGoods>[] = [
    {
      title: "id",
      dataIndex: "id",
      hideInTable: true
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseQuantity',
      readonly: true,
    },
    {
      title: '收货数量',
      dataIndex: 'receivingQuantity',
    },
    {
      title: '已收数量',
      dataIndex: 'arrivalQuantity',
      readonly: true,
    },
  ];

  return (
    <>
      <Modal {...rest} title="修改价格/数量" width={800} onOk={onFinish}>
      <EditableProTable<PurchaseOrderGoods>
        columns={columns}
        rowKey="id"
        scroll={{
          x: 500,
        }}
        value={dataSource}
        maxLength={1}
        onChange={setDataSource}
        toolBarRender={() => {
          return [
            <Button
              type="primary"
              key="save"
              onClick={() => {
                // dataSource 就是当前数据，可以调用 api 将其保存
                modify(dataSource);
              }}
            >
              保存数据
            </Button>,
          ];
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
          },
          onChange: setEditableRowKeys,
        }}
      />
      </Modal>
    </>
  );
};
