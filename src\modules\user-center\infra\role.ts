import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import { EndPoint, Role } from '../domain/user';

export interface AddRoleParams {
  roleDesc: string;
  roleName: string;
}

// 添加角色
export async function addRole(params: AddRoleParams) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/addRole',
    method: 'POST',
    data: params,
  });
}

// 删除用户角色
export async function deleteRole(params: { roleId: string }) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/deleteRole',
    method: 'POST',
    data: params,
  });
}

export interface RoleMenuItem {
  menuId: string;
}

export interface RoleMenusData {
  menus: RoleMenuItem[];
  roleId: string;
  roleName: string;
}

// 获取角色权限
export async function getRoleMenus(params: { roleId: string }) {
  return mallRequest<API.ApiBaseResult<RoleMenusData>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/getRoleMenus',
    method: 'POST',
    data: params,
  });
}

// 创建目录
export async function createMenus(params: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/menu/create',
    method: 'POST',
    data: params,
  });
}

export interface QueryPermissonPageParams {
  roleName?: string;
  pageCondition: API.PageCondition;
  endpoint?: string;
}

// 分页查询用户角色
export async function queryUserRolePage(params: QueryPermissonPageParams) {
  return mallRequest<API.ApiQueryPageResult<Role[]>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/pageQuery',
    method: 'POST',
    data: params,
  });
}

export interface UpdateRoleParams {
  roleDesc: string;
  roleId: string;
  roleName: string;
}

// 分页查询用户角色
export async function updateRole(params: UpdateRoleParams) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/updateRole',
    method: 'POST',
    data: params,
  });
}

export interface MenuItem {
	childrenMenu: MenuItem[];
	endpoint: string;
	isLeaf: boolean;
	menuId: string;
	perms: string;
	title: string;
}

// 分页查询用户角色
export async function getMenus() {
  return mallRequest<API.ApiBaseResult<MenuItem[]>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/endpoint/menu-biz/getMenus',
    method: 'POST',
  });
}

export interface RolePermissionItem {
  opt: 0 | 1;
  menuId: string;
}

export interface BindRolePermissionParams {
  roleId: string;
  menuOpts: RolePermissionItem[];
}

// 绑定用户角色
export async function bindRoleMenus(params: BindRolePermissionParams) {
  return mallRequest<API.ApiBaseResult<MenuItem[]>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/bindRoleMenus',
    method: 'POST',
    data: params,
  });
}


export type QueryEndpointRoleParams = {
  endpoint: string;
  roleName?: string
}

// 获取终端所有角色
export async function getRoles(data: QueryEndpointRoleParams) {
  return mallRequest<API.ApiBaseResult<Role[]>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/getRoles',
    method: 'POST',
    data
  });
}


// 保存用户数据角色
export async function saveUserDataRole(data: any) {
  return mallRequest<API.ApiBaseResult<Role[]>>(mallApiConfig.userCenterApiGatewayUrl, {
    requestPath: '/user-center/user/role-biz/user-role/saveUserDataRole',
    method: 'POST',
    data
  });
}
