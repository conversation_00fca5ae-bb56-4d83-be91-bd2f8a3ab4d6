import React, { useMemo } from 'react';
import type { AccessProps } from 'umi';
import { Access, useModel } from 'umi';

export type PermissionKey = string | string[];

export type PermissionsProps = {
  permissionKey?: PermissionKey;
} & Pick<AccessProps, 'fallback'>;


export const usePermission = (permissionKey?: PermissionKey) => {
  const { initialState } = useModel('@@initialState');
  const accessible = useMemo(() => {
    if (!permissionKey || initialState?.isSuperAdmin) {
      return true;
    }
    if (!initialState?.currentMenu || !initialState?.currentMenu.length) {
      return false;
    }
    const permissionKeys = Array.isArray(permissionKey) ? permissionKey : [permissionKey];
    const hasPermission = initialState.currentMenu.some((menuItem) => permissionKeys.includes(menuItem.permission));
    return hasPermission;
  }, [permissionKey, initialState?.isSuperAdmin, initialState?.currentMenu]);

  return accessible;
}

const Permission: React.FC<PermissionsProps> = ({ children, permissionKey, fallback }) => {
  const accessible = usePermission(permissionKey)
  return (
    <Access accessible={accessible} fallback={fallback}>
      {children}
    </Access>
  );
}

// 提供高阶组件给组件使用
export function hocPermission<T>(Child: React.FC<T>): React.FC<T & PermissionsProps> {
  return ({ permissionKey, fallback, ...rest }: any) => {
    return (
      <Permission permissionKey={permissionKey} fallback={fallback}>
        <Child {...rest} />
      </Permission>
    )
  }
}

export default Permission;
