import type {ProColumns} from '@ant-design/pro-table';
import React, {useState} from 'react';
import {useRequest} from "ahooks";
import {Button, Card, Spin, Table, Tag, Modal, Form, notification} from "antd";
import {SupplierGoods} from "@/modules/supplier/domain/vender";
import BindAliProductModal from "@/pages/supplier/SupplierGoodsList/components/BindAliProductModal";
import {ProForm, ProFormSelect, ProFormText, ProFormItem} from "@ant-design/pro-components";
import {createSupplierGoods, removeSupplierGoods, updateSupplierGoods} from "@/modules/goods/infra/api/goods";
import Permission from "@/components/Permission";
import BindSpuSupplierModal from "@/pages/product/CreateProduct/components/BindSpuSupplierModal";
import aliwangwang from "@/assets/images/aliwangwang.gif";
import {Access, Link} from "umi";
import {reqByBody, reqByUrl} from "@/modules/common/infra/api/common";
import SelectVender from '@/components/Select/SelectVender';
export type  productSupplierModalParams = {
  code: string,
  spuId: string
};
export default (props: productSupplierModalParams) =>  {
  const { code, spuId } = props;
  const { data, refresh, loading } = useRequest(() => reqByUrl('/purchase-mgmt-biz/purchase-center/supplier/goods/getSupplierGoods',{"code": code}).then((res) => res?.body));
  const [bindModal, setBindModal] = useState<boolean>(false);
  const [supplierGoodsData, setSupplierGoodsData] = useState<SupplierGoods>();
  const [supplierId, setSupplierId] = useState<any>();
  const [bindSupplierModal, setBindSupplierModal] = useState<boolean>(false);
  const [form] = Form.useForm();

  const removeGoods = (record: SupplierGoods)=>{
    Modal.confirm({
      content: "确定移除供应商商品吗？",
      onOk: function () {
        removeSupplierGoods(record?.id).then((result) => {
          if (result.status.success) {
            notification.success({message: '删除成功'});
            refresh();
          }
        });
      },
    });
    form.resetFields();
  }

  const editSupplierGoods = (record: any)=>{
    Modal.confirm({
      icon: false,
      width: 500,
      centered: true,
      content: (
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProForm.Group>
            <ProFormItem label="供应商" name="supplierName" initialValue={record?.supplierName} rules={[{ required: true, message: '请输入供应商' }]}> 
              <SelectVender disabled={record?.supplierName} useVenderName style={{ minWidth: 300 }}  />
            </ProFormItem>
            <ProFormText width={300} label="SKU" name="goodsSku" disabled={record?.goodsSku} initialValue={record?.goodsSku} rules={[{ required: true, message: '请输入SKU' }]}/>
            {/* <ProFormText width={300} label="上次采购价" name="lastPurchasePrice" initialValue={record?.lastPurchasePrice}/> */}
            <ProFormText width={300} label="采购价格" name="purchasePrice" initialValue={record?.purchasePrice} rules={[{ required: true, message: '请输入采购价格' }]}/>
            <ProFormText width={300} label="最小采购量" name="minPurchaseQuantity" initialValue={record?.minPurchaseQuantity}/>
            <ProFormText width={300} label="备货周期" name="purchaseCycle" initialValue={record?.purchaseCycle}/>
            <ProFormSelect
              width={300}
              name="currency"
              label="币种"
              initialValue={record?.currency}
              options={[
                {label: 'CNY',value:"CNY" },
                {label: 'USD',value:"USD" }
              ]}
            />
            <ProFormSelect
              width={300}
              name="isDefault"
              label="是否首选"
              initialValue={record?.isDefault?.toString() || '0'}
              options={[
                {label: '否',value: "0"},
                {label: '是',value: "1"}
              ]}
              allowClear={false}
            />
            <ProFormText hidden={true} name="supplierGoodsId" initialValue={record?.id}/>
          </ProForm.Group>
        </Form>
      ),
      onOk: function () {
        const params = form.getFieldsValue();
        if (record?.id){
          updateSupplierGoods(params).then((result) => {
            if (result.status.success) {
              notification.success({message: '修改成功'});
              refresh();
            }
          });
        }else {
          createSupplierGoods(params).then((result) => {
            if (result.status.success) {
              notification.success({message: '修改成功'});
              refresh();
            }
          });
        }

      },
    });
    form.resetFields();
  }

  const columns: ProColumns<SupplierGoods>[] = [
    {
      title: "供应商",
      dataIndex: 'supplierName',
      render: (v, record) => {
        return <>
          <span style={{fontSize: 13}}>
            <Link key="show" style={{fontSize: 12, color: '#009FCC'}} to={`/purchase/supplier/SupplierList/${record.supplierId}`}>
            {record.supplierName}
            </Link>
          </span>
          {record?.aliWangWangLink ? <><a href={record?.aliWangWangLink} target={"_blank"} rel="noreferrer"><img src={aliwangwang}/></a></> : null}
        </>;
      }
    },
    {
      title: "SKU",
      dataIndex: 'goodsSku',
      render: (v, record) => {
        return <>{record?.aliLink ? <a href={record?.aliLink} target={"_blank"} rel="noreferrer"><span style={{fontSize: 13}}>{record?.goodsSku}</span></a> : <span style={{fontSize: 13}}>{record?.goodsSku}</span>}</>;
      }
    },
    // {
    //   title: "上次采购价",
    //   dataIndex: "lastPurchasePrice",
    //   render: (v, record) => {
    //     let lastPurchasePrice=record?.lastPurchasePrice;

    //     if (lastPurchasePrice==null){
    //       lastPurchasePrice=0;
    //     }

    //     return lastPurchasePrice?.toFixed(2)
    //   }
    // },
    {
      title: "采购价",
      dataIndex: "purchasePrice",
      render: (v, record) => {
        return record?.purchasePrice?.toFixed(2)
      }
    },
    {
      title: "1688价格",
      dataIndex: "platformPurchasePrice",
      render: (v, record) => {
        return record?.platformPurchasePrice?.toFixed(2)
      }
    },
    {
      title: "币种",
      dataIndex: 'currency'
    },
    {
      title: "最小采购量",
      dataIndex: 'minPurchaseQuantity',
      render: (v, record) => {
        return record?.minPurchaseQuantity;
      }
    },
    {
      title: "是否绑定",
      dataIndex: 'platformProductId',
      render: (v, record) => {
        return record?.platformProductId  ? <Tag color={"green"}>已绑定</Tag> : <Tag color={"default"}>未绑定</Tag>;
      }
    },

    {
      title: "是否首选",
      dataIndex: 'isDefault',
      render: (v, record) => {
       return record?.isDefault == 1 ? "首选" : '--';
      }
    },
    {
      title: "操作",
      dataIndex: 'gmtCreate',
      render: (v, record) => {
        return (<>
          <Permission permissionKey={"purchase:product_manager:sku:supplierDelete"}>
            <Button type="primary" size={"small"} danger ghost style={{fontSize:12,borderRadius:5}} onClick={() =>  removeGoods(record)}>
              删除
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:product_manager:sku:supplierEdit"}>
            <Button type="primary" size={"small"} ghost  style={{fontSize:12,borderRadius:5,margin: "0 5px"}} onClick={() =>  editSupplierGoods(record)}>
              编辑
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:product_manager:sku:supplierBind"}>
            <Button type="primary" size={"small"} ghost  style={{fontSize:12,borderRadius:5,margin: "0 5px"}} onClick={function(){
              setSupplierId(record?.supplierId);
              setBindSupplierModal(true);
            }}>
              绑定供应商
            </Button>
          </Permission>
          <Permission permissionKey={"purchase:product_manager:sku:supplierBind"}>
            <Button type="primary" size={"small"}  style={{fontSize:12,borderRadius:5}} onClick={function(){
              setSupplierGoodsData(record);
              setBindModal(true);
            }}>
              绑定
            </Button>
          </Permission>
          </>)
      }
    }
  ];
  return (
    <>
      <Spin spinning={loading} style={{paddingTop:0}}>
        <Card style={{border:"none",marginBottom:10}} bodyStyle={{padding: 0}}>
          {code.indexOf("sku-")>=0 ?
            <Permission permissionKey={"purchase:product_manager:sku:supplierEdit"}>
            <Button
              size={"small"}
              type="primary"
              style={{marginRight:20,fontSize: 13,borderRadius:"5px"}}
              key="bundledAlibaba"
              onClick={()=>editSupplierGoods({goodsSku: code.replace("sku-", "")})}
            >
              添加
            </Button>
            </Permission> : null}
          <Permission permissionKey={"purchase:product_manager:sku:bindGoodsSupplier"}>
            <Button
              size={"small"}
              type="primary"
              style={{marginRight:20,fontSize: 13,borderRadius:"5px"}}
              onClick={()=>{
                setBindSupplierModal(true);
                setSupplierId(null)
              }}
            >
              绑定供应商
            </Button>
          </Permission>
        </Card>
        <Card bodyStyle={{padding: 0}}>
          <Table
            dataSource={data}
            columns={columns}
            rowKey="id"
            size={"small"}
            pagination = {false}
            // rowSelection={{
            //   onChange: (_, selectedRows) => {
            //     setSelectedRows(selectedRows);
            //   },
            // }}
          />
        </Card>
      </Spin>
      <Access  accessible={bindSupplierModal}>
        <BindSpuSupplierModal visible={bindSupplierModal} spuId={spuId} supplierId={supplierId} onCancel={()=>setBindSupplierModal(false)} onFinish={()=>setBindSupplierModal(false)}/>
      </Access>
      <Access accessible={bindModal}>
        <BindAliProductModal visible={bindModal} supplierData={supplierGoodsData} onCancel={() => setBindModal(false) } onFinish={()=>setBindModal(false)} />
      </Access>
    </>
  );
};


