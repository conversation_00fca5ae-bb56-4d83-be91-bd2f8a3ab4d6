import type {ModalProps} from 'antd';
import {Button, message, Modal, Table} from 'antd';
import type {ProColumns} from '@ant-design/pro-table';
import {useRequest} from "ahooks";
import {cancelInStorage, queryInStorageList} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import moment from "moment";
import {PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";

// 定义参数格式
export type queryInStorageModalProps = {
  orderData: PurchaseOrderGoods;
  onFinish: (values: any) => void;
  setInStorage: (values: any) => void;
} & ModalProps;

const QueryInStorageModal = (props: queryInStorageModalProps) => {
  const { onFinish, orderData,setInStorage, ...rest } = props;

  const { data,loading } = useRequest(() => queryInStorageList(orderData.orderId, orderData.sku).then(item=>item.body));

  const columns: ProColumns<any>[] = [
    {
      title: '操作人',
      align: 'left',
      dataIndex: 'createrName',
    },
    {
      title: '采购单号',
      align: 'left',
      dataIndex: 'orderCode',
    },
    {
      title: '是否取消',
      align: 'center',
      dataIndex: 'gmtCreate',
      render: (v,record)=>{
        return record?.isCancelInstorage == 1 ?<span style={{color:"red"}}>是</span>:'否';
      }
    },
    {
      title: 'SKU',
      align: 'left',
      dataIndex: 'sku',
    },
    {
      title: '入库数量',
      align: 'left',
      dataIndex: 'quantityReceived',
    },
    {
      title: '入库时间',
      align: 'left',
      dataIndex: 'gmtCreate',
      render: (v,record)=>{
       return moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm")
      }
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      render: (v, record) => {
        return (
          record?.isCancelInstorage != 1 ?
            (<Button type="primary" size={"small"} onClick={function () {
            Modal.confirm({
              title: '取消入库',
              onOk: async () => {
                cancelInStorage(record.id).then((result)=>{
                  if (result.status.success) {
                    message.success("取消成功");
                    setInStorage(result)
                  }
                });
              },
            });
          }
          }  ghost style={{fontSize:12,borderRadius:5}}>
            取消入库
          </Button>) : ''
        );
      }
    }
  ];

  return (
    <Modal {...rest}  title="入库记录" closable={false} width="800px" onOk={onFinish}>
      <Table
        dataSource={data}
        columns={columns}
        rowKey="id"
        pagination = {false}
        loading={loading}
      />
    </Modal>
  );
};

export default QueryInStorageModal;
