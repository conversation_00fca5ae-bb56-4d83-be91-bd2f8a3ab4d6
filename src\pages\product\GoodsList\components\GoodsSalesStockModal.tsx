import type { ModalProps } from 'antd';
import {Image, message, Modal, Space, Table, Tag} from 'antd';
import React, {useState} from 'react';
import {SkuWarehouse} from "@/pages/PurchasePlanList/PurchasePlanTableList/data";
import {getGoodsSalesStock} from "@/modules/goods/infra/api/goods";
import {useRequest} from "ahooks";
import {ColumnProps} from "antd/es/table";
import {SnippetsTwoTone} from "@ant-design/icons";
import {copyText} from "@/utils/comUtil";

// 定义参数格式
export type CurModalProps = {
  reqParams: any[];
  onFinish: (values: SkuWarehouse) => void;
} & ModalProps;

const GoodsSalesStockModal = (props: CurModalProps) => {
  const { onFinish, reqParams, ...rest } = props;
  const [goodsSalesRes, setGoodsSalesRes] = useState<any[]>();
  useRequest(() => getGoodsSalesStock({sku: reqParams.map(item=>{return item?.sku}).join(',')}).then((res) => {
    const body = res?.body;
    reqParams?.map(item=>{
      item['salesStock'] = body?.find(b=>b.sku==item.sku)?.salesStock;
    })
    setGoodsSalesRes(reqParams);
  }));

  const columns: ColumnProps<SkuWarehouse>[] = [
    {
      title: '仓库',
      align: 'center',
      dataIndex: 'warehouseName',
    },
    {
      title: '生命周期',
      align: 'center',
      dataIndex: 'goodsStatus',
    },
    {
      title: '通途可用',
      align: 'center',
      dataIndex: 'availableStockQuantity',
    },
    {
      title: '通途在途',
      align: 'center',
      dataIndex: 'intransitStockQuantity',
    },
    {
      title: '采购/转仓',
      align: 'center',
      dataIndex: 'purchaseIntransit',
    },
    {
      title: '新系统在途',
      align: 'center',
      dataIndex: 'newSysPcIntransit',
    },
    {
      title: '通途近7天销量',
      align: 'center',
      dataIndex: 'salesSevenDays',
    },
    {
      title: '通途近15天销量',
      align: 'center',
      dataIndex: 'salesFifteenDays',
    },
    {
      title: '通途近30天销量',
      align: 'center',
      dataIndex: 'salesThirtyDays',
    },{
      title: '通途未配货',
      align: 'center',
      dataIndex: 'outOfStock',
    }
  ];

  const mainColumns: ColumnProps<any>[] = [
    {
      title: '图片',
      dataIndex: 'index',
      align: "left",
      width: 250,
      render: (v, record) => {
        return <>
          <div>
            {record.mainSpecInfo ? <Image src={record.mainSpecInfo.image.fileUrl} width={80} /> : null}
            {record.skuImage ? <Image src={record.skuImage} width={80} /> : null}
          </div>
          <div style={{ fontWeight: "bold"}}>SKU: {record?.sku}&nbsp;<SnippetsTwoTone onClick={() => copyText(record.sku)}/></div>
          <div style={{fontSize:12, fontWeight: "bold"}}>标题: &nbsp;{record?.skuName}</div>
        </>;
      }
    },
    {
      title: '库存销量信息',
      dataIndex: 'salesStock',
      align: "center",
      render: (v, record) => {
        return (
          <Table
            size={"small"}
            bordered={true}
            dataSource={record?.salesStock}
            style={{margin: 4}}
            columns={columns}
            pagination={false}
          />
        );
      }
    },
  ];

  return (
    <Modal {...rest} title="库存/销量" closable={false} width="80%" onOk={onFinish}>
      <Table
        dataSource={goodsSalesRes}
        columns={mainColumns}
        showHeader={false}
        scroll={{y: 550}}
        size={"small"}
        rowKey="sku"
        pagination={false}
      />
    </Modal>
  );
};

export default GoodsSalesStockModal;
