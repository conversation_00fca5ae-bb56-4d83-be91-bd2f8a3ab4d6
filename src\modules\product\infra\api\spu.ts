import mallRequest from '@/utils/mallRequest';
import mallRequest1 from '@/utils/mallRequest1';
import mallApiConfig from 'config/mallApiConfig';
import type { Spu, SpuDetailInfo, SpuListItem } from '../../domain/spu';
import {SupplierGoods} from "@/modules/supplier/domain/vender";
import {Goods} from "@/modules/goods/domain/goods";

export type QueryProductParams = API.QueryPageParams &
  Pick<SpuListItem, 'categoryId' | 'brandId' | 'customCode'> & {
    applyDateEnd?: string;
    applyDateStart?: string;
  };

// 分页查询
export async function queryPageSpu(params?: QueryProductParams) {
  return mallRequest<API.ApiQueryPageResult<SpuListItem[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/pageQuery',
      method: 'POST',
      data: params,
    },
  );
}

export type CreateSpuParams = {};

// 创建Spu
export async function createSpu(params?: Spu) {
  return mallRequest<API.ApiBaseResult<boolean>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/newCreate',
      method: 'POST',
      data: params,
    },
  );
}
// 复制Spu
export async function copySpu(params: { spuId: string }) {
  return mallRequest<API.ApiBaseResult<boolean>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/copy',
      method: 'POST',
      data: params,
    },
  );
}

// 删除商品
export async function deleteSpu(params?: { spuId: string }) {
  return mallRequest<API.ApiBaseResult<boolean>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/delete',
      method: 'POST',
      data: params,
    },
  );
}

export type UpdateSpuParams = Partial<Spu> & { spuId: string };

// 编辑商品
export async function updateSpu(params?: UpdateSpuParams) {
  return mallRequest<API.ApiBaseResult<boolean>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/update',
      method: 'POST',
      data: params,
    },
  );
}

// 编辑商品
export async function getSpuDetail(spuId: string) {
  return mallRequest<API.ApiBaseResult<SpuDetailInfo>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: `/purchase-mgmt-biz/pd/spu/get/${spuId}`,
      method: 'GET',
    },
  );
}

//下载商品模板
export async function downloadGoodsTemplate() {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/downloadGoodsTemplate',
      method: 'GET',
      responseType: 'blob',
    },
  );
}

//下载商品编辑模板
export async function downloadGoodsEditTemplate() {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/downloadGoodsEditTemplate',
      method: 'GET',
      responseType: 'blob',
    },
  );
}

//下载商品模板
export async function downloadSkuTagsTemplate() {
  return mallRequest1<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/sku/downloadSkuTagsTemplate',
      method: 'GET',
      responseType: 'blob',
    },
  );
}

//删除商品
export async function removeSku(skuId: string | undefined) {
  return mallRequest<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: `/purchase-mgmt-biz/pd/sku/removeSku`,
      method: 'POST',
      params:{
        skuId
      }
    },
  );
}


//创建sku
export async function createSku(data: Goods) {
  return mallRequest<API.ApiBaseResult<string>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: `/purchase-mgmt-biz/pd/sku/create`,
      method: 'POST',
      data
    },
  );
}


//导入商品
export async function importGoods(link?: string,flag?: string) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/pd/spu/importGoods',
    params:{
      link,flag
    }
  });
}

//导入商品标签
export async function importSkuTags(link?: string,addOrDel?: string) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/pd/sku/importSkuTags',
    params:{
      link,
      addOrDel
    }
  });
}

//导入商品图片
export async function importSkuImage(link?: string) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/pd/spu/importSkuImage',
    params:{
      link
    }
  });
}


