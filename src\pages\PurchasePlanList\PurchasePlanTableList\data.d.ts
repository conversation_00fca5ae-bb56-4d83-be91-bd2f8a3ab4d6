export type PurchasePlanListItem = {
  priceAmount: number;
  supplierName: string;
  aliWangWangLink: string;
  planGoodsInfos: PlanGoodsInfo[];
}

export type PlanGoodsInfo = {
  goodsRemark: string;
  id: number;
  title: string;
  isUrgent: boolean;
  isManual: boolean;
  isReject: boolean;
  organizationId?: number;
  purchasePlanId: number;
  purchasePrice: number;
  purchaseUsername: string;
  lastPurchasePrice: number;
  platformPurchasePrice: number;
  auditor: string;
  auditStatus: string;
  auditLabel: string;
  purchaseQuantity: number;
  purchaseRemark: string;
  purchaseUid?: number;
  salesWarehouse: string;
  gmtCreate: string;
  gmtModified: string;
  sku: string;
  link: string;
  status: number;
  supplierId: string;
  supplierName: string;
  totalPrice: number;
  purchaseEntity: string;
  warehouseInfos: WarehouseInfo[];
  skuWarehouseList: SkuWarehouse[];
  needQc: boolean;
}

export type WarehouseInfo = {
  goodsRemark: string;
  id: number;
  isUrgent: boolean;
  organizationId: boolean;
  purchasePlanId: number;
  purchasePrice: number;
  purchaseQuantity: number;
  purchaseRemark: string;
  purchaseUid: number;
  salesWarehouse: string;
  sku: string;
  status: string;
  supplierId: string;
  supplierName: string;
  totalPrice: number;
}


export type SkuWarehouse = {
  sku: string;
  warehouseName: string;
  intransitStockQuantity: string;
  availableStockQuantity: string;
  salesSevenDays: string;
  salesFifteenDays: string;
  salesThirtyDays: string;
  purchaseIntransit: string;
  newSysPcIntransit: string;
  outOfStock: string;
  goodsStatus: string;
}
