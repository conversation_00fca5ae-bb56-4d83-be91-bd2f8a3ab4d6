import React, { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Popconfirm, Row, Spin } from 'antd';
import ProTable from '@ant-design/pro-table';
import TableTabs from '@/components/TableTabs';
import { useColumn } from './hook';
import useLocael from '@/hooks/useLocael';
import { cloneDeep } from 'lodash';
import './index.less';
import type { FormInstance } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import type { SortOrder } from 'antd/lib/table/interface';
import type { CommonTableProps } from './type';
import { useTableSearch } from './useTableHook';

// 2 Function
function CommonTable<Data extends Record<string, any> = any>(props: CommonTableProps<Data>) {
  const { tableRest = {}, columns, fetchRequest, tableRef, bg = '#e3effb', renderProp, actionMerge, isUnLabel, labelWidth, flex } = props;
  const [dataSource, setDataSource] = useState<Data[]>([]);
  const [count, setCount] = useState(0);
  const [chooseCount, setChooseCount] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const { $t, isEn } = useLocael();
  const contentRef = useRef<any>();
  const { rowSpan } = useTableSearch(contentRef, tableRest.search !== false, isUnLabel);

  // 0) 搜索栏文字宽度
  const _labelWidth = useMemo(() => {
    if (!labelWidth) return isEn ? 120 : 80;
    if (Array.isArray(labelWidth)) {
      return isEn ? labelWidth[1] : labelWidth[0];
    } else {
      return Number(labelWidth);
    }
  }, [labelWidth, isEn]);

  // 1) Util
  const createColumn = useColumn<Data>();
  const renderRow = useCallback(
    (item: any, rowColumns: any[]) => {
      const { rowTableRest = {} } = props;
      // 1 设置数据源
      const childDataSource = renderProp ? item[renderProp] : [item];
      // 2 合并操作栏
      if (actionMerge) {
        const actionColumn = rowColumns[rowColumns.length - 1];
        if (actionColumn['dataIndex'] === '_Action') {
          actionColumn['onCell'] = (_: any, index: number) => {
            if (index === 0) return { rowSpan: childDataSource.length };
            return { rowSpan: 0 };
          };
        }
        // 3 传递父 Row
        childDataSource[0].faRow = cloneDeep(item);
      }
      return (
        <ProTable
          className="row-table"
          showHeader={false}
          columns={rowColumns}
          dataSource={childDataSource}
          pagination={false}
          search={false}
          toolBarRender={false}
          {...rowTableRest}
        />
      );
    },
    [props, renderProp],
  );

  // 2) Tabs
  const [tab, setTab] = useState<string>(props.tabOption ? props.tabOption[0].key : '');
  useEffect(() => {
    actionRef.current?.setPageInfo && actionRef.current?.setPageInfo({ current: 1 });
    actionRef.current?.reload();
  }, [tab]);

  // 3) Columns
  const mockColumns: ProColumns<Data>[] = useMemo(() => {
    return [
      createColumn('商品', 'userId'),
      createColumn('运费', 'plaformId'),
      createColumn('金额', 'transportId'),
      createColumn('买家', 'packStatus'),
      createColumn('订单状态', 'createTime'),
      createColumn('备注', 'trackNumber'),
    ];
  }, [createColumn]);
  const tableColumns: ProColumns<Data>[] = useMemo(() => {
    // 1 获取数据
    const usedColumns = columns ? columns : mockColumns;
    const { insertBefore, insertAfter, actions } = props;

    // 2 插入操作项
    if (actions && !usedColumns.some((v) => v.dataIndex === '_Action')) {
      // 1 获取插入之后的数据
      const afterArr = usedColumns.slice(usedColumns.length - (actions.lastindex || 0), usedColumns.length);
      const AlignMap = {
        start: 'left',
        end: 'right',
        center: 'center',
        'space-around': 'center',
        'space-between': 'center',
        'space-evenly': 'center',
      };
      // 2 插入指定位置
      usedColumns.splice(
        usedColumns.length - (actions.lastindex || 0),
        1,
        createColumn($t('操作'), '_Action', {
          render: (text, row, index) => {
            return (
              <Row justify={actions.align} className={`common-table-actions-${actions.layout || 'tile'}`}>
                {actions.items.map((action) => {
                  const { show, name, title, color, onAction, disabled, render } = action;
                  const _disabled = disabled && disabled(row);
                  // 如果存在 title，渲染为气泡窗提示
                  if (title) {
                    return show === undefined || show(row) ? (
                      <Popconfirm
                        title={typeof title === 'function' ? title(row) : title}
                        onConfirm={async () => {
                          if (onAction) {
                            await onAction(row);
                            actionRef.current?.reload();
                          }
                        }}
                      >
                        <a key={name} style={{ color: _disabled ? '#bbbbbb' : color }}>
                          {name}
                        </a>
                      </Popconfirm>
                    ) : null;
                  }
                  // 如果存在 render, 自定义渲染
                  if (render) {
                    return show === undefined || show(row) ? render(row, index) : null;
                  }
                  // 无则正常判断返回
                  return show === undefined || show(row) ? (
                    <a key={name} style={{ color: _disabled ? '#bbbbbb' : color }} onClick={() => !_disabled && onAction && onAction(row)}>
                      {name}
                    </a>
                  ) : null;
                })}
              </Row>
            );
          },
          // @ts-ignore
          align: AlignMap[actions.align],
          fixed: Boolean(insertBefore || insertAfter) ? undefined : actions.fixed || 'right',
          width: actions.width || 200,
        }),
      );
      // 3 放置剩余数据
      usedColumns.push(...afterArr);
    }

    // 3 排除不显示的列
    const filterColumns = usedColumns.filter((v) => !v.hideInTable);
    const initIndex = usedColumns.findIndex((v) => !v.hideInTable);

    // 4 返回使用的 columns
    return usedColumns?.map((item, itemIndex) => {
      // 1 判断是否有插入元素
      if (insertBefore || insertAfter) {
        return {
          ...item,
          onCell: () => ({
            colSpan: itemIndex === initIndex ? filterColumns.length : 0,
          }),
          render: (_, row: any) => (
            <>
              <div className="before-box">{insertBefore && insertBefore(row)}</div>
              {renderRow(row, filterColumns)}
              <div className="after-box">{insertAfter && insertAfter(row)}</div>
            </>
          ),
        };
      }
      // 2 无则直接返回
      return item;
    });
  }, [$t, columns, createColumn, mockColumns, props, renderRow]);

  // 3.1) 初始化滚动值
  const { scrollX, scrollY, autoScrollX, fixedTop } = props;
  const _scrollX = useMemo(
    () =>
      tableColumns
        .filter((v) => !v.hideInTable)
        .map((v) => v.width || 100)
        .reduce((pre, next) => Number(pre) + Number(next)),
    [tableColumns],
  );
  const _scroll = useMemo(() => {
    // 1 校验
    if (fixedTop !== undefined) return undefined;
    // 2 设置初始值
    const scrollVal: any = {};
    // 3 设置 y 值
    if (flex || scrollY) {
      scrollVal.y = scrollY || 'calc(100% - 31px)';
    }
    // 4 设置 x 值
    if (autoScrollX || props.actions || scrollX) scrollVal.x = scrollX || _scrollX;
    // 5 返回值
    return scrollVal;
  }, [_scrollX, autoScrollX, fixedTop, flex, props.actions, scrollX, scrollY]);

  // 4) Request
  const request = async (params: any, sort: Record<string, SortOrder>, filter: Record<string, React.ReactText[] | null>) => {
    let data: any = [];
    let total = dataSource?.length || 0;

    setLoading(true);
    // 1 无请求函数返回 Mock 数据
    if (!fetchRequest) {
      data = new Array(params.pageSize).fill('').map(() => ({
        id: new Array(8)
          .fill('')
          .map(() => Math.ceil(Math.random() * 10))
          .join(''),
        userId: '00001111',
        plaformId: '00001111',
        transportId: '00001111',
        transportStatus: Math.ceil(Math.random() * 4),
        createTime: 1692778184561,
        trackNumber: 'NJ2312213',
      }));
    }
    // 2  执行函数，发送请求获取数据
    else {
      setLoading(true);
      // 过滤字符串为空的值
      Object.keys(params).forEach((key) => {
        if (typeof params[key] === 'string' && !params[key]) {
          delete params[key];
        }
      });
      const res = await fetchRequest({ tab: tab || undefined, ...params }, sort, filter);
      setLoading(false);
      data = res.data;
      total = res.total || 0;
    }
    // 3 设置数据源
    setDataSource(data);
    setCount(total);
    // 4 返回值
    return { data, total };
  };

  // 5) Export
  useImperativeHandle(tableRef, () => ({
    tab,
    dataSource,
    actionRef: actionRef.current,
    formRef: formRef.current,
    reload: () => actionRef.current?.reload(),
    setPageInfo: (params) => actionRef.current?.setPageInfo && actionRef.current?.setPageInfo(params),
    reloadTable: () => {
      actionRef.current?.reload();
      actionRef.current?.clearSelected && actionRef.current?.clearSelected();
    },
    initSelect: (keys: React.Key[]) => {
      setSelectedRowKeys(keys);
    },
    initTable: () => {
      actionRef.current?.clearSelected && actionRef.current?.clearSelected();
      setDataSource([]);
    },
    resetAndReloadTable: () => {
      actionRef.current?.clearSelected && actionRef.current?.clearSelected();
      // actionRef.current?.reloadAndRest && actionRef.current?.reloadAndRest();
      actionRef.current?.reset && actionRef.current?.reset();
    },
    // 设置加载
    setLoading: setLoading,
  }));

  // 6) DataSource
  useEffect(() => {
    if (props.dataSource) {
      setDataSource(props.dataSource.slice());
    }
  }, [props.dataSource]);

  // 7) Table
  const TableCom = (
    <ProTable<Data>
      actionRef={actionRef}
      dataSource={props.dataSource ? dataSource : undefined}
      tableClassName={`common-table ${props.fixedTop !== undefined ? 'fixed-top-table' : ''}`}
      tableStyle={{
        // @ts-ignore
        '--fixedTop': typeof props.fixedTop === 'number' ? `${props.fixedTop}px` : '0',
      }}
      rowKey={(props.rowKey as string) || 'id'}
      rowClassName="common-row"
      scroll={_scroll}
      // @ts-ignore
      style={{ '--bg': bg || '#fafafa' }}
      // 1 分页
      pagination={
        props.pagination === undefined
          ? {
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50'],
            defaultPageSize: Number(props.defaultPageSize) || 10,
            // current: 1,
          }
          : props.pagination
      }
      // 2 选择项
      rowSelection={
        props.onSelection
          ? {
            selectedRowKeys,
            type: 'checkbox',
            // (selectedRowKeys: React.Key[], selectedRows: Data[], info:
            onChange: (selectKeys, ...args) => {
              // 1 选中值
              setSelectedRowKeys(selectKeys);
              // 2 是否显示选中数量
              if (props.showChooseCount) {
                setChooseCount(selectKeys.length);
              }
              // 3 调用传入值
              props.onSelection && props.onSelection(selectKeys, ...args);
            },
          }
          : undefined
      }
      // 3 搜索栏
      search={
        props.search === undefined
          ? ({
            defaultCollapsed: false,
            span: rowSpan,
            labelWidth: isUnLabel ? 0 : _labelWidth,
          } as any)
          : props.search
      }
      // 4 请求函数(不存在 dataSource 则绑定)
      request={props.fetchRequest ? (request as any) : undefined}
      // 5 表格列
      columns={tableColumns}
      // 6 搜索栏
      toolBarRender={(action, rows) => [props.toolBarRender ? props.toolBarRender(action, rows) : null]}
      toolbar={{
        title: (props.showChooseCount && chooseCount && `选中数量：${chooseCount}`) || '',
        subTitle: props.showCount && `${$t('订单总数')}：${count}`,
      }}
      // 7 form
      formRef={formRef}
      loading={false}
      // 更多 ProTable 选项
      {...tableRest}
    />
  );

  return (
    <div className={flex ? 'common-table-wrapper flex-table' : 'common-table-wrapper'}>
      <Spin spinning={loading}>
        <div className="common-table-spin" ref={contentRef}>
          {
            props.tabOption ? (
              <TableTabs
                tabs={props.tabOption}
                activeKey={tab}
                onTabClick={(_tab) => {
                  setTab(_tab);
                  // 1 选中值
                  setSelectedRowKeys([]);
                  // 2 是否显示选中数量
                  if (props.showChooseCount) {
                    setChooseCount(0);
                  }
                  // 3 调用传入值
                  props.onSelection && props.onSelection([], [], {} as any);
                }}
              >
                {TableCom}
              </TableTabs>
            ) : (
              TableCom
            )
            // TableCom
          }
        </div>
      </Spin>
    </div>
  );
}

export default CommonTable;
