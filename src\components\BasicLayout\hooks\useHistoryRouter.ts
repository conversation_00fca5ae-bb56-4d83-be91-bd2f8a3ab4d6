import { RouteContext } from '@ant-design/pro-layout';
import { findIndex } from 'lodash';
import React from 'react';
import { useContext, useState, useEffect } from 'react';
import { useLocation, history } from 'umi';
import type { RouterItemData } from '..';
import { v4 as uuidV4 } from 'uuid';
import getServiceToken from '@/hooks/getServiceToken';

export const HistoryRouterContext = getServiceToken(useHistoryRouter);

// 提供给组件调用函数
export const useHistoryRouterContext = () => {
  return useContext(HistoryRouterContext);
}

export function useHistoryRouter(children: React.ReactNode) {
  const { currentMenu } = useContext(RouteContext);
  const location = useLocation();
  const [routers, setRouters] = useState<RouterItemData[]>([]);
  const [currentRouter, setCurrentRouter] = useState<string>();

  useEffect(() => {
    if (currentMenu?.redirect) {
      history.push(currentMenu.redirect);
    }
    const strArray = location.pathname.split('/').filter((v: string) => !!v);
    if (strArray.length) {
      const index = findIndex(routers, { path: location.pathname });
      if (index === -1 && currentMenu && currentMenu.name) {
        const name = currentMenu?.name || '';
        setRouters([
          ...routers,
          {
            name,
            path: location.pathname,
            key: location.pathname,
            content: children,
          },
        ]);
      }
      if (index !== -1 && !routers[index].content) {
        const newRouters = [...routers];
        newRouters[index].content = children;
        setRouters(newRouters);
      }

      if (currentRouter !== location.pathname) {
        setCurrentRouter(location.pathname);
      }
    }
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
  }, [location]);

  useEffect(() => {
    if (routers.length) {
      const index = findIndex(routers, { path: currentRouter });
      if (index === -1) {
        setCurrentRouter(routers[0].path);
      }
    }
  }, [routers]);

  const clear = () => {
    setRouters([]);
  };


  // 页面刷新
  const refresh = () => {
    const index = findIndex(routers, { path: currentRouter });
    const routes = routers[index];
    routes.content = React.createElement(React.Fragment, { key: uuidV4() }, children);
    routers[index] = routes;
    setRouters([...routers])
  }

  const removeByKey = (key: string) => {
    const newArray = routers.filter((r: RouterItemData) => r.key !== key);
    if (key === currentRouter) {
      const index = findIndex(routers, { path: currentRouter });
      if (index !== -1) {
        const currentIndex = index ? index - 1 : 0;
        setCurrentRouter(newArray[currentIndex].path);
        history.push(newArray[currentIndex].path);
      }
    }
    setRouters(newArray);
  };

  const removeOther = (key: string) => {
    const newArray = routers.filter((r: RouterItemData) => r.key === key);
    setRouters(newArray);
  };

  // 刷新指定页面
  const refreshKey = (key: string) => {
    const index = findIndex(routers, { path: key });
    if(index === -1){
      return;
    }
    const newRouters = [...routers];
    const routes = newRouters[index];
    routes.content = undefined;
    newRouters[index] = routes;
    setRouters([...newRouters]);
  }

  // 跳转页面并且更新内容
  const historyPushAndRefresh = (pathName: string) => {
    refreshKey(pathName);
    history.push(pathName);
  }

  return {
    routers,
    currentRouter: currentRouter,
    setCurrentRouter,
    setRouters,
    clear,
    removeByKey,
    removeOther,
    refresh,
    refreshKey,
    historyPushAndRefresh
  };
}
