


import { Department, DepartmentTree } from '@/modules/user-center/domain/department';
import { batchUpdate, BatchUpdateParams, getDeptTreeByEndpoint } from '@/modules/user-center/infra/organize';
import { GlobalWindow } from '@/types/global';
import { ModalFormProps, ProFormTreeSelect } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type CreateFormProps = Pick<ModalFormProps, 'visible' | 'onVisibleChange' | 'trigger' | 'onFinish'> & {
  deptIds?: string[];
};

const formattedDepartTreeData = (data: DepartmentTree[] = []): any[] => {
  if (!data.length) {
    return []
  }
  return data.map(item => {
    const children = formattedDepartTreeData(item.children || [])
    return {
      title: item.name,
      value: item.deptId,
      children,
    }
  })
}

const BatchUpdateDepartmentModal: React.FC<CreateFormProps> = (props) => {
  const [form] = Form.useForm();
  const { deptIds, onFinish, ...rest } = props;

  useEffect(() => {
    if (deptIds?.length) {
      form.setFieldsValue({
        deptIds
      })
    }
  }, [deptIds])

  const onSubmit = async (value: BatchUpdateParams) => {
    const res = await batchUpdate(value)
    if (res.status.success) {
      await onFinish?.(value);
    }
    return res.status.success;
  }

  return (
    <ModalForm
      title="批量编辑部门"
      width={600}
      form={form}
      {...rest}
      onFinish={onSubmit}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormTreeSelect
        label="上级部门"
        name="parentId"
        rules={[{ required: true, message: '请选择上级部门' }]}

        request={() => {
          return getDeptTreeByEndpoint({
            endpoint: (window as GlobalWindow).endpoint as string,
            // parentId,
          }).then(res => {
            return formattedDepartTreeData(res.body || [])
          })
        }}
        fieldProps={{
          allowClear: true,
          treeLine: true,

        }}
      />
      <ProFormText hidden name='deptIds'></ProFormText>
    </ModalForm>
  );
};

export default BatchUpdateDepartmentModal;
