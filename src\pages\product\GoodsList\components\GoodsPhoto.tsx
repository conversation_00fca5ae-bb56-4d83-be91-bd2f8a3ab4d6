import type { CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import { Button, Card, Checkbox, Drawer, Space, Image, message } from "antd";
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { forwardRef, memo, useImperativeHandle, useMemo, useState } from 'react';
import { SkuMap } from '../typs';
import { batchDownloadSkuImages, getSkuProductImagesBySku } from '../api';
import { cloneDeep, isNumber } from 'lodash';
import { SnippetsTwoTone } from '@ant-design/icons';
import { copyText } from '@/utils/comUtil';
import { DownFile, downZip, formatDate } from '@/utils/utils';
const FileSaver = require('file-saver')
const list = [
  {
    title: '基础产品图片组',
    detail: '用于沉淀产品基础资料，QC使用等',
    arr: [
      {
        name: '正面图',
        img: ''
      },
      {
        name: '右侧面',
        img: ''
      },
      {
        name: '背面图',
        img: ''
      },
      {
        name: '左侧面',
        img: ''
      },
      {
        name: '顶部图',
        img: ''
      },
      {
        name: '底部图',
        img: ''
      },
      {
        name: '三面立体图',
        img: ''
      },
    ]
  },
  {
    title: 'GPSR外包装标签图组',
    detail: '用于满足欧盟电商商品申报规范',
    arr: [
      {
        name: '正面图',
        img: ''
      },
      {
        name: '右侧面',
        img: ''
      },
      {
        name: '背面图',
        img: ''
      },
      {
        name: '左侧面',
        img: ''
      },
      {
        name: '顶部图',
        img: ''
      },
      {
        name: '底部图',
        img: ''
      },
      {
        name: '亚马逊GPSR外包装标签图',
        img: '',
      },
      {
        name: '速卖通GPSR外包装标签图',
        img: '',
      },
    ]
  },
  {
    title: '自定义标签组（未分类）',
    detail: '产品比较复杂或其他拍照需求，拍照后用标签自定义管理',
    arr: [
      {
        name: '',
        img: ''
      },
    ]
  }
];

export type GoodsPhotoActions = {
  onOpen: (sku: string) => void;
};

const GoodsPhoto = forwardRef((props: { onConfirm?: (rows: CSI[]) => void }, ref) => {
  const [open, setOpen] = useState(false)
  const [data, setData] = useState<SkuMap>()

  const actions: GoodsPhotoActions = {
    onOpen: (sku: string) => {
      onOpen(sku)
    }
  };

  // 1) Export
  useImperativeHandle(ref, () => actions);

  // 2) Anction
  const onOpen = async (sku: string) => {
    setOpen(true)
    const res = await getSkuProductImagesBySku(sku)
    setData(res.body)
  };

  const onChange = (e: CheckboxChangeEvent, pIndex: number, cIndex: number) => {
    if (!data) return;
    const tempData = cloneDeep(data)
    tempData.imageTagGroups[pIndex].imagesTags[cIndex].checked = e.target.checked
    setData(tempData)
  }

  const selectDown = (num?: number) => {
    // 取消选中
    if (num == 0) {
      const tempData = cloneDeep(data)
      tempData?.imageTagGroups.forEach(item => {
        item.imagesTags.forEach(v => {
          v.checked = false
        })
      })
      setData(tempData)
      return;
    }

    // 下载全部 或 选中的
    if (num == 1 || !isNumber(num)) {
      const imageMap: DownFile[] = []
      data?.imageTagGroups.forEach(item => {
        item.imagesTags.forEach(v => {
          if (v.imageUrl && num == 1) {
            imageMap.push({
              name: `${data.sku}_${item.tagGroupName}_${v.tagName}`,
              url: v.imageUrl
            })
            return;
          }
          if (v.imageUrl && num != 1 && v.checked) {
            imageMap.push({
              name: `${data.sku}_${item.tagGroupName}_${v.tagName}`,
              url: v.imageUrl
            })
          }
        })
      })
      if (!imageMap.length) {
        message.warn('暂无图片')
        return
      }
      if (imageMap.length <= 2) {
        imageMap.forEach((item) => {
          FileSaver.saveAs(item.url, `图片管理-${item.name}.jpg`)
        })
        return
      }
      downZip(imageMap, `图片管理-${data?.sku || ''}-${formatDate(+new Date, 'MM-DD')}`)
      return;
    }
  }

  return (
    <Drawer
      closable
      destroyOnClose
      title="图片管理"
      placement="right"
      open={open}
      onClose={() => setOpen(false)}
      width={700}
      footerStyle={{ textAlign: 'right' }}
      footer={
        <Space align="baseline">
          <Button onClick={() => selectDown(0)}>取消选中</Button>
          <Button type="primary" onClick={() => selectDown()}>下载选中的</Button>
          <Button type="primary" onClick={() => selectDown(1)}>下载全部</Button>
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <Card>
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            <div style={{ 'color': '#999', flexBasis: 75 }}>SKU编码：</div>
            <span>{data?.skuInfo?.customCode}
              &nbsp;<SnippetsTwoTone onClick={() => copyText(data?.skuInfo?.customCode)} />
            </span>
          </div>
          <div style={{ display: 'flex' }}>
            <div style={{ 'color': '#999', flexBasis: 75 }}>产品名称：</div>
            <span style={{ flex: 1 }}>{data?.skuInfo?.skuName}</span>
          </div>
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            <div style={{ 'color': '#999', flexBasis: 75 }}>产品规格：</div>
            <span style={{ flex: 1 }}>{data?.skuInfo?.specs}</span>
          </div>
        </Card>
        {
          data?.imageTagGroups.map((item, pIndex) => <Card title={item.tagGroupName} key={item.tagGroupId} extra={<span style={{ 'color': '#999' }}>{item.description}</span>}  >
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {
                item?.imagesTags.map((v, cIndex) => (<div style={{ margin: '0 0 10px 10px' }} key={v.tagId}>
                  <div style={{ border: '1px solid #eee', borderRadius: '5px', display: 'flex', alignItems: 'center', justifyContent: 'center', overflow: 'hidden', width: 80, height: 80 }}>
                    {!!v.imageUrl && <Image src={v.imageUrl} width={80} height={80} />}
                    {!v.imageUrl && <span style={{ color: '#999', fontSize: '12px' }}>暂无图片</span>}
                  </div>
                  <div style={{ marginTop: '5px' }}><Checkbox disabled={!v.imageUrl} checked={v.checked} onChange={(e) => onChange(e, pIndex, cIndex)}>{v.tagName}</Checkbox></div>
                </div>))
              }
            </div>
          </Card>)
        }
      </Space>
    </Drawer>
  );
});

export default memo(GoodsPhoto);
