import CommonModal from '@/components/Common-UI/CommonModal';
import { forwardRef, memo, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import CommonTable from '@/components/Common-UI/CommonTable';
import { queryCSIList } from '../api';
import { Image } from "antd"
import type { ModalAction, CommonModalProps } from '@/components/Common-UI/CommonModal';
import type { CommonTableAction, CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import type { CSI } from '../api';
import SelectInput, { solveSelect } from '@/components/Select/SelectInput';
import SelectEntity from '@/components/Select/SelectEntity';

type TableProps = CommonTableProps<CSI>;

const AddReamrk = forwardRef((props: { onConfirm?: (rows: CSI[]) => void }, ref) => {
  const setSearch = useSearch<CSI>();
  const setColumn = useColumn<CSI>();
  const tableRef = useRef<CommonTableAction>();
  const modalRef = useRef<ModalAction<CSI>>();
  const [selecteRows, setSelectedRows] = useState<CSI[]>([]);
  const [purchaseEntity, setPurchaseEntity] = useState("")

  // 1) Export
  useImperativeHandle(ref, () => ({
    open: modalRef?.current?.open,
  }));

  // 2) Anction
  const onOpen = (_purchaseEntity: string) => {
    tableRef.current?.resetAndReloadTable()
    setPurchaseEntity(_purchaseEntity)
  };
  const onConfirm = () => {
    props.onConfirm && props.onConfirm(selecteRows);
    modalRef.current?.close();
  };

  // 3) Request
  const fetchRequest = useMemo<TableProps['fetchRequest']>(() => {
    return async (params: any) => {
      // 1 获取数据
      const { current, pageSize, id, ...rest } = params;
      const pageCondition = { pageSize, pageNum: current };
      // 2 查询参数
      solveSelect(id, rest);
      const { body } = await queryCSIList({ pageCondition, ...rest, supplierId: purchaseEntity, saleState: "ON_SALE" });
      // 3 返回值
      return {
        data: body?.items || [],
        total: body?.pageMeta?.total || 0,
      };
    };
  }, [purchaseEntity]);

  // 4) Column
  const columns = useMemo(() => {
    return [
      setSearch("商家", "supplierId", { renderFormItem: () => <SelectEntity disabled={true} value={purchaseEntity} /> }),
      setSearch("商品名称", "title"),
      setSearch('', 'id', {
        renderFormItem: () => (
          <SelectInput
            options={[
              {
                value: 'supplierSpuId',
                label: '商家SPUID',
              },
              {
                value: 'supplierSkuId',
                label: '商家SKUID',
              },
            ]}
          />
        ),
      }),
      setColumn('商品图片', 'mainImages', {
        render: (_, row) => <Image src={row?.mainImages?.[0]} width={80} height={80} />,
        width: 120
      }),
      setColumn('商品信息', '_info', {
        render: (_, row) => {
          return <div>
            <div>{row.title}</div>
            <div>类目：{row.categoryName}</div>
            <div>品牌：{row.brandName}</div>
            <div>规格：{row?.specs?.map(v => `${v.specName}/${v.specValue}`).join("、")}</div>
          </div>
        }
      }),
      setColumn('', '', {
        render: (_, row) => {
          return <div>
            <div>商家SPU：{row?.supplierSpuId || "-"}</div>
            <div>商家SKU：{row?.supplierSkuId || "-"}</div>
          </div>
        }
      }),
    ];
  }, [purchaseEntity, setColumn, setSearch]);

  return (
    <CommonModal title="新增商品" width={800} modalRef={modalRef} onOpen={onOpen} onConfirm={onConfirm}>
      <div className="add-modal">
        <CommonTable<CSI>
          rowKey="saleItemId"
          scrollY={600}
          tableRef={tableRef}
          fetchRequest={fetchRequest}
          columns={columns}
          onSelection={(selectedRowKeys, _selectedRows) => {
            setSelectedRows(_selectedRows || [])
          }}
        />
      </div>
    </CommonModal>
  );
});

export default memo(AddReamrk);
