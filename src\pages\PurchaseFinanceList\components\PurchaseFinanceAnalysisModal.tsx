import {Card, Col, Modal, ModalProps, Row, Table} from "antd";
import React, {Suspense} from "react";
import {useRequest} from "ahooks";
import {SnippetsTwoTone} from "@ant-design/icons";
import {financeAnalysis} from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import {copyText} from "@/utils/comUtil";
// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: any) => void;
} & ModalProps;

const PurchasePlanAnalysisModal = (props: CreateModalProps) => {
  const { onFinish, ...rest } = props;
  const { loading, data } = useRequest(() => financeAnalysis().then(res=>res.body));

  const columns = [
    {
      title: '采购主体',
      dataIndex: 'purchaseEntity',
      align: "center",
      key: 'purchaseEntity',
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      align: "center",
    },
    {
      title: '总支付金额',
      align: "center",
      dataIndex: 'amount',
      key: 'amount'
    }
  ];
  const columns1 = [
    {
      title: '收款人',
      dataIndex: 'accountName',
      align: "center",
      key: 'accountName',
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      align: "center",
    },
    {
      title: '总支付金额',
      align: "center",
      dataIndex: 'amount',
      key: 'amount'
    },
  ];

  return <Modal width={"60%"} closable={false} {...rest} style={{padding: 0}} bodyStyle={{padding: 0, margin: 0}} onOk={onFinish}>
    <Row style={{padding:0,height:80}}>
      {/*<Col span={4}>*/}
      {/*  <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>*/}
      {/*    <p style={{margin:0}}>申请付定金</p>*/}
      {/*    <b>0.00（0）</b>*/}
      {/*  </Card>*/}
      {/*</Col>*/}
      {/*<Col span={4}>*/}
      {/*  <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>*/}
      {/*    <p style={{margin:0}}>申请付余额</p>*/}
      {/*    <b>0.00（0）</b>*/}
      {/*  </Card>*/}
      {/*</Col>*/}
      <Col span={6}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>付款进行中</p>
          <b>{data?.sumByPayStatus?.ps40?.payAmount?.toFixed(2)  || 0.00}（{data?.sumByPayStatus?.ps40?.payCount || 0}）</b><SnippetsTwoTone onClick={() => copyText(data?.sumByPayStatus?.ps40?.payAmount?.toFixed(2))}/>
        </Card>
      </Col>
      <Col span={6}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>账期待付款</p>
          <b>{data?.sumByPayStatus?.ps35?.payAmount?.toFixed(2) || 0.00}（{data?.sumByPayStatus?.ps35?.payCount || 0}）</b>
        </Card>
      </Col>
      <Col span={6}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>账期待还款</p>
          <b>{data?.sumByPayStatus?.ps45?.payAmount?.toFixed(2) || 0.00}（{data?.sumByPayStatus?.ps45?.payCount||0}）</b>
        </Card>
      </Col>
      <Col span={6}>
        <Card bordered={false} style={{textAlign:"center", padding:"0px"}}>
          <p style={{margin:0}}>驳回</p>
          <b>{data?.sumByPayStatus?.ps50?.payAmount?.toFixed(2) || 0.00}（{data?.sumByPayStatus?.ps50?.payCount||0}）</b>
        </Card>
      </Col>
    </Row>

    <Row style={{padding:0}}>
      <Col xl={12} lg={24} md={24} sm={24} xs={24}>
        <Suspense fallback={null}>
          <Card
            loading={loading}
            bordered={false}
            style={{
              height: '100%',marginTop: 0
            }}
          >
            付款进行中采购主体统计
            <Table<any>
              rowKey={(record) => record.index}
              size="small"
              columns={columns}
              dataSource={data?.listByPurchaseEntity}
              pagination={{
                style: { marginBottom: 0 },
                pageSize: 5,
              }}
            />
          </Card>
        </Suspense>
      </Col>
      <Col xl={12} lg={24} md={24} sm={24} xs={24}>
        <Suspense fallback={null}>
          <Card
            loading={loading}
            bordered={false}
            style={{
              height: '100%',marginTop: 0
            }}
          >
            线下账期待还统计
            <Table<any>
              rowKey={(record) => record.index}
              size="small"
              columns={columns1}
              dataSource={data?.listByPayAccountName}
              pagination={{
                style: { marginBottom: 0 },
                pageSize: 5,
              }}
            />
          </Card>
        </Suspense>
      </Col>
    </Row>

  </Modal>
}

export default PurchasePlanAnalysisModal;
