/* eslint-disable @typescript-eslint/indent */
import { RouteContext } from '@ant-design/pro-layout';
import React from 'react';
import { history } from 'umi';
import { Tabs, Dropdown, Menu, Result, Button } from 'antd';
import { useContext } from 'react';
import styles from './styles.less';
import {CloseCircleFilled, DownOutlined, ReloadOutlined, SyncOutlined} from '@ant-design/icons';
import { HistoryRouterContext, useHistoryRouter } from './hooks/useHistoryRouter';
import $ from 'jquery';

export interface RouterItemData {
  name: string;
  path: string;
  key?: string;
  content: React.ReactNode;
}

/**
 * 全局监听enter回车事件
 */
$(document).keypress(function (e){
  if(e.keyCode == 13){
    const modals = $('.ant-modal');
    if(modals.length > 0){
      const curModal = modals.eq(modals.length-1);
      if(curModal.attr("class").indexOf("globalEnterKeySubmit") >= 0){
        curModal.find('span:contains("确 定")').click();
      }
    }
  }
})

const BasicLayout = ({ children }: { children: React.ReactNode }) => {
  const { collapsed, matchMenuKeys, hasSiderMenu } = useContext(RouteContext);
  const historyRouter = useHistoryRouter(children);
  const { routers, currentRouter: currenRouter, setCurrentRouter } = historyRouter;

  const onEdit = (targetKey: string | any, action: 'remove' | 'add') => {
    if (action === 'remove') {
      historyRouter.removeByKey(targetKey);
    }
  };

  const handleMenuClick = ({ key }: any) => {
    switch (key) {
      case 'current': {
        historyRouter.removeByKey(currenRouter as string);
        break;
      }
      case 'other': {
        historyRouter.removeOther(currenRouter as string);
        break;
      }
      case 'all': {
        historyRouter.clear();
        break;
      }
      default:
        break;
    }
  };

  const onTabChange = (activeKey: string) => {
    setCurrentRouter(activeKey);
    // 路由跳转
    history.push(activeKey);
  };

  const renderExtraContent = () => {
    const menu = (
      <Menu onClick={handleMenuClick}>
        <Menu.Item key="current">
          <a>关闭当前标签页</a>
        </Menu.Item>
        <Menu.Item key="other">
          <a>关闭其他标签页</a>
        </Menu.Item>
        <Menu.Item key="all">
          <a>关闭所有标签页</a>
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link" style={{ display: 'inline-block', padding: '0 10px' }}>
          更多 <DownOutlined />
        </a>
      </Dropdown>
    );
  };

  const transformTabBarStyle = () => {
    if (hasSiderMenu && collapsed) {
      return {
        background: '#fff',
        left: collapsed ? '48px' : '208px',
      };
    }
    return {
      background: '#fff',
      left: '0px',
    };
  };

  // 如果当前没有匹配的菜单项，则显示无权访问
  if (!matchMenuKeys?.length) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="对不起, 无权限访问该页面"
        extra={
          <Button type="primary" onClick={() => history.push('/')}>
            返回首页
          </Button>
        }
      />
    );
  }
  const reloadPage = () => {
    historyRouter.refresh();
  }
  return (
    <HistoryRouterContext.Provider value={historyRouter}>
      <div className={styles.basicLayout}>
        <SyncOutlined style={{ position: "fixed", right: 20, top: 58, zIndex: 999, fontSize: 16 }} title={"刷新"} onClick={reloadPage} />
        <Tabs
          className="headerTab"
          hideAdd
          activeKey={currenRouter}
          onEdit={onEdit}
          type="editable-card"
          onChange={onTabChange}
          tabBarStyle={transformTabBarStyle()}
          // tabBarExtraContent={{ right: routers.length >= 2 ? renderExtraContent() : <></> }}
        >
          {routers.map((item) => {
            return (
              <Tabs.TabPane
                // @ts-ignore
                tab={item?.content?.props?.location?.state?.title ? `${item.name}-${item?.content?.props?.location?.state?.title }`: item.name}
                key={item.path}
                tabKey={item.key}
                closable={routers.length > 1}
                closeIcon={<CloseCircleFilled />}
              >
                <div style={{ margin: '38px 10px 10px', borderRadius: 5}}>{item.content}</div>
              </Tabs.TabPane>
            );
          })}
        </Tabs>
      </div>
    </HistoryRouterContext.Provider>

  );
};

export default BasicLayout;
