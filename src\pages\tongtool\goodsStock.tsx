import React, {useEffect} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import styles from "@/pages/PurchaseOrderList/styles.less";
import {TongtoolGoodsStock} from "@/pages/tongtool/data";
import {pageQueryGoodsStock} from "@/modules/tongtool/infra/api/tongtool";

const GoodsStock: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryGoodsStock({
      ...params,
    });
  });

  const columns: ProColumns<TongtoolGoodsStock>[] = [
    // {
    //   title: 'SKU',
    //   dataIndex: 'sku',
    //   hideInTable: true,
    //   colSize: (5  / 24),
    // },
    // {
    //   title: '仓库',
    //   dataIndex: 'warehouseName',
    //   hideInTable: true,
    //   colSize: (5  / 24),
    // },

    {
      title: 'SKU',
      dataIndex: 'sku',
      // hideInSearch: true,
      render: (v, record) => {
        return record.goodsSku;
      }
    },
    {
      title: '仓库',
      dataIndex: 'warehouseName',
      // hideInSearch: true,
      render: (v, record) => {
        return record.warehouseName;
      }
    },
    {
      title: '可用库存',
      dataIndex: 'availableStockQuantity',
      hideInSearch: true,
      render: (v, record) => {
        return record.availableStockQuantity;
      }
    },
    {
      title: '在途库存',
      dataIndex: 'intransitStockQuantity',
      hideInSearch: true,
      render: (v, record) => {
        return record.intransitStockQuantity;
      }
    },
    {
      title: '货位',
      dataIndex: 'cargoSpace',
      hideInSearch: true,
      render: (v, record) => {
        return record.cargoSpace || "--";
      }
    },
    {
      title: '故障库存',
      dataIndex: 'defectsStockQuantity',
      hideInSearch: true,
      render: (v, record) => {
        return record.defectsStockQuantity;
      }
    },
    {
      title: '待发库存',
      dataIndex: 'waitingShipmentStockQuantity',
      hideInSearch: true,
      render: (v, record) => {
        return record.waitingShipmentStockQuantity;
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);

  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        //可以隐藏选择多少项提示
        tableAlertRender={false}
        // search={{
        //   filterType: 'query',
        //   layout: 'horizontal',
        //   span: 24,
        //   collapseRender: () => null,
        //   defaultCollapsed: false,
        // }}
        columns={columns}
        className={styles.inline_search_table}
        toolBarRender={() => []}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};
export default GoodsStock;
