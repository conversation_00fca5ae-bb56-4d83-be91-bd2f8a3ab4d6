import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "@/../config/mallApiConfig";
import { DelParams, DownParams, SkuMap } from "./typs";

// 删除
export function deleteSkuTagImage(data: DelParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/pd/sku/deleteSkuTagImage',
    method: 'POST',
    data,
  },
  );
}

// 下载
export function batchDownloadSkuImages(data: DownParams) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/pd/sku/batchDownloadSkuImages ',
    method: 'POST',
    data,
  },
  );
}

// 全部
export function getSkuProductImagesBySku(sku: string) {
  return mallRequest<API.ApiBaseResult<SkuMap>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/pd/sku/getSkuProductImagesBySku',
    method: 'GET',
    params: {sku},
  },
  );
}