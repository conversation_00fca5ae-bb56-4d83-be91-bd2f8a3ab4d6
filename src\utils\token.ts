export const TOKEN_KEY = 'PURCHASE-PLATFORM-TOKEN';

export const SIGN_SECRET_KEY = 'PURCHASE-PLATFORM-SIGN-SECRET';

export const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

export const getToken = () => {
  return localStorage.getItem(TOKEN_KEY) || '';
};

export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

export const setSignSecret = (secretKey: string) => {
  localStorage.setItem(SIGN_SECRET_KEY, secretKey);
};

export const getSignSecret = () => {
  return localStorage.getItem(SIGN_SECRET_KEY) || '';
};

export const removeSignSecret = () => {
  localStorage.removeItem(SIGN_SECRET_KEY);
};

export default {
  TOKEN_KEY,
  setToken,
  getToken,
  removeToken,
  setSignSecret,
  getSignSecret,
  removeSignSecret,
};
