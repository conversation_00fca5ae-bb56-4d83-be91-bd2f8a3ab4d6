import { DeleteOutlined } from '@ant-design/icons';
import { Space, Input, Select } from 'antd';
import { find } from 'lodash';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import type { SpuSpecValue } from '..';
import type { CategorySpecFieldProps } from '../PlatformSpecField';

export default (props: Omit<CategorySpecFieldProps, 'categoryId'>) => {
  const [value, setValue] = useMergedState<SpuSpecValue>([], {
    value: props.value,
    onChange: props.onChange,
  });
  const onDelete = (index: number) => {
    const newSpecList = value.map((item) => ({ ...item }));
    newSpecList.splice(index, 1);
    setValue(newSpecList);
  };

  return (
    <Space direction="vertical">
      {value.map((item, index) => {
        return (
          <Space key={index}>
            <Input value={item.specName} disabled />
            <Select
              style={{ width: '350px' }}
              value={item.specValues}
              mode="multiple"
              options={item.specValues?.map((v) => ({ label: v, value: v }))}
              onChange={(v) => {
                const currentValue = find(value, { specName: item.specName });
                if (currentValue) {
                  currentValue.specValues = v;
                }
                setValue([...value]);
              }}
            />
            <DeleteOutlined onClick={() => onDelete(index)} />
          </Space>
        );
      })}
    </Space>
  );
};
