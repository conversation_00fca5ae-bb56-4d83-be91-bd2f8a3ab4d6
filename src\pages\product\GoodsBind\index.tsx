import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Form, Modal, notification, Space} from "antd";
import Permission from "@/components/Permission";
import {downloadDataTemplate, reqByBody, reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import {GoodsComposeEnum, GoodsMskuBind} from "@/modules/product/domain/goodsSync";
import EditGoodsBindModal from "@/pages/product/GoodsBind/components/EditGoodsBindModal";
import {ProFormField} from "@ant-design/pro-form";
import UploadFile from "@/components/UploadFile";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {commonExport} from "@/utils/comUtil";

const TableList: React.FC = () => {

  const [editComposeModal, setEditComposeModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<GoodsMskuBind>();
  const [selectedRowsState, setSelectedRows] = useState<GoodsMskuBind[]>([]);
  const [form] = Form.useForm();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/goods-compose/mskuPageQuery',{
      ...params,
    });
  });


  const importCreateGoodsBind=()=>{
    Modal.confirm({
      icon: null,
      // type: "confirm",
      title: <b style={{fontSize: 14}}>导入绑定组合关系</b>,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadDataTemplate("/sales-mgmt-biz/sales-center/goods-compose/downloadGoodsCompose").then(res=>{
                commonExport(res, '导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;
        reqByUrl("/sales-mgmt-biz/sales-center/goods-compose/importMsku",{link: link});
        // actionRef.current?.reloadAndRest?.();
        notification.success({message: '后台任务正在执行请稍后查看'});
      },
    });
  }


  const bindAsinkingListing = (record: any) => {
    Modal.confirm({
      title: "确认绑定领星商品?",
      centered: true,
      onOk: function () {
        reqByBody('/sales-mgmt-biz/sales-center/goods-compose/bindAsinkingListing',record).then((result) => {
          if (result.status.success) {
            notification.success({message: '同步成功'});
            actionRef.current?.reload?.();
          }
        });
      },
    });
  }


  // const createGoodsCompose = (record: any) => {
  //   Modal.confirm({
  //     title: "确认绑定领星商品?",
  //     centered: true,
  //     onOk: function () {
  //       reqByBody('/sales-mgmt-biz/sales-center/goods-compose/createGoodsCompose',record).then((result) => {
  //         if (result.status.success) {
  //           notification.success({message: '同步成功'});
  //           actionRef.current?.reload?.();
  //         }
  //       });
  //     },
  //   });
  // }

  const columns: ProColumns<GoodsMskuBind>[] = [
    {
      title: 'MSKU',
      dataIndex: 'msku',
      colSize: (4 / 24),
      width: 300
    },
    {
      title: '标准SKU',
      dataIndex: 'standardSku',
      colSize: (4 / 24),
    },
    {
      title: '组合关系',
      dataIndex: 'relation',
      colSize: (4 / 24),
      width: 300
    },
    {
      title: '销售类型',
      dataIndex: 'composeType',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: GoodsComposeEnum,
    },
    {
      title: '通途已绑定',
      dataIndex: 'isTongtoolBind',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '是', '0': '否','-1':'异常'},
      hideInTable: true
    },
    {
      title: '领星已绑定',
      dataIndex: 'isAsinkingBind',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '是', '0': '否','-1':'异常'},
      hideInTable: true
    },
    {
      title: '本地已创建',
      dataIndex: 'isLocalCreate',
      colSize: (4 / 24),
      valueType: 'select',
      valueEnum: {'1': '是', '0': '否','-1':'异常'},
      hideInTable: true
    },
    {
      title: '本地已创建',
      dataIndex: 'isLocalCreate',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.isLocalCreate==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.isLocalCreate==1?<span style={{color: "green"}}>是</span>:  <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '通途SKU',
      dataIndex: 'tongtoolSku',
      hideInSearch: true,
    },
    {
      title: '通途已创建',
      dataIndex: 'tongtoolSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.isTongtoolCreate==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.isTongtoolCreate==1?<span style={{color: "green"}}>是</span>:  <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '通途已绑定',
      dataIndex: 'tongtoolSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.isTongtoolBind==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.isTongtoolBind==1?<span style={{color: "green"}}>是</span>:  <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '领星SKU',
      dataIndex: 'asinkingSku',
      hideInSearch: true,
    },
    {
      title: '领星已创建',
      dataIndex: 'asinkingSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.isAsinkingCreate==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.isAsinkingCreate==1?<span style={{color: "green"}}>是</span>: <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '领星已绑定',
      dataIndex: 'asinkingSyncStatus',
      hideInSearch: true,
      render: (v, record) => {
        if (record?.isAsinkingBind==-1){
          return <span style={{color: "green"}}>异常</span>
        }else {
          return <div>{record?.isAsinkingBind==1?<span style={{color: "green"}}>是</span>: <span style={{color: "red"}}>否</span>}</div>;
        }
      }
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 100,
      render: (v, record) => {
        return (
          <Space >
            <a type="primary" onClick={()=>{bindAsinkingListing({id:record.id})}}>
              绑定listing
            </a>
            {/*<a type="primary" onClick={()=>{createGoodsCompose({id:record.id})}}>*/}
            {/*  创建组合商品*/}
            {/*</a>*/}
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<GoodsMskuBind>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        size={"small"}
        scroll={{ y: 660}}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
                <Button size={"small"} key="level" type="primary" onClick={() => {importCreateGoodsBind()}}>
                  销售导入绑定
                </Button>
                // <Button size={"small"} key="level" type="primary" onClick={() => {createGoodsCompose({ids:selectedRowsState.map((item) => item.id)})}}>
                //   创建组合商品
                // </Button>
            ]
            return [...options, ...dom];
          }
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <EditGoodsBindModal visible={editComposeModal} goodsCompose={currentRow} onCancel={()=>setEditComposeModal(false)} onFinish={()=>{setEditComposeModal(false);actionRef.current?.reload()}}/>
    </>
  );
};

export default TableList;
