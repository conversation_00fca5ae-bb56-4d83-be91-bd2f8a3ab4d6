import { useRequestTable } from '@/hooks/useRequestTable';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import moment from 'moment';
import { useEffect } from 'react';
import { downloadFile, queryTaskInfo, queryTaskList } from './api';

export function useTaskList() {
  const taskList = useRequestTable(queryTaskList);
  return taskList;
}

export function useTask() {
  const taskList = useTaskList();

  const run = () => {};

  const batchDownload = async (
    urlList: string[],
    name: string = `批量导出-${moment().format('YYYYMMDDHHmmss')}`,
  ) => {
    const res = await downloadFile({ urlList });
    if (res instanceof Blob) {
      const blob = new Blob([res as any], { type: 'application/zip' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a'); // 创建a标签
      link.href = url;
      if (name) {
        link.download = name; // 重命名文件
      }
      link.click();
      URL.revokeObjectURL(url); // 释放内存
    } else {
      message.error('导出失败');
    }
  };

  return {
    taskList,
    batchDownload,
    run,
  };
}

export function useTaskInfo(taskId?: string) {
  const {
    data: taskInfo,
    loading,
    refresh: loadTaskInfo,
  } = useRequest(() => queryTaskInfo({ taskId: taskId as string }).then((res) => res.body), {
    manual: true,
  });
  useEffect(() => {
    if (taskId) {
      loadTaskInfo();
    }
  }, [taskId]);

  return {
    taskInfo,
    loading,
  };
}
