import type {ModalProps} from 'antd';
import {Card, Descriptions, Modal, Space, Table} from 'antd';
import type {ActionType, ProColumns} from '@ant-design/pro-table';
import type {PurchasePlan, PurchasePlanLog} from '@/pages/PurchasePlanList/data';
import React, {useEffect, useRef} from 'react';
import {purchaseAdvicePageQuery} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {OrderStatusEnum} from "@/modules/purchaseOrder/domain/purchaseOrder";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {ProTable} from '@ant-design/pro-components';
import {useRequest} from "ahooks";
import {ExclamationCircleOutlined} from "@ant-design/icons";
import {PurchaseAdvice} from "@/pages/purchaseAdvice/data";

// 定义参数格式
export type PurchaseDetailListModalProps = {
  planData: any;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const PurchaseAdviceModal = (props: PurchaseDetailListModalProps) => {
  const { onFinish, planData, ...rest } = props;
  const actionRef = useRef<ActionType>();

  const { loading, data } = useRequest(() => purchaseAdvicePageQuery({sku: planData.sku}).then(item=>item.body));

  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [data]);

  const columns: ProColumns<PurchaseAdvice>[] = [
    {
      title: '基本信息',
      width: 150,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
       return <>
         <div style={{fontSize:"12px"}}> SKU：{record?.goodsSku||"--"}</div>
         <div style={{fontSize:"12px"}}> 分类：{record?.categoryName||"--"}</div>
         <div style={{fontSize:"12px"}}> 小组：{record?.organizationName||"--"}</div>
       </>
      }
    },
    {
      title: '仓库',
      width: 150,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 发货仓库：{record?.deliverWarehouseName||"--"}</div>
          <div style={{fontSize:"12px"}}> 销售仓库：{record?.salesWarehouseName||"--"}</div>
          <div style={{fontSize:"12px"}}> 备货类型：{record?.warehouseType||"--"}</div>
        </>
      }
    },    {
      title: '销量',
      width: 150,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 7天：{record?.sales7Days||"0"} &nbsp;&nbsp;14天：{record?.sales14Days||"0"}&nbsp;&nbsp;15天：{record?.sales15Days||"0"}</div>
          <div style={{fontSize:"12px"}}> 21天：{record?.sales21Days||"0"}&nbsp;&nbsp;30天：{record?.sales30Days||"0"}</div>
        </>
      }
    },
    {
      title: '库存',
      width: 100,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 可用：{record?.availableStockQuantity||"0"}</div>
          <div style={{fontSize:"12px"}}> 在途：{record?.intransitStockQuantity||"0"}</div>
        </>
      }
    },
    {
      title: '趋势',
      width: 200,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 日均销量：{record?.salesDayAvgQuantity||"0"}</div>
          <div style={{fontSize:"12px"}}> 公式：{record?.purchaseFormula||"--"}</div>
          <div style={{fontSize:"12px"}}> 趋势：{record?.salesTrend||"--"}</div>
        </>
      }
    },
    {
      title: '配置',
      width: 150,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 上限：{record?.upDay||"--"}</div>
          <div style={{fontSize:"12px"}}> 下限：{record?.downDay||"--"}</div>
          <div style={{fontSize:"12px"}}> 开卖天数：{record?.availableSalesDay||"0"}</div>
        </>
      }
    },
    {
      title: '需求',
      width: 150,
      align: 'left',
      dataIndex: 'orderCode',
      render: (v, record) => {
        return <>
          <div style={{fontSize:"12px"}}> 备货数量：{record.purchaseQuantity||'0'}</div>
          <div style={{fontSize:"12px"}}> 触发备货：{record.isPurchase==1?"是":"否"}</div>
        </>
      }
    },
  ];

  return (
    <Modal {...rest} title="备货建议" closable={false} width="90%" onOk={onFinish}>
      <Table<any>
        rowKey={(record) => record.index}
        size="small"
        columns={columns}
        dataSource={data}
        pagination={false}
        pagination={{
          style: { marginBottom: 0 },
          pageSize: 10,
        }}
      />
    </Modal>
  );
};

export default PurchaseAdviceModal;
