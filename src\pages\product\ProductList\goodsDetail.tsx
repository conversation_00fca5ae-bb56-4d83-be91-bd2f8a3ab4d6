import React, {useState} from "react";
import {Card, Spin, Tabs} from 'antd';
import TabPane from "@ant-design/pro-card/es/components/TabPane";
import GoodsBaseInfoModal from "@/pages/product/ProductList/components/GoodsBaseInfoModal";
import {useParams} from "umi";
import ProductSupplierModal from "@/pages/product/CreateProduct/components/ProductSupplierModal";
import {Spu} from "@/modules/product/domain/spu";

const CreateOrUpdateSpu = () => {
  const { code } = useParams<{ code: string}>();
  const [spuInfo, setSpuInfo] = useState<Spu>();

  return (
    <Spin spinning={false}>
      <Card>
        <Tabs  type="line">
          <TabPane tab="基本信息" key="1">
            <>
              <GoodsBaseInfoModal code={code} onRefresh={(e)=>{setSpuInfo(e)}}/>
            </>
          </TabPane>
          <TabPane tab="供应商" key="2">
            <>
              <ProductSupplierModal code={code} spuId={spuInfo?.spuId}/>
            </>
          </TabPane>
          <TabPane tab="维护人" key="3">
            <>
              虚位以待
            </>
          </TabPane>
          <TabPane tab="商品附件" key="4">
            <>
              虚位以待
            </>
          </TabPane>
        </Tabs>
      </Card>
    </Spin>
  );
};

export default CreateOrUpdateSpu;
