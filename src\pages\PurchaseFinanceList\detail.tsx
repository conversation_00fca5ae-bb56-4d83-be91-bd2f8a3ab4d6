import React, {useEffect, useState} from 'react';
import {Card, Descriptions, Spin, Steps, Table, Tabs} from "antd";
import {useParams} from "umi";
import styles from "@/pages/PurchaseOrderList/styles.less";
import TabPane from "@ant-design/pro-card/es/components/TabPane";
import {PurchaesPlan} from "@/pages/PurchasePlanList/data";
import {ProColumns} from "@ant-design/pro-table";
import {PlanState, PlanStateText} from "@/modules/purchasePlan/domain/purchasePlan";
import {useRequest} from "ahooks";
import {getOrderDetail} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import PurchaseOrderGoodsModal from "@/pages/PurchaseOrderList/components/PurchaseOrderGoodsModal";


const TableList: React.FC = () => {
  const { id: orderId } = useParams<{ id: string }>();
  const { data, loading } = useRequest(() => getOrderDetail(orderId).then((res) => res.body));
  // const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const [modalVisible, setModalVisible] = useState<boolean>(true);


  // const { fetchList, actionRef } = useRequestTable((params) => {
  //   return pageQueryPlanDetail({
  //     ...params,
  //     purchasePlanId:orderId,
  //     status: activeStatusKey === '' ? undefined : activeStatusKey,
  //   });
  // });

  // useEffect(() => {
  //   actionRef.current?.reloadAndRest?.();
  // }, [activeStatusKey]);

  const onChange = (key: string) => {
    console.log(key);
  };


  const columns: ProColumns<PurchaesPlan>[] = [
    {
      title: '计划单号',
      dataIndex: 'id',
    },
    {
      title: '采购仓库',
      dataIndex: 'warehouseName',
      formItemProps: {
        name: 'warehouseName',
      },
    },
    {
      title: 'sku款数',
      dataIndex: 'skuCount',
      hideInSearch: true,
    },
    {
      title: '总金额',
      dataIndex: 'amount',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        [PlanState.WAIT]: {
          text: PlanStateText.WAIT,
          status: 'Processing',
        },[PlanState.PART_CREATED]: {
          text: PlanStateText.PART_CREATED,
          status: 'Warning',
        },[PlanState.ALL_CREATED]: {
          text: PlanStateText.ALL_CREATED,
          status: 'Success',
        },[PlanState.CANCEL]: {
          text: PlanStateText.CANCEL,
          status: 'Error',
        },
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      formItemProps: {
        name: 'creatorName',
      },
    },
  ];

  return (
    <>
     <Spin spinning={loading}>
      <Card>
        <Descriptions column={4}>
          <Descriptions.Item label="采购单号">{data?.orderCode}</Descriptions.Item>
          <Descriptions.Item label="采购仓库">广州仓</Descriptions.Item>
          <Descriptions.Item label="采购员">黄郭兵</Descriptions.Item>
          <Descriptions.Item label="跟单员">何梁莉</Descriptions.Item>
          <Descriptions.Item label="平台单号">{data?.platformOrderCode}</Descriptions.Item>
          <Descriptions.Item label="采购渠道">{data?.platform}</Descriptions.Item>
          <Descriptions.Item label="采购账号">juying66999</Descriptions.Item>
          <Descriptions.Item label="平台订单状态">卖家已发货</Descriptions.Item>
          <Descriptions.Item label="快递单号">YT29260327496</Descriptions.Item>
          <Descriptions.Item label="平台订单总金额">320.00</Descriptions.Item>
          <Descriptions.Item label="平台运费">10.00</Descriptions.Item>
          <Descriptions.Item label="实付运费">10.00</Descriptions.Item>
          <Descriptions.Item label="下单时间">2022-08-23 11:51</Descriptions.Item>
          <Descriptions.Item label="付款时间">2022-08-23 11:51</Descriptions.Item>
          <Descriptions.Item label="签收时间">2022-08-23 11:51</Descriptions.Item>
        </Descriptions>
      </Card>
      <Card className={styles.card}>
        <Steps  current={4} percent={60}>
          <Steps.Step title="创建采购单" description="2022-08-01 11:51" />
          <Steps.Step title="审核" description="2022-08-02 11:51" />
          <Steps.Step title="平台下单" description="2022-08-03 11:51" />
          <Steps.Step title="财务付款" description="2022-08-04 11:51" />
          <Steps.Step title="卖家发货" description="2022-08-04 13:51" />
          <Steps.Step title="仓库签收" description="2022-08-05 11:51" />
          <Steps.Step title="仓库上架" description="2022-08-06 11:51" />
        </Steps>
      </Card>
      <Card className={styles.card}>
        <Tabs onChange={onChange} type="card">
          <TabPane tab="商品信息" key="1">
            <PurchaseOrderGoodsModal orderId={orderId}/>
          </TabPane>
          <TabPane tab="供应商信息" key="2">
            <>
              <PurchaseOrderGoodsModal orderId={orderId}/>
            </>
          </TabPane>
          <TabPane tab="采购附件" key="3">
            <>
              <PurchaseOrderGoodsModal orderId={orderId}/>
            </>
          </TabPane>
          <TabPane tab="审核明细" key="4">
            Content of Tab Pane 3
          </TabPane>
          <TabPane tab="物流信息" key="5">
            Content of Tab Pane 3
          </TabPane>
          <TabPane tab="操作日志" key="6">
            Content of Tab Pane 3
          </TabPane>
        </Tabs>
      </Card>
     </Spin>
    </>
  );
};

export default TableList;
