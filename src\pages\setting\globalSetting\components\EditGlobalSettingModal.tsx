import React, {useEffect} from 'react';
import {Modal, ModalProps, Form, message} from 'antd';
import ProForm, {ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {executeSave} from "@/modules/common/infra/api/common";

// 定义参数格式
export type editProps = {
  onFinish: () => void;
  globalSetting: any;
} & ModalProps;

export default (props: editProps) => {
  const [form] = ProForm.useForm();
  const {onFinish, globalSetting, ...rest} = props;


  useEffect( () => {
      form.setFieldsValue(globalSetting);
    },
    [globalSetting]
  );

  const onSubmit = () => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      executeSave('/purchase-mgmt-biz/purchase-center/setting/dict/saveSysDict',params).then((result) => {
        if (result.status.success) {
          message.success('保存成功');
          onFinish();
        }
      });
    })
  }

  return (
    <>
      <Modal {...rest} title="全局配置" className={'globalEnterKeySubmit'} onOk={() => onSubmit()} maskClosable={false}>
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProFormText name="id" hidden={true}/>
          <ProFormText width={"md"} label="名称" name="dictName" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="简码" name="dictCode" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="业务键" name="itemText" rules={[{required: true}]}/>
          <ProFormText width={"md"} label="业务值" name="dictValue" rules={[{required: true}]}/>
          <ProFormTextArea width={"md"} label="描述" name="description"/>
        </Form>
      </Modal>
    </>
  );
};
