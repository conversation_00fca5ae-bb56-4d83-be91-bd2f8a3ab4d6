import {GoldOutlined, InboxOutlined, PlusOutlined, SnippetsTwoTone} from '@ant-design/icons';
import {Button, Descriptions, Form, Image, Modal, notification, Space, Table, Tag} from 'antd';
import React, {useEffect, useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import {history, useIntl} from 'umi';
import useTableSelection from '@/hooks/useTableSelection';
import type {Sku, SpuListItem} from '@/modules/product/domain/spu';
import {useSpu} from '@/modules/product/application/spu';
import {formatDate} from '@/utils/utils';
import {ProFormField} from "@ant-design/pro-form";
import SelfImage from "@/components/SelfImage";
import UploadFile from "@/components/UploadFile";
import {downloadGoodsTemplate, importGoods, importSkuImage} from "@/modules/product/infra/api/spu";
import Permission from "@/components/Permission";
import CustomPage from "@/components/CustomPage";
import {ColumnProps} from "antd/es/table";
import {commonExport, copyText} from "@/utils/comUtil";
import GoodsSalesStockModal from "@/pages/product/GoodsList/components/GoodsSalesStockModal";
import {Access} from "@@/plugin-access/access";
import {Goods} from "@/modules/goods/domain/goods";
import {ProductTypeEnum} from "@/modules/product/domain/spu";

const ProductList: React.FC<{}> = () => {
  const { rowSelection, } = useTableSelection<SpuListItem>();
  const [activeStatusKey, setActiveStatusKey] = useState<string>('all');
  const [goodsSalesStock, setGoodsSalesStock] = useState<boolean>(false);
  const [goodsSalesStockParam, setGoodsSalesStockParam] = useState<any>();
  const spuDomain = useSpu();
  const { actionRef } = spuDomain;

  const [form] = Form.useForm();
  const downloadTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadGoodsTemplate().then(res=>{
                commonExport(res, '商品导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;

        importGoods(link).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }

  const improtGoodsImage=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;

        importSkuImage(link).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }

  const skuColumns: ColumnProps<Goods>[] = [
    {
      title: '图片',
      dataIndex: 'mainImage',
      align: "left",
      width: 50,
      render: (v, record) => {
        return <>
          <div style={{ width: 50}}>
            {record.mainSpecInfo ? <SelfImage src={record.mainSpecInfo.image.fileUrl} title={record?.skuName} width={50} height={50}/> : null}
          </div>
        </>;
      },
    },
    {
      title: 'SKU信息',
      dataIndex: 'sku',
      width: 400,
      align: "left",
      render: (v, record) => {
        return <>
          <div>SKU: {record?.customCode}&nbsp;<SnippetsTwoTone onClick={() => copyText(record.customCode)}/></div>
          <div>标题: {record?.skuName}</div>
        </>;
      },
    },
    {
      title: '商品类型',
      dataIndex: 'sku',
      width: 300,
      align: "left",
      render: (v, record) => {
        return <>
          <div>{record?.skuAttrName ? <span>{record?.skuAttrName}：{record.skuAttrValue}</span>: null}</div>
          <div>商品类型: <Tag color={record?.productType=="BIND"?"blue":record?.productType=="ASSEMBLE"?"orange":"green"}>{ProductTypeEnum[record?.productType]}</Tag></div>
          <div>组合关系: {record?.relation || '--'}</div>
        </>;
      },
    },
    {
      title: '体积/重量',
      dataIndex: 'weight',
      width: 200,
      render: (v, record) => {
        return <>
          <div><InboxOutlined />&nbsp;{(record.sizeLength || 0) +" * "+ (record.sizeWidth || 0) + " * "+ (record.sizeHeight || 0)}</div>
          <div><GoldOutlined />&nbsp;{(record.netWeight || 0) +" g"}</div>
        </>
      }
    },
    {
      title: '时间',
      align: "left",
      width: 200,
      render: (v, record) => {
        return <>
          <div>创建: {formatDate(record?.gmtCreate)}</div>
          <div>修改: {formatDate(record?.gmtModified)}</div>
        </>;
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 100,
      align: 'left',
      render: (v, record) => {
        const tags = record?.tagsName ? record.tagsName.split(",") : [];
        const options: {} | null | undefined = [];
        if(tags.length > 0 ){
          tags?.map((item: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined)=>{
            options.push(<Tag color="green" style={{cursor:"pointer"}}> {item}</Tag>)
          })
        }
        return <Space wrap={true} direction="horizontal" aria-colindex={2}>{options}</Space>;
      }
    },
  ];

  const columns: ProColumns<SpuListItem>[] = [
    {
      title: '商品名称',
      dataIndex: 'title',
      colSize: (4 / 24),
      hideInTable: true,
    },
    {
      title: 'SPU',
      dataIndex: 'spuCustomCode',
      colSize: (4 / 24),
      hideInTable: true,
    },
    {
      title: 'SKU',
      dataIndex: 'skuCustomCode',
      colSize: (4 / 24),
      hideInTable: true,
    },
    {
      title: '图片',
      dataIndex: 'mainImage',
      hideInSearch: true,
      align: "left",
      width: 60,
      render: (v, record) => {
        return <>
          <div style={{ width: 80}}>
            {record.mainImage ? <Image src={record.mainImage} width={80} /> : null}
          </div>
        </>;
      },
    },
    {
      title: '基础信息',
      dataIndex: 'title',
      hideInSearch: true,
      width: 200,
      render: (v, record) => {
        return <>
            <div style={{fontWeight: "bold"}}>SPU: {record.spu}&nbsp;<SnippetsTwoTone onClick={() => copyText(record.spu)}/></div>
            <div style={{fontWeight: "bold"}}>标题：{record.title}</div>
            <div style={{fontWeight: "bold", color: "green"}}>分类：{record?.categoryName}</div>
            {/*<div>中文申报名：{record?.zhDeclaredName || '--'}</div>*/}
            {/*<div>英文申报名：{record?.enDeclaredName || '--'}</div>*/}
        </>;
      },
    },
    {
      title: '维护人',
      hideInSearch: true,
      dataIndex: 'info',
      width: 100,
      render: (v, record) => {
        return <>
          <div>采购员：{record?.purchaser || '--'}</div>
          <div>开发员：{record?.developer || '--'}</div>
          <div>维护人：{record?.maintainer || '--'}</div>
          <div>美工：{record?.designer || '--'}</div>
        </>;
      },
    },
    {
      title: 'SKU信息',
      hideInSearch: true,
      width: 800,
      render: (v, record) => {
        return (
          <Table
            size={"small"}
            bordered={false}
            dataSource={record?.skus}
            columns={skuColumns}
            style={{maxHeight: 500, overflow:record?.skus?.length > 5 ? "scroll": ""}}
            pagination={false}
            // pagination={record?.skus && record?.skus?.length > 5 ? {pageSize: 5} :false}
          />
        );
      },
    },
    {
      title: '操作',
      // dataIndex: 'option',
      width: 100,
      hideInSearch: true,
      render: (_, record) => {
        return <Space>
          <a
            key="show"
            onClick={() => history.push({ pathname: `/product-manager/goods/code/${record.spu}`, state: { title: record.spu } })}
          >
            查看
          </a>
          <a
            key="salesStock"
            onClick={() => {
              setGoodsSalesStock(true);
              setGoodsSalesStockParam(record?.skus);
            }}
          >
            库存
          </a>
        </Space>;
      }
    },
  ];

  useEffect(() => {
    if (actionRef?.current) {
      actionRef.current.reload();
    }
  }, [activeStatusKey]);

  return (
    <>
      <CustomPage<SpuListItem>
        headerTitle=""
        actionRef={actionRef}
        rowKey="spuId"
        scroll={{ y: 'calc(100vh - 280px)' }}
        request={(params, sort, filter) => {
          const { category, ...rest } = params;
          return spuDomain.fetchList(
            {
              ...rest,
              developState:
                activeStatusKey !== 'all' && activeStatusKey
                  ? activeStatusKey
                  : params?.developState,
            },
            sort,
            filter,
          );
        }}
        toolBarRender={()=>[
          <Permission permissionKey={"purchase:product_manager:sku:spuCreate"}>
            <Button
              type="primary"
              ghost={true}
              size={"small"}
              onClick={() => history.push('/product-manager/goods/sku/create')}
            >
              新增商品
            </Button>
          </Permission>,
          <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
            <Button
              type="primary"
              size={"small"}
              onClick={() => downloadTemplate()}
            >
              导入商品
            </Button>
          </Permission>,
          <Permission permissionKey={"purchase:product_manager:sku:importGoods"}>
            <Button
              type="primary"
              size={"small"}
              onClick={() => improtGoodsImage()}
            >
              导入商品图片
            </Button>
          </Permission>
        ]}
        columns={columns}
        // rowSelection={{
        //   ...rowSelection,
        // }}
      />
      <Access accessible={goodsSalesStock}>
        <GoodsSalesStockModal visible={goodsSalesStock}  onCancel={() => setGoodsSalesStock(false)} reqParams={goodsSalesStockParam} onFinish={()=>{setGoodsSalesStock(false)}}/>
      </Access>
    </>
  );
};

export default ProductList;
