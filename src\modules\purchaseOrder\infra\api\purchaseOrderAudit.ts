import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {PurchaseOrderAudit} from "@/pages/PurchaseOrderAuditList/data";
import {SupplierGoods} from "@/modules/supplier/domain/vender";

//分页search参数
export type PageQueryOrderAuditParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
  channelOrderNo?: string;
  orderState?: string;
  distributionOrderId?: string;
} & API.QueryPageParams;

// 采购单列表
export async function pageQueryOrderAudit(data?: PageQueryOrderAuditParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderAudit[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/orderAudit/pageQuery',
    data,
  });
}

//分页search参数
export type PageQueryOrderPriceAuditParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
  channelOrderNo?: string;
  orderState?: string;
  distributionOrderId?: string;
} & API.QueryPageParams;

// 价格审核列表
export async function pageQueryOrderPriceAudit(data?: PageQueryOrderAuditParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderAudit[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/orderAudit/priceAuditPageQuery',
    data,
  });
}


// 财务审核列表
export async function financeAuditPageQuery(data?: PageQueryOrderAuditParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrderAudit[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/orderAudit/financeAuditPageQuery',
    data,
  });
}

/**
 * 财务勾选查询采购单列表
 */
export type queryFinanceCheckOrdersParams = {
  purchaseOrderIds: string[];
};
export async function queryFinanceCheckOrders(data?: queryFinanceCheckOrdersParams) {
  return mallRequest<API.ApiQueryPageResult<SupplierGoods>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/pageQuery',
      data,
    },
  );
}

// 价格审核
export async function purchaseOrderPriceAudit(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/orderAudit/priceAudit',
    data,
  });
}


//财务人工审核
export async function financeManualAudit(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/financeManualAudit',
    data,
  });
}




