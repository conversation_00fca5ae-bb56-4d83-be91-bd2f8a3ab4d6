import React, {useEffect, useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import {message, Space, Tag} from "antd";
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {pageQueryOrder} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {BankOutlined, RocketOutlined, SnippetsTwoTone, UserOutlined} from "@ant-design/icons";
import moment from "moment";
import {OrderStatusEnum} from "@/modules/purchaseOrder/domain/purchaseOrder";
import {Link} from "umi";
import styles from './styles.less'
import Filters from './components/Filters';
import aliwangwang from "@/assets/images/aliwangwang.gif";
import {copyText} from "@/utils/comUtil";
const TableList: React.FC = () => {

  const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const [selectedRowsState, setSelectedRows] = useState<PurchaseOrder[]>([]);


  const { fetchList, actionRef } = useRequestTable((params) => {
    return pageQueryOrder({
      ...params,
      // orderStatus: activeStatusKey === '' ? undefined : activeStatusKey,
    });
  });




  const payStatusEnum = {
    '-10': '无需付款',
    '0': '初始未付',
    '10': '申请付定金',
    '20': '申请付余额',
    '30': '申请付全款',
    '40': '付款进行中',
    '50': '驳回',
    '60': '已付定金',
    '70': '已付全款'
  }

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      hideInTable: true,
      colSize: (5 / 24),
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      hideInTable: true,
      colSize: (5 / 24),
    },
    {
      title: '标签',
      dataIndex: 'tagName',
      hideInTable: true,
      colSize: (5 / 24),
      valueEnum: {
        [0]: {
          text: "全部",
          status: 'Success',
        },
        [1]: {
          text: "手工单",
          status: 'Success',
        },
        [2]: {
          text: "加急",
          status: 'Success',
        },
        [3]: {
          text: "线下单",
          status: 'Success',
        },
        [4]: {
          text: "线上单",
          status: 'Success',
        }
      }
    },
    {
      title: '处理人',
      dataIndex: 'userName',
      hideInTable: true,
      colSize: (8 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [conductorKey, conductorValue] = value;
          return {
            conductorKey,
            conductorValue
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectInput options={
          [{
            value: 'purchaseUsername',
            label: '采购员',
          }, {
            value: 'merchandiserUsername',
            label: '跟单员',
          }
          ]
        }
                                    defaultValue={'purchaseUsername'}
        />
      }
    },
    {
      title: '单号',
      dataIndex: 'orderNo',
      hideInTable: true,
      colSize: (12 / 24),
      search: {
        transform: (value: any) => {
          if (!value) {
            return {}
          }
          const [numberKey, numberValue] = value;
          return {
            numberKey,
            numberValue
          }
        },
      },
      renderFormItem: () => {
        return <Filters.SelectInput options={
          [{
            value: 'orderCode',
            label: '采购单号',
          }, {
            value: 'trackingNumber',
            label: '快递单号',
          }, {
            value: 'platformOrderCode',
            label: '平台单号',
          }
          ]
        }
          defaultValue={'orderCode'}
          placeholder='单号查询'
        />
      }
    },
    {
      title: '采购状态',
      dataIndex: 'orderStatus',
      hideInTable: true,
      renderFormItem: () => {
        return <Filters.CheckboxButton
          defaultValue={[1]}
          options={[
            { value: 1, label: '全部' },
            { value: 0, label: '草稿' },
            { value: 10, label: '待审核' },
            { value: 20, label: '已审核' },
            { value: 30, label: '已下单' },
            { value: 40, label: '已付款' },
            { value: 45, label: '部分到货' },
            { value: 50, label: '全部到货' },
            { value: 60, label: '已上架' },
            { value: 70, label: '作废' },
            { value: 90, label: '驳回' },
          ]}
        />
      }
    },
    {
      title: '审核状态',
      dataIndex: 'purchaseAuditStatus',
      hideInTable: true,
      renderFormItem: () => {
        return <Filters.CheckboxButton
          defaultValue={[1]}
          options={[
            { value: 1, label: '全部' },
            { value: "wait", label: '待审核' },
            { value: "pass", label: '通过' },
            { value: "reject", label: '驳回' }
          ]}
        />
      }
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      hideInTable: true,
      renderFormItem: () => {
        return <Filters.CheckboxButton
          defaultValue={[1]}
          options={[
            { value: 1, label: '全部' },
            { value: 0, label: '初始未付' },
            { value: 40, label: '付款进行中' },
            { value: 70, label: '已付全款' },
            { value: 50, label: '驳回' },
          ]}
        />
      }
    },
    {
      title: '单号',
      dataIndex: 'id',
      width: 130,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div> {record.orderCode}<SnippetsTwoTone onClick={() => copyText(record.orderCode)} /></div>
          </>
        );
      }
    },
    {
      title: '标题/供应商',
      dataIndex: 'id',
      width: 240,
      hideInSearch: true,
      render: (v, record) => {
        const link = record.aliWangWangLink != null ? (<a onClick={function () {
          if (record.aliWangWangLink != null) {
            window.open(record.aliWangWangLink);
          }
        }}><img src={aliwangwang} /></a>) :
          <span className="grey" style={{ color: "red" }}>暂未绑定供应商</span>

        return (
          <>
            <div style={{ fontSize: 12 }}>{record.title}</div>
            <div>
              <Link key="show" style={{ fontSize: 13, color: 'blue' }} to={`/supplier/venderList/${record.supplierId}`}>
                {record.supplierName}
              </Link>
            </div>
            <div>{link}</div>
          </>
        );
      }
    },
    {
      title: '采购员/跟单员',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div><UserOutlined title='采购员' />&nbsp;&nbsp;{record.purchaseUsername ? record.purchaseUsername : "--"}</div>
            <div><UserOutlined
              title='跟单员' />&nbsp;&nbsp;{record.merchandiserUsername ? record.merchandiserUsername : "--"}</div>
          </>
        );
      }
    },
    {
      title: '采购仓库/快递单号',
      dataIndex: 'id',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div><BankOutlined title='采购仓库' />&nbsp;&nbsp;{record.purchaseWarehouse}</div>
            {record.trackingNumber ? (
              <div><RocketOutlined title='快递单号' />&nbsp;&nbsp;{record.trackingNumber}<SnippetsTwoTone
                onClick={() => copyText(record.trackingNumber)} /></div>) : null}
          </>
        );
      }
    },
    {
      title: '货款/运费',
      dataIndex: 'id',
      width: 180,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div>货款：{record.totalPrice}</div>
            {record?.shippingFee == null ? null : (<div>运费：{record.shippingFee}</div>)}
          </>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        const orderStatus = OrderStatusEnum[record?.orderStatus];
        const options = [
          orderStatus != undefined ? (
            <span><span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text">{orderStatus}</span></span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '付款状态',
      dataIndex: 'id',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        const payStatus = payStatusEnum[record?.payStatus]
        const options = [
          payStatus != undefined ? (
            <span><span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text">{payStatus}</span></span>) : null

        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '审核',
      dataIndex: 'auditor',
      width: 200,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div>{record.auditor!=null?(<div>审核人：{record.auditor}</div>):null}</div>
            <div>{record.auditLabel!=null?(<div>备注：{record.auditLabel}</div>):null}</div>
          </>
        );
      }
    },

    {
      title: '标签',
      dataIndex: 'remark',
      width: 130,
      align: 'center',
      hideInSearch: true,
      render: (v, record) => {
        const options = [
          record.isUrgent == 1 ?
            <Tag color='red'>加急</Tag> : null,
          record.isManual == 1 ? (
            <Tag color='yellow'>手工单</Tag>) : null,
          record.platform != '1688' ? (
            <Tag color='yellow'>线下单</Tag>) : null,
          // record.platformOrderTime != null ? (
          //   <Tag color='blue'>跨境专供</Tag>): null,

        ];
        return <Space direction="vertical">{options}</Space>;
        // return (
        //   <>
        //     <Tag color=''></Tag>
        // <Tag color='yellow'>手工单</Tag>
        //     <Tag color='green'>账期</Tag>
        //   </>
        // );
      }
    },
    {
      title: '时间',
      dataIndex: 'id',
      width: 250,
      hideInSearch: true,
      align: 'center',
      render: (v, record) => {
        const options = [
          record.gmtCreate != null ? (
            <div>创建:{moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss")}</div>) : null,
          record.gmtModified != null ? (
            <div>修改:{moment(record.gmtModified as number).format("YYYY-MM-DD HH:mm:ss")}</div>) : null,
          record.platformOrderTime != null ? (
            <div>下单:{moment(record.platformOrderTime as number).format("YYYY-MM-DD HH:mm:ss")}</div>) : null,
          record.platformPayTime != null ? (
            <div>付款:{moment(record.platformPayTime as number).format("YYYY-MM-DD HH:mm:ss")}</div>) : null,

        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <Space>
            <Link key="show" to={`/warehouse/stock/detail/${record.id}`}>
              详情
            </Link>
          </Space>
        );
      }
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [activeStatusKey]);

  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        size="small"
        className={styles.inline_search_table}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
        }}
        columns={columns}
        // toolBarRender={() => }
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;

