import { Card, Col, Form, Modal, ModalProps, Row, Checkbox, Table } from "antd";
import React, { useContext, useEffect, useState } from 'react';
import { useRequest } from "ahooks";
import { queryPurchaseAnalysis } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import { getUserPurchaseEntityList } from "@/modules/setting/infra/api/PurchaseEntityConfig";

export type CreateModalProps = {
  onFinish: (values: any) => void;
} & Omit<ModalProps, 'open'>;

const PurchaseAnalysisModal = (props: CreateModalProps) => {
  const { onFinish, open, ...rest } = props;
  const [form] = Form.useForm();

  // 添加状态管理采购主体选项
  const [purchaseEntityOptions, setPurchaseEntityOptions] = useState([]);
  const [purchaseDetails, setPurchaseDetails] = useState([]);
  const [summaryData, setSummaryData] = useState({
    lastMonthTotal: 0,
    currentMonthTotal: 0
  });

  // 加载采购主体选项
  const { loading: loadingOptions, run: runLoadOptions } = useRequest(() => getUserPurchaseEntityList().then((res) => {
    if (res && res.status?.success) {
      // 转换数据格式
      const options = (res.body || []).map(item => ({
        label: item,
        value: item
      }));
      setPurchaseEntityOptions(options);

      // 设置初始全选值
      const allValues = options.map(opt => opt.value);
      form.setFieldValue('purchaseEntityList', allValues);

      // 初始加载数据
      runLoadAnalysis();
    }
  }), { manual: true });

  // 采购分析数据请求
  const { loading: loadingAnalysis, run: runLoadAnalysis } = useRequest(() => queryPurchaseAnalysis({
    purchaseEntityList: form.getFieldValue('purchaseEntityList') || [],
  }).then((res) => {
    if (res && res.status?.success) {
      // 处理明细数据 - 将对象转换为数组格式
      const details = Object.entries(res.body?.detailInfo || {}).map(([date, info]) => ({
        date,
        orderCount: info.orderCount || 0,
        skuCount: info.skuCount || 0,
        productCount: info.productCount || 0,
        totalAmount: info.totalAmount || 0,
        periodAmount: info.periodAmount || 0,
        creditAmount: info.creditAmount || 0,
        cashAmount: info.cashAmount || 0,
        shippedCount: info.shippedCount || 0,
        unshipCount: info.unshipCount || 0,
        receivedCount: info.receivedCount || 0
      }));
      setPurchaseDetails(details);
      // 设置汇总数据
      setSummaryData({
        lastMonthTotal: res.body?.lastMonthTotal || 0,
        currentMonthTotal: res.body?.currentMonthTotal || 0
      });
    }
  }));

  // 当选择变化时重新请求数据
  const handlePurchaseEntityChange = (values: string[]) => {
    form.setFieldValue('purchaseEntityList', values)
    runLoadAnalysis();
  };

  // 监听弹窗显示状态
  useEffect(() => {
    if (open) {
      // 弹窗显示时加载数据
      runLoadOptions();
    } else {
      // 弹窗关闭时重置数据
      setPurchaseDetails([]);
      setPurchaseEntityOptions([]);
      setSummaryData({
        lastMonthTotal: 0,
        currentMonthTotal: 0
      });
      form.resetFields();
    }
  }, [open]);

  const detailColumns = [
    {
      title: '时间',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '采购单量',
      dataIndex: 'orderCount',
      key: 'orderCount',
    },
    {
      title: 'SKU数',
      dataIndex: 'skuCount',
      key: 'skuCount',
    },
    {
      title: '产品数量',
      dataIndex: 'productCount',
      key: 'productCount',
    },
    {
      title: '采购总货值',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
    },
    {
      title: '账期金额',
      dataIndex: 'periodAmount',
      key: 'periodAmount',
    },
    {
      title: '诚意赊金额',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
    },
    {
      title: '现金金额',
      dataIndex: 'cashAmount',
      key: 'cashAmount',
    },
    {
      title: '已发货单量',
      dataIndex: 'shippedCount',
      key: 'shippedCount',
    },
    {
      title: '未发货单量',
      dataIndex: 'unshipCount',
      key: 'unshipCount',
    },
    {
      title: '签收单量',
      dataIndex: 'receivedCount',
      key: 'receivedCount',
    }
  ];

  return (
    <Modal open={open} width={"80%"} {...rest} style={{ padding: 0 }} destroyOnClose>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card loading={loadingAnalysis || loadingOptions} bordered={false} style={{ textAlign: "center", padding: "0px" }}>
            <p style={{ margin: 0 }}>上月 采购总金额</p>
            <b>{summaryData.lastMonthTotal?.toLocaleString() || '0'}</b>
          </Card>
        </Col>
        <Col span={6}>
          <Card loading={loadingAnalysis || loadingOptions} bordered={false} style={{ textAlign: "center", padding: "0px" }}>
            <p style={{ margin: 0 }}>本月 采购总金额</p>
            <b>{summaryData.currentMonthTotal?.toLocaleString() || '0'}</b>
          </Card>
        </Col>
        <Col span={24}>
          <Form form={form}>
            <Form.Item label="采购主体" name="purchaseEntityList" initialValue={purchaseEntityOptions.map(opt => opt.value)}>
              <Checkbox.Group
                options={purchaseEntityOptions}
                style={{ width: '100%' }}
                onChange={handlePurchaseEntityChange}
              />
            </Form.Item>
          </Form>
        </Col>
        <Col span={24}>
          <Card loading={loadingAnalysis || loadingOptions} bordered={false}>
            <Table
              rowKey="date"
              columns={detailColumns}
              dataSource={purchaseDetails}
              pagination={false}
              size="small"
              scroll={{
                x: true,
                y: 400  // 设置固定高度为400px
              }}
              style={{
                // 添加最大高度限制
                maxHeight: 400
              }}
            />
          </Card>
        </Col>
      </Row>
    </Modal>
  );
}

export default PurchaseAnalysisModal;