import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import type { CategoryData } from '../../domain/category';

export async function queryRootCategory() {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/supplier-biz/category/platform/queryRoot',
      method: 'GET',
    },
  );
}

export async function queryCategoryByParent(params: { parentCategoryId: string | number }) {
  return mallRequest<API.ApiBaseResult<CategoryData[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/mgmt-category/querySubCategory',
      method: 'POST',
      data: params,
    },
  );
}

export async function addCategoryParamsValue(data: { paramId: string; value: string }) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/supplier-biz/category/custom-param/addValue',
    method: 'POST',
    data,
  });
}

export async function createCategoryCustomParams(data: { categoryId: string; name: string }) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/supplier-biz/category/custom-param/create',
    method: 'POST',
    data,
  });
}

export async function getCustomParamsValueById(params: { paramId: string }) {
  return mallRequest<API.ApiBaseResult<string[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/supplier-biz/category/custom-param/getValues',
      method: 'GET',
      params,
    },
  );
}

export interface CategoryCustomParamsItem {
  name: string;
  paramId: string;
}

export async function queryCustomParamsByCategoryId(params: { categoryId: string }) {
  return mallRequest<API.ApiBaseResult<CategoryCustomParamsItem[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/supplier-biz/category/custom-param/queryByCategoryId',
      method: 'GET',
      params,
    },
  );
}
