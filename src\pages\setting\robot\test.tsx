import React from 'react';
import FormRender, { useForm } from 'form-render';

export default () => {
  const form = useForm();
  const schema =  {
    "type": "object",
    "properties": {
      "fr-37gw": {
        "title": "单行文本",
        "type": "string",
        "widget": "input"
      },
      "fr-ky5w": {
        "title": "下拉单选",
        "type": "string",
        "props": {
          "options": [
            {
              "label": "A",
              "value": "A"
            },
            {
              "label": "B",
              "value": "B"
            }
          ],
          "placeholder": "请选择"
        },
        "widget": "select"
      },
      "fr-9hw9": {
        "title": "滑动条",
        "widget": "slider"
      },
      "fr-4nsq": {
        "title": "日期选择",
        "type": "string",
        "props": {
          "placeholder": "请选择日期"
        },
        "widget": "datePicker"
      },
      "fr-i3qz": {
        "title": "点击多选",
        "type": "array",
        "props": {
          "options": [
            {
              "label": "A",
              "value": "A"
            },
            {
              "label": "B",
              "value": "B"
            },
            {
              "label": "C",
              "value": "C"
            }
          ],
          "direction": "row"
        },
        "widget": "checkboxes"
      }
    },
    "displayType": "row",
    "maxWidth": "340px"
  };

  const onFinish = (formData) => {
    console.log('formData:', formData);
  };

  return (
    <FormRender
      form={form}
      schema={schema}
      onFinish={onFinish}
      maxWidth={360}
      footer={true}
    />
  );
}
