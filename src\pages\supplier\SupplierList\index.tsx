import {useCurrency} from '@/modules/currency/application/currency';
import {useVender} from '@/modules/supplier/application/vender';
import type {Vender} from '@/modules/supplier/domain/vender';
import {isActiveVender, VenderStatusEnums,} from '@/modules/supplier/domain/vender';
import {PlusOutlined} from '@ant-design/icons';
import type {ProColumns} from '@ant-design/pro-table';
import {Button, Form, Modal, notification, Space} from 'antd';
import React, {useState} from 'react';
import {history, Link, useParams} from 'umi';
import {ProFormField, ProFormText} from '@ant-design/pro-form';
import {
  addSupplierGoods,
  bindingSupplierUrl,
  downloadSupplierTemplate,
  importSupplier
} from "@/modules/supplier/infra/api/vender";
import AddSupplierGoodsModal from "@/pages/supplier/SupplierList/components/AddSupplierGoodsModal";
import aliwangwang from "@/assets/images/aliwangwang.gif";
import Permission from "@/components/Permission";
import CustomPage from "@/components/CustomPage";
import UploadFile from "@/components/UploadFile";
import {commonExport} from "@/utils/comUtil";


const SupplierList: React.FC<Vender> = () => {
  const { venderId } = useParams<{ venderId: string }>();
  let supplierId=venderId;
  if (venderId===':venderId'){
    supplierId='';
  }
  const { currencyMap } = useCurrency();
  const { venderList, disable, active } = useVender(supplierId);
  const { actionRef, fetchList } = venderList;
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<Vender>();

  /**
   * 修改采购计划需求数量
   * @param record
   */
  const [form] = Form.useForm();
  const bindingStoreUrl = (record: Vender) => {
    Modal.confirm({
      icon: false,
      content: (
        <Form form={form}>
            <ProFormText label="链接" name="url" initialValue={record.aliStoreLink}/>
        </Form>
      ),
      onOk: function () {
        const params = {
          venderId: record.venderId,
          url: form.getFieldValue('url'),
        };

        bindingSupplierUrl(params).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '绑定成功'});
          }
        });

      },
    });
    form.resetFields();
  };

  //供应商添加商品
  const addGoods= (data: Vender) => {

    addSupplierGoods(data).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
        notification.success({message: '添加成功'});
      }
    });

    setModalVisible(false);
  }



  const downloadTemplate=()=>{
    Modal.confirm({
      icon: false,
      centered: true,
      content: (
        <Form  form={form}>
          <Space>
            <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
              <UploadFile limit={1} />
            </ProFormField>
            <a  style={{marginLeft:10}} onClick={function () {
              downloadSupplierTemplate().then(res=>{
                commonExport(res, '供应商导入模板');
              });
            }}>下载模板</a>
          </Space>
        </Form>
      ),
      onOk: function () {
        const link = form.getFieldValue('importExcelUrl')[0].link;

        importSupplier(link).then((result) => {
          if (result.status.success) {
            actionRef.current?.reloadAndRest?.();
            notification.success({message: '导入成功'});
          }
        });

      },
    });

  }

  const columns: ProColumns<Vender>[] = [
    {
      title: '名称',
      dataIndex: 'venderName',
      render: (v,record) => {
        return <>
          {record?.loginId ? <a onClick={function () { window.open("https://amos.alicdn.com/getcid.aw?v=3&uid="+record?.loginId+"&site=cnalichn&groupid=0&s=1&charset=UTF-8&fromid='") }}><img src={aliwangwang}/>&nbsp;&nbsp;</a> : null}
          <span>{record?.venderName}</span>
        </>
      }
    },
    {
      title: '等级',
      hideInSearch: true,
      dataIndex: 'level',
    },
    {
      title: '跨境供应商',
      hideInSearch: true,
      dataIndex: 'isCrossBorder',
      renderText: (val) => {
        return val ? "是" : "否";
      },
    },
    {
      title: '结算币种',
      hideInSearch: true,
      dataIndex: 'settleCurrency',
      renderText: (val) => {
        return currencyMap ? currencyMap[val] : val;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      hideInForm: true,
      valueEnum: {
        [VenderStatusEnums.ACTIVATED]: { text: '启用中', status: 'Processing' },
        [VenderStatusEnums.DISABLED]: { text: '禁用中', status: 'Error' },
      },
    },
    {
      title: '绑定店铺',
      dataIndex: 'platformProductId',
      hideInSearch:true,
      render: (v, record) => {
        const div=record.aliStoreLink!="" && record.aliStoreLink!=null?
          <span className="ant-badge ant-badge-status ant-badge-not-a-wrapper"><span
            className="ant-badge-status-dot ant-badge-status-processing"></span><span
            className="ant-badge-status-text">已绑定</span></span>:
          <span className="ant-badge ant-badge-status ant-badge-not-a-wrapper"><span
            className="ant-badge-status-dot  ant-badge-status-error"></span><span
            className="ant-badge-status-text">未绑定</span></span>;
        return (
          <>
            {div}
          </>
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      align: "center",
      render: (_, record) => {
        return <>
          <Space wrap={true} direction="horizontal" aria-colindex={3}>
            <Permission permissionKey={"purchase"}>
              {isActiveVender(record) ?
                <Button type="primary" size={"small"} danger ghost style={{fontSize:12,borderRadius:5}} onClick={() => disable(record.venderId)}>
                  禁用
                </Button> :
                <Button type="primary" size={"small"} ghost style={{fontSize:12,borderRadius:5}} onClick={() => active(record.venderId)}>
                  启用
                </Button>}
            </Permission>
            <Link key="show" to={`/purchase/supplier/detail/${record.venderId}`}>
              <Button type="primary" size={"small"} ghost style={{fontSize:12,borderRadius:5}}>
                查看
              </Button>
            </Link>
            {/*<Permission permissionKey={"purchase"}>*/}
              <Button type="primary" size={"small"} ghost style={{fontSize:12,borderRadius:5}}  onClick={() => bindingStoreUrl(record)}>
                绑定店铺
              </Button>
            {/*</Permission>*/}
          </Space>
        </>
      },
    },
  ];

  return (
    <>
    <CustomPage<Vender>
      actionRef={actionRef}
      rowKey="venderId"
      toolBarRender={() => [
        <Permission permissionKey={"purchase:purchase"}>
          <Button size={"small"} style={{fontSize: 13,borderRadius:"5px"}} key="level" type="primary" onClick={() => history.push('/purchase/supplier/create')}>
            添加供应商
          </Button>
        </Permission>,
        <Permission permissionKey={"purchase:purchase"}>
        <Button
          type="primary"
          size={"small"}
          icon={<PlusOutlined />}
          onClick={() => downloadTemplate()}
        >
          导入供应商
        </Button>
        </Permission>
      ]}
      request={fetchList}
      columns={columns}
      bordered={false}
      recordCreator={false}
      recordDelete={false}
      recordUpdater={false}
    />
      <AddSupplierGoodsModal visible={modalVisible} onCancel={() => setModalVisible(false)} data={dataSource} onFinish={addGoods}/>
    </>
  );
};

export default SupplierList;
