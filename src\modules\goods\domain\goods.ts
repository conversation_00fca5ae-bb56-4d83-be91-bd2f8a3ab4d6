import {MainSpecInfo, Spec} from "@/modules/product/domain/spu";

export type Goods = {
  referenceCost: number;
  relation: string;
  productType: string;
  skuName: string;
  nameEn: string;
  salesStatus: string;
  skuAttrValue: string;
  skuAttrName: string;
  minPurchaseQuantity: number;
  title: string;
  sku: string;
  skuImage: string;
  categoryName: string;
  brandName: string;
  spu: string;
  spuId: string;
  codeEAN: string;
  codeUPC: string;
  customCode: string;
  grossWeight: number;
  guarantyPeriod: number;
  mainSpecInfo?: MainSpecInfo;
  measuringUnit: string;
  netWeight: number;
  refProductLink: string;
  zhDeclaredName: string;
  enDeclaredName: string;
  declaredPrice: number;
  declaredCurrency: string;
  supplierName: string;
  currency: string;
  purchasePrice: string;
  lastPurchasePrice: number;
  firstPurchasePrice: number;
  sizeHeight: number;
  sizeLength: number;
  sizeWidth: number;
  specs: Spec[];
  skuId?: string;
  price: number;
  specialTags: string;
  tagsName: string;
  productMaterial: string;
  packingMaterial: string;
  promotionSuggestion: string;
  copywritingSuggestion: string;
};

export enum GoodsStatusTagsEnum  {
  '10'= 'success',
  '20'='info',
  '30'= 'info',
  '40'='warning',
  '50'='warning',
  '60'='error',
  '70'='error',
};

export enum GoodsStatusEnum  {
  '10'= '新品',
  '20'= '正常',
  '30'= '热销',
  '40'='滞销',
  '50'='清货',
  '60'='停售',
  '70'='销毁',
};





