import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import { useCurrency } from '@/modules/currency/application/currency';
import { formatDate } from '@/utils/utils';
import { memo, useMemo } from 'react';
import type { Item } from '../purchase-supplier/api';
import { VenderStatusMap } from '../purchase-supplier/api';
import { usePurchaseSupplierList, usePurchaseSupplierOptions } from '../purchase-supplier/purchaseSupplier';
import styles from './index.less';


const ShelfOrder = memo(() => {
  const setColumn = useColumn<Item>({ align: "center" });
  const setSearch = useSearch<Item>();
  const { actionRef, fetchEntityList } = usePurchaseSupplierList();
  const { suppliers } = usePurchaseSupplierOptions();
  const { currencyMap } = useCurrency();
  // column
  const columns = useMemo(() => {
    return [
      setSearch('公司名称', 'supplierId', {
        valueEnum: Object.fromEntries(suppliers.map(item => [item.venderId, item?.venderName])),
        fieldProps: {
          showSearch: true,
        }
      }),
      setSearch('公司状态', 'status', {
        valueEnum: VenderStatusMap
      }),
      setColumn('序号', 'index', {
        width: 48,
        fixed: 'left',
        render: (v, row, i) => i + 1
      }),
      setColumn('公司编码', 'venderCode', {
        width: 200,
      }),
      setColumn('公司名称', 'venderName', {
        width: 200,
      }),
      setColumn('公司英文', 'foreignName', {
        width: 200,
      }),
      setColumn('公司状态', 'status', {
        valueEnum: VenderStatusMap,
      }),
      setColumn('联系人', 'contractor', {
        render: (v, row) => row?.venderContractorDTO?.contractor
      }),
      setColumn('联系电话', 'cellphone', {
        render: (v, row) => row?.venderContractorDTO?.cellphone
      }),
      setColumn('国家', 'nationName', {
        render: (v, row) => row?.venderAddressInfoDTO?.nationName
      }),
      setColumn('地区', 'venderAddressInfoDTO', {
        render: (v, row) => `${row?.venderAddressInfoDTO?.provinceName || ''}${row?.venderAddressInfoDTO?.cityName || ''}${row?.venderAddressInfoDTO?.areaName || ''}${row?.venderAddressInfoDTO?.streetName || ''}` || '-'
      }),
      setColumn('地址详情', 'venderAddressInfoDTO', {
        render: (v, row) => `${row?.venderAddressInfoDTO?.detail}` || '-'
      }),
      setColumn('创建人', 'creator'),
      setColumn('创建时间', 'gmtCreate', {
        format: formatDate,
        sorter: true,
        width: 150
      }),
    ];
  }, [setColumn, suppliers, currencyMap]);

  return (
    <div className={styles.tableWrapper}>
      <CommonTable<Item>
        rowKey="venderId"
        autoScrollX
        defaultPageSize={20}
        tableRef={actionRef as any}
        // @ts-ignore
        fetchRequest={(params, sort, filter) => {
          const requestParams = { ...params, orderBy: { filed: 'GMT_CREATE', direction: sort?.gmtCreate === "ascend" ? 1 : 0 } };
          return fetchEntityList(requestParams, sort, filter)
        }}
        columns={columns}
        tableRest={{ size: "small" }}
      />
    </div>
  );
});

export default ShelfOrder;
