.page-purchase-order {
  height: calc(100vh - 138px);

  .common-table-spin {
    display: flex;
    flex-direction: column;

    &>div:nth-child(2) {
      flex: 1;
      overflow: hidden;
    }
  }

  .ant-pro-table {
    .ant-pro-table-search {
      padding-right: 0 !important;

      &>form {
        padding: 10px;

        &>.ant-row {
          &>.ant-col:nth-child(1) {
            .ant-form-item-row {
              .ant-form-item-control {
                max-width: unset !important;
              }
            }
          }
        }
      }
    }
  }
}