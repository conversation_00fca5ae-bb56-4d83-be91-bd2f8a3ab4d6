import type { CategoryParamsActions } from '@/modules/product/infra/components/CategoryParamsModal';
import CategoryParamsList from '@/modules/product/infra/components/CategoryParamsModal';
import type { CategorySpecActions } from '@/modules/product/infra/components/CategorySpecModal';
import CategorySpecModal from '@/modules/product/infra/components/CategorySpecModal';
import type { CreateCategoryActions } from '@/modules/product/infra/components/CreateCategoryModal';
import CreateCategoryModal from '@/modules/product/infra/components/CreateCategoryModal';
import type { UpdateCategoryActions } from '@/modules/product/infra/components/UpdateCategoryModal';
import UpdateCategoryModal from '@/modules/product/infra/components/UpdateCategoryModal';
import { createRef, forwardRef, useImperativeHandle } from 'react';

export type CategoryActionsProps = {
  create: (values: any) => Promise<boolean>;
  update?: (value: any) => Promise<boolean>;
  batchUpdate?: (value: any) => Promise<boolean>; // 是否批量操作
};

export type CategoryAction = {
  createAction?: CreateCategoryActions['create'];
  updateAction?: UpdateCategoryActions;
  setSpecAction?: CategorySpecActions['showCategorySpec'];
  setParamsAction?: CategoryParamsActions['showCategoryParams'];
};

export const CategoryActions = forwardRef((props: CategoryActionsProps, ref) => {
  const { batchUpdate, update, create } = props;
  const createActions = createRef<CreateCategoryActions>();
  const paramsActions = createRef<CategoryParamsActions>();
  const specActions = createRef<CategorySpecActions>();
  const updateActions = createRef<UpdateCategoryActions>();

  useImperativeHandle(ref, (): CategoryAction => {
    return {
      createAction: createActions.current?.create,
      updateAction: updateActions.current as any,
      setSpecAction: specActions.current?.showCategorySpec,
      setParamsAction: paramsActions.current?.showCategoryParams,
    };
  });

  return (
    <>
      {/* 新增分类 */}
      <CreateCategoryModal title="新增分类" ref={createActions} onFinish={create} />
      <UpdateCategoryModal
        title="编辑分类"
        ref={updateActions}
        onUpdate={update}
        onBatchUpdate={batchUpdate}
      />
      <CategoryParamsList ref={paramsActions} />
      <CategorySpecModal ref={specActions} />
    </>
  );
});
