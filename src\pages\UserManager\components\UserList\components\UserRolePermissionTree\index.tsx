import { useUserSystemMenu } from '@/modules/user-center/application/role';
import { Role } from '@/modules/user-center/domain/user';
import { bindRoleMenus, getRoleMenus, RolePermissionItem } from '@/modules/user-center/infra/role';

import type { TreeProps } from 'antd';
import { message, Spin, Tree } from 'antd';
import { difference } from 'lodash';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { findMenu, formatTreeData } from '../../utils/utils';

export interface UserRolePermissionTreeProps
  extends Omit<
    TreeProps,
    | 'checkable'
    | 'treeData'
    | 'defaultExpandedKeys'
    | 'checkedKeys'
    | 'checkedKeys'
    | 'onExpand'
    | 'onCheck'
    | 'expandedKeys'
  > {
  role: Role;
}

const UserRolePermissionTree = forwardRef((props: UserRolePermissionTreeProps, ref) => {
  const { role, ...rest } = props;
  const { userSystemMenus } = useUserSystemMenu();
  const [rolePermission, setRolePermission] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [checkedPermissionKeysValue, setCheckedPermissionKeysValue] = useState<React.Key[]>([]);
  const [halfCheckedKeys, setHalfCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  /** 筛选不全选的树节点 */
  useEffect(() => {
    const checkedKeys: string[] = [];
    const defaultHalfCheckedKeys: string[] = [];
    if (userSystemMenus?.length) {
      rolePermission.forEach((item) => {
        const menu = findMenu(userSystemMenus, item);
        if (menu && menu.childrenMenu) {
          const childList = menu.childrenMenu.map((c) => c.menuId);
          const isAllIn = childList.every((i) => rolePermission.includes(i));
          if (isAllIn) {
            checkedKeys.push(item);
          } else {
            defaultHalfCheckedKeys.push(item);
          }
        } else {
          checkedKeys.push(item);
        }
      });
      setExpandedKeys(userSystemMenus.map((i) => i.menuId));
      setHalfCheckedKeys(defaultHalfCheckedKeys);
      setCheckedPermissionKeysValue(checkedKeys);
    } else {
      setCheckedPermissionKeysValue([]);
    }
  }, [userSystemMenus, rolePermission]);

  const onCheck = (checkedKeysValue: any, info: any) => {
    setCheckedPermissionKeysValue(checkedKeysValue);
    setHalfCheckedKeys(info.halfCheckedKeys);
  };

  const fetchRoleMenus = () => {
    setLoading(true);
    getRoleMenus({
      roleId: role.roleId,
    })
      .then((res) => {
        const menusIds = (res.body?.menus || []).map((item) => {
          return item.menuId;
        });
        setRolePermission(menusIds);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (role.roleId) {
      fetchRoleMenus();
    }
  }, [role.roleId]);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  const changeOpt = useMemo(() => {
    const allSelectedMenuKeys = checkedPermissionKeysValue.concat(halfCheckedKeys);
    const addKeys = difference(allSelectedMenuKeys, rolePermission);
    const deleteKeys = difference(rolePermission, allSelectedMenuKeys);
    const opt: RolePermissionItem[] = [];
    addKeys.forEach((key) => {
      opt.push({
        opt: 0,
        menuId: key as string,
      });
    });
    deleteKeys.forEach((key) => {
      opt.push({
        opt: 1,
        menuId: key as unknown as string,
      });
    });
    return opt;
  }, [halfCheckedKeys, checkedPermissionKeysValue]);

  const onSubmit = () => {
    if (changeOpt.length) {
      message.error('该角色菜单权限没有更改,如需变更,请重新编辑');
      return;
    }
    bindRoleMenus({
      roleId: role.roleId,
      menuOpts: changeOpt,
    }).then((res) => {
      if (res.status.success) {
        message.success('保存成功');
        fetchRoleMenus();
      } else {
        message.error('保存失败');
      }
    });
  };

  useImperativeHandle(ref, () => ({
    submit: onSubmit,
    refresh: fetchRoleMenus,
    canSubmit: !!changeOpt.length,
  }));

  return (
    <Spin spinning={loading}>
      <Tree
        {...rest}
        checkable
        treeData={userSystemMenus ? formatTreeData(userSystemMenus) : []}
        defaultExpandedKeys={userSystemMenus?.map((item) => item.menuId)}
        checkedKeys={checkedPermissionKeysValue}
        onCheck={onCheck}
        onExpand={onExpand}
        expandedKeys={expandedKeys}
      />
    </Spin>
  );
});

export default UserRolePermissionTree;
