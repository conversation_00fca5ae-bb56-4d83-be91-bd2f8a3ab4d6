import { MomentInput } from "moment";
import moment from 'moment';
import { all, create } from 'mathjs';
import JS<PERSON><PERSON> from 'jszip';
import { isString } from "lodash";
export const isPhone = (phone: string) => /^0?1[1-9][0-9]\d{8}$/.test(phone);
export const isEmail = (email: string) =>
  /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/.test(email);
export const isQQ = (qq: string) => /^\d{5,11}$/.test(qq);
export const formatDate = (val: string | number, formatStr = 'YYYY-MM-DD HH:mm:ss') => {
  return moment(val as MomentInput).format(formatStr);
};

export const userNameReg = /^(?=.*[a-zA-Z]+)[a-zA-Z\d._]{5,30}$/;

// 保留小数点后几位（超过几位截取保留几位）
export const isUndef = (val: any) => val === undefined || val === null;
export const getFloat = (number: any, n: number) => {
  n = n ? parseInt(String(n)) : 0;
  if (n <= 0) {
    return Math.round(number);
  }
  number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n); //四舍五入
  return number;
};
export const math = create(all, {
  epsilon: 1e-12,
  matrix: 'Matrix',
  number: 'number', // 可选值：number BigNumber
  precision: 16,
  predictable: false,
  randomSeed: null,
});
export const onMultiply = (num1: number, num2: number) => {
  if (isUndef(num1) || isUndef(num2)) return 0;
  return math.multiply(math.bignumber(num1), math.bignumber(num2));
};

export interface DownFile {
  name: string
  url: string
}
// 打包图片并压缩
export const downZip = (allTypeCodeFile: string[] | DownFile[], fileName = '文件') => {
  const zip = new JSZip();
  let result = [];
  if (isString(allTypeCodeFile[0])) {
    for (let i in allTypeCodeFile) {
      const item = allTypeCodeFile[i] as string
      let promise = getFileBlob(item).then((res: any) => {
        if (res) {
          let format = item.substring(
            item.lastIndexOf('.'),
            item.length
          );
          zip.file(i + format, res, { binary: true });
        }
      });
      result.push(promise);
    }
  } else {
    for (let i in allTypeCodeFile) {
      let promise = getFileBlob(allTypeCodeFile[i].url).then((res: any) => {
        if (res) {
          const item = allTypeCodeFile[i] as DownFile
          let format = item.url.substring(
            item.url.lastIndexOf('.'),
            item.url.length
          );
          zip.file(item.name + format, res, { binary: true });
        }
      });
      result.push(promise);
    }
  }

  Promise.all(result).then(() => {
    zip.generateAsync({ type: 'blob' }).then((res) => {
      let evt = document.createEvent('HTMLEvents');
      //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      let aLink = document.createElement('a');
      evt.initEvent('click', true, true);
      aLink.download = `${fileName}.zip`;
      aLink.href = URL.createObjectURL(res);
      aLink.click();
    });
  });
};

const getFileBlob = (url: string) => {
  return new Promise((resolve, reject) => {
    let request = new XMLHttpRequest();
    request.open('GET', url, true);
    request.responseType = 'blob';
    request.onload = (res: any) => {
      if (res.target.status === 200) {
        resolve(res.target.response);
      } else {
        reject(res);
      }
    };
    request.send();
  });
};
