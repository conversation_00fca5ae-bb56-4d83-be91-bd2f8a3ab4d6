import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {PurchaseInstorage} from "@/pages/warehouse/ScanArrival/data";
import moment from "moment";
import {pageQueryArrivalList} from "@/modules/warehouse/infra/api/warehouse";

const TableList: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryArrivalList({
      ...params,
    });
  });
  const columns: ProColumns<PurchaseInstorage>[] = [
    {
      title: '采购单号',
      dataIndex: 'orderCode',
      colSize: (6 / 24)
    },
    {
      title: '入库员',
      dataIndex: 'createrName',
      colSize: (6 / 24)
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      hideInSearch: true,
    },
    {
      title: '入库数量',
      dataIndex: 'quantityReceived',
      hideInSearch: true,
      render: (v, record) => {
        return <>{record?.quantityReceived}</>;
      }
    },
    {
      title: '取消入库',
      dataIndex: 'isCancelInstorage',
      hideInSearch: true,
      render: (v, record) => {
        return <div style={{fontSize: 12}}>{record.isCancelInstorage == 1 ? <span style={{color: "red"}}>是</span>: '--'}</div>
      }
    },
    {
      title: '入库时间',
      dataIndex: 'gmtCreate',
      hideInSearch: true,
      render: (v, record) => {
        return <div style={{fontSize: 12}}>{moment(record.gmtCreate).format("YYYY-MM-DD HH:mm:ss")}</div>
      }
    }
  ];

  return (
    <>
      <CustomPage<PurchaseInstorage>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};

export default TableList;
