import {ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {Form, Modal, ModalProps} from "antd";
import {useRequest} from "ahooks";
import {getApplyReducePayment} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {ExclamationCircleOutlined} from "@ant-design/icons";
import React from "react";


// 定义参数格式
export type CreateModalProps = {
  orderId: string
  onFinish: (values: any) => void;
} & ModalProps;


export const ApplyReducePaymentModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish,orderId, ...rest } = props;
  const { data } = useRequest(() => getApplyReducePayment(orderId).then((res) => res.body));

  return <Modal {...rest}  title="申请退款/优惠减免" onOk={()=>form.submit()}>
      <Form form={form} labelCol={{flex: '110px'}} onFinish={onFinish}>
        {/*{ data.shippingFee.setValue(0)}*/}
        <Form.Item label="平台总金额" style={{height:"10px",fontWeight:'bold',marginTop:"-10px"}}>
          <span className="ant-form-text">{data?.platformOrderAmount == null ? '--' : data.platformOrderAmount?.toFixed(2)}</span>
          <span className="ant-form-text">平台运费：{data?.platformShippingFee == null ? '--' : data.platformShippingFee?.toFixed(2)}</span>
        </Form.Item>
        <Form.Item label="本地总金额" style={{height:"10px",fontWeight:'bold'}}>
          <span className="ant-form-text">
            {data?.shippingFee == null && data?.purchaseTotalAmount == null ? '--' : (data.purchaseTotalAmount+data.shippingFee)?.toFixed(2)}
          </span>
          <span className="ant-form-text">本地运费：{data?.shippingFee == null ? '--' : data.shippingFee?.toFixed(2)}</span>
          <span className="ant-form-text">商品金额：{data?.purchaseTotalAmount == null ? '--' : data.purchaseTotalAmount?.toFixed(2)}</span>
        </Form.Item>
        <Form.Item label="差额" style={{height:"10px",fontWeight:'bold', marginLeft:"42px",paddingBottom:"20px"}}>
          <span className="ant-form-text">{(data?.platformOrderAmount == null || data?.shippingFee == null || data?.purchaseTotalAmount == null) ? '--' :
            (data.platformOrderAmount - (data.purchaseTotalAmount+data.shippingFee))?.toFixed(2)}</span>
        </Form.Item>

        <ProFormText
          width="md"
          name="applyRefundAmount"
          label="平台涨价差额"
          initialValue={data?.applyRefundAmount}
        />
        <ProFormText
          width="md"
          name="applyPromotionAmount"
          label="平台优惠金额"
          initialValue={data?.applyPromotionAmount}
        />
        <ProFormTextArea
          width="md"
          name="remark"
          label="申请备注"
        />
      </Form>
      </Modal>
}

export default ApplyReducePaymentModal;
