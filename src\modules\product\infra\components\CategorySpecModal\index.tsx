import { useCategorySpec } from '@/modules/product/application/category';
import type { CategoryData } from '@/modules/product/domain/category';
import type { ProductSpecData } from '@/services/productSpec';
import { PlusOutlined } from '@ant-design/icons';
import type { ModalFormProps, ProFormInstance } from '@ant-design/pro-form';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Button, Modal, Row, Tag } from 'antd';
import { omit } from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

export type CreateCategoryParamsModalProps<T> = Pick<
  ModalFormProps<T>,
  'visible' | 'onVisibleChange' | 'onFinish'
>;

export type CategorySpecActions = {
  showCategorySpec: (row: CategoryData) => void;
};

const CategorySpecModal = forwardRef((props, ref) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [category, setCategory] = useState<CategoryData>();
  const formRef = useRef<ProFormInstance<any>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const actionRef = useRef<ActionType>();
  const { categorySpec, update, create } = useCategorySpec(category);
  const actions: CategorySpecActions = {
    showCategorySpec: (row: CategoryData) => {
      setCategory(row);
      setModalVisible(true);
    },
  };

  useImperativeHandle(ref, () => actions);

  useEffect(() => {
    setEditableRowKeys([]);
  }, [modalVisible]);

  const columns: ProColumnType<ProductSpecData>[] = [
    {
      title: '规格名称',
      dataIndex: 'name',
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入规格名称',
          },
        ],
      },
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      align: 'center',
      render: (v, record) => {
        return (
          <Tag color={record.required ? 'success' : 'error'}>{record.required ? '是' : '否'}</Tag>
        );
      },
      fieldProps: {
        options: [
          {
            value: true,
            label: '是',
          },
          {
            value: false,
            label: '否',
          },
        ],
      },
      valueType: 'select',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择是否必填',
          },
        ],
      },
    },
    {
      title: '规格值',
      dataIndex: 'values',
      align: 'center',
      fieldProps: {
        mode: 'tags',
        style: {
          marginBottom: 0,
        },
        placeholder: '请输入规格值',
      },
      formItemProps: {
        rules: [{ type: 'array', min: 1, message: '请填写规格值，至少填写一个规格值' }],
      },
      valueType: 'select',
      render: (v, record) => {
        return (
          <>
            {record?.values?.map((item) => {
              return <Tag>{item}</Tag>;
            })}
          </>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      width: 120,
      render: (_, row) => [
        <a
          key="edit"
          onClick={() => {
            actionRef.current?.startEditable(row.specId);
          }}
        >
          编辑
        </a>,
      ],
    },
  ];
  return (
    <Modal
      title="设置自定义规格"
      width={1000}
      bodyStyle={{ minHeight: 400 }}
      visible={modalVisible}
      onCancel={() => setModalVisible(false)}
      footer={
        <Row justify="space-between">
          <a
            onClick={() => {
              actionRef.current?.addEditRecord({ specId: 'new' }, { position: 'top' });
              setEditableRowKeys(['new']);
            }}
          >
            <PlusOutlined /> 添加参数
          </a>
          <Button onClick={() => setModalVisible(false)}>关闭</Button>
        </Row>
      }
    >
      <EditableProTable<ProductSpecData>
        key={category?.categoryId}
        formRef={formRef}
        actionRef={actionRef}
        bordered
        rowKey="specId"
        columns={columns}
        value={categorySpec?.current}
        recordCreatorProps={false}
        editable={{
          type: 'multiple',
          actionRender: (row, config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
          editableKeys,
          onSave: async (key, row) => {
            let res;
            if (row.specId !== 'new') {
              res = await update({
                ...row,
                specId: row.specId,
              });
            } else {
              res = await create({
                ...omit(row, ['specId']),
                categoryId: category?.categoryId as string,
              });
            }
            return res;
          },
          onChange: setEditableRowKeys,
        }}
      />
    </Modal>
  );
});

export default CategorySpecModal;
