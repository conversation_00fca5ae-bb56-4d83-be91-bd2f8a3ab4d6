import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {FinanceExpense} from "@/pages/financeExpense/data";
import mallRequest1 from "@/utils/mallRequest1";

export async function applyExpense(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/sales-mgmt-biz/sales-center/finance/expense/applyFinanceExpense',
    data,
  });
}

export async function editExpense(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/sales-mgmt-biz/sales-center/finance/expense/saveFinanceExpense',
    data,
  });
}

export async function invalidExpense(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/sales-mgmt-biz/sales-center/finance/expense/invalidExpense',
    data,
  });
}

export async function auditFinanceExpense(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/sales-mgmt-biz/sales-center/finance/expense/auditFinanceExpense',
    data,
  });
}

export async function getExpenseAuditConfig(data?: any) {
  return mallRequest<API.ApiBaseResult<any>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/sales-mgmt-biz/sales-center/setting/finance/queryByAuditModel',
    params: {'auditModel': data?.auditModel},
  });
}

export type QueryExpenseParams = {
  cooperateModal?: string;
}& API.QueryPageParams;

export async function getExpenseList(params: QueryExpenseParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<FinanceExpense[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/sales-mgmt-biz/sales-center/finance/expense/pageQuery',
      method: 'POST',
      data: params,
    },
  );
}

//财务导出
export async function exportFinanceExpense(data?: any) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/finance/expense/exportFinanceExpense',
    data,
    responseType: 'blob',
  });
}


export async function getExpenseDetail(expenseId?: string) {
  return mallRequest<API.ApiBaseResult<FinanceExpense>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/sales-mgmt-biz/sales-center/finance/expense/getFinanceExpense',
    params: {
      id: expenseId,
    },
  });
}

export async function getPaymentBankList() {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/finance/payment-bank/queryPaymentBankList',
  });
}

export async function getFinanceSubjectList() {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/finance/subject/getFinanceSubject',
  });
}

export async function expenseBatchPayment(data: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/finance/expense/batchAuditFinanceExpense',
    data
  });
}

export async function copyExpenseById(expenseId: string) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/finance/expense/copyFinanceExpense',
    params: {id: expenseId}
  });
}


//报销数据汇总
export async function financeExpenseDataCount(data?: any) {
  return mallRequest<API.ApiBaseResult<null>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/finance/expense/financeExpenseDataCount',
    data,
  });
}




