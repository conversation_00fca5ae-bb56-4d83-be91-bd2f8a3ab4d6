import type {ProColumns} from '@ant-design/pro-table';
import React, {useRef, useState} from 'react';
import {Card, Input, message, Select, Table} from "antd";
import Search from "antd/es/input/Search";
import {Option} from "antd/es/mentions";
import {scanSign} from "@/modules/warehouse/infra/api/warehouse";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {OrderStatusEnum} from "@/modules/purchaseOrder/domain/purchaseOrder";
import moment from "moment";

export const ScanArrivalPage = () => {
  // const scanInput = useRef(null)
  const [scanCodeType, setScanCodeType] = useState<string>("trackingNumber");
  const [dataSource, setDataSource] = useState<any>();
  const [scanCodeString, setScanCodeString] = useState<string>("");

  /**
   * 扫描请求
   * @param value
   */
  const scanCode = (code: string) => {
    const params = {
        'code': code,
        'scanArrivalType':scanCodeType
    };
    scanSign(params).then((result) => {
      setScanCodeString("");
      if (result.status.success) {
        message.success("扫描成功！！")
        setDataSource(result.body)
      }
    });
  };

  const columns: ProColumns<PurchaseOrder, 'text'>[] = [
    {
      title:"采购单号",
      align:"center",
      dataIndex:'orderCode',
    },
    {
      title:"采购单状态",
      align:"center",
      dataIndex:'orderStatus',
      render: (v, record) => {
        const orderStatus = OrderStatusEnum[record?.orderStatus];
        return (orderStatus != undefined ? (
          <span style={{fontSize:"12px"}}>
              <span className="ant-badge-status-dot ant-badge-status-success"/>
              <span className="ant-badge-status-text" style={{fontSize:"12px"}}>{orderStatus}</span>
            </span>
        ) : null);
      }
    },
    {
      title: '快递单号',
      align:"center",
      dataIndex: 'trackingNumber',
    },
    {
      title: '签收时间',
      align:"center",
      dataIndex: 'signDate',
      render: (v, record) => {
        return moment(record?.signDate as number).format("YYYY-MM-DD HH:mm");
      }
    },
    {
      title: '扫描人',
      align:"center",
      dataIndex: 'signer',
    }
  ];
  return (
    <>
        <Card style={{padding:"0", marginBottom:10}} title={"扫描区域"} headStyle={{height:25,fontWeight:"bold"}}>
          <Input.Group compact >
            <Select  defaultValue="trackingNumber" style={{ width: 150}} onChange={setScanCodeType}>
              <Option value="trackingNumber">快递单号</Option>
            </Select>
            <Search placeholder="扫描单号" value={scanCodeString} onChange={(e)=>setScanCodeString(e.target.value)} onSearch={scanCode} style={{ width: 500}} />
          </Input.Group>
        </Card>
          <Table
            dataSource={dataSource}
            columns={columns}
            search={false}
            size={'small'}
            rowKey="supplierName"
            bordered
            pagination={false}
          />

    </>
  );
};
export default ScanArrivalPage;
