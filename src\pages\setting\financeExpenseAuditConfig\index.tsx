import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Button, Space} from "antd";
import {FinanceExpenseAuditConfig} from "@/pages/setting/financeExpenseAuditConfig/data";
import {pageQueryExpenseAudit} from "@/modules/setting/infra/api/financeExpenseAuditConfig";
import Permission from "@/components/Permission";
import {auditModelEnum} from "@/modules/financeExpense/domain/expense";
import EditConfigModal from "@/pages/setting/financeExpenseAuditConfig/components/EditConfigModal";

const TableList: React.FC = () => {

  const [editConfigModal, setEditConfigModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<FinanceExpenseAuditConfig>();
  const {fetchList, actionRef} = useRequestTable((params) => {
    return pageQueryExpenseAudit({
      ...params,
    });
  });
  const columns: ProColumns<FinanceExpenseAuditConfig>[] = [
    {
      title: '单据类型',
      dataIndex: 'auditModel',
      valueType: 'select',
      valueEnum: auditModelEnum,
      colSize: (6 / 24)
    },
    {
      title: '审核名称',
      dataIndex: 'auditRole',
      hideInSearch: true,
    },
    {
      title: '审核步骤',
      dataIndex: 'step',
      hideInSearch: true,
    },
    {
      title: '默认审核人',
      dataIndex: 'defaultAuditName',
      hideInSearch: true,
    },
    {
      title: '是否核算',
      dataIndex: 'isCostAudit',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.isCostAudit==1?<span style={{color: "green"}}>是</span>: '--'}</div>;
      }
    },
    {
      title: '是否付款',
      dataIndex: 'isPaymentAudit',
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.isPaymentAudit==1?<span style={{color: "green"}}>是</span>: '--'}</div>;
      }
    },


    // {
    //   title: '所有审核人',
    //   dataIndex: 'auditRoleId',
    //   hideInSearch: true,
    //   width: 200,
    //   render: (v, record) => {
    //     return <div style={{width: 200}}>{record?.auditRoleId}</div>;
    //   }
    // },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 400,
      render: (v, record) => {
        return (
          <Space>
            <Button type="primary" size={"small"} ghost onClick={()=>{setEditConfigModal(true);setCurrentRow(record)}}>
              编辑
            </Button>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<FinanceExpenseAuditConfig>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Permission permissionKey={"purchase:purchase"}>
                <Button size={"small"} key="level" type="primary" onClick={() => {
                  setEditConfigModal(true);
                  setCurrentRow({id:null});
                }}>
                  添加配置
                </Button>
              </Permission>
            ]
            return [...options, ...dom];
          }
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <EditConfigModal visible={editConfigModal} configId={currentRow?.id} onCancel={()=>setEditConfigModal(false)} onFinish={()=>{setEditConfigModal(false);actionRef.current?.reload()}}/>
    </>
  );
};

export default TableList;
