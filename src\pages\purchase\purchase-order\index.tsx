import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import { Button, message, Row } from 'antd';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { PurchaseOrderStatus, queryAbandon, queryAudit, queryComplete, queryDelete, queryExport, queryOrderList } from './api';
import { formatDate } from '@/utils/utils';
import SelectVender from '@/components/Select/SelectVender';
import SelectInput, { solveSelect } from '@/components/Select/SelectInput';
import { history } from "umi"
import "./index.less"
import type { OrderItem } from './api';
import type { TabsItem } from '@/components/TableTabs';
import type { CommonTableAction, CommonTableProps } from '@/components/Common-UI/CommonTable/type';
import SelectEntity from '@/components/Select/SelectEntity';
import SelectWarehouse from '@/components/Select/SelectWarehouse';

type TableProps = CommonTableProps<OrderItem>;

const PurchaseOrder = memo(() => {
  const setColumn = useColumn<OrderItem>({ align: "center" });
  const setSearch = useSearch<OrderItem>();
  const [selecteRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const tableRef = useRef<CommonTableAction>();
  const [curTab, setCurTab] = useState("ALL")
  const [curSearchParmas, setCurSearchParmas] = useState<any>({})

  // 1) Tabs
  const tabs: TabsItem[] = useMemo(() => {
    return [{ key: 'ALL', tab: '全部' }, ...Object.keys(PurchaseOrderStatus).map((key) => ({ key, tab: PurchaseOrderStatus[key as unknown as keyof typeof PurchaseOrderStatus] }))];
  }, []);

  // 2) Request
  const fetchRequest = useMemo<TableProps['fetchRequest']>(() => {
    return async (params: any, sort: any) => {
      // 1 获取数据
      const { current, pageSize, tab, time, order, ...rest } = params;
      const { gmtCreate } = sort;
      if (gmtCreate) rest.orderBy = { filed: 'GMT_CREATE', direction: gmtCreate === "ascend" ? 0 : 1 }
      const pageCondition = { pageSize, pageNum: current };
      // 2 查询参数
      solveSelect(time, rest, 'time');
      solveSelect(['purchaseOrderCodeList', order], rest, 'batch');
      setCurTab(tab)
      setCurSearchParmas({ pageCondition, purchaseOrderStatus: tab !== "ALL" ? tab : undefined, ...rest })
      const { body } = await queryOrderList({ pageCondition, purchaseOrderStatus: tab !== "ALL" ? tab : undefined, ...rest });
      // 3 返回值
      return {
        data: body?.items || [],
        total: body?.pageMeta?.total || 0,
      };
    };
  }, []);

  // 3) Column
  const columns = useMemo(() => {
    return [
      setSearch('', 'time', {
        renderFormItem: () => (
          <SelectInput
            valueType="RangePicker"
            options={[
              {
                value: 'gmtCreateStartTime gmtCreateEndTime',
                label: '创建时间',
              },
              {
                value: 'gmtstockinOrderStockingStart gmtstockinOrderStockingEnd',
                label: '预计到货时间',
              },
            ]}
          />
        ),
      }),
      setSearch("采购单号", "order", { placeholder: "批量请使用中英文或空格隔开" }),
      setSearch("供应商名称", "supplierId", { renderFormItem: () => <SelectVender /> }),
      setSearch("收货仓库", "purchaseWarehouse", { renderFormItem: () => <SelectWarehouse /> }),
      setSearch("采购公司主体", "purchaseEntity", { renderFormItem: () => <SelectEntity /> }),
      setColumn('采购单号', 'purchaseOrderCode', { width: 140, render: (_, row) => <a onClick={() => history.push(`/purchase-check/${row.id}`, { isCheck: 1 })}>{row.purchaseOrderCode}</a> }),
      setColumn('供应商名称', 'supplierName'),
      setColumn('采购公司主体', 'purchaseEntity', { width: 140 }),
      setColumn('收货仓库', 'purchaseWarehouse'),
      setColumn('收货地址', 'warehouseDetails'),
      setColumn('采购总金额', 'totalAmount'),
      setColumn('单据状态', 'purchaseOrderStatus', { valueEnum: PurchaseOrderStatus }),
      setColumn('审核状态', 'auditStatus', { valueEnum: { 0: "未审核", 1: "已审核" }, hideInSearch: false, searchProp: 'purchaseAuditStatus' }),
      setColumn('收货情况', 'receivingInfo'),
      setColumn('预计到货时间', 'estimatedTimeArrival', { format: formatDate, width: 150 }),
      setColumn('备注', 'remark'),
      setColumn('创建人', 'purchaseUserName'),
      setColumn('创建时间', 'gmtCreate', { format: formatDate, width: 150, sorter: true }),
    ];
  }, [setColumn, setSearch]);

  // 4) Operate
  const actions = useMemo<TableProps['actions']>(() => {
    return {
      items: [
        {
          name: '编辑',
          onAction: (row) => history.push(`/purchase-edit/${row.id}`),
          show: (row) => [0].includes(row.purchaseOrderStatus),
        },
        {
          name: '审核',
          title: row => `确定 ${row.purchaseOrderCode} 审核通过？`,
          onAction: async (row) => {
            const res = await queryAudit({ purchaseOrderId: row.id })
            res.status.success && message.success("操作成功")
          },
          show: (row) => [0].includes(row.purchaseOrderStatus),
        },
        {
          name: '作废',
          title: row => `确定 ${row.purchaseOrderCode} 作废？`,
          onAction: async (row) => {
            const res = await queryAbandon({ purchaseOrderId: row.id })
            res.status.success && message.success("操作成功")
          },
          show: (row) => [40].includes(row.purchaseOrderStatus),
        },
        {
          name: '收货完成',
          title: row => `确定完成 ${row.purchaseOrderCode} 收货？`,
          onAction: async (row) => {
            const res = await queryComplete({ purchaseOrderId: row.id })
            res.status.success && message.success("操作成功")
          },
          show: (row) => [45].includes(row.purchaseOrderStatus),
        },
        {
          name: '删除',
          title: row => `确定删除 ${row.purchaseOrderCode}？`,
          onAction: async (row) => {
            const res = await queryDelete({ purchaseOrderId: row.id })
            res.status.success && message.success("操作成功")
          },
          show: (row) => [0].includes(row.purchaseOrderStatus),
        },
      ],
      align: "center",
      width: 160
    };
  }, []);

  // 5) ToolTip
  const toolBarRender = useCallback(() => {
    const onExport = async () => {
      // 有勾选，则将勾选的数据导出
      let res: any = null
      if (selecteRowKeys.length > 0) {
        res = await queryExport({ pageCondition: { pageNum: 1, pageSize: selecteRowKeys.length }, purchaseOrderCodeList: selecteRowKeys })
      }
      // 未勾选，则将当前筛选条件的数据全部导出
      else {
        res = await queryExport(curSearchParmas)
      }
      res.status.success && message.success("导出任务创建成功")
    }

    return (
      <Row justify="end" className="toolbar-box">
        <Button type="primary" onClick={onExport}>
          导出
        </Button>
        <Button type="primary" onClick={() => history.push('/purchase-add')}>
          新增采购单
        </Button>
      </Row>
    );
  }, [curSearchParmas, selecteRowKeys]);

  return (
    <div className="page-purchase-order">
      <CommonTable<OrderItem>
        rowKey="purchaseOrderCode"
        flex
        autoScrollX
        tabOption={tabs}
        defaultPageSize={20}
        labelWidth={100}
        tableRef={tableRef}
        fetchRequest={fetchRequest}
        columns={columns}
        actions={["50", "70"].includes(curTab) ? undefined : actions}
        toolBarRender={toolBarRender}
        tableRest={{ size: "small" }}
        onSelection={(selectedRowKeys) => {
          setSelectedRowKeys(selectedRowKeys as string[]);
        }}
      />
    </div>
  );
});

export default PurchaseOrder;
