import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import {usePurchasePlan} from '@/modules/purchasePlan/application/purchasePlan';
import {PlanState, PlanStateText} from "@/modules/purchasePlan/domain/purchasePlan";
import {Button, Cascader, message, Space} from "antd";
import CustomPage from "@/components/CustomPage";
import CreatePurchasePlanModal from './components/CreatePurchasePlanModal';
import SetStrategyModal from './components/SetStrategyModal';
import type {CreatePlanParmas} from '@/modules/purchasePlan/infra/api/purchasePlan';
import {createOrder, createPurchasePlan, getUser} from '@/modules/purchasePlan/infra/api/purchasePlan';
import type {PurchasePlan} from "@/pages/PurchasePlanList/data";
import PurchasePlanLogModal from "@/pages/PurchasePlanList/components/PurchasePlanLogModal";
import {Link} from "umi";
import Permission from "@/components/Permission";

const TableList: React.FC = () => {

  const planDomain = usePurchasePlan();
  const { fetchList, actionRef } = planDomain.purchasePlanList;
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [StrategyModal, setStrategyModal] = useState<boolean>(false);
  const [PlanLogModal, setPlanLogModal] = useState<boolean>(false);

  const [selectedRowsState, setSelectedRows] = useState<PurchasePlan[]>([]);
  const [searchPlanLogParams, setSearchPlanLogParams] = useState<any>();

  /**
   * 导入创建采购计划请求
   * @param values
   */
  const handleCreate = (values: CreatePlanParmas) => {

    createPurchasePlan(values).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
        message.success("创建成功")
      }
    });
    // 处理完成后把弹窗关闭
    setModalVisible(false)
  }

  const createPurchaseOrder = (values: PurchasePlan[]) => {
    const array: string[]=[];
    values.map((item) => {
      array.push(item.id);
    })

    const obj={
      "ids":array,
    }

    createOrder(obj).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
        message.success("后台生成中，请稍后查看!")
      }
    });

  }


  const doSearchPurchasePlanLog = (values: PurchasePlan) => {
    setPlanLogModal(true);
    const params = {
      planId:values.id,
      planGoodsIds:null
    }
    setSearchPlanLogParams(params)
  }
  const { SHOW_CHILD } = Cascader;

  interface Option {
    value: string | number;
    label: string;
    children?: Option[];
    checkbox: boolean
  }
  const options: Option[] = [
    {
      label: '采购计划标签',
      value: 'bamboo',
      checkbox: false,
      children: [
        {
          label: '加急',
          value: 'little1'
        },
        {
          label: '侵权',
          value: 'little2'
        },
        {
          label: '季节性商品',
          value: 'little3',
          children:[
            {
              label: '加急',
              value: 'little11'
            },
            {
              label: '侵权',
              value: 'little22'
            }
          ]
        },
        {
          label: '缺货',
          value: 'little4'
        },
      ],
    },
  ];
  const columns: ProColumns<PurchasePlan>[] = [
    {
      title: '计划单号',
      dataIndex: 'id',
    },
    {
      title: '采购仓库',
      dataIndex: 'warehouseName',
      formItemProps: {
        name: 'warehouseName',
      },
    },
    {
      title: 'sku款数',
      dataIndex: 'skuCount',
      hideInSearch: true,
    },
    {
      title: '总金额',
      dataIndex: 'amount',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        [PlanState.WAIT]: {
          text: PlanStateText.WAIT,
          status: 'Processing',
        }, [PlanState.PART_CREATED]: {
          text: PlanStateText.PART_CREATED,
          status: 'Warning',
        }, [PlanState.ALL_CREATED]: {
          text: PlanStateText.ALL_CREATED,
          status: 'Success',
        }
        // , [PlanState.CANCEL]: {
        //   text: PlanStateText.CANCEL,
        //   status: 'Success',
        // }
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: true,
      renderFormItem: ()=>{
        return (  <Cascader
          style={{ width: '200px' }}
          options={options}
          onChange={console.log(111)}
          multiple
          maxTagCount="responsive"
          showCheckedStrategy={SHOW_CHILD}
          // defaultValue={}
        />);
      }
    },
    {
      title: '操作人',
      hideInSearch: true,
      dataIndex: 'createrName',
    },
    {
      title: '生成时间',
      valueType: 'dateTime',
      hideInSearch: true,
      dataIndex: 'gmtCreate',
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      hideInTable: true,
      hideInSearch: true,
      valueType: 'dateTimeRange',
      search: {
        transform: (values) => {
          if (values && values.length) {
            const [gmtCreateDateStart, gmtCreateDateEnd] = values;
            return {
              gmtCreateDateStart,
              gmtCreateDateEnd,
            };
          }
          return values;
        },
      },
    },
    {
      title: '操作',
      align: 'center',
      hideInSearch: true,
      width: 220,
      render: (v, record) => {
        return (
          <Space split={<span style={{color: "rgb(24, 144, 255)"}}>|</span>}>
            <a key="log" type="primary"  onClick={() => doSearchPurchasePlanLog(record)}>
              日志
            </a>
            <Link key="show"  to={`/purchase/purchase/purchasePlan/purchaseCheck/${record.id}`}>
              <a key="level" type="primary">
                审核
              </a>
            </Link>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<PurchasePlan>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        toolBarRender={() => [
          <Permission permissionKey={"purchase:purchase:purchasePlan:importPlan"}>
            <Button size={"small"} key="level" type="primary" onClick={() => setModalVisible(true)}>
              导入计划
            </Button>
          </Permission>,
          <Permission permissionKey={"purchase:purchase:purchasePlan:createPurchaseOrder"}>
            <Button size={"small"} key="level2" type="primary" onClick={() => createPurchaseOrder(selectedRowsState)}>
              生成采购单
            </Button>
          </Permission>
        ]}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      <CreatePurchasePlanModal visible={modalVisible} onCancel={() => setModalVisible(false)} onFinish={handleCreate} />
      <SetStrategyModal visible={StrategyModal} onCancel={() => setStrategyModal(false)} onFinish={handleCreate} />
      <PurchasePlanLogModal visible={PlanLogModal} onCancel={() => setPlanLogModal(false)} searchPlanLogParams={searchPlanLogParams} onFinish={function (){setPlanLogModal(false)}}/>
    </>
  );
};

export default TableList;
