import CommonTable from '@/components/Common-UI/CommonTable';
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import useLocael from '@/hooks/useLocael';
import { <PERSON><PERSON>, Row } from 'antd';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import type { CommonTableAction, CommonTableProps } from '@/components/Common-UI/CommonTable/type';

type TableProps = CommonTableProps<any>;

const ShelfOrder = memo(() => {
  const { $t } = useLocael();
  const setColumn = useColumn<any>();
  const setSearch = useSearch<any>();
  const [selecteRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const tableRef = useRef<CommonTableAction>();

  // 1) Request
  const fetchRequest = useMemo<TableProps['fetchRequest']>(() => {
    return async (params) => {
      // 1 获取数据
      const { current, pageSize, ...rest } = params;
      const pageCondition = { pageSize, pageNum: current };
      return {
        data: [
          { id: 1, a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 1, h: 8 },
          { id: 2, a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 2, h: 8 },
          { id: 3, a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 1, h: 8 },
          { id: 4, a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 2, h: 8 },
        ],
        total: 4,
      };
      // // 2 查询参数
      // const {  body } = await queryPage({ pageCondition, ...rest });
      // // 3 返回值
      // return {
      //   data: body?.items || [],
      //   total: body?.pageMeta?.total || 0,
      // };
    };
  }, []);

  // 2) Column
  const columns = useMemo(() => {
    return [
      setColumn('上架单', 'a'),
      setColumn('推荐库位', 'b'),
      setColumn('上架库位', 'c'),
      setColumn('产品编码', 'd'),
      setColumn('上架数量', 'e'),
      setColumn('仓库', 'f', {
        hideInSearch: false,
        renderFormItem: () => <div>123</div>,
      }),
      setColumn('状态', 'g', {
        hideInSearch: false,
        valueEnum: { 1: '待上架', 2: '已完成' },
      }),
      setColumn('上架员/上架时间', 'h'),
    ];
  }, [setColumn]);

  // 3) Operate
  const actions = useMemo<TableProps['actions']>(() => {
    return {
      items: [
        {
          name: '编辑',
          onAction: (row) => {},
        },
        {
          name: '确认上架',
          onAction: (row) => {},
        },
      ],
    };
  }, []);

  // 4) ToolTip
  const toolBarRender = useCallback(() => {
    return (
      <Row justify="end" className="toolbar-box">
        <Button type="primary" disabled={selecteRowKeys.length === 0}>
          批量上架
        </Button>
        <Button type="primary" disabled={selecteRowKeys.length === 0}>
          批量导出
        </Button>
        <Button type="primary" disabled={selecteRowKeys.length === 0}>
          打印上架单
        </Button>
        <Button type="primary">新增上架单</Button>
      </Row>
    );
  }, [selecteRowKeys]);

  return (
    <div className="page-shelf-order">
      <CommonTable<any>
        rowKey="id"
        tableRef={tableRef}
        fetchRequest={fetchRequest}
        columns={columns}
        actions={actions}
        toolBarRender={toolBarRender}
        onSelection={(selectedRowKeys) => {
          setSelectedRowKeys(selectedRowKeys as string[]);
        }}
      />
    </div>
  );
});

export default ShelfOrder;
