import React, {useEffect, useState} from 'react';
import {reqByBody, reqByUrl} from "@/modules/common/infra/api/common";
import {TransferOrderGoods} from "@/pages/warehouse/TransferOrder/data";
const Print: React.FC = () => {
  let params = location.search.replace("?","").split("&")
  let paramsMap = {};
  params.forEach(item =>{
    const tmp = item.split("=");
    paramsMap[tmp[0]] = tmp[1];
  })

  const eachTable = (content: string, row: TransferOrderGoods) =>{

    content += '<table align="center" cellPadding="0" cellSpacing="0" style=" width:6.95cm; height:1.96cm; page-break-after:always;overflow:hidden" valign="top">\n';

    for(let i=0; i<row.quantity; i++){
      content += '<tr class="tr10" align="left" style="height: 42px">\n' +
        '        <td colspan="2" align="center" valign="middle" class="STYLE20">\n' +
        '            <img src="http://120.238.183.57:3937/barcode/generate.php?text='+row?.thirdpartyCoding+'&is_show=0" style="height: 40px" alt="barcode"/>\n' +
        '        </td>\n' +
        '    </tr>\n' +
        '    <tr class="tr10" align="left" style="line-height: 12px">\n' +
        '        <td align="right" class="font-size12 tr10" style="padding-top: 4px">'+row?.thirdpartyCoding+'</td>\n' +
        '        <td align="right" class="font-size12 tr10" style="padding-top: 4px">'+row?.sku+'</td>\n' +
        '    </tr>' +
        '      <tr className="tr10" align="left" style="line-height: 12px">\n' +
        '        <td align="left" className="font-size12 tr10"><span className="font10 tr10 height10" style="font-size: 12px;font-weight: bold">New '+(row?.skuDetailInfo?.spuBaseInfo?.enDeclaredName || '')+'</span>\n' +
        '        </td>\n' +
        '        <td align="right" className="font-size12 tr10"><span\n' +
        '          className="font10 tr10" style="font-size: 12px;font-weight: bold">'+row?.thirdpartyCode+'</span>\n' +
        '        </td>\n' +
        '      </tr>\n' +
        '      <tr className="tr10" align="left"  style="line-height: 12px">\n' +
        '        <td align="center"\n' +
        '            colSpan="2"\n' +
        '            className="font-size12 tr10"><span className="font10 tr10 height10" style="font-size: 12px;font-weight: bold;">Made In China</span></td>\n' +
        '      </tr>\n';
    }
    content += '</table><table align="center" cellpadding="0" cellspacing="0" style="width:6.95cm; height:1.96cm; page-break-after:always;overflow:hidden" valign="top">\n' +
      '                <tbody><tr align="left">\n' +
      '                    <td width="auto" align="center" valign="middle" class="font-size20">'+row?.sku+'</td>\n' +
      '                    <td height="18" colspan="2" align="center" valign="middle" class="STYLE21"></td>\n' +
      '                </tr>\n' +
      '                <tr align="left">\n' +
      '                    <td width="auto" rowspan="2" align="center" valign="top"><img src="http://120.238.183.57:3937/barcode/generate.php?text='+row?.thirdpartyCoding+'&is_show=0" alt="barcode"/></td>\n' +
      '                    <td width="47" align="center" valign="center" class="font-size12"></td>\n' +
      '                    <td width="48" align="left" valign="center" class="font-size12"></td>\n' +
      '                </tr>\n' +
      '                </tbody></table>\n' +
      '            <table align="center" cellpadding="0" cellspacing="0" style="width:6.95cm; height:1.95cm; border:0px #000000 solid;table-layout:fixed;overflow:hidden;">\n' +
      '                <tr>\n' +
      '                    <td height="20%">\n' +
      '                        <p>\n' +
      '                            <span class="STYLE20">'+row?.skuName+'，共&nbsp;'+row?.quantity+'&nbsp;张</br></span>\n' +
      '                            <span class="STYLE20" style="word-wrap:break-word">质检要求：暂无\n' +
      '                            </span>\n' +
      '                        </p>\n' +
      '                    </td>\n' +
      '                </tr>\n' +
      '            </table>';
    return content;

  }

  const [contentHtml, setContentHtml] = useState<string>("");
  useEffect( () => {
      reqByBody('/sales-mgmt-biz/sales-center/warehouse/transfer/getByTransferCode',{transferCode: paramsMap['transferCode'],sku: paramsMap['sku'],quantity: paramsMap['quantity']}).then((res) => {
          if (res.status.success) {
            //头部
            let content = '<html>\n' +
              '    <head>\n' +
              '    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">\n' +
              '    <meta http-equiv="X-UA-Compatible" content="IE=7/8/9/10" >\n' +
              '    <meta http-equiv="X-UA-Compatible" content="IE=Edge" >\n' +
              '    <style type="text/css">\n' +
              '    <!--\n' +
              '    body{margin: 0;padding: 0}\n' +
              '    .STYLE17 {font-size: 11px;\n' +
              '    }\n' +
              '    .font-size12{font-size: 12px;font-family: Arial, Helvetica, sans-serif;font-weight: bold;height: 12px;line-height: 12px;}\n' +
              '    .STYLE18 {font-size: 10px;\n' +
              '    }\n' +
              '    \n' +
              '    .STYLE19 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 7px;\n' +
              '    }\n' +
              '\n' +
              '    .STYLE20 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 12px;\n' +
              '\t    font-weight: bold;\n' +
              '        overflow:hidden;\n' +
              '        line-height: 14px;\n' +
              '    }\n' +
              '    .STYLE20sameheight {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 12px;\n' +
              '        font-weight: bold;\n' +
              '        overflow:hidden;\n' +
              '        line-height: 12px;\n' +
              '    }\n' +
              '    .STYLE21 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 14px;\n' +
              '        line-height: 14px;\n' +
              '        font-weight:bold;\n' +
              '    }\n' +
              '    .font-size18 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 18px;\n' +
              '        font-weight:bold;\n' +
              '        line-height: 18px;\n' +
              '    }\n' +
              '    .font-size22 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 22px;\n' +
              '        font-weight:bold;\n' +
              '        line-height: 18px;\n' +
              '    }\n' +
              '    .font-size20 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 20px;\n' +
              '        font-weight:bold;\n' +
              '        line-height: 18px;\n' +
              '    }\n' +
              '    .STYLE22 {\n' +
              '        font-family: Arial, Helvetica, sans-serif;\n' +
              '        font-size: 26px;\n' +
              '\tfont-weight:bold\n' +
              '    }\n' +
              '    .font-size24{\n' +
              '        font-size: 24px;\n' +
              '    }\n' +
              '    .font10{\n' +
              '       font-size: 10px;font-family: Arial, Helvetica, sans-serif;font-weight: bold;\n' +
              '    }\n' +
              '    .tr14{\n' +
              '        line-height: 14px;\n' +
              '    }\n' +
              '    .tr12{\n' +
              '        line-height: 12px;\n' +
              '    }\n' +
              '    .tr10{\n' +
              '        line-height: 10px\n' +
              '    }\n' +
              '    .error{text-align: center;margin-top: 50px;color:red;font-size: 24px;font-weight: 700}\n' +
              '    .height10{\n' +
              '        height: .3cm;\n' +
              '        overflow: hidden;\n' +
              '        display: block;\n' +
              '        line-height: 11px;\n' +
              '    }\n' +
              '    -->\n' +
              '    </style>\n' +
              '    </head>\n' +
              '    <body>';


            //中间
            let skuTable = '';
            res?.body.map((item: TransferOrderGoods)=>{
              //循环没个sku
              skuTable = eachTable(skuTable, item);
            })
            content += skuTable;

            //尾部
            content += '</body>\n' +
              '</html>';

            setContentHtml(content);
          }
        })
    },
    [paramsMap]
  );

  return (<>
    <div dangerouslySetInnerHTML={{__html:contentHtml}}></div>
  </>);
};
export default Print;
