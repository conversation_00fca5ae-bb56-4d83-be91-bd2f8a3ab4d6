import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "@/../config/mallApiConfig";

// 1.1 采购单创建
export type QueryCreateParams = {
  supplierId?: string,
  supplierName?: string,
  purchaseEntity?: string,
  remark?: string,
  estimatedTimeArrival?: number,
  purchaseWarehouse?: string,
  transportationType?: string,
  freightBearer?: string,
  logisticsCarrier?: string,
  trackingNumber?: string,
  shippingFee?: number,
  orderGoodsDetailDirectCreateRequestList?: {
    sku: string,
    purchaseQuantity: number,
    price: string
  }[]
}
export function queryCreate(data: QueryCreateParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/directCreate',
    method: 'POST',
    data,
  });
}
// 1.2 采购单更新
export type QueryUpdateParams = {
  id: string,
  supplierName: string,
  remark: string,
  transportationType: string,
  freightBearer: string,
  logisticsCarrier: string,
  trackingNumber: string,
  shippingFee: number,
  purchaseOrderGoodsDetailUpdateRequestList: {
    sku: string,
    purchaseQuantity: number,
    price: string
  }[]
}
export function queryUpdate(data: QueryUpdateParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/update',
    method: 'POST',
    data,
  });
}

// 2 获取采购单详情
export type Detail = {
  id: number,
  amountPayable: number,
  purchaseEntity: string,
  orderCode: string,
  orderStatus: number,
  shippingFee: number,
  title: string,
  payStatus: number,
  payType: string,
  purchaseWarehouse: string,
  trackingNumber: string,
  supplierId: string,
  supplierName: string,
  memo: string,
  remark: string,
  gmtCreate: number,
  gmtModified: number,
  purchaseOrderGoodsList: Goods[],
  purchaseAuditStatus: string,
  settleType: string,
  settleCircle: string,
  settleCurrency: string,
  contrator: string,
  cellphone: string,
  detailAddress: string,
  transportationType: string,
  freightBearer: string,
  estimatedTimeArrival: number,
  logisticsCarrier: string
}
export type Goods = {
  id: number,
  orderId: number,
  name: string,
  attrName: string,
  attrValue: string,
  categoryName: string,
  mainImage: string,
  sku: string,
  currency: string,
  lastPurchasePrice: number,
  currentPurchasePrice: number,
  totalAmount: string,
  purchaseQuantity: number,
  goodsRemark: string,
  gmtCreate: number,
  gmtModified: number,
  arrivalQuantity: number,
  receivingQuantity: number,
  remark: string,
  freightSharingPrice: string,
  costPrice: number
}
export function queryDetail(data: { purchaseOrderId: string }) {
  return mallRequest<API.ApiBaseResult<Detail>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/getDetail',
    method: 'POST',
    params: data,
  });
}

// 3 获取采购入库明细列表
export type QueryStockDetailResult = {
  stockinOrderId: string,
  referencePurchaseOrderId: string,
  stockinOrderState: string,
  stockinOrderDetailInfoList: StockinDetail[],
  creatorName: string,
  gmtCreate: number
}
export type StockinDetail = {
  skuId: string,
  goodsName: string,
  mainImage: string,
  quantity: number,
  actualQuantity: number,
  goodQuantity: number,
  badQuantity: number,
  warehousingTime: number
}
export enum StockinOrderState {
  WAIT_STOCKIN = "待入库",
  STOCKINING = "入库中",
  STOCKIN_DONE = "已入库",
  STOCKIN_FINISH = "已完成",
  CANCEL = "已作废",
}
export function queryStockDetail(data: { purchaseOrderCode: string }) {
  return mallRequest<API.ApiBaseResult<QueryStockDetailResult>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/getStockinOrderDetail',
    method: 'POST',
    params: data,
  });
}

// 4 商品销售Item查询
export type QueryCSIListParams = {
  pageCondition: {
    pageNum: number,
    pageSize: number
  },
  saleItemId?: string,
  supplierItemId?: string,
  supplierId?: string,
  supplierSkuId?: string,
  supplierSpuId?: string,
  title?: string,
  categoryId?: string,
  brandId?: string,
  country?: string,
  batchStockId?: string,
  warehouseId?: string,
  saleState?: string
}
export type CSI = {
  saleItemId: string,
  saleSpecs: {
    specName: string,
    specValue: string,
    specType: string
  }[],
  costPrice: string,
  costPriceCurrency: string,
  supplierSpuId: string,
  supplierSkuId: string,
  mainImages: any[],
  title: string,
  specs: {
    specName: string,
    specValue: string
  }[],
  categoryId: string,
  categoryName: string,
  brandId: string,
  brandName: string,
  supplierId: string,
  supplierName: string,
  defaultSupplierItemId: string,
  defaultSupplierItemDetailInfo: {
    supplierItemId: string,
    supplierSkuId: string,
    beforeSupplyPrice: string,
    supplyPrice: string,
    supplyPriceCurrency: string,
    saleMode: string,
    country: string,
    warehouseId: string,
    batchStockId: string,
    supplierId: string,
    combineDetails: {
      supplierItemId: string,
      supplierSkuId: string,
      beforeSupplyPrice: string,
      supplyPrice: string,
      supplyPriceCurrency: string,
      saleMode: string,
      country: string,
      warehouseId: string,
      batchStockId: string,
      supplierId: string,
      combineDetails: any[]
    }[]
  },

  // 自定义需求增加
  purchaseQuantity: number,
  price: number,
  currency: string,
  expenses: number,       // 单个分摊费用
}
export function queryCSIList(data: QueryCSIListParams) {
  return mallRequest<API.ApiQueryPageResult<CSI[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/sale-item/pageQuery',
    method: 'POST',
    data,
  });
}

// 5 获取供应商详情
export type SupplierDetail = {
  venderId: string,
  supplierType: string,
  venderName: string,
  venderCode: string,
  foreignName: string,
  status: string,
  level: string,
  cooperateModal: string,
  nationName: string,
  provinceName: string,
  cityName: string,
  areaName: string,
  streetName: string,
  detail: string,
  addressId: string,
  taobaoAccount: string,
  aliPayAccount: string,
  zipCode: string,
  contractor: string,
  cellphone: string,
  fax: string,
  email: string,
  qq: string,
  payType: string,
  settleType: string,
  settleCircle: string,
  settleCurrency: string,
  settleMode: string,
  settleRatio: string,
  account: string,
  bankName: string,
  accountName: string,
  licenseImages: {
    fileId: string,
    link: string,
    name: string
  }[],
  qualificationImages: {
    fileId: string,
    link: string,
    name: string
  }[],
  companyName: string,
  loginId: string,
  memberId: string,
  userId: string,
  aliStoreLink: string,
  isCross: string,
  isCrossBorder: string,
  supportCreditBuy: number
}
export function querySupplierDetail(data: { venderId: string }) {
  return mallRequest<API.ApiBaseResult<SupplierDetail>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/render',
    method: 'GET',
    params: data,
  });
}
