import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

/**
 * 查询商品供应商列表
 */
export type sendMsgParams = {
  content: string;
};
export async function sendMsg(data?: sendMsgParams) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/robot/sendMessage',
    data
  });
}

