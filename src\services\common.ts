import mallRequest from '@/utils/mallRequest';
import mallApiConfig from '../../config/mallApiConfig';

export interface QuerySubAddressesParams {
  parentCode?: string;
  type: 'PROVINCE' | 'CITY' | 'AREA' | 'STREET';
}

export interface AddressData {
  code: string;
  name: string;
  type: string;
}

export async function querySubAddresses(params?: QuerySubAddressesParams) {
  return mallRequest<API.ApiBaseResult<AddressData[]>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/address/querySubAddresses',
    method: 'POST',
    data: params,
  });
}

export interface NationData {
  code: string;
  englishName: string;
  name: string;
  twoLetterCode: string;
}

export async function queryNations() {
  return mallRequest<API.ApiBaseResult<NationData[]>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/nation/queryNations',
    method: 'POST',
  });
}

export interface UploadImageResult {
  fileId: string;
  link: string;
}

export async function uploadImage(fileName: string, fileContent: any) {
  return mallRequest<API.ApiBaseResult<UploadImageResult>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/file/uploadImg',
    method: 'POST',
    data: fileContent,
    params: {
      fileName,
    },
  });
}

export async function uploadVideo(fileName: string, fileContent: any) {
  return mallRequest<API.ApiBaseResult<UploadImageResult>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/file/uploadVideo',
    method: 'POST',
    data: fileContent,
    params: {
      fileName,
    },
  });
}

export interface UploadFileResult {
  fileId: string;
  link: string;
  name?: string;
}

export async function uploadFile(fileName: string, fileContent: any) {
  return mallRequest<API.ApiBaseResult<UploadImageResult>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/file/uploadFile',
    method: 'POST',
    data: fileContent,
    params: {
      fileName,
    },
  });
}

export interface BaseCurrencyListItem {
  alphabeticCode: string;
  minorUnit: number;
  name: string;
  nameZh: string;
  numericCode: string;
}

export async function queryAllCurrencyList() {
  return mallRequest<API.ApiBaseResult<BaseCurrencyListItem[]>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/base/currency/queryAll',
    method: 'GET',
  });
}

export async function queryByAlphabeticCode(alphabeticCode: string) {
  return mallRequest<API.ApiBaseResult<BaseCurrencyListItem>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/base/currency/queryByAlphabeticCode',
    method: 'GET',
    params: {
      alphabeticCode,
    },
  });
}
