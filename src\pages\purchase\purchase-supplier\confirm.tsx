import type { ModalAction } from '@/components/Common-UI/CommonModal';
import CommonModal from '@/components/Common-UI/CommonModal';
import { Row } from 'antd';
import { forwardRef, memo, useImperativeHandle, useRef, useState } from 'react';
import type { Item } from './api';
import { usePurchaseSupplier } from './purchaseSupplier';
type ConfirmProps = {
  reload: () => void;
}
const Index = forwardRef((props: ConfirmProps, ref) => {
  const { reload } = props;
  const { activeApi, disabledApi} = usePurchaseSupplier();
  const modalRef = useRef<ModalAction<any>>();
  const [record, setRecord] = useState<Item & { operation?: string, tips?: string, title?: string }>();
  useImperativeHandle(ref, () => ({
    open: modalRef?.current?.open,
  }));
  const onOpen = (row: any) => {
    setRecord(row);
  };
  const onConfirm = async () => {
    if (record?.operation === 'active') {
      const res = await activeApi(record?.venderId);
      if (!res) return;
    }
    if (record?.operation === 'disabled') {
      const res = await disabledApi(record?.venderId);
      if (!res) return;
    }
    modalRef?.current?.close();
    if(reload) reload()
  };

  return (
    <CommonModal title={record?.title || '提示'} modalRef={modalRef} onOpen={onOpen} onConfirm={onConfirm}>
      <Row>
        <p>
          {record?.tips || ''}
        </p>
      </Row>
    </CommonModal>
  );
});

export default memo(Index);
