import React, {useEffect} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import styles from "@/pages/PurchaseOrderList/styles.less";
import {reqByPage} from "@/modules/common/infra/api/common";
import {AsinkingMskuProfit} from "@/pages/sales/profit/data";
import {Image} from "antd";

const Profit: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage("/sales-mgmt-biz/sales-center/asinking/profitMskuPageQuery",{
      ...params,
    });
  });

  const columns: ProColumns<AsinkingMskuProfit>[] = [
    {
      title: '图片',
      width: 100,
      dataIndex: 'smallimageurl',
      hideInSearch: true,
      render: (v, record) => {
        return  (
          <div><Image src={record.smallimageurl} width={80} height={80} /> </div>
        );
      }
    },
    {
      title: 'item名称',
      dataIndex: 'itemname',
      width: 400,
      hideInSearch: true,
      render: (v, record) => {
        return record.itemname;
      }
    },
    {
      title: 'MSKU',
      dataIndex: 'msku',
      // hideInSearch: true,
      render: (v, record) => {
        return record?.msku;
      }
    },
    {
      title: 'SKU',
      dataIndex: 'localsku',
      // hideInSearch: true,
      render: (v, record) => {
        return record?.localsku;
      }
    },
    {
      title: '统计日期',
      dataIndex: 'posteddatelocale',
      hideInSearch: true,
      render: (v, record) => {
        return record.posteddatelocale;
      }
    },
    {
      title: 'FBA仓储费',
      dataIndex: 'grossprofit',
      hideInSearch: true,
      render: (v, record) => {
        return record.totalstoragefee;
      }
    },
    {
      title: '月度仓库费',
      dataIndex: 'fbastoragefee',
      hideInSearch: true,
      render: (v, record) => {
        return record.fbastoragefee;
      }
    },
    {
      title: '月度仓储费差异',
      dataIndex: 'grossprofit',
      hideInSearch: true,
      render: (v, record) => {
        return record.sharedfbastoragefee;
      }
    },
    {
      title: '毛利润',
      dataIndex: 'grossprofit',
      hideInSearch: true,
      render: (v, record) => {
        return record.grossprofit;
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);

  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        scroll={{y:670}}
        //可以隐藏选择多少项提示
        tableAlertRender={false}
        columns={columns}
        className={styles.inline_search_table}
        toolBarRender={() => []}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};
export default Profit;
