import type { CascaderProps } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import { useState } from 'react';
import { querySubCategory } from '../../api/tags';

function useCategoryCascader() {
  const [options, setOptions] = useState<CascaderProps<DataNode>['options']>([]);

  const loadData: CascaderProps<DataNode>['loadData'] = async (selectedOptions) => {
    if (selectedOptions) {
      const targetOption = selectedOptions[selectedOptions.length - 1] as any;
      targetOption.loading = true;

      const res = await querySubCategory({
        parentCategoryId: targetOption.value as number,
      });
      // load options lazily
      targetOption.loading = false;
      targetOption.children = res.body?.map((item) => {
        return {
          value: item.categoryId,
          label: item.name,
          isLeaf: Boolean(item.leaf),
        };
      });
      setOptions([...(options as any)]);
    }
  };

  const initTreeData = () => {
    querySubCategory({ parentCategoryId: 0 }).then((res) => {
      setOptions(
        res.body?.map((item) => {
          return {
            key: item.categoryId,
            value: item.categoryId,
            label: item.name,
            isLeaf: Boolean(item.leaf),
          };
        }),
      );
    });
  };
  return {
    options,
    loadData,
    setOptions,
    initTreeData,
  };
}

export default useCategoryCascader;
