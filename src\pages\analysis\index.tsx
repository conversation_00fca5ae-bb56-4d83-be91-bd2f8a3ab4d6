import type { FC } from 'react';
import { useRequest } from 'umi';
import { Suspense, useEffect, useState } from 'react';
import { EllipsisOutlined } from '@ant-design/icons';
import { Col, Dropdown, Menu, Row, notification, Popconfirm } from 'antd';
import { GridContent } from '@ant-design/pro-layout';
import type { RadioChangeEvent } from 'antd/es/radio';
import type { RangePickerProps } from 'antd/es/date-picker/generatePicker';
import type moment from 'moment';
import IntroduceRow from './components/IntroduceRow';
import SalesCard from './components/SalesCard';
import TopSearch from './components/TopSearch';
import ProportionSales from './components/ProportionSales';
import OfflineData from './components/OfflineData';
import { fakeChartData } from './service';
import PageLoading from './components/PageLoading';
import type { TimeType } from './components/SalesCard';
import { getTimeDistance } from './utils/utils';
import type { AnalysisData } from './data.d';
import styles from './style.less';
import { exportPurchaseSupplierGoodsSyncPriceDiff, getPurchaseSupplierGoodsSyncPriceDiff } from './api';

type RangePickerValue = RangePickerProps<moment.Moment>['value'];

type AnalysisProps = {
  analysis: AnalysisData;
  loading: boolean;
};

type SalesType = 'all' | 'online' | 'stores';

const Analysis: FC<AnalysisProps> = () => {
  const [salesType, setSalesType] = useState<SalesType>('all');
  const [currentTabKey, setCurrentTabKey] = useState<string>('');
  const [rangePickerValue, setRangePickerValue] = useState<RangePickerValue>(
    getTimeDistance('year'),
  );
  // const [data1, setData1] = useState<AnalysisData>();

  const { loading } = useRequest(fakeChartData);
  const data = { "visitData": [{ "x": "2023-03-02", "y": 7 }, { "x": "2023-03-03", "y": 5 }, { "x": "2023-03-04", "y": 4 }, { "x": "2023-03-05", "y": 2 }, { "x": "2023-03-06", "y": 4 }, { "x": "2023-03-07", "y": 7 }, { "x": "2023-03-08", "y": 5 }, { "x": "2023-03-09", "y": 6 }, { "x": "2023-03-10", "y": 5 }, { "x": "2023-03-11", "y": 9 }, { "x": "2023-03-12", "y": 6 }, { "x": "2023-03-13", "y": 3 }, { "x": "2023-03-14", "y": 1 }, { "x": "2023-03-15", "y": 5 }, { "x": "2023-03-16", "y": 3 }, { "x": "2023-03-17", "y": 6 }, { "x": "2023-03-18", "y": 5 }], "visitData2": [{ "x": "2023-03-02", "y": 1 }, { "x": "2023-03-03", "y": 6 }, { "x": "2023-03-04", "y": 4 }, { "x": "2023-03-05", "y": 8 }, { "x": "2023-03-06", "y": 3 }, { "x": "2023-03-07", "y": 7 }, { "x": "2023-03-08", "y": 2 }], "salesData": [{ "x": "1月", "y": 394 }, { "x": "2月", "y": 480 }, { "x": "3月", "y": 496 }, { "x": "4月", "y": 874 }, { "x": "5月", "y": 278 }, { "x": "6月", "y": 1164 }, { "x": "7月", "y": 340 }, { "x": "8月", "y": 442 }, { "x": "9月", "y": 611 }, { "x": "10月", "y": 647 }, { "x": "11月", "y": 1087 }, { "x": "12月", "y": 707 }], "searchData": [{ "index": 1, "keyword": "搜索关键词-0", "count": 976, "range": 62, "status": 1 }, { "index": 2, "keyword": "搜索关键词-1", "count": 531, "range": 56, "status": 0 }, { "index": 3, "keyword": "搜索关键词-2", "count": 418, "range": 97, "status": 0 }, { "index": 4, "keyword": "搜索关键词-3", "count": 481, "range": 98, "status": 0 }, { "index": 5, "keyword": "搜索关键词-4", "count": 553, "range": 89, "status": 1 }, { "index": 6, "keyword": "搜索关键词-5", "count": 235, "range": 19, "status": 1 }, { "index": 7, "keyword": "搜索关键词-6", "count": 689, "range": 64, "status": 0 }, { "index": 8, "keyword": "搜索关键词-7", "count": 960, "range": 37, "status": 0 }, { "index": 9, "keyword": "搜索关键词-8", "count": 910, "range": 4, "status": 0 }, { "index": 10, "keyword": "搜索关键词-9", "count": 329, "range": 99, "status": 1 }, { "index": 11, "keyword": "搜索关键词-10", "count": 213, "range": 53, "status": 1 }, { "index": 12, "keyword": "搜索关键词-11", "count": 766, "range": 76, "status": 1 }, { "index": 13, "keyword": "搜索关键词-12", "count": 768, "range": 31, "status": 0 }, { "index": 14, "keyword": "搜索关键词-13", "count": 562, "range": 42, "status": 1 }, { "index": 15, "keyword": "搜索关键词-14", "count": 197, "range": 11, "status": 0 }, { "index": 16, "keyword": "搜索关键词-15", "count": 96, "range": 69, "status": 0 }, { "index": 17, "keyword": "搜索关键词-16", "count": 683, "range": 87, "status": 1 }, { "index": 18, "keyword": "搜索关键词-17", "count": 493, "range": 3, "status": 1 }, { "index": 19, "keyword": "搜索关键词-18", "count": 368, "range": 19, "status": 0 }, { "index": 20, "keyword": "搜索关键词-19", "count": 128, "range": 30, "status": 0 }, { "index": 21, "keyword": "搜索关键词-20", "count": 783, "range": 8, "status": 0 }, { "index": 22, "keyword": "搜索关键词-21", "count": 122, "range": 38, "status": 1 }, { "index": 23, "keyword": "搜索关键词-22", "count": 751, "range": 75, "status": 0 }, { "index": 24, "keyword": "搜索关键词-23", "count": 782, "range": 38, "status": 0 }, { "index": 25, "keyword": "搜索关键词-24", "count": 514, "range": 92, "status": 1 }, { "index": 26, "keyword": "搜索关键词-25", "count": 590, "range": 52, "status": 0 }, { "index": 27, "keyword": "搜索关键词-26", "count": 905, "range": 4, "status": 0 }, { "index": 28, "keyword": "搜索关键词-27", "count": 735, "range": 18, "status": 0 }, { "index": 29, "keyword": "搜索关键词-28", "count": 836, "range": 38, "status": 1 }, { "index": 30, "keyword": "搜索关键词-29", "count": 798, "range": 41, "status": 0 }, { "index": 31, "keyword": "搜索关键词-30", "count": 241, "range": 0, "status": 1 }, { "index": 32, "keyword": "搜索关键词-31", "count": 249, "range": 18, "status": 1 }, { "index": 33, "keyword": "搜索关键词-32", "count": 47, "range": 91, "status": 1 }, { "index": 34, "keyword": "搜索关键词-33", "count": 756, "range": 52, "status": 0 }, { "index": 35, "keyword": "搜索关键词-34", "count": 509, "range": 67, "status": 0 }, { "index": 36, "keyword": "搜索关键词-35", "count": 71, "range": 65, "status": 0 }, { "index": 37, "keyword": "搜索关键词-36", "count": 672, "range": 23, "status": 1 }, { "index": 38, "keyword": "搜索关键词-37", "count": 665, "range": 43, "status": 1 }, { "index": 39, "keyword": "搜索关键词-38", "count": 9, "range": 19, "status": 1 }, { "index": 40, "keyword": "搜索关键词-39", "count": 693, "range": 69, "status": 0 }, { "index": 41, "keyword": "搜索关键词-40", "count": 964, "range": 29, "status": 1 }, { "index": 42, "keyword": "搜索关键词-41", "count": 12, "range": 83, "status": 0 }, { "index": 43, "keyword": "搜索关键词-42", "count": 16, "range": 51, "status": 0 }, { "index": 44, "keyword": "搜索关键词-43", "count": 499, "range": 8, "status": 0 }, { "index": 45, "keyword": "搜索关键词-44", "count": 639, "range": 69, "status": 1 }, { "index": 46, "keyword": "搜索关键词-45", "count": 152, "range": 60, "status": 0 }, { "index": 47, "keyword": "搜索关键词-46", "count": 630, "range": 92, "status": 1 }, { "index": 48, "keyword": "搜索关键词-47", "count": 224, "range": 90, "status": 0 }, { "index": 49, "keyword": "搜索关键词-48", "count": 252, "range": 23, "status": 1 }, { "index": 50, "keyword": "搜索关键词-49", "count": 291, "range": 47, "status": 0 }], "offlineData": [{ "name": "Stores 0", "cvr": 0.3 }, { "name": "Stores 1", "cvr": 0.9 }, { "name": "Stores 2", "cvr": 0.3 }, { "name": "Stores 3", "cvr": 0.9 }, { "name": "Stores 4", "cvr": 0.5 }, { "name": "Stores 5", "cvr": 0.7 }, { "name": "Stores 6", "cvr": 0.8 }, { "name": "Stores 7", "cvr": 0.9 }, { "name": "Stores 8", "cvr": 0.9 }, { "name": "Stores 9", "cvr": 0.2 }], "offlineChartData": [{ "date": "11:37", "type": "客流量", "value": 52 }, { "date": "11:37", "type": "支付笔数", "value": 54 }, { "date": "12:07", "type": "客流量", "value": 73 }, { "date": "12:07", "type": "支付笔数", "value": 95 }, { "date": "12:37", "type": "客流量", "value": 12 }, { "date": "12:37", "type": "支付笔数", "value": 37 }, { "date": "13:07", "type": "客流量", "value": 67 }, { "date": "13:07", "type": "支付笔数", "value": 14 }, { "date": "13:37", "type": "客流量", "value": 59 }, { "date": "13:37", "type": "支付笔数", "value": 49 }, { "date": "14:07", "type": "客流量", "value": 28 }, { "date": "14:07", "type": "支付笔数", "value": 26 }, { "date": "14:37", "type": "客流量", "value": 26 }, { "date": "14:37", "type": "支付笔数", "value": 37 }, { "date": "15:07", "type": "客流量", "value": 104 }, { "date": "15:07", "type": "支付笔数", "value": 108 }, { "date": "15:37", "type": "客流量", "value": 86 }, { "date": "15:37", "type": "支付笔数", "value": 21 }, { "date": "16:07", "type": "客流量", "value": 38 }, { "date": "16:07", "type": "支付笔数", "value": 40 }, { "date": "16:37", "type": "客流量", "value": 64 }, { "date": "16:37", "type": "支付笔数", "value": 105 }, { "date": "17:07", "type": "客流量", "value": 107 }, { "date": "17:07", "type": "支付笔数", "value": 31 }, { "date": "17:37", "type": "客流量", "value": 53 }, { "date": "17:37", "type": "支付笔数", "value": 89 }, { "date": "18:07", "type": "客流量", "value": 61 }, { "date": "18:07", "type": "支付笔数", "value": 62 }, { "date": "18:37", "type": "客流量", "value": 65 }, { "date": "18:37", "type": "支付笔数", "value": 12 }, { "date": "19:07", "type": "客流量", "value": 85 }, { "date": "19:07", "type": "支付笔数", "value": 43 }, { "date": "19:37", "type": "客流量", "value": 19 }, { "date": "19:37", "type": "支付笔数", "value": 31 }, { "date": "20:07", "type": "客流量", "value": 40 }, { "date": "20:07", "type": "支付笔数", "value": 107 }, { "date": "20:37", "type": "客流量", "value": 37 }, { "date": "20:37", "type": "支付笔数", "value": 39 }, { "date": "21:07", "type": "客流量", "value": 27 }, { "date": "21:07", "type": "支付笔数", "value": 65 }], "salesTypeData": [{ "x": "家用电器", "y": 4544 }, { "x": "食用酒水", "y": 3321 }, { "x": "个护健康", "y": 3113 }, { "x": "服饰箱包", "y": 2341 }, { "x": "母婴产品", "y": 1231 }, { "x": "其他", "y": 1231 }], "salesTypeDataOnline": [{ "x": "家用电器", "y": 244 }, { "x": "食用酒水", "y": 321 }, { "x": "个护健康", "y": 311 }, { "x": "服饰箱包", "y": 41 }, { "x": "母婴产品", "y": 121 }, { "x": "其他", "y": 111 }], "salesTypeDataOffline": [{ "x": "家用电器", "y": 99 }, { "x": "食用酒水", "y": 188 }, { "x": "个护健康", "y": 344 }, { "x": "服饰箱包", "y": 255 }, { "x": "其他", "y": 65 }], "radarData": [{ "name": "个人", "label": "引用", "value": 10 }, { "name": "个人", "label": "口碑", "value": 8 }, { "name": "个人", "label": "产量", "value": 4 }, { "name": "个人", "label": "贡献", "value": 5 }, { "name": "个人", "label": "热度", "value": 7 }, { "name": "团队", "label": "引用", "value": 3 }, { "name": "团队", "label": "口碑", "value": 9 }, { "name": "团队", "label": "产量", "value": 6 }, { "name": "团队", "label": "贡献", "value": 3 }, { "name": "团队", "label": "热度", "value": 1 }, { "name": "部门", "label": "引用", "value": 4 }, { "name": "部门", "label": "口碑", "value": 1 }, { "name": "部门", "label": "产量", "value": 6 }, { "name": "部门", "label": "贡献", "value": 5 }, { "name": "部门", "label": "热度", "value": 7 }] };

  const selectDate = (type: TimeType) => {
    setRangePickerValue(getTimeDistance(type));
  };

  const handleRangePickerChange = (value: RangePickerValue) => {
    setRangePickerValue(value);
  };

  const isActive = (type: TimeType) => {
    if (!rangePickerValue) {
      return '';
    }
    const value = getTimeDistance(type);
    if (!value) {
      return '';
    }
    if (!rangePickerValue[0] || !rangePickerValue[1]) {
      return '';
    }
    if (
      rangePickerValue[0].isSame(value[0] as moment.Moment, 'day') &&
      rangePickerValue[1].isSame(value[1] as moment.Moment, 'day')
    ) {
      return styles.currentDate;
    }
    return '';
  };

  let salesPieData;
  if (salesType === 'all') {
    salesPieData = data?.salesTypeData;
  } else {
    salesPieData = salesType === 'online' ? data?.salesTypeDataOnline : data?.salesTypeDataOffline;
  }

  const menu = (
    <Menu>
      <Menu.Item>操作一</Menu.Item>
      <Menu.Item>操作二</Menu.Item>
    </Menu>
  );

  const dropdownGroup = (
    <span className={styles.iconGroup}>
      <Dropdown overlay={menu} placement="bottomRight">
        <EllipsisOutlined />
      </Dropdown>
    </span>
  );

  const handleChangeSalesType = (e: RadioChangeEvent) => {
    setSalesType(e.target.value);
  };

  const handleTabChange = (key: string) => {
    setCurrentTabKey(key);
  };

  const activeKey = currentTabKey || (data?.offlineData[0] && data?.offlineData[0].name) || '';

  const openNotification = (value: any) => {
    notification.info({
      message: `1688供应商商品价格差异`,
      description:
        <Popconfirm
          title="确定导出吗?"
          onConfirm={async () => {
            const res = await exportPurchaseSupplierGoodsSyncPriceDiff()
            window.open(res.body)
          }}
          okText="是"
          cancelText="否"
        >
          <a>{value}</a>
        </Popconfirm>
      ,
      placement: 'bottomRight',
      duration: 0,
    });
  };

  useEffect(() => {
    (async () => {
      const res = await getPurchaseSupplierGoodsSyncPriceDiff()
      openNotification(res.body)
    })()
  }, [])

  return (
    <GridContent>
      <>
        <Suspense fallback={<PageLoading />}>
          <IntroduceRow loading={loading} visitData={data?.visitData || []} />
        </Suspense>

        <Suspense fallback={null}>
          <SalesCard
            rangePickerValue={rangePickerValue}
            salesData={data?.salesData || []}
            isActive={isActive}
            handleRangePickerChange={handleRangePickerChange}
            loading={loading}
            selectDate={selectDate}
          />
        </Suspense>

        <Row
          gutter={24}
          style={{
            marginTop: 24,
          }}
        >
          <Col xl={12} lg={24} md={24} sm={24} xs={24}>
            <Suspense fallback={null}>
              <TopSearch
                loading={loading}
                visitData2={data?.visitData2}
                searchData={data?.searchData}
                dropdownGroup={dropdownGroup}
              />
            </Suspense>
          </Col>
          <Col xl={12} lg={24} md={24} sm={24} xs={24}>
            <Suspense fallback={null}>
              <ProportionSales
                dropdownGroup={dropdownGroup}
                salesType={salesType}
                loading={loading}
                salesPieData={salesPieData || []}
                handleChangeSalesType={handleChangeSalesType}
              />
            </Suspense>
          </Col>
        </Row>

        <Suspense fallback={null}>
          <OfflineData
            activeKey={activeKey}
            loading={loading}
            offlineData={data?.offlineData || []}
            offlineChartData={data?.offlineChartData || []}
            handleTabChange={handleTabChange}
          />
        </Suspense>
      </>
    </GridContent>
  );
};

export default Analysis;
