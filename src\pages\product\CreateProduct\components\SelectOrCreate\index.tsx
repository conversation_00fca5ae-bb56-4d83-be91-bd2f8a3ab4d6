import { SearchOutlined } from '@ant-design/icons';
import type { SelectProps } from 'antd';
import { Divider, Input, Select } from 'antd';
import type { CSSProperties } from 'react';
import { useMemo, useState } from 'react';
import useMergedState from 'rc-util/es/hooks/useMergedState';
import { find, isArray, isString, omit } from 'lodash';
import { v4 as uuid } from 'uuid';

export interface SelectOrCreateProps<T> extends SelectProps<T> {
  onCreate?: (name: string | string[]) => Promise<string>;
  style?: CSSProperties;
  showCreate?: boolean;
}

const SelectOrCreateSingle = (props: SelectOrCreateProps<any>) => {
  const { options = [], onCreate, showCreate = true } = props;

  const newId = uuid();

  const [currName, onNameSelected] = useMergedState<any>('', {
    value: props.value,
    onChange: props.onSelect,
  });
  const [currInputName, onNameChange] = useState<string>();

  const createNewName = async (value: string) => {
    const id = await onCreate?.(value);
    onNameChange(id);
  };

  const valuesOptions = useMemo(() => {
    if (currInputName) {
      const isExit = find(options, { label: currInputName });
      if (isExit) {
        return options.filter((item) =>
          isString(item.label) ? item.label.includes(currInputName) : false,
        );
      }
      return [{ value: newId, label: currInputName }, ...options];
    }
    return options;
  }, [currName, currInputName, options]);

  const onSelect = async (value: string) => {
    if (value === newId && currInputName) {
      await onCreate?.(currInputName);
    } else {
      onNameSelected(value);
    }
  };
  return (
    <Select
      {...omit(props, ['onCreate', 'showCreate'])}
      onSelect={onSelect}
      onDeselect={() => onNameSelected(undefined)}
      value={isArray(currName) ? currName[0] : currName}
      options={valuesOptions}
      onClear={() => onNameSelected(undefined)}
      dropdownRender={(menu) => (
        <div key={currName}>
          {showCreate && (
            <>
              <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                <Input
                  style={{ flex: 'auto' }}
                  prefix={<SearchOutlined />}
                  value={currInputName}
                  onChange={(e) => {
                    onNameChange(e.target.value);
                  }}
                  onPressEnter={(e: any) => createNewName(e.target.value)}
                  onKeyDown={(e) => {
                    const keyName = e.key;
                    if (keyName === 'Backspace') {
                      e.stopPropagation();
                    }
                  }}
                />
              </div>
              <Divider style={{ margin: '4px 0' }} />
            </>
          )}

          <div key={currInputName}>{menu}</div>
        </div>
      )}
    />
  );
};

const SelectOrCreateMultiple = (props: SelectOrCreateProps<any>) => {
  const { options = [], onCreate, showCreate = true } = props;

  const newId = uuid();

  const [currName, onNameSelected] = useMergedState<any>([], {
    value: props.value || [],
    onChange: props.onChange,
  });
  const [currInputName, onNameChange] = useState<string>();

  const createNewName = async (value: string) => {
    const id = await onCreate?.(value);
    onNameChange(id);
  };

  const valuesOptions = useMemo(() => {
    if (currInputName) {
      const isExit = find(options, { label: currInputName });
      if (isExit) {
        return options.filter((item) =>
          isString(item.label) ? item.label.includes(currInputName) : false,
        );
      }
      return [{ value: newId, label: currInputName }, ...options];
    }
    return options;
  }, [currName, currInputName, options]);

  const onSelect = async (value: string[]) => {
    if (value.includes(newId) && currInputName) {
      await onCreate?.(currInputName);
    } else {
      onNameSelected(value);
    }
  };

  const onDeselect = (val: string) => {
    const newData = currName.map((item: any) => item);
    const index = currName.indexOf(val);
    if (index !== -1) {
      newData.splice(index, 1);
      onNameSelected(newData);
    }
  };

  return (
    <Select
      {...omit(props, ['onCreate', 'showCreate'])}
      mode="multiple"
      onChange={onSelect}
      onDeselect={onDeselect}
      value={currName}
      onClear={() => onNameSelected(undefined)}
      options={valuesOptions}
      dropdownRender={(menu) => (
        <div key={currName}>
          {showCreate && (
            <>
              <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                <Input
                  style={{ flex: 'auto' }}
                  prefix={<SearchOutlined />}
                  value={currInputName}
                  onChange={(e) => {
                    onNameChange(e.target.value);
                  }}
                  onPressEnter={(e: any) => createNewName(e.target.value)}
                  onKeyDown={(e) => {
                    const keyName = e.key;
                    if (keyName === 'Backspace') {
                      e.stopPropagation();
                    }
                  }}
                />
              </div>
              <Divider style={{ margin: '4px 0' }} />
            </>
          )}

          <div key={currInputName}>{menu}</div>
        </div>
      )}
    />
  );
};

const SelectOrCreate = (props: SelectOrCreateProps<any>) => {
  if (props.mode === 'multiple') {
    return <SelectOrCreateMultiple {...props} />;
  }
  return <SelectOrCreateSingle {...props} />;
};

export default SelectOrCreate;
