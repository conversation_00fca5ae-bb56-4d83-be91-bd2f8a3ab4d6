import { checkConfirmPassword, userPasswordRules } from '@/modules/user-center/domain/user';
import { ProFormText } from '@ant-design/pro-form';

const PasswordField = ({ name, label }: { name: string; label: string }) => {
  return (
    <>
      <ProFormText.Password fieldProps={{
        autocomplete: 'new-password',
      } as any} label={label} name={name} rules={userPasswordRules} />
      <ProFormText.Password
        label="确认密码"
        name="confirmPassword"
        rules={[
          {
            required: true,
            message: '请输入确认密码!',
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              return checkConfirmPassword(value, getFieldValue(name));
            },
          }),
        ]}
      />
    </>
  );
};

export default PasswordField;
