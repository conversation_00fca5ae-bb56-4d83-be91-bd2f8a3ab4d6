import type { RequestOptionsInit } from 'umi-request';
import { extend } from 'umi-request';
import { notification } from 'antd';
import mallApiConfig from '../../config/mallApiConfig';
import mallSign from '@/utils/mallSign';
import type { NewnaryCommonResponse } from './newnaryApiUtils';
import { v4 as uuidv4 } from 'uuid';
import { history } from 'umi';
import { getSignSecret, getToken, removeToken, setToken, setSignSecret } from './token';
import { apiDomainMap } from 'config/mallApiConfig';

/**
 * 错误码定义
 */
const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/**
 * http请求，通用异常处理
 */
const errorHandler = (error: { response: Response }): Response => {
  const { response } = error;
  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;

    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  } else if (!response) {
    notification.error({
      description: `您的网络发生异常，无法连接服务器${JSON.stringify(response)}`,
      message: '网络异常',
    });
  }
  return response;
};

/**
 * 业务公共错误码
 */
const bizCommonCodeMessage = {
  100: '服务器异常',
  101: '查询数据异常',
  102: '请求数据验证失败',
  103: '领域断言异常',
  104: '服务调用异常',
  105: '访问拒绝',
  106: '并发异常',
  107: '您的登录状态已失效，请重新登录',
  108: '访问凭证已过期',
  109: '无权访问',
  110: '签名错误',
};

/**
 * Http请求，通用业务异常处理
 * @param response
 */
const errorBizHandler = (response: NewnaryCommonResponse) => {
  if (!response) {
    notification.error({
      description: '网关未返回任何信息',
      message: 'Newnary网关服务异常',
    });
  }
  if (!response.status) {
    notification.error({
      description: '网关未返回status节点信息',
      message: 'Newnary网关服务异常',
    });
  }
  if (response.status.success !== true) {
    if (response.status.returnCode !== '0') {
      const toLoginCodes = ['109', '107', '105'];
      if (toLoginCodes.indexOf(response.status.returnCode) > -1) {
        removeToken();
        // 强制跳转登录页
        // 判断是不是子应用，如果是子应用，则直接跳转到主应用登录页
        if ((window as any).__POWERED_BY_QIANKUN__) {
          window.location.replace('/user/login');
        } else {
          history.replace('/user/login');
        }
      }
      const showGwErrorCodes = ['101', '103'];
      if (showGwErrorCodes.includes(response.status.returnCode)) {
        notification.error({
          description: response.status.message,
          message: `操作提示，提示码${response.status.returnCode}`,
        });
      } else {
        notification.error({
          description: response.status.message
            ? response.status.message
            : bizCommonCodeMessage[response.status.returnCode],
          message: `操作提示，提示码${response.status.returnCode}`,
        });
      }
    } else {
      notification.warn({
        description: response.status.message,
        message: `操作提示`,
      });
    }
  }

  return response;
};

interface NewnaryExtendOptionsInit extends RequestOptionsInit {
  requestPath: string;
}

function mallRequest<T>(baseUrl: string, requestParams: NewnaryExtendOptionsInit): Promise<T> {
  /**
   * 配置request请求时的默认参数
   */
  const baseRequest = extend({
    errorHandler, // 默认错误处理
    credentials: 'include', // 默认请求是否带上cookie
  });

  // request拦截器, 改变url 或 options.
  baseRequest.interceptors.request.use(
    (url, options: RequestOptionsInit) => {
      let signSecret: string = getSignSecret();
      if (!signSecret) {
        signSecret = mallApiConfig.staticSecretKey;
      }
      const validRequestTimestamp: string = new Date().getTime().toString();
      const validRequestRandom: string = uuidv4();
      const validRequestSignature = mallSign(
        requestParams.requestPath,
        validRequestTimestamp,
        validRequestRandom,
        signSecret,
      );

      //TODO 临时代码
      const userName=localStorage.getItem("userName");

      const userMap={
        "huangguobing":"USER8969268142600450609152",
        "heliangli":"USER0598275397199662288896",
        "zhangzelan":"USER4248275412475137101824",
        "hezong":"USER0992275398843321946112",
        "zenglifen":"USER4192275412251832356864",
        "tianli":"USER1950270691802221252608",
      };

      const token = getToken();

      const headers = {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'valid-request-signature': validRequestSignature,
        'valid-request-timestamp': validRequestTimestamp,
        'valid-request-random': validRequestRandom,
        // 'newnary-user-id': "USER8969268142600450609152",
        'newnary-user-id': userMap[userName],
        'newnary-tenant-id':"TENANT3924117688555169382400",
        Authorization: token,
      };
      const lastOptions: RequestOptionsInit = { ...options, headers } as RequestOptionsInit;
      return {
        url,
        options: lastOptions,
      };
    },
    { global: false },
  );

  // response拦截器, 处理response
  baseRequest.interceptors.response.use(
    async (response) => {
      try{
        const respData = await response.clone().json();
        response = errorBizHandler(respData);

        // 如果是授权请求类型的，需要客户端保存相关秘钥信息
        if (respData.body) {
          const { signSecret } = respData.body;
          if (signSecret) {
            setSignSecret(signSecret);
          }
          const { token } = respData.body;
          if (token) {
            setToken(token);
          }
        }
        return response;
      }catch (e) {
        return response;
      }
    },
    { global: false },
  );
  const allUrl = ['2bFat', '2bPro'].includes(REACT_APP_ENV) ? ((apiDomainMap[baseUrl] ?? baseUrl) + requestParams.requestPath) : (baseUrl + requestParams.requestPath)
  return baseRequest(allUrl, requestParams);
}

export default mallRequest;
