export type VenderItemData = {
  cooperateModal: string;
  foreignName: string;
  level: string;
  status: string;
  venderCode: string;
  venderId: string;
  venderName: string;
};

export type Vender = {
  account: string;
  accountName: string;
  areaName: string;
  bankName: string;
  cellPhone: string;
  cityName: string;
  cooperateModal: string;
  detail: string;
  email: string;
  fax: string;
  foreignName: string;
  level: string;
  name: string;
  nationName: string;
  payType: string;
  provinceName: string;
  qq: string;
  settleCircle: string;
  settleCurrency: string;
  settleType: string;
  status: VenderStatusEnums;
  venderCode: string;
  venderId: string;
  venderName: string;
  zipCode: string;
  streetName: string;
  companyName: string,
  loginId: string,
  memberId: string,
  userId: string,
  aliStoreLink: string,
};

export enum VenderStatusEnums {
  'ACTIVATED' = 'ACTIVATED',
  'DISABLED' = 'DISABLED',
}

export enum SettleCircleType {
  DAY_SETTLE = '日结',
  WEEK_SETTLE = '周结',
  HALF_MONTH_SETTLE = '半月结',
  MONTH_SETTLE = '月结',
}

export enum CooperateModalEnums {
  // 'CO-BUY' = 'CO-BUY',
  // 'CO-DISTRIBUTION' = 'CO-DISTRIBUTION',
  'CONSIGNMENT' = 'CONSIGNMENT',
  'PURCHASING_AGENT' = 'PURCHASING_AGENT',
}

export const PayMethodMap = {
  CAHS: '现金',
  BANK_TRANS: '银行转账',
  ALIPAY: '支付宝',
  CROSS_BORDER: '跨境宝',
};

// 是否跨境
export const isCrossBorderMap = {
  '0': '否',
  '1': '是',
};

// 是否跨境宝
export const isCrossMap = {
  '0': '否',
  '1': '是',
};
// 是否支持诚意赊账
export const supportCreditBuyMap = {
  '0' : '否',
  '1' : '是',
}

// 合作模式
export const CooperateModalMap = {
  // 'CO-BUY': '采买',
  // 'CO-DISTRIBUTION': '代销',
  CONSIGNMENT: '寄售代销',
  PURCHASING_AGENT: '代购',
};

export const supplierTypeMap = {
  "0": '线下订单',
  "1": '线上订单',
};

export const SettleCurrencyMap = {
  RMB: '人民币',
};

// 结算类型
export const SettleTypeMap = {
  CASH_ON_DELIVERY: "货到付款",
  DELIVERY_ON_ARRIVAL: "款到发货",
  ACCOUNT_PERIOD: "账期结算",
};

export const SettleCircleTypeMap = {
  'ONLINE7': "线上7天",
  'ONLINE10': "线上10天",
  'ONLINE20': "线上20天",
  'ONLINE30': "线上30天",
  'OFFLINE7': "线下7天",
  'OFFLINE15': "线下15天",
  'OFFLINE30': "线下30天"
};

export const SettleModeMap = {
  SUPPLY_PRICE: '商品供货价',
  // CONSIGNMENT_COMMISSION: '寄售代销服务费',
  // PURCHASING_AGENT_COMMISSION: '代购服务费',
  // PLATFORM_COMMISSION: '供应商控价，平台提成结算模式',
};

// 供应商等级
export const LevelMap = {
  L1: 'L1',
  L2: 'L2',
  L3: 'L3',
};

export const isActiveVender = (vender: Vender) => {
  return vender.status === VenderStatusEnums.ACTIVATED;
};

export const venderFieldLabels = {
  account: '账户',
  accountName: '账户名称',
  areaName: '区',
  bankName: '开户行',
  cellphone: '电话/手机',
  cityName: '市',
  contractor: '联系人',
  cooperateModal: '合作模式',
  detail: '详细地址',
  email: '邮箱',
  foreignName: '外文名称',
  level: '供应商等级',
  nationName: '国家',
  payType: '支付方式',
  provinceName: '省',
  qq: 'qq号码',
  settleCircle: '结算周期',
  settleCurrency: '结算币种',
  settleType: '结算类型',
  settleMode: '结算模式',
  venderCode: '编码',
  venderName: '名称',
  province: '省/市/区',
  supplierType: '订单类型',
  isCross: '是否跨境宝',
  isCrossBorder: '跨境供应商',
  'supportCreditBuy': '是否支持诚意赊'
};





export type SupplierGoods = {
  aliWangWangLink: string;
  id: string;
  supplierId: number;
  supplierName: string;
  goodsId: number;
  goodsSku: string;
  isDefault: number;
  currency: string;
  purchasePrice: number;
  lastPurchasePrice: number;
  platformPurchasePrice: number;
  platformProductId: string;
  platformSpecId: string;
  gmtCreate: number;
  gmtModified: number;
  aliLink: string;
  minPurchaseQuantity: number;
  purchaseCycle: string
};


export type SupplierGoodsBind = {
  platformPurchasePrice: number;
  sku: string;
  skuId: string;
  skuName: string;
  skuAttrName: string;
  skuAttrValue: string;
  skuReferenceCost: number;
  skuLastPurchasePrice: number;
  skuImage: string;
  supplierId: string;
  supplierName: string;
  platformSpecName: string;
  platformProductId: string;
  platformSpecId: string;
  platformSkuId: string;
  platformQuantity: number;
  aliLink: string;
  isDefault: number;
  currency: string;
  purchasePrice: number;
  lastPurchasePrice: number;
  minPurchaseQuantity: number;
  purchaseCycle: number;
};




