import type { Spec, SpuSkuData } from '@/modules/product/domain/spu';
import { v4 as uuid } from 'uuid';
import type { SpecItemData } from '../components/CategorySpecField';
import type { ProductSpecSkuData } from '../components/ProductSpecsManagePanel/components/ProductSpecSkuList';

// 根据规格查询出对应的sku
const findCurrentSkuBySpec = (currentSpecs: Spec[], dataSource: ProductSpecSkuData[]) => {
  const [currentSku] = dataSource.filter((item) => {
    const skuSpec = item.specs;
    return (
      currentSpecs.length === skuSpec?.length &&
      skuSpec?.every((spec, index) => {
        const cSpec = currentSpecs[index];
        return (
          spec.specName && cSpec?.specName === spec.specName && cSpec.specValue === spec.specValue
        );
      })
    );
  });

  return currentSku;
};

const createSku = (currentSpecs: Spec[], skuList: any[]) => {
  const currentSku = findCurrentSkuBySpec(currentSpecs, skuList);
  if (currentSku) {
    return currentSku;
  }
  const sku: SpuSkuData & { id: string } = {
    id: uuid(),
    codeEAN: '',
    codeUPC: '',
    customCode: '',
    grossWeight: 0,
    guarantyPeriod: 0,
    measuringUnit: '',
    netWeight: 0,
    refProductLink: '',
    sizeHeight: 0,
    sizeLength: 0,
    sizeWidth: 0,
    specs: currentSpecs,
  };
  return sku;
};

// 根据spec计算笛卡尔积
function cartesianProductOf(...rest: Spec[][]) {
  return Array.prototype.reduce.call(
    rest,
    function (a: any, b: any) {
      const ret: any = [];
      a.forEach(function (i: any[]) {
        b.forEach(function (j: any) {
          ret.push(i.concat([j]));
        });
      });
      return ret;
    },
    [[]],
  );
}

// 创建和更新skuList
export const createOrUpdateSkuList = (currentSpec: SpecItemData[], skuList: any[]) => {
  const selectedSpec = currentSpec.filter((item) => item?.specValues?.length);
  if (!selectedSpec.length) {
    return [];
  }
  const specArray = selectedSpec.reduce((arr: any, item) => {
    const newSpecList: Spec[] = item?.specValues?.map((v) => ({
      specName: item.specName,
      specValue: v,
    }));
    arr.push(newSpecList);
    return arr;
  }, []);
  const newSpecList = cartesianProductOf(...specArray) as any[][];
  return newSpecList.map((item) => {
    return createSku(item, skuList);
  });
};
