import { ProFormSelect, ProFormField, ProFormTextArea } from "@ant-design/pro-form";
import type { ModalProps } from "antd";
import { Form } from "antd";
import { Modal } from "antd";
import type { CreatePlanParmas } from "@/modules/purchasePlan/infra/api/purchasePlan";
import { getWarehouse } from "@/modules/purchasePlan/infra/api/purchasePlan";
import UploadFile from "@/components/UploadFile";

// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: CreatePlanParmas) => void;
} & ModalProps;

const CreatePurchasePlanModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, ...rest } = props;

  return <Modal {...rest} onOk={() => form.submit()}>
    <Form form={form} onFinish={onFinish}>
      <ProFormSelect
        width="md"
        name="warehouseName"
        label="仓库"
        request={async () => {//返回的select网络请求
          const params = await getWarehouse();
          console.log('获取到的下拉省分为：', params);
          const res = [];
          const body = params.body;
          for (const i in body) {
            const temp = {};
            temp.label = body[i];
            temp.value = i;
            res.push(temp)
          }
          console.log('最终组装的数据=', res);
          return res;
        }}
        valueEnum={{
          '广州仓': '广州仓',
          '东莞仓': '东莞仓',
        }}
      />
      <ProFormTextArea
        width="md"
        name="remark"
        label="备注"
      />
      <ProFormField name="importExcelUrl" label="文件">
        <UploadFile limit={1} />
      </ProFormField>
    </Form>
  </Modal>
}

export default CreatePurchasePlanModal;
