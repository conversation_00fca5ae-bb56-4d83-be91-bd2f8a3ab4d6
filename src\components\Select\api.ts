import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';

// 1 品牌列表查询
export type QueryBrandListParams = {
  name?: string;
  locale?: string;
  brandIds?: string[];
  pageCondition: {
    pageNum: number;
    pageSize: number;
  };
};
export type QueryBrandListResult = {
  pageMeta: {
    pageNum: number;
    pageSize: number;
    total: number;
    pages: number;
  };
  items: {
    brandId: string;
    name: string;
    brandName: string;
    nickname: string;
    logo: string;
    country: string;
    defaultLocale: string;
    descList: {
      brandId: string;
      locale: string;
      brandName: string;
      nickname: string;
      description: string;
    }[];
  }[];
};
export async function queryBrandList(params: QueryBrandListParams) {
  return mallRequest<API.ApiBaseResult<QueryBrandListResult>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/product-center/mgmt-brand/pageQuery',
    method: 'POST',
    data: params,
  });
}

// 2 供应商列表查询
export interface QueryVenderParams {
  venderId?: string,
  venderName?: string,
  venderCode?: string,
  foreignName?: string,
  level?: string,
  cooperateModal?: string,
  status?: string,
  pageCondition: {
    pageNum: number,
    pageSize: number
  }
}
export type Vender = {
  account: string;
  accountName: string;
  areaName: string;
  bankName: string;
  cellPhone: string;
  cityName: string;
  cooperateModal: string;
  detail: string;
  email: string;
  fax: string;
  foreignName: string;
  level: string;
  name: string;
  nationName: string;
  payType: string;
  provinceName: string;
  qq: string;
  settleCircle: string;
  settleCurrency: string;
  settleType: string;
  status: VenderStatusEnums;
  venderCode: string;
  venderId: string;
  venderName: string;
  zipCode: string;
  streetName: string;
  companyName: string,
  loginId: string,
  memberId: string,
  userId: string,
  aliStoreLink: string,
};
export enum VenderStatusEnums {
  'ACTIVATED' = 'ACTIVATED',
  'DISABLED' = 'DISABLED',
}
export async function queryVenderList(params: QueryVenderParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Vender[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/pageQuery',
      method: 'POST',
      data: params,
    },
  );
}

// 3.1 查询所有标签组
export interface Option {
  value: string | number;
  label: string;
  isLeaf?: boolean;
  children?: Option[];
  loading?: boolean;
  isLoad?: boolean;
}
export type QueryAllTagGroupResult = {
  tagGroupId: string;
  groupTitle: string;
}[];
export async function queryAllTagGroup() {
  return mallRequest<API.ApiBaseResult<QueryAllTagGroupResult>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/tag-group/queryAllTagGroup',
    method: 'GET',
  });
}
// 3.2 标签分页查询
export type QueryTagListParams = {
  tagGroupId?: string;
  tagName?: string;
  pageCondition: {
    pageNum: number;
    pageSize: number;
  };
};
export type QueryTagListResult = {
  pageMeta: {
    pageNum: number;
    pageSize: number;
    total: number;
    pages: number;
  };
  items: Tag[];
};
export type Tag = {
  tagId: string;
  tagName: string;
  tagGroupTitle: string;
  tagGroupId: string;
  tagRemark: string;
  gmtCreate: number;
  productQuantity: number;
};
export async function queryTagList(params: QueryTagListParams) {
  return mallRequest<API.ApiBaseResult<QueryTagListResult>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/tag/pageQuery',
    method: 'POST',
    data: params,
  });
}

// 4 获取国家
export type Country = {
  name: string;
  code: string;
  englishName: string;
  twoLetterCode: string;
};
export async function queryCountryList() {
  return mallRequest<API.ApiBaseResult<Country[]>>(mallApiConfig.commonApiGatewayUrl, {
    requestPath: '/common-open-biz/basic-data-center/nation/queryNations',
    method: 'POST',
  });
}

// 5 可用物流渠道列表查询
export type Logistics = {
  logisticsId: string;
  logisticsCode: string;
  name: string;
  enabled: boolean;
  logisticsThirdPush: boolean;
};
export function queryAvailableLogistics(params?: { carrierId?: string }) {
  return mallRequest<API.ApiBaseResult<Logistics[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/logistics-center/logistics/queryList4Enable',
    method: 'POST',
    data: params,
  });
}

// 6 获取仓库
export type WarehouseItem = {
  warehouseId: string,
  customCode: string,
  name: string,
  type: string,
  operationType: string,
  country: string,
  province: string,
  city: string,
  region: string,
  street: string,
  detailAddress: string,
  zipCode: string,
  addressId: string,
  status: string,
  thirdWarehouseId: string,
  thirdWarehouseName: string,
  thirdWarehouseCustomCode: string,
  thirdShipperId: string,
  thirdShipperName: string,
  thirdShipperCustomCode: string,
  thirdRelevanceState: string
}
export function queryWarehouse() {
  return mallRequest<API.ApiBaseResult<WarehouseItem[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/logistics-center/warehouse/getAll',
    method: 'GET',
  });
}

// 7 采购主体
export type Entity = {
  venderId: string,
  venderName: string,
  venderCode: string,
  foreignName: string,
  status: string,
  venderAddressInfoDTO: {
    nation: string,
    province: string,
    city: string,
    area: string,
    street: string,
    detail: string,
    zipCode: string,
    addressId: string
  },
  venderContractorDTO: {
    contractor: string,
    cellphone: string,
    fax: string,
    email: string,
    qq: string
  },
  gmtCreate: number,
  gmtModified: number
}
export function queryPurchasingEntity(params: { pageCondition: { pageNum: number; pageSize: number; } }) {
  return mallRequest<API.ApiQueryPageResult<Entity[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseEntity/pageQuery',
    method: 'POST',
    data: params,
  });
}

// 8 商品分类
export type CategoryListItem = {
  categoryId?: string;
  parentId?: string;
  name?: string;
  parentName?: string;
  sequence?: number;
  level?: number;
  leaf?: boolean;
  gmtCreate?: number;
  allCategoryName?: string;
}
export function queryCategoryList(params: { pageCondition: { pageNum: number; pageSize: number; }; categoryName?: string; categoryId?: string }) {
  return mallRequest<API.ApiQueryPageResult<CategoryListItem[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/mgmt-category/queryLeafCategoryList',
    method: 'POST',
    data: params,
  });
}
