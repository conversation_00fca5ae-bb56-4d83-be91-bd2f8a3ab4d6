import {useRequestTable} from '@/hooks/useRequestTable';
import type {ProColumns} from '@ant-design/pro-components';
import {ProTable} from '@ant-design/pro-components';
import {Button, Form, message, Modal, notification, Table, Tag} from 'antd';
import React, {useEffect, useState} from 'react';
import type {TabsItem} from '@/components/TableTabs';
import {
  financeAuditPageQuery, financeManualAudit,
  purchaseOrderPriceAudit
} from "@/modules/purchaseOrder/infra/api/purchaseOrderAudit";
import {PurchaseOrderAuditStatus} from "@/modules/purchaseOrder/domain/purchaseOrder";
import {SnippetsTwoTone} from "@ant-design/icons";
import {PurchaseOrderGoods, PurchaseOrderPriceAuditRecord} from "@/pages/PurchaseOrderList/PurchaseOrderAudit/data";
import moment from "moment";
import {ArrowDownOutlined, ArrowUpOutlined} from "@ant-design/icons/lib";
import {ProFormTextArea} from "@ant-design/pro-form";
import {
  PurchaseOrderAuditStatusText,
  purchaseOrderAuditTypeEnum
} from "@/modules/purchaseOrder/domain/purchaseOrderAudit";
import {copyText} from "@/utils/comUtil";
import CustomPage from "@/components/CustomPage";

export default () => {
  const [activeStatusKey, setActiveStatusKey] = useState<string>();

  const { fetchList, actionRef } = useRequestTable((params) => {
    return financeAuditPageQuery({
      ...params,
      auditStatus: activeStatusKey,
    });
  });

  // 记录整个表单选择的rows
  const [selectedRowsState, setSelectedRows] = useState<PurchaseOrderPriceAuditRecord[]>([]);

  const [form] = Form.useForm();

  /**
   * 通用审核
   * @param flag
   * @param status
   */
  const audit = (status: string) => {

    const dataList = selectedRowsState;
    if (dataList.length <= 0) {
      message.error("请选择数据!")
      return null;
    }

    const param = {
      "priceAuditId": dataList.map(item=>item.id),
      "auditStatus": status
    }

    Modal.confirm({
      title: status == PurchaseOrderAuditStatus.PASS ? '确认通过吗?' : '确认驳回吗',
      content: (
        <Form form={form}>
          <ProFormTextArea label="" name="remark" />
        </Form>
      ),
      onOk: async () => {
        param["remark"]=form.getFieldValue('remark');
        //驳回添加备注
        if (status == PurchaseOrderAuditStatus.REJECT && (param["remark"]=="" || param["remark"]==null)){
          notification.error({ message: '请填写驳回备注' });
        }else {
          financeManualAudit(param).then((result) => {
            if (result.status.success) {
              actionRef.current?.reloadAndRest?.();
              message.success("操作成功")
            }
          });
        }
        form.resetFields();
      },
    });
  }


  const columnsTab: ProColumns<PurchaseOrderGoods, 'text'>[] = [
    {
      title: 'sku',
      dataIndex: 'sku',
      width:"60px",
    },
    {
      title: '上次采购价',
      dataIndex: 'lastPurchasePrice',
      width:"50px",
      render: (v, record) => {
        return record.lastPurchasePrice?.toFixed(2);
      }
    },
    {
      title: '当前采购价',
      dataIndex: 'currentPurchasePrice',
      width:"120px",
      render: (v, record) => {

        if (record.auditPrice !=null  && record.auditPrice!=record.currentPurchasePrice){
          return <>由&nbsp;<span style={{textDecoration: 'line-through'}}>{record.currentPurchasePrice?.toFixed(2)}</span>&nbsp;改为&nbsp;{record.auditPrice?.toFixed(2)}
            {record.auditPrice == null || record.auditPrice==record.currentPurchasePrice? ('')
              :record.currentPurchasePrice > record.auditPrice ?
                (<ArrowDownOutlined style={{ color: 'green' }} />)
                : (<ArrowUpOutlined style={{ color: 'red' }} />)}
          </>;
        }

        return record.currentPurchasePrice?.toFixed(2);
      }
    },
    {
      title: '采购数',
      dataIndex: 'purchaseQuantity',
      width:"120px",
      render: (v, record) => {

        if (record.auditQuantity !=null  && record.purchaseQuantity!=record.auditQuantity){
          return <>由&nbsp;<span style={{textDecoration: 'line-through'}}>{record.purchaseQuantity}</span>&nbsp;改为&nbsp;{record.auditQuantity}
            {record.auditQuantity == null || record.purchaseQuantity==record.auditQuantity ? ('')
              :record.purchaseQuantity > record.auditQuantity ?
                (<ArrowDownOutlined style={{ color: 'green' }} />)
                : (<ArrowUpOutlined style={{ color: 'red' }} />)}
          </>;
        }

        return record.purchaseQuantity;
      }
    },
    {
      title: '采购额',
      dataIndex: 'currentPurchasePrice',
      width:"50px",
      render: (v, record) => {
        let price=record.currentPurchasePrice;
        let quantity=record.purchaseQuantity;

        if (record.auditPrice!=null && record.auditPrice!=record.currentPurchasePrice){
          price=record.auditPrice;
        }

        if (record.auditQuantity!=null && record.auditQuantity!=record.purchaseQuantity){
          quantity=record.auditQuantity;
        }

        return (price*quantity)?.toFixed(2);
      }
    },
    // {
    //   title: '备注',
    //   dataIndex: 'remark',
    //   width:"80px"
    // },
  ];
  const columns: ProColumns<PurchaseOrderPriceAuditRecord, 'text'>[] = [
    {
      title: '采购单',
      dataIndex: 'orderCode',
      colSize: (4 / 24),
      render: (v, record) => {
        return (
          <>
            <div> 采购单号：{record.purchaseOrderInfo.orderCode}<SnippetsTwoTone onClick={() => copyText(record.purchaseOrderInfo.orderCode)}/></div>
            <div> 标题：{record.purchaseOrderInfo.title}</div>
            <div> 备注：{record.purchaseOrderInfo.remark}</div>
            <div> 供应商：{record.purchaseOrderInfo.supplierName}</div>
            <div> 采购员：{record.purchaseOrderInfo.purchaseUsername}</div>
            <div> 跟单员：{record.purchaseOrderInfo.merchandiserUsername}</div>
          </>
        );
      }
    },
    {
      title: '审核信息',
      dataIndex: 'audit',
      colSize: (4 / 24),
      render: (v, record) => {
        const auditType = purchaseOrderAuditTypeEnum[record?.auditType]
        return (
          <>
            <div> 审核类型：<Tag color='blue'>{auditType}</Tag></div>
            <div> 申请人：{record.applyUsername}</div>
            <div> 状态：{PurchaseOrderAuditStatusText[record?.auditStatus]}</div>
            <div> 备注：{record.remark}{record.remakeAudit!=null?(<Tag color='red'>{record.remakeAudit}</Tag>):record.remakeAudit}</div>
            <div> 申请时间：{moment(record.gmtCreate as number).format("YYYY-MM-DD HH:mm:ss")}</div>
          </>
        );
      }
    },
    {
      dataIndex: 'goodsSku',
      hideInSearch: true,
      align:"center",

      render: (v, record) => {
        return (
          <>
            <Table
              showHeader={true}
              dataSource={record.purchaseOrderGoodsInfoList} columns={columnsTab}
              rowKey="id"
              pagination = {false}
            />
          </>
        );
      }

    }
  ];

  const tabs: TabsItem[] = [
    { key: '', tab: '全部' },
    { key: PurchaseOrderAuditStatus.WAIT, tab: PurchaseOrderAuditStatusText.WAIT},
    { key: PurchaseOrderAuditStatus.PASS, tab: PurchaseOrderAuditStatusText.PASS},
    { key: PurchaseOrderAuditStatus.REJECT, tab: PurchaseOrderAuditStatusText.REJECT},
  ];

  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [activeStatusKey]);

  return (
    <>
      <CustomPage<PurchaseOrderPriceAuditRecord>
        actionRef={actionRef}
        request={fetchList}
        // scroll={{ y: 660 }}
        columns={columns}
        size={'small'}
        rowKey="id"
        bordered
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        search={{
          filterType: 'query',
          layout: 'horizontal',
          span: 24,
          collapseRender: () => null,
          defaultCollapsed: false,
          optionRender: (_, c, dom) => {
            const options = [
              <Button key="level" type="primary" onClick={() => audit(PurchaseOrderAuditStatus.PASS)}>
                通过
              </Button>,
              <Button key="level" type="primary" onClick={() => audit(PurchaseOrderAuditStatus.REJECT)}>
                驳回
              </Button>,
            ];
            return [...options, ...dom];
          }}}
        revalidateOnFocus={false}
        recordCreator={false}
        recordDelete={false}
        recordUpdater={false}
      />

    </>
  );
};
