import type {ModalProps} from 'antd';
import {Button, Form, message, Modal, notification, Row, Space, Table, Typography} from 'antd';
import type {ProColumns} from '@ant-design/pro-table';
import React, {useEffect, useState} from 'react';
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import {
  financeAuditQuery,
  periodBatchPayment
} from "@/modules/purchaseFinance/infra/api/purchaseOrder";
import {useRequest} from 'ahooks';
import {
  alibabaStatusTextEnum, payTypeEnum,
} from "@/modules/purchaseOrder/domain/purchaseOrder";
import {confirmThePayment, getTradeOrder} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {ProFormText} from "@ant-design/pro-components";
import {ExclamationCircleOutlined, SnippetsTwoTone} from "@ant-design/icons";
import {copyText} from "@/utils/comUtil";
import {ColumnProps} from "antd/es/table";
import moment from "moment";

const auditData= async (param: any)=>{
  const data=await financeAuditQuery(param);
  return data;
}

const CheckedOrderModal = (props: CheckedOrderModalProps) => {
  const {onFinish, orderData,onCancel, ...rest} = props;

  const param = {
    "orderIdList": orderData.map((item) => item.id),
  }

  const { data, refresh, loading} = useRequest(() => auditData(param).then(item=>item.body));

  const getOrderAmount =()=>{
    let purchaseOrderAmount=0;
    for (const item in data) {
      purchaseOrderAmount+=data[item].periodAmount;
    }
    return purchaseOrderAmount?.toFixed(2);
  }

  //提交财务付款
  const onOkManage = () =>{
    const payTypeUnique = orderData.filter( (ele, ind) => ind === orderData.findIndex( elem => elem.payType === ele.payType ));
    if(payTypeUnique.length > 1){
      message.error("当前选择订单包含多种交易方式，请重新选择");
      return;
    }

    // @ts-ignore
    if('1' == payTypeUnique[0]?.payType){
      Modal.confirm({
        // icon: false,
        title: "当前选择订单为银行转账支付，点击确认后则完成支付，请确认！！！",
        onOk: function () {
          periodBatchPayment(param).then((res) => {
              if (res.status.success) {
                message.success("支付成功");
                refresh();
              }
            })
        },
      });
    }else{
      periodBatchPayment(param).then((res) => {
          if (res.status.success) {
            window.open(res.body);
            refresh();
          }
        })
    }
  }


  //确认支付
  const confirmPayment=()=>{
    getTradeOrder(param.orderIdList).then((result) => {
      if (result.status.success) {
        message.success("刷新成功")
      }else {
        notification.error({
          duration: null,
          placement: "top",
          description: <Space direction="vertical" size="small">{result.body.split("<br>")}</Space>,
          message: "异常订单",
          style: {
            width: "auto"
          }
        });
      }
      onCancel();
    });
  }

  const columns: ColumnProps<PurchaseOrder>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      render: (v, record) => {
        return <span style={{fontSize:12}}>{record?.title}</span>;
      }
    },
    {
      title: '采购/平台单号',
      dataIndex: 'orderCode',
      width: 200,
      render: (v, record) => {
        return <><div>{record?.orderCode}</div><div>{record?.platformOrderCode}</div></>;
      }
    },
    {
      title: '采购员/跟单员',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{fontSize:12}}>{record?.purchaseUsername}/{record?.merchandiserUsername}</span>;
      }
    },
    {
      title: '收款人',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span>{record?.accountName || '--'}</span>;
      }
    },
    {
      title: '账期审核状态',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{fontSize:12}}>{record?.periodStatus || '--'}</span>;
      }
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      render: (v, record) => {
        const payStatus = payTypeEnum[record?.payType];
        const options = [
          payStatus != undefined ? (
            <span>
              <span className="ant-badge-status-dot ant-badge-status-success"></span>
              <span className="ant-badge-status-text" style={{fontSize:12}}>{payStatus}</span>
            </span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: '申请结算金额',
      dataIndex: 'periodAmount',
      render: (v, record) => {
        return <>
          <div>
            {record?.periodAmount?.toFixed(2) || 0.00}
            {record?.periodAmount?.toFixed(2)!=(record?.amountPayable-record?.afterAmount)?.toFixed(2) ? (
              <ExclamationCircleOutlined style={{color:'red',fontSize:10,float:"right"}}/>
            ) : null}
          </div>
        </>;
      }
    },
    {
      title: '应付金额',
      width: 300,
      dataIndex: 'amountPayable',
      render: (v, record) => {
        return <><span style={{fontSize: 12}}>{(record?.amountPayable-record?.afterAmount)?.toFixed(2)}(账期应付) =  {record?.amountPayable?.toFixed(2) || 0.00}(下单金额) - {record?.afterAmount?.toFixed(2) || 0.00}(售后金额)</span></>
      }
    },
    {
      title: '申请结算时间',
      dataIndex: 'periodTime',
      render: (v, record) => {
        return record?.periodTime ? moment(record?.periodTime)?.format("YYYY-MM-DD HH:mm") : '--';
      }
    }
  ];

  return (
    <Modal {...rest} title="财务付款" closable={false} width="90%"  footer={[
      <Button key="back" loading={loading} onClick={onCancel}>
        关闭
      </Button>,
      <Button key="submit" type="primary" loading={loading} onClick={onOkManage}>
        申请支付
      </Button>,
      <Button key="pay" style={{float: "left",backgroundColor:"#FF8800",border:"0"}} type="primary" loading={loading} onClick={confirmPayment}>
        确认支付
      </Button>,
    ]}  destroyOnClose>
      <Row>
        <b>选中单数：{data!=null?data.length:0} &nbsp;</b>
        <b>账期应付：{getOrderAmount()} &nbsp;</b>
      </Row>
      <Table
        dataSource={data}
        columns={columns}
        size={"small"}
        scroll={{ y: 550 }}
        rowKey="id"
        pagination = {false}
        loading={loading}
      />
    </Modal>
  );
}

// 定义参数格式
export type CheckedOrderModalProps = {
  orderData: PurchaseOrder[];
  onFinish: (values: PurchaseOrder[]) => void;

} & ModalProps;
export default CheckedOrderModal;
