{"name": "purchasing-center", "version": "5.2.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "build:fat": "cross-env REACT_APP_ENV=fat umi build", "build:2bFat": "cross-env REACT_APP_ENV=2bFat umi build", "build:2bPro": "cross-env REACT_APP_ENV=2bPro umi build", "build:pro": "cross-env REACT_APP_ENV=pro umi build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:2bFat", "dev:2bPro": "npm run start:2bPro", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "playwright": "playwright install && playwright test", "prepare": "husky install", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev umi dev", "start:fat": "cross-env REACT_APP_ENV=fat MOCK=none UMI_ENV=dev umi dev", "start:2bFat": "cross-env REACT_APP_ENV=2bFat MOCK=none UMI_ENV=dev umi dev", "start:2bPro": "cross-env REACT_APP_ENV=2bPro MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pro": "cross-env REACT_APP_ENV=pro UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "test": "umi test", "test:component": "umi test ./src/components", "test:e2e": "node ./tests/run-tests.js", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^1.2.11", "@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "1.1.1", "@ant-design/pro-table": "2.72.0", "@types/md5": "^2.3.2", "@types/uuid": "^9.0.0", "@umijs/plugin-qiankun": "^2.42.0", "@umijs/route-utils": "^2.0.0", "ahooks": "^3.7.0", "antd": "^4.20.0", "braft-editor": "^2.3.9", "braft-extensions": "^0.1.1", "classnames": "^2.3.0", "file-saver": "^2.0.5", "form-render": "^2.2.13", "jsbarcode": "^3.11.5", "jszip": "^3.10.1", "lodash": "^4.17.0", "mathjs": "^13.1.1", "md5": "^2.3.0", "moment": "^2.29.0", "newnary-components": "^1.0.9", "numeral": "^2.0.6", "omit.js": "^2.0.2", "rc-menu": "^9.1.0", "rc-util": "^5.16.0", "react": "^17.0.0", "react-dev-inspector": "^1.7.0", "react-dom": "^17.0.0", "react-helmet-async": "^1.2.0", "react-iframe": "^1.8.5", "styled-components": "^6.1.12", "table-render": "^2.0.19", "umi": "^3.5.0", "use-subscription": "^1.8.0", "uuid": "^9.0.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@playwright/test": "^1.17.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.0", "@types/history": "^4.7.0", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^2.11.1", "@umijs/openapi": "^1.6.0", "@umijs/plugin-blocks": "^2.2.2", "@umijs/plugin-esbuild": "^1.4.0", "@umijs/plugin-openapi": "^1.3.3", "@umijs/preset-ant-design-pro": "^1.3.0", "@umijs/preset-dumi": "^1.1.0", "@umijs/preset-react": "^2.1.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.3.0", "detect-installer": "^1.0.0", "eslint": "^7.32.0", "gh-pages": "^3.2.0", "husky": "^7.0.4", "jsdom-global": "^3.0.0", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "prettier": "^2.5.0", "stylelint": "^13.0.0", "swagger-ui-dist": "^4.12.0", "typescript": "^4.5.0", "umi-serve": "^1.9.10"}, "engines": {"node": ">=12.0.0"}}