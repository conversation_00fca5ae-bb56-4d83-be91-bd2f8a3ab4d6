import { Select } from 'antd';
import { memo, useEffect, useState } from 'react';
import { queryWarehouse } from './api';

const SelectWarehouse = memo((props: Record<keyof any, any>) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);
  const [origin, setOrigin] = useState<any[]>([]);
  useEffect(() => {
    queryWarehouse().then((res) => {
      const { body } = res;
      setOrigin(body)
      setSelectOption(body?.map((v) => ({ value: v.warehouseId, label: v.name })));
    });
  }, []);

  const onChange = (...args: any[]) => {
    props.onChange(...args)
    const [id] = args
    if (id) {
      const curItem = origin.find(val => id === val.warehouseId)
      props.onChoose && props.onChoose(curItem)
    }
  }

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择收获仓库"
      optionFilterProp="children"
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
      onChange={onChange}
    />
  );
});

export default SelectWarehouse;
