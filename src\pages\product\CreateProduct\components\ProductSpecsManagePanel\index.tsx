import { ProFormCheckbox, ProFormDependency } from '@ant-design/pro-form';
import { Drawer, Space } from 'antd';
import type { ProductSpecSkuData } from './components/ProductSpecSkuList';
import ProductSpecSkuList from './components/ProductSpecSkuList';
import styles from './styles.less';
import useMergedState from 'rc-util/es/hooks/useMergedState';

import ProFormItem from '@ant-design/pro-form/lib/components/FormItem';
import { useState } from 'react';
import RenderTitle from '@/components/RenderTitle';
import { EditOutlined } from '@ant-design/icons';
import type { SpecItemData } from '../CategorySpecField';
import CategorySpecField from '../CategorySpecField';

export interface ProductSpecsManagePanelProps {
  value?: ProductSpecSkuData[];
  onChange?: (data: ProductSpecSkuData[]) => void;
  disabled?: boolean;
  categoryId: string;
}

const ProductSpecsManagePanel = (props: ProductSpecsManagePanelProps) => {
  const { categoryId } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [skuList, onChangeSkuList] = useMergedState([], {
    value: props.value,
    onChange: props.onChange,
  });

  return (
    <>
      <a onClick={() => setVisible(true)}>
        <EditOutlined /> {skuList.length ? '编辑规格' : '添加规格'}
      </a>
      <Drawer
        title="商品规格/商品列表"
        width="70vw"
        placement="right"
        visible={visible}
        onClose={() => setVisible(false)}
      >
        <RenderTitle>规格</RenderTitle>
        <div className={styles.specsBox}>
          <ProFormItem
            name={'specs'}
            rules={[
              {
                validator: (rule, value: any[]) => {
                  const errors = value
                    ?.filter((item) => item.required && !item.specValues.length)
                    .map((item) => {
                      return `【${item.specName}】规格为必填项`;
                    });
                  if (errors.length) {
                    return Promise.reject(errors.join(','));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <CategorySpecField categoryId={categoryId} />
          </ProFormItem>
          <>
            <RenderTitle>商品列表</RenderTitle>
            <div style={{ marginLeft: 24 }}>
              <Space align="center">
                <ProFormCheckbox name="isShowFirstImage">添加规格图片</ProFormCheckbox>
                <div style={{ fontSize: '12px', color: 'red', marginBottom: '24px' }}>
                  仅支持为第一组规格设置规格图片，买家选择不同规格会看到对应规格图片，建议尺寸：800
                  x 800像素
                </div>
              </Space>
            </div>
            <ProFormDependency name={['isShowFirstImage', 'specs']}>
              {({ isShowFirstImage, specs }) => {
                return (
                  <ProductSpecSkuList
                    value={skuList}
                    onChange={onChangeSkuList}
                    specs={(specs || []).filter((item: SpecItemData) => item.specValues.length)}
                    isShowFirstImage={isShowFirstImage}
                  />
                );
              }}
            </ProFormDependency>
          </>
        </div>
      </Drawer>
    </>
  );
};

export default ProductSpecsManagePanel;
