import {ProFormSelect, ProFormField, ProFormTextArea, ProFormText} from "@ant-design/pro-form";
import {Form, ModalProps} from "antd";
import { Modal } from "antd";
import {CreatePlanParmas, getWarehouse} from "@/modules/purchasePlan/infra/api/purchasePlan";
import UploadFile from "@/components/UploadFile";

// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: CreatePlanParmas) => void;
} & ModalProps;

const SetStrategyModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, ...rest } = props;

  return <Modal {...rest} onOk={()=>form.submit()}>
    <Form form={form} onFinish={onFinish}>
      <ProFormSelect
        width="md"
        name="summaryDimension"
        label="汇总维度"
        valueEnum={{
          '供应商': '供应商',
          '小组': '小组',
          '销售仓库':'销售仓库',
          '类目':'类目',
        }}
      />
      <ProFormSelect
        width="md"
        name="conditions"
        label="汇总条件"
        valueEnum={{
          '大于': '大于',
          '等于': '等于',
          '小于':'小于',
          '大于等于':'大于等于',
          '小于等于':'小于等于',
        }}
      />
      <ProFormText
        width="md"
        name="auditParam"
        label="审核阈值"
        placeholder="请输入参数"
      />
      <ProFormSelect
        width="md"
        name="auditMechanism"
        label="审核机制"
        valueEnum={{
          '自动作废': '自动作废',
          '等于': '=',
          '小于':'<',
          '大于等于':'>=',
          '小于等于':'<=',
        }}
      />
    </Form>
  </Modal>
}

export default SetStrategyModal;
