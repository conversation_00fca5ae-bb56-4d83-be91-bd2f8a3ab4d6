import { useNation } from '@/modules/common/application/nations';
import type { NationData } from '@/modules/common/infra/api/common';
import type { SelectProps } from 'antd';
import { Select } from 'antd';
import { useMemo } from 'react';

type NationSelectProps = Omit<SelectProps<any>, 'options'> & {
  valueKey?: keyof NationData;
};

export default (props: NationSelectProps) => {
  const { valueKey = 'name', ...rest } = props;
  const { nations } = useNation();
  const options = useMemo(() => {
    return nations?.map((item) => ({ label: item.name, value: item[valueKey] }));
  }, [valueKey, nations]);

  return (
    <Select
      options={options}
      filterOption={(input, option) => {
        return (
          (option?.label as string).toLowerCase().includes(input.toLowerCase()) ||
          (option?.value as string).includes(input.toLowerCase())
        );
      }}
      {...rest}
    />
  );
};
