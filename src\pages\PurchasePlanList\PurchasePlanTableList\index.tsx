import {useRequestTable} from '@/hooks/useRequestTable';
import type {ProColumns} from '@ant-design/pro-components';
import {ProTable} from '@ant-design/pro-components';
import {Badge, Button, Card, Checkbox, Divider, Form, message, Modal, notification, Row, Select, Space} from 'antd';
import React, {useEffect, useState} from 'react';
import type {PurchasePlanListItem, WarehouseInfo} from './data';
import ExpandedRowList from './ExpandedRowList';
import {Link, useParams} from 'umi';
import type {CreatePlanParmas} from '@/modules/purchasePlan/infra/api/purchasePlan';
import {
  getPlanSupplier,
  importPlanGoods,
  pageQueryPlanDetailCheck,
  updatePlanStatus,
  updateSupplier,
} from '@/modules/purchasePlan/infra/api/purchasePlan';
import {PlanGoodsStatus, PlanGoodsStatusText} from '@/modules/purchasePlan/domain/purchasePlan';
import ImportPlanGoodsModal from '@/pages/PurchasePlanList/components/ImportPlanGoodsModal';
import {ProFormField, ProFormSelect, ProFormTextArea} from "@ant-design/pro-form";
import {DingdingOutlined, SnippetsTwoTone} from "@ant-design/icons/lib";
import {useRequest} from "ahooks";
import PurchasePlanAnalysisModal from "@/pages/PurchasePlanList/components/PurchasePlanAnalysisModal";
import {purchaseDataCount} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {Access} from "@@/plugin-access/access";
import aliwangwang from "@/assets/images/aliwangwang.gif";
import {copyText} from "@/utils/comUtil";
import CustomPage from "@/components/CustomPage";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import Permission from "@/components/Permission";
import $ from "jquery";
import CreatePurchasePlanGoodsModal from "@/pages/PurchasePlanList/components/CreatePurchaseGoodsModal";

export default () => {
  const { id: orderId } = useParams<{ id: string }>();
  const { data } = useRequest(() => purchaseDataCount({type:"planGoodsReject",purchasePlanId:orderId}).then((res) => res.body));
  const [activeStatusKey, setActiveStatusKey] = useState<string>("0");
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [cretaePlanGoodsModal, setCretaePlanGoodsModal] = useState<boolean>(false);
  // 整个表单选中的数据
  const [purchasePlanAnalysisModal, setPurchasePlanAnalysisModal] = useState<boolean>(false);
  const [form] = Form.useForm();

  const { fetchList, actionRef } = useRequestTable((params) => {
    return pageQueryPlanDetailCheck({
      ...params,
      purchasePlanId: orderId,
      status: activeStatusKey,
    });
  });
  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reload?.();
  }, [activeStatusKey]);

  /**
   * 导入创建采购计划请求
   * @param values
   */
  const importGoods = (values: CreatePlanParmas) => {
    values.purchasePlanId = orderId==null?0:orderId;
    importPlanGoods(values).then((result) => {
      if (result.status.success) {
        actionRef.current?.reload?.();
        message.success('创建成功');
      }
    });
    // 处理完成后把弹窗关闭
    setModalVisible(false);
  };

  /**
   * 更换供应商
   */
  const updateSupplierButton = (selectedIds: string[]) => {
    const param = {
      planGoodsId:  selectedIds,
    };
    if (param.planGoodsId.length == 0) {
      notification.error({ message: '请勾选采购计划' });
      return false;
    }

    Modal.confirm({
      title: "更换供应商",
      content: (
        <Form form={form}>
          <ProFormSelect
            label="供应商"
            name="supplierId"
            showSearch={true}
            request={async () =>  {//返回的select网络请求
              const params = await getPlanSupplier(param.planGoodsId);
              const res = [];
              const body = params.body;
              for(let i in body){
                const temp = {};
                temp['label'] = body[i];
                temp['value'] = i;
                res.push(temp)
              }
              return res;
            }}
          />
        </Form>
      ),
      onOk: async () => {
        param["supplierId"]=form.getFieldValue("supplierId");
        updateSupplier(param).then((result) => {
          if (result.status.success) {
            notification.success({ message: '操作成功' });
            actionRef.current?.reload?.();
          }
        });
      },
    });

    form.resetFields();
  };

  /***
   * 获取已经选择的planGoodsIds
   */
  const getIds = () => {
    const planGoodsIds = [];
    $('.planGoodsItemCheck').each(function (){
      if($(this).prop("checked")){
        planGoodsIds.push($(this).val())
      }
    })
    console.log(planGoodsIds);
    return planGoodsIds;
  }

  /**
   * 审核公用方法
   * @param values
   * @param status
   */
  // @ts-ignore
  const audit = (selectedIds: string[], status: string) => {
    const param = {
      ids: selectedIds,
      status: status,
    };
    console.log(param);
    if (param.ids.length == 0) {
      notification.error({ message: '请勾选采购计划' });
      return false;
    }
    Modal.confirm({
      icon: null,
      content: (
        <>
          <Form form={form}>
          {status == PlanGoodsStatus.ABOLISH ?
          <ProFormField
            label="原因"
            name="remark"
          >
            <Select placeholder={""}
                    options={[
                      {value: "一单多量引发备货", label: '一单多量引发备货'},
                      {value: "积压库存刚消耗完", label: '积压库存刚消耗完'},
                      {value: "有库存够需求", label: '有库存够需求'},
                      {value: "有在途够需求", label: '有在途够需求'},
                      {value: "销量差无缺货", label: '销量差无缺货'},
                      {value: "货值低无缺货", label: '货值低无缺货'},
                      {value: "季节性产品", label: '季节性产品'},
                      {value: "待整合备货", label: '待整合备货'},
                      {value: "重复提需求", label: '重复提需求'},
                      {value: "其余原因", label: '其余原因'},
                    ]}
            />
          </ProFormField>
        :
            <ProFormTextArea label="备注" name="remark" />
        }
          </Form>
        </>
      ),
      onOk: async () => {

        param["remark"]=form.getFieldValue('remark');
        //驳回添加备注
        if (status == PlanGoodsStatus.REJECT && (param["remark"]=="" || param["remark"]==null)){
          notification.error({ message: '请填写驳回备注' });
        }else {
          updatePlanStatus(param).then((result) => {
            if (result.status.success) {
              notification.success({ message: '操作成功' });
              actionRef.current?.reload?.();
            }
          });
        }
        form.resetFields();
      },
    });
  };

  const columns: ProColumns<PurchasePlanListItem, 'text'>[] = [
    {
      title: 'SKU',
      align: "center",
      dataIndex: 'sku',
      colSize: (6 / 24),
      hideInTable: true
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      colSize: (6 / 24),
      hideInTable: true
    },
    {
      title: '审核步骤',
      dataIndex: 'auditLabel',
      colSize: (6 / 24),
      hideInTable: true,
      valueEnum: {
        '待审核': {
          text: "待审核",
          status: 'Success',
        },
        '组长审核': {
          text: "组长审核",
          status: 'Success',
        },
        '销售审核': {
          text: "销售审核",
          status: 'Success',
        },
        '老板审核(金额大于1w)': {
          text: "CEO审核",
          status: 'Success',
        },
      }
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      colSize: (6 / 24),
      hideInTable: true,
      valueEnum: {
        '待审核': {
          text: "待审核",
          status: 'Success',
        },
        '已审核': {
          text: "已审核",
          status: 'Success',
        }
      }
    },
    {
      width: '100%',
      title: <div><Checkbox style={{float:"right"}} onChange={(event)=>{
        // setCheckAll(event.target.checked)
        if(event.target.checked){
          //全选
          $('.planSupplierCheckBox').each(function (){
            $(this).prop("checked", true)
          })
        }else{
          //取消全选
          $('.planSupplierCheckBox').each(function (){
            $(this).prop("checked",false)
          })
        }
      }}/>基础信息</div>,
      align: "center",
      dataIndex: 'sku',
      hideInSearch: true,
      render: (v, record) => {
        const link = record.aliWangWangLink != null ? (<a onClick={function () {
            if (record.aliWangWangLink != null) {
              window.open(record.aliWangWangLink);
            }
          }}><img style={{float:"left"}} src={aliwangwang}/></a>) :
          <span className="grey" style={{color: "red"}}>暂未绑定供应商</span>

        const totalAmount = record.planGoodsInfos
          ?.reduce((sum, item) => sum + item.purchaseQuantity * item.purchasePrice, 0.0)
          ?.toFixed(2);
        return (
          <>
            <Card bodyStyle={{padding:0, margin: 0}} headStyle={{padding:0, margin: 0}} style={{width: "100%"}}>
              <Row justify="space-between" style={{height:30, padding:5, backgroundColor: "#DDDDDD", borderRadius: 4}}>
                <div>
                  <input type={"checkbox"}  className={"planSupplierCheckBox"}  style={{float: "left", marginRight: 5, border: "1px sold red"}} value={record.supplierName}
                         onChange={(event)=>{
                           if(event.target.checked){
                             $(event.target.closest('tr')).find(".planGoodsItemCheck").each(function (){
                               $(this).prop("checked", true)
                             })
                           }else{
                             $(event.target.closest('tr')).find(".planGoodsItemCheck").each(function (){
                               $(this).prop("checked", false)
                             })
                           }}
                         }
                  />
                  {link} &nbsp;&nbsp; {record.supplierName} <SnippetsTwoTone onClick={() => copyText(record.supplierName)}/> &nbsp;&nbsp;
                  <b>
                    <span style={(totalAmount>5000 || totalAmount<30) ? {color:"red"} : null}>金额：
                      <span className={"planSupplierTotalAmount"+record.supplierName}>{totalAmount}</span>
                    </span>
                    &nbsp;&nbsp;&nbsp;
                    <span>共有 {record.planGoodsInfos.length} 种SKU</span>
                  </b>
                  &nbsp;&nbsp;&nbsp;
                  <a onClick={()=>{message.success("功能开发中！")}}><DingdingOutlined/>发送钉钉审核</a>
                </div>
                <div>
                  <Space>
                    <Button
                      size="small"
                      danger
                      onClick={() => {
                        let curIds = [];
                        record?.planGoodsInfos.map((item)=>{
                          curIds.push(item.id);
                        });
                        audit(curIds, PlanGoodsStatus.ABOLISH)}
                      }
                    >
                      作废计划
                    </Button>
                  </Space>
                </div>
              </Row>
              <Divider style={{margin:0, padding: 0}}/>
              <Row>
                <ExpandedRowList record={record} actionRef={actionRef} />
              </Row>
            </Card>
          </>
        );
      },
    },
    {
      title: '审核状态',
      dataIndex: 'checkStatus',
      hideInTable: true,
      renderFormItem: (t, config, formInstance) => {
        return <Filters.CheckboxButton
          defaultValue={["0"]}
          options={[
            {value: "", label: '全部'},
            {value: PlanGoodsStatus.PENDING, label: PlanGoodsStatusText.PENDING},
            {value: PlanGoodsStatus.ABOLISH, label: PlanGoodsStatusText.ABOLISH},
            {value: PlanGoodsStatus.PASS, label: PlanGoodsStatusText.PASS},
            {value: PlanGoodsStatus.CREATED, label: PlanGoodsStatusText.CREATED},
            { value: PlanGoodsStatus.REJECT, label: (<Badge overflowCount={9999} size={"small"} style={{backgroundColor:"#FF3333",marginTop:-6,marginRight:-6}} count={data?.planGoodsReject}>驳回</Badge>) }
          ]}
          value={formInstance.getFieldValue('checkStatus')}
          onChange={(e) => {
            if(e.length>0 && e!=formInstance.getFieldValue('checkStatus')){
              setActiveStatusKey(e[0].toString())
              formInstance.setFieldValue('checkStatus', e);
            }
            formInstance.submit();
          }}

        />
      }
    },
  ];

  return (
    <>
        <CustomPage<PurchasePlanListItem>
          actionRef={actionRef}
          request={fetchList}
          scroll={{ y: 'calc(100vh - 320px)' }}
          columns={columns}
          size={'small'}
          bordered
          //  接口返回需要添加supplierId,表格需要指定唯一Key
          rowKey="supplierName"
          tableAlertRender={false}
          search={{
            filterType: 'query',
            layout: 'horizontal',
            span: 24,
            collapseRender: () => null,
            defaultCollapsed: false,
            optionRender: (_, c, dom) => {
              const options = [
                <Permission permissionKey={"purchase:purchase:purchasePlan:checkPass"}>
                  <Button
                    key="level"
                    disabled={activeStatusKey!="0" && activeStatusKey!="50"?true:false}
                    type="primary"
                    size={"small"}
                    style={{fontSize: 13,borderRadius:"5px"}}
                    onClick={() => audit(getIds(), PlanGoodsStatus.PASS)}
                  >
                    通过
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:checkReject"}>
                  <Button
                    key="level"
                    disabled={activeStatusKey!="0" && activeStatusKey!="50"?true:false}
                    type="primary"
                    size={"small"}
                    style={{fontSize: 13,borderRadius:"5px"}}
                    onClick={() => audit(getIds(), PlanGoodsStatus.REJECT)}>
                    驳回
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:changeSupplier"}>
                  <Button
                    key="level"
                    type="primary"
                    size={"small"}
                    style={{fontSize: 13,borderRadius:"5px"}}
                    onClick={() => updateSupplierButton(getIds())}
                  >
                    更换供应商
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:importPlanGoods"}>
                  <Button key="level" type="primary"
                          size={"small"}
                          style={{fontSize: 13,borderRadius:"5px"}}
                          onClick={() => setCretaePlanGoodsModal(true)}>
                    创建商品
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:importPlanGoods"}>
                  <Button key="level" type="primary"
                          size={"small"}
                          style={{fontSize: 13,borderRadius:"5px"}}
                          onClick={() => setModalVisible(true)}>
                    导入商品
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:batchCancel"}>
                  <Button
                    key="level"
                    type="primary"
                    disabled={activeStatusKey!="0" && activeStatusKey!="50"?true:false}
                    size={"small"}
                    style={{fontSize: 13,borderRadius:"5px"}}
                    onClick={() => audit(getIds(), PlanGoodsStatus.ABOLISH)}
                  >
                    批量作废
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:batchCancel"}>
                  <Button
                    key="level"
                    type="primary"
                    disabled={activeStatusKey!="20"?true:false}
                    size={"small"}
                    style={{fontSize: 13,borderRadius:"5px"}}
                    onClick={() => audit(getIds(), PlanGoodsStatus.PENDING)}
                  >
                    取消作废
                  </Button>
                </Permission>,
                <Permission permissionKey={"purchase:purchase:purchasePlan:planAnalyse"}>
                  <Button size={"small"} style={{fontSize: 13,borderRadius:"5px"}} key="level" type="primary" onClick={() => setPurchasePlanAnalysisModal(true)}>
                    计划分析
                  </Button>
                </Permission>,
              ];
              return [...options, ...dom];
            },
          }}
          pagination={{pageSize: 30}}
          recordCreator={false}
          recordUpdater={false}
          recordDelete={false}
        />
      <ImportPlanGoodsModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onFinish={importGoods}
      />
      <Access accessible={cretaePlanGoodsModal}>
      <CreatePurchasePlanGoodsModal
        visible={cretaePlanGoodsModal}
        planId={orderId}
        onCancel={() => setCretaePlanGoodsModal(false)}
        onFinish={() => setCretaePlanGoodsModal(false)}
      />
      </Access>
      <Access accessible={purchasePlanAnalysisModal}>
        <PurchasePlanAnalysisModal visible={purchasePlanAnalysisModal} planId={orderId} onCancel={() => setPurchasePlanAnalysisModal(false)} onFinish={() => setPurchasePlanAnalysisModal(false)}/>
      </Access>

    </>
  );
};
