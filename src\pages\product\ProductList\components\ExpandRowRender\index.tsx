import type { SpuListItem, SpuListSku } from '@/modules/product/domain/spu';
import {Descriptions, Image, Space, Table, Tabs} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import styles from './styles.less';

export type ExpandRowRenderProps = {
  dataSource: SpuListItem;
};

const SkuTableList = (props: ExpandRowRenderProps) => {
  const { dataSource } = props;
  const columns: ColumnsType<SpuListSku> = [
    {
      title: 'SKU信息',
      dataIndex: 'sku',
      width: 550,
      render: (v, record) => {
        return (
          <Space>
            <div style={{ width: 80 }}>
              <Image src={record?.mainSpecInfo?.image?.fileUrl} width={80} />
            </div>
            <div>
              <Descriptions column={1} className={styles.list}>
                {record.specs?.map((item, index) => {
                  return (
                    <Descriptions.Item key={index} label={item.specName}>
                      {item.specValue}
                    </Descriptions.Item>
                  );
                })}
                <Descriptions.Item label="系统编码">{record.skuId}</Descriptions.Item>
                <Descriptions.Item label="SKU">{record.customCode || '--'}</Descriptions.Item>
              </Descriptions>
            </div>
          </Space>
        );
      },
    },
    // {
    //   title: '首选供应商',
    //   dataIndex: 'logistic',
    //   width: 150,
    //   render: (v, record) => {
    //     return (
    //       <Space wrap>
    //         {record?.purchaseSuppliers?.map((item) => {
    //           return <span key={item.supplierId}>{item.supplierName}</span>;
    //         })}
    //       </Space>
    //     );
    //   },
    // },
    {
      title: '长',
      dataIndex: 'weight',
      render: (v, record) => {
        return (record.sizeLength || 0);
      },
    },
    {
      title: '宽',
      dataIndex: 'weight',
      render: (v, record) => {
        return (record.sizeWidth || 0);
      },
    },
    {
      title: '高',
      dataIndex: 'weight',
      render: (v, record) => {
        return (record.sizeHeight || 0);
      },
    },
    {
      title: '毛重',
      dataIndex: 'weight',
      render: (v, record) => {
        return (record.grossWeight || 0);
      },
    },
    {
      title: '净重',
      dataIndex: 'weight',
      render: (v, record) => {
        return (record.netWeight || 0);
      },
    },
    {
      title: '是否带电',
      dataIndex: 'weight',
      render: (v, record) => {
        let isCharged="";
        if (dataSource.isCharged!=null){
          isCharged="否";
          if (dataSource.isCharged=="1"){
            isCharged="是";
          }

        }

        return isCharged;
      },
    },
    {
      title: '是否含液体',
      dataIndex: 'weight',
      render: (v, record) => {
        let isContainLiquid="";
        if (dataSource.isContainLiquid!=null){
          isContainLiquid="否";
          if (dataSource.isContainLiquid=="1"){
            isContainLiquid="是";
          }

        }

        return isContainLiquid;
      },
    },
    {
      title: '是否特货',
      dataIndex: 'weight',
      render: (v, record) => {
        let isContainSpecial="";
        if (dataSource.isContainSpecial!=null){
          isContainSpecial="否";
          if (dataSource.isContainSpecial=="1"){
            isContainSpecial="是";
          }

        }
        return isContainSpecial;
      },
    },
  ];

  return <Table columns={columns} dataSource={dataSource.skus} pagination={false} />;
};

export default (props: ExpandRowRenderProps) => {
  return (
    <Tabs type="card">
      <Tabs.TabPane tab="SKU信息" tabKey="sku" key="sku">
        <SkuTableList {...props} />
      </Tabs.TabPane>
      <Tabs.TabPane tab="销售分析" tabKey="sale" key="sale" disabled />
    </Tabs>
  );
};
