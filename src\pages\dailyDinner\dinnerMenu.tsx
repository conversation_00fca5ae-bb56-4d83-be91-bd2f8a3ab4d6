import React, {useState} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import {Alert, Badge, Button, Form, Input, message, Modal, Space, Tag} from "antd";
import Permission from "@/components/Permission";
import {executeSave, reqByPage, reqByUrl} from "@/modules/common/infra/api/common";
import moment from "moment";
import {DailyDinner, DailyDinnerMenu} from "@/pages/dailyDinner/data";
import OrderDailyDinnerModal from "@/pages/dailyDinner/components/OrderDailyDinnerModal";
import Filters from "@/pages/PurchaseOrderList/components/Filters";
import {Access} from "umi";
import EditDinnerMenuModal from "@/pages/dailyDinner/components/EditDinnerMenuModal";

const TableList: React.FC = () => {

  const [editMenuModal, setEditMenuModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<DailyDinnerMenu>();

  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage('/sales-mgmt-biz/sales-center/setting/daily-dinner/menuPageQuery',{
      ...params,
    });
  });

  const deleteDinnerMenu = (record: DailyDinnerMenu) =>{
    Modal.confirm({
      title: '确认删除吗?',
      onOk: async () => {
        reqByUrl('/sales-mgmt-biz/sales-center/setting/daily-dinner/deleteDailyDinnerMenu', {'id': record?.id}).then((result) => {
          if (result.status.success) {
            message.success('删除成功');
            actionRef?.current?.reload();
          }
        });
      },
    });
  }

  const columns: ProColumns<DailyDinnerMenu>[] = [
    // {
    //   title: "图片",
    //   dataIndex: 'name',
    //   align: "left",
    //   hideInSearch: true,
    //   width: 80,
    //   render: (v, record) => {
    //     return <Image src={record?.img} width={80}/>
    //   }
    // },
    {
      title: "菜名",
      dataIndex: 'name',
      fieldProps: {'searchTitle': '菜名'},
      align: "left",
      render: (v, record) => {
        return <div>{record?.name}</div>
      }
    },
    {
      title: '价格',
      key: 'price',
      dataIndex: 'price',
      align: "left",
      hideInSearch: true,
      render: (v, record) => {
        return <div>{record?.price?.toFixed(2)}</div>
      }
    },
    {
      title: '类别',
      key: 'type',
      dataIndex: 'type',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.type}</div>
      }
    },
    {
      title: '餐厅',
      key: 'canteenName',
      dataIndex: 'canteenName',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.canteenName}</div>
      }
    },
    {
      title: '供应时间',
      key: 'provisionTime',
      dataIndex: 'provisionTime',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.provisionTime}</div>
      }
    },
    {
      title: '备注',
      key: 'remark',
      dataIndex: 'remark',
      hideInSearch: true,
      align: "left",
      render: (v, record) => {
        return <div>{record?.remark}</div>;
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 150,
      hideInSearch: true,
      render: (v, record) => {
        return (
          <Space>
            <Permission permissionKey={"purchase:setting:deleteDinnerMenu"}>
              <a style={{color: "red"}} onClick={()=>{deleteDinnerMenu(record)}}>
                删除
              </a>
            </Permission>
            <Permission permissionKey={"purchase:setting:editDinnerMenu"}>
              <a onClick={()=>{setCurrentRow(record); setEditMenuModal(true)}}>
                编辑
              </a>
            </Permission>
          </Space>
        );
      }
    }
  ];

  return (
    <>
      <CustomPage<DailyDinner>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        columns={columns}
        scroll={{y: 660}}
        toolBarRender={()=>[
          <Permission permissionKey={"purchase:setting:editDinnerMenu"}>
            <Button size={"small"} ghost={true} key="dinner" type="primary" onClick={() => {
              setEditMenuModal(true);
            }}>
              添加菜单
            </Button>
          </Permission>
        ]}
      />
      <Access  accessible={editMenuModal}>
        <EditDinnerMenuModal menuData={currentRow} visible={editMenuModal} onCancel={()=>setEditMenuModal(false)} onFinish={()=>{setEditMenuModal(false);actionRef.current?.reload()}}/>
      </Access>
    </>
  );
};

export default TableList;
