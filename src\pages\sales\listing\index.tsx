import React, {useEffect} from 'react';
import type {ProColumns} from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import {useRequestTable} from "@/hooks/useRequestTable";
import type {PurchaseOrder} from "@/pages/PurchaseOrderList/data";
import styles from "@/pages/PurchaseOrderList/styles.less";
import {reqByPage} from "@/modules/common/infra/api/common";
import {Image} from "antd";
import {AsinkingMskuListing} from "@/pages/sales/listing/data";

const Index: React.FC = () => {
  const {fetchList, actionRef} = useRequestTable((params) => {
    return reqByPage("/sales-mgmt-biz/sales-center/asinking/ListingPageQuery",{
      ...params,
    });
  });

  const columns: ProColumns<AsinkingMskuListing>[] = [
    {
      title: '图片',
      width: 100,
      dataIndex: 'smallImageUrl',
      hideInSearch: true,
      render: (v, record) => {
        return  (
          <div><Image src={record?.smallImageUrl} width={80} height={80} /> </div>
        );
      }
    },
    {
      title: 'item名称',
      dataIndex: 'itemName',
      width: 400,
      hideInSearch: true,
      render: (v, record) => {
        return record?.itemName;
      }
    },
    {
      title: 'MSKU',
      dataIndex: 'msku',
      // hideInSearch: true,
      render: (v, record) => {
        return record?.sellerSku;
      }
    },
    {
      title: 'SKU',
      dataIndex: 'localsku',
      // hideInSearch: true,
      render: (v, record) => {
        return record?.localSku;
      }
    },
    {
      title: 'asin',
      dataIndex: 'asin',
      hideInSearch: true,
      render: (v, record) => {
        return record.asin;
      }
    },
    {
      title: '渠道',
      dataIndex: 'fulfillmentChannelType',
      hideInSearch: true,
      render: (v, record) => {
        return record?.fulfillmentChannelType;
      }
    },
    {
      title: '币种',
      dataIndex: 'currencyCode',
      hideInSearch: true,
      render: (v, record) => {
        return record.currencyCode;
      }
    },
    {
      title: '上架时间',
      dataIndex: 'openDate',
      hideInSearch: true,
      render: (v, record) => {
        return record?.openDate;
      }
    }
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, []);

  return (
    <>
      <CustomPage<PurchaseOrder>
        actionRef={actionRef}
        request={fetchList}
        rowKey="id"
        scroll={{y:670}}
        //可以隐藏选择多少项提示
        tableAlertRender={false}
        columns={columns}
        className={styles.inline_search_table}
        toolBarRender={() => []}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
    </>
  );
};
export default Index;
