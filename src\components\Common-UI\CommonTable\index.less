.common-table-wrapper {
  height: 100%;

  .common-table-spin {

    // 1 toolBar
    .ant-pro-table-list-toolbar-container {
      .ant-pro-table-list-toolbar-left {
        margin-right: 16px;
      }

      .ant-pro-table-list-toolbar-right {
        width: 100%;

        .ant-space-item:first-child {
          flex: 1;
        }

        .ant-space-align-center {
          width: 100%;

          button {
            margin-right: 12px;
          }
        }
      }
    }

    .row-hide-table {
      td {
        border: unset;
      }
    }

    .common-table-actions-vertical {
      flex-direction: column;
      align-items: center;

      a {
        padding: 2px 0;
      }
    }

    .common-table-actions-tile {
      align-items: center;

      a {
        padding-top: 4px;
        padding-right: 8px;
      }
    }

    .ant-pro-table {
      position: relative;
      // z-index: 101 !important;
    }

    .ant-pro-table-alert {
      display: none !important;
    }

    .ant-pro-core-label-tip-title {
      font-size: 14px;
      white-space: nowrap;
    }

    .ant-table-cell-row-hover {
      background-color: var(--bg) !important;

      table {
        background-color: var(--bg) !important;
      }
    }

    .ant-pro-card {
      border-radius: 6px;
    }

    .ant-table-thead {
      top: 88px;
      background-color: rgb(250, 250, 250);

      .ant-table-cell {
        color: rgba(0, 0, 0, 0.88);
        font-weight: 600;
      }
    }

    .ant-table-tbody {
      .ant-table-cell {
        white-space: pre-wrap;
      }
    }

    .before-box {
      position: relative;
      z-index: 22;
      width: 100%;
      margin-bottom: 22px;
    }

    .after-box {
      position: relative;
      z-index: 22;
      width: 100%;
      margin-top: 22px;
    }

    .row-table {
      width: 100%;

      .ant-table {
        margin: 0 !important;

        .ant-table-tbody>tr>td {
          border-bottom: unset;
        }
      }
    }

    // 子表格背景颜色
    .ant-table-expanded-row {
      .ant-pro-card-body {
        background-color: #f2f2f2;
      }

      .ant-pagination {
        margin: 0;
        padding: 16px 0;
        background-color: #fff;
      }
    }

    // 固定表头
    .fixed-top-table {
      .ant-table-thead {
        position: sticky;
        top: var(--fixedTop);
        z-index: 99;
      }
    }

    // 表格总内容
    .ant-pro-table {

      // 1) 搜索栏
      &>.ant-pro-table-search {
        margin-bottom: 8px;
        padding: 16px 24px 4px 24px;

        .ant-form {
          &>.ant-row {
            .ant-col {
              height: 32px;
              margin-bottom: 12px;
            }

            .ant-col:last-child {
              .ant-row {
                .ant-col:first-child {
                  display: none;
                }

                .ant-form-item-control-input-content {
                  text-align: right;

                  .ant-space {
                    flex-wrap: nowrap !important;
                  }

                  .ant-space-item {
                    white-space: nowrap;
                  }
                }
              }
            }
          }
        }
      }

      // 2) 表格主体
      &>.ant-pro-card {
        &>.ant-pro-card-body {
          padding: 0 24px;

          // 2.1 展开子表格默认背景色
          .ant-table-expanded-row {
            .ant-table-cell {
              .ant-table {
                .ant-table-thead {
                  tr {
                    th {
                      font-weight: normal;
                      background-color: #d7d7d7;
                    }
                  }
                }

                .ant-table-tbody {
                  tr {
                    td {
                      background-color: #f2f2f2;
                    }
                  }
                }
              }
            }
          }

          // 2.2 表格滚动轴
          .ant-table-body::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          /*定义滚动条的轨道颜色、内阴影及圆角*/
          .ant-table-body::-webkit-scrollbar-track {
            background-color: #f8f8f8;
            border-radius: 3px;
          }

          /*定义滑块颜色、内阴影及圆角*/
          .ant-table-body::-webkit-scrollbar-thumb {
            background-color: #b8b8b8;
            border-radius: 7px;
          }
        }
      }

      // 3) 表格展开
      .ant-table-expanded-row-fixed {
        .common-table {
          .ant-table {
            margin: 0 !important;
          }
        }
      }

      // 4) 表格列内容
      .ant-image {
        img {
          object-fit: cover;
        }
      }
    }
  }
}

// 设置弹窗弹性宽高（需要将页面的高度设置）
.flex-table {
  .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;

      .common-table-spin {
        height: 100%;

        .ant-pro-table {
          display: flex;
          flex-direction: column;
          height: 100%;

          &>.ant-pro-card:nth-child(2) {
            flex: 1;
            overflow: hidden;

            .ant-pro-card-body {
              display: flex;
              flex-direction: column;
              height: 100%;

              &>.common-table {
                flex: 1;
                overflow: hidden;

                .ant-spin-nested-loading {
                  height: 100%;

                  .ant-spin-container {
                    display: flex;
                    flex-direction: column;
                    height: 100%;

                    &>.ant-table {
                      // flex: 1;
                      overflow: hidden;

                      .ant-table-container {
                        height: 100%;

                        .ant-table-body {
                          height: calc(100% - 31px);
                          overflow-y: auto !important;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
