import { ProFormField } from '@ant-design/pro-form';
import {Form, Modal, ModalProps, Row, Space} from 'antd';
import {CreatePlanParmas, downloadPurchasePlanTemplate} from '@/modules/purchasePlan/infra/api/purchasePlan';
import UploadFile from '@/components/UploadFile';
import mallApiConfig from "../../../../config/mallApiConfig";
import {Link} from "@umijs/preset-dumi/lib/theme";
import {commonExport} from "@/utils/comUtil";

// 定义参数格式
export type CreateModalProps = {
  onFinish: (values: CreatePlanParmas) => void;
} & ModalProps;

const ImportPlanGoodsModal = (props: CreateModalProps) => {
  const [form] = Form.useForm();
  const { onFinish, ...rest } = props;

  return (
    <Modal {...rest} title="导入采购计划" onOk={() => form.submit()}>
      <Form form={form} onFinish={onFinish}>
        <Space>
          <ProFormField formItemProps={{style: {marginBottom: 0}}} name="importExcelUrl" label="文件">
            <UploadFile limit={1} />
          </ProFormField>
          <a  style={{marginLeft:10}} onClick={function () {
            downloadPurchasePlanTemplate().then(res=>{
              commonExport(res, '采购计划导入模板');
            });
          }}>下载模板</a>
        </Space>
      </Form>
    </Modal>
  );
};

export default ImportPlanGoodsModal;
