import { InfoCircleOutlined } from '@ant-design/icons';
import {<PERSON><PERSON>, Col, Row, Tabs, Tooltip} from 'antd';
import {ChartCard, Field} from "@/pages/analysis/components/Charts";
import DemoWaterfall from "@/pages/datahouse/purchase/components/upChart";
import <PERSON><PERSON><PERSON> from "@/pages/datahouse/purchase/components/zhuChart";
import DemoPie from "@/pages/datahouse/purchase/components/paiChart";
import RelationChart from "@/pages/datahouse/purchase/components/relationChart";
import Yuan from "@/pages/analysis/utils/Yuan";
import numeral from "numeral";
import Trend from "@/pages/analysis/components/Trend";
import styles from "@/pages/analysis/style.less";
import {Progress, TinyArea, TinyColumn} from "@ant-design/charts";
import TableChart from "@/pages/datahouse/purchase/components/tableChart";

const topColResponsiveProps = {
  xs: 24,
  sm: 12,
  md: 12,
  lg: 12,
  xl: 6,
  style: { marginBottom: 24 },
};

const IntroduceRow = ({ loading, visitData = [] }: { loading: boolean; visitData: any[] }) => (
  <>
    <Row gutter={24} style={{marginTop: 15}}>
      <Col {...topColResponsiveProps}>
        <ChartCard
          bordered={false}
          title="当天采购金额"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
          loading={loading}
          total={() => <Yuan>99999</Yuan>}
          footer={<Field label="日采购金额" value={`￥${numeral(99999).format('0,0')}`} />}
          contentHeight={46}
        >
          <Trend flag="up" style={{ marginRight: 16 }}>
            周同比
            <span className={styles.trendText}>12%</span>
          </Trend>
          <Trend flag="down">
            日同比
            <span className={styles.trendText}>11%</span>
          </Trend>
        </ChartCard>
      </Col>

      <Col {...topColResponsiveProps}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="采购数量"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
          total={numeral(9999).format('0,0')}
          footer={<Field label="日均采购量" value={numeral(1234).format('0,0')} />}
          contentHeight={46}
        >
          <TinyArea
            height={46}
            autoFit
            smooth
            areaStyle={{
              fill: 'l(270) 0:rgb(151 95 228 / 10%) 0.5:rgb(151 95 228 / 60%) 1:rgb(151 95 228)',
            }}
            line={{
              color: '#975FE4',
            }}
            data={visitData.map((item) => item.y)}
          />
          <Trend flag="up" style={{ marginRight: 16 }}>
            周同比
            <span className={styles.trendText}>12%</span>
          </Trend>
          <Trend flag="down">
            日同比
            <span className={styles.trendText}>11%</span>
          </Trend>
        </ChartCard>
      </Col>

      <Col {...topColResponsiveProps}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="支付笔数"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
          total={numeral(999).format('0,0')}
          footer={<Field label="转化率" value="60%" />}
          contentHeight={46}
        >
          <TinyColumn height={46} autoFit data={visitData.map((item) => item.y)} />
          <Trend flag="up" style={{ marginRight: 16 }}>
            周同比
            <span className={styles.trendText}>12%</span>
          </Trend>
          <Trend flag="down">
            日同比
            <span className={styles.trendText}>11%</span>
          </Trend>
        </ChartCard>
      </Col>
      <Col {...topColResponsiveProps}>
        <ChartCard
          loading={loading}
          bordered={false}
          title="采购下单完成率"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
          total="88%"
          footer={
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden' }}>
              <Trend flag="up" style={{ marginRight: 16 }}>
                周同比
                <span className={styles.trendText}>12%</span>
              </Trend>
              <Trend flag="down">
                日同比
                <span className={styles.trendText}>11%</span>
              </Trend>
            </div>
          }
          contentHeight={46}
        >
          <Progress
            height={46}
            padding={[15, 0]}
            percent={0.78}
            color="#13C2C2"
            autoFit
            annotations={[
              {
                type: 'line',
                start: ['80%', '0%'],
                end: ['80%', '100%'],
                style: {
                  stroke: '#13C2C2',
                },
              },
            ]}
          />
        </ChartCard>
      </Col>
    </Row>
    <Row gutter={24}>
      <Col span={24}>

        <ChartCard
          bordered={false}
          loading={loading}
          title="采购数量"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }

        >
          <Tabs
            tabBarExtraContent={
              <div className={styles.salesExtraWrap}>
                <div className={styles.salesExtra}>
                  <a>
                    今日
                  </a>
                  <a>
                    本周
                  </a>
                </div>
                {/*<RangePicker*/}
                {/*  value={rangePickerValue}*/}
                {/*  onChange={handleRangePickerChange}*/}
                {/*  style={{ width: 256 }}*/}
                {/*/>*/}
              </div>
            }
            size="large"
            tabBarStyle={{ marginBottom: 24 }}
          >
          <TableChart />
          </Tabs>
        </ChartCard>
      </Col>
    </Row>
    <Row gutter={24} style={{marginTop: 10}}>
      <Col span={12}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="采购数量"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
        >
          <ZhuChart />
        </ChartCard>
      </Col>
      <Col span={12}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="采购数量"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
        >
          <DemoWaterfall />
        </ChartCard>
      </Col>

    </Row>
    <Row gutter={24} style={{marginTop: 10}}>

      <Col span={12}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="采购数量"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
        >
          <DemoPie />
        </ChartCard>
      </Col>
      <Col span={12}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="采购数量"
          action={
            <Tooltip title="指标说明">
              <InfoCircleOutlined />
            </Tooltip>
          }
          // contentHeight={420}
        >
          <RelationChart />
        </ChartCard>
      </Col>
    </Row>
  </>
);

export default IntroduceRow;
