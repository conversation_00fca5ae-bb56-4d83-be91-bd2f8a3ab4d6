import React from 'react';
import {Card, Descriptions, Spin} from "antd";
import {useRequest} from "ahooks";
import {getVenderInfo} from "@/modules/supplier/infra/api/vender";
import {PurchaseOrder} from "@/pages/PurchaseOrderList/data";

// 定义参数格式
export type CreateModalProps = {
  orderData: PurchaseOrder,
  venderId: string
};

const SupplierModal = (props: CreateModalProps) => {
  const { venderId,orderData } = props;

  const { data,loading } = useRequest(() => getVenderInfo({venderId}).then((res) => res.body));

  return (
    <>
      <Spin spinning={loading}>
        <Card>
          <Descriptions column={4}>
            <Descriptions.Item label="采购单供应商" labelStyle={{fontWeight:"bold",fontSize: 14}}>{orderData?.supplierName}</Descriptions.Item>
            <Descriptions.Item label="联系人">{orderData?.contrator}</Descriptions.Item>
            <Descriptions.Item label="联系电话">{orderData?.cellphone}</Descriptions.Item>
            <Descriptions.Item label="联系地址">{orderData?.detailAddress}</Descriptions.Item>
          </Descriptions>
          <Descriptions column={4}>
            <Descriptions.Item label="开户行">{orderData?.bankName}</Descriptions.Item>
            <Descriptions.Item label="开户人">{orderData?.accountName}</Descriptions.Item>
            <Descriptions.Item label="银行账号">{orderData?.account}</Descriptions.Item>
            <Descriptions.Item label="银行地址">--</Descriptions.Item>
          </Descriptions>
        </Card>
        <Card style={{marginTop:10}}>
          <Descriptions column={4}>
            <Descriptions.Item label="供应商" labelStyle={{fontWeight:"bold",fontSize: 14}}>{data?.venderName}</Descriptions.Item>
            <Descriptions.Item label="联系人">{data?.contractor}</Descriptions.Item>
            <Descriptions.Item label="联系电话">{data?.cellphone}</Descriptions.Item>
            <Descriptions.Item label="联系地址">{data?.detail}</Descriptions.Item>
          </Descriptions>
          <Descriptions column={4}>
            <Descriptions.Item label="开户行">{data?.bankName}</Descriptions.Item>
            <Descriptions.Item label="开户人">{data?.accountName}</Descriptions.Item>
            <Descriptions.Item label="银行账号">{data?.account}</Descriptions.Item>
            <Descriptions.Item label="银行地址">--</Descriptions.Item>
          </Descriptions>
        </Card>
      </Spin>
    </>
  );
};


export default SupplierModal;
