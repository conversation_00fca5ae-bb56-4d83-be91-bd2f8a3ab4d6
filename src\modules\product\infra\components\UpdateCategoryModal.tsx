import type { CustomModalFormProps } from '@/components/CustomModalForm';
import CustomModalForm from '@/components/CustomModalForm';
import type { CategoryData } from '@/modules/product/domain/category';
import type { CategoryDetailInfo } from '@/modules/product/infra/api/category';
import { queryCategoryInfo } from '@/modules/product/infra/api/category';
import type { CategoryCascaderActions } from '@/modules/product/infra/components/CategoryCascader';
import CategoryCascader from '@/modules/product/infra/components/CategoryCascader';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ProFormDigit, ProFormField, ProFormText } from '@ant-design/pro-form';
import { Form, Space, Tooltip } from 'antd';
import { omit } from 'lodash';
import { createRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';

export interface UpdateCategoryParams {
  parentCategoryId: string;
  name: string;
}

export type UpdateCategoryActions = {
  update: (row: CategoryData) => void;
  batchUpdate: (row: CategoryData) => void;
};

export type UpdateCategoryModalProps = {
  onUpdate?: (value: any) => Promise<boolean>;
  onBatchUpdate?: (value: any) => Promise<boolean>; // 是否批量操作
} & CustomModalFormProps<UpdateCategoryParams>;

const UpdateCategoryModal = forwardRef((props: UpdateCategoryModalProps, ref) => {
  const [isBatchUpdate, setIsBatchUpdate] = useState<boolean>(false);
  const [record, setRecord] = useState<CategoryData>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [categoryDetailInfo, setCategoryDetailInfo] = useState<CategoryDetailInfo>();
  const categoryActions = createRef<CategoryCascaderActions>();

  const actions: UpdateCategoryActions = {
    update: (row) => {
      setRecord(row);
      setIsBatchUpdate(false);
      setModalVisible(true);
    },
    batchUpdate: (row) => {
      setRecord(row);
      setIsBatchUpdate(true);
      setModalVisible(true);
    },
  };

  useImperativeHandle(ref, () => actions);

  useEffect(() => {
    if (modalVisible) {
      categoryActions.current?.reload();
    }
  }, [modalVisible]);

  useEffect(() => {
    if (record?.categoryId) {
      queryCategoryInfo(record.categoryId).then((res) => {
        const data = res.body;
        const formData = {
          ...record,
          parentCategoryIds: data.paths.slice(0, data.paths.length - 1).map((item) => item.name),
          parentCategoryId: data.categoryId,
          sameLevelSequence: data.sameLevelSequence,
        };
        setCategoryDetailInfo(data);
        form.setFieldsValue({
          ...formData,
        });
      });
    } else {
      form.resetFields();
    }
  }, [form, record, record?.categoryId, modalVisible]);

  const onFinish = async (values: any) => {
    if (isBatchUpdate) {
      return props.onBatchUpdate?.({
        ...values,
      });
    } else {
      return props.onUpdate?.({
        ...values,
        categoryId: record?.categoryId,
      });
    }
  };

  return (
    <CustomModalForm<UpdateCategoryParams>
      {...omit(props, ['onBatchUpdate', 'onUpdate'])}
      form={form}
      visible={modalVisible}
      onCancel={() => setModalVisible(false)}
      onVisibleChange={setModalVisible}
      onFinish={onFinish}
    >
      <ProFormField
        name="parentCategoryIds"
        label={
          <Space>
            父级分类
            <Tooltip title="此项不填表示一级">
              <QuestionCircleOutlined />
            </Tooltip>
          </Space>
        }
        transform={(values) => {
          const categoryId = values[values.length - 1] || 0;
          const initialParentCategoryNames = categoryDetailInfo?.paths
            .slice(0, categoryDetailInfo.paths.length - 1)
            .map((item) => item.name);
          if (initialParentCategoryNames?.includes(categoryId)) {
            return {
              parentCategoryId: categoryDetailInfo?.parentCategoryId,
            };
          }
          return {
            parentCategoryId: categoryId || '0',
          };
        }}
      >
        <CategoryCascader ref={categoryActions} changeOnSelect />
      </ProFormField>
      {isBatchUpdate ? null : (
        <>
          <ProFormText
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          />
          <ProFormDigit
            name="sameLevelSequence"
            label={
              <Space>
                排序
                <Tooltip title="暂限制最高9999">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
          />
        </>
      )}
    </CustomModalForm>
  );
});

export default UpdateCategoryModal;
