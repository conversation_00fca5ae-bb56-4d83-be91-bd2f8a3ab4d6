import CommonTable from "@/components/Common-UI/CommonTable";
import { useColumn, useSearch } from '@/components/Common-UI/CommonTable/hook';
import usePageQueryRequest from '@/hooks/usePageQueryRequest';
import { getSupplierGoodsList } from "@/modules/supplier/infra/api/vender";
import { batchCreatePurchaseGoods } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type { ActionType } from '@ant-design/pro-table';
import { Input, InputNumber, message, Modal, ModalProps } from "antd";
import { useMemo, useRef, useState, useEffect } from 'react';

// 定义参数格式
export type CreateModalProps = {
  orderId?: string;
  supplierId: string,
  onFinish: () => void;
} & ModalProps;
export interface DataType {
  goodsId?: string;
  goodsSku?: string;
  purchaseQuantity?: number;
  goodsRemark?: string;
}
export const useGoodsList = () => {
  const actionRef = useRef<ActionType>();
  const fetchList = usePageQueryRequest(getSupplierGoodsList);
  return {
    actionRef,
    fetchList,
  };
};

const PurchaseOrderGoodsCreateModal = (props: CreateModalProps) => {
  const { onFinish, supplierId, orderId, visible, ...rest } = props;
  const setColumn = useColumn<DataType>({ width: 150 });
  const setSearch = useSearch<DataType>();
  const { fetchList, actionRef } = useGoodsList();
  const [selectedRows, setSelectedRows] = useState<DataType[]>([]);
  // submit
  const onOk = async () => {
    if (!selectedRows.length) {
      message.error("请选择需要添加的商品");
      return;
    }
    if (selectedRows?.find(v => !v?.purchaseQuantity)) {
      message.error("采购数量不能为空");
      return;
    }
    const res = await batchCreatePurchaseGoods({ list: selectedRows?.map(v => ({ orderId: orderId, sku: v?.goodsSku, purchaseQuantity: v?.purchaseQuantity, goodsRemark: v?.goodsRemark })), purchaseOrderId: orderId });
    if (res.status.success) {
      message.success("添加成功");
      onFinish();
    }
  };
  const columns = useMemo(() => {
    return [
      setColumn('序号', 'index', {
        valueType: 'index',
        width: 48,
        fixed: 'left',
      }),
      setSearch('商品SKU', 'goodsSkus',{
        search: {
          transform: (value: any) => {
            if (!value) {
              return {}
            }
            const goodsSkus = value?.trim()?.split(/[,，\s]/)?.map((v: string) => v?.trim())
            return {
              goodsSkus,
            }
          },
        },
        fieldProps: {
          placeholder: '输入多个，中/英文逗号或空格隔开'
        }
      }),
      setColumn('商品SKU', 'goodsSku', {
        width: 200,
      }),
      setColumn(<span><span style={{ color: 'red' }}>*</span>采购数量</span>, 'purchaseQuantity', {
        width: 200,
        render: (_, row) => {
        const currentSeletedRow = selectedRows?.find(v => v?.goodsId === row?.goodsId);
        return <InputNumber defaultValue={currentSeletedRow?.purchaseQuantity || ''} onChange={(value: any) => setSelectedRows(pre => pre?.map(v => (v?.goodsId === row?.goodsId ? { ...v, purchaseQuantity: value  } : v)))} placeholder="采购数量" min={1} disabled={!currentSeletedRow} />
        }
      }),
      setColumn('商品备注', 'goodsRemark', {
        width: 200,
        render: (_, row) => {
          const currentSeletedRow = selectedRows?.find(v => v?.goodsId === row?.goodsId);
        return <Input value={currentSeletedRow?.goodsRemark || ''} onChange={(e: any) => setSelectedRows(pre => pre?.map(v => (v?.goodsId === row?.goodsId ? { ...v, goodsRemark: e?.target?.value  } : v)))} placeholder="商品备注" disabled={!currentSeletedRow} />
        }
      }),
    ];
  }, [setColumn, selectedRows]);
  
  useEffect(() => {
    if(!visible) return;
    setSelectedRows([]);
    // @ts-ignore
    actionRef?.current?.actionRef?.reset();
  },[visible])

  return <Modal
    title="添加商品"
    visible={visible}
    onOk={onOk}
    width={1200}
    bodyStyle={{
      minHeight: 500,
      maxHeight: '90vh',
    }}
    {...rest}

  >
    <CommonTable
      tableRef={actionRef as any}
      rowKey="goodsId"
      columns={columns}
      // @ts-ignore
      fetchRequest={(params, sort, filter) => {
        return fetchList({ ...params, supplierId  }, sort, filter);
      }}
      tableRest={{
        options: false,
        rowSelection: {
          // 弃用（查询翻页前面选择会变成undefined 官方BUG）
          // preserveSelectedRowKeys: true,
          selectedRowKeys: selectedRows?.map(v => v?.goodsId as string),
          onSelect: (record: DataType) => {
            if (selectedRows?.find(v => v?.goodsId === record?.goodsId)) {
              setSelectedRows(selectedRows?.filter(v => v?.goodsId !== record?.goodsId))
            } else {
              setSelectedRows([...selectedRows, record])
            }
          },
          onSelectAll: (flag, rows, changeRow) => {
            if (!flag) {
              setSelectedRows(selectedRows?.filter(v => !changeRow?.find(vv => vv?.goodsId === v?.goodsId)))
            } else {
              setSelectedRows([...selectedRows, ...changeRow])
            }
          },
        }
      }}
      scrollY={600}
    />
  </Modal>
}

export default PurchaseOrderGoodsCreateModal;
