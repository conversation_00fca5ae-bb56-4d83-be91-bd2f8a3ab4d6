import type {ProColumns} from '@ant-design/pro-table';
import React, {useState} from 'react';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  notification,
  Row,
  Select,
  Space,
  Table,
  Tag
} from "antd";
import Search from "antd/es/input/Search";
import {Option} from "antd/es/mentions";
import {ProFormSelect, ProFormText} from "@ant-design/pro-components";
import {scanArrival} from "@/modules/warehouse/infra/api/warehouse";
import {PurchaseOrder, PurchaseOrderGoods} from "@/pages/PurchaseOrderList/data";
import {batchTakeStock} from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import {getTagsPrintInfo, updateGoodsSize} from "@/modules/goods/infra/api/goods";
import QueryInStorageModal from "@/pages/warehouse/ScanArrival/components/QueryInStorageModal";
import {Access} from "@@/plugin-access/access";
import {FormOutlined} from "@ant-design/icons";
import {history, Link} from "umi";
import {getWarehouse} from "@/modules/purchasePlan/infra/api/purchasePlan";
import useRequest from "@ahooksjs/use-request";
import {reqByBody, reqByUrl} from "@/modules/common/infra/api/common";
import {ProFormTextArea} from "@ant-design/pro-form";
import {PurchaseExceptionReason} from "@/modules/warehouse/domain/warehouse";
export const ScanArrivalPage = () => {

  const [scanCodeType, setScanCodeType] = useState<string>("trackingNumber");
  const [WarehouseType, setWarehouseType] = useState<string>("广州仓");
  const [selectedRows, setSelectedRows] = useState<PurchaseOrderGoods[]>([]);
  const [dataSource, setDataSource] = useState<any>();
  const [scanCodeString, setScanCodeString] = useState<string>("");
  const [oldScanCodeString, setOldScanCodeString] = useState<string>("");
  const [queryInstorageParam, setQueryInstorageParam] = useState<any>();
  const [InStorageModal, setInStorageModal] = useState<boolean>(false);

  const {data} = useRequest(async () => {
    let params = await getWarehouse();
    console.log('获取到的下拉省分为：',params);
    let res = [];
    var body = params.body;
    for(var i in body){
      let temp = {};
      temp['label'] = body[i];
      temp['value'] = body[i];
      res.push(temp)
    }
    return res;
  });

  /**
   * 扫描请求
   * @param value
   */
  const scanCode = (code: string) => {
    const params = {
      'code': code,
      'scanArrivalType':scanCodeType,
      'warehouseType':WarehouseType
    };
    scanArrival(params).then((result) => {
      setScanCodeString("")
      setOldScanCodeString(code);
      if (result.status.success) {
        setDataSource(result.body)
      }
    });
  };

  /**
   * 添加商品尺寸信息
   */
  const [form] = Form.useForm();
  const [exceptionForm] = Form.useForm();
  const addGoodsSize=(orderGoods: PurchaseOrderGoods)=>{

    Modal.confirm({
      icon: false,
      width: 300,
      centered: true,
      content: (
        <Form labelCol={{flex: '50px'}} form={form}>
          <ProFormText  label="SKU" disabled={true} initialValue={orderGoods.sku} name="sku"/>
          <ProFormText  label="长" name="sizeLength" initialValue={orderGoods?.sizeLength}/>
          <ProFormText  label="宽" name="sizeWidth" initialValue={orderGoods?.sizeWidth}/>
          <ProFormText  label="高" name="sizeHeight" initialValue={orderGoods?.sizeHeight}/>
          <ProFormText  label="净重" name="netWeight" initialValue={orderGoods?.netWeight}/>
        </Form>
      ),
      onOk: function () {
        const params = {
          netWeight: form.getFieldValue('netWeight'),
          sizeHeight: form.getFieldValue('sizeHeight'),
          sizeWidth: form.getFieldValue('sizeWidth'),
          sizeLength: form.getFieldValue('sizeLength'),
          sku: orderGoods.sku,
        };

        updateGoodsSize(params).then((result) => {
          if (result.status.success) {
            notification.success({message: '修改成功'});
            scanCode(oldScanCodeString);
          }
        });

      },
    });

    form.resetFields();

  }

  /**
   * 批量盘点入库
   * @constructor
   */
  const InventoryEntry = (orderGoods: PurchaseOrderGoods)=>{

    if (selectedRows.length<=0 && orderGoods==null){
      message.error("请选择需要入库的数据");
      return null;
    }
    const list = orderGoods==null?selectedRows:[orderGoods];
    const obj={
      "orderGoodsList":list.map(item=>{
        item.receivingQuantity = item?.receivingQuantity==null ? item?.purchaseQuantity : item?.receivingQuantity;
        return item;
      })
    }
    batchTakeStock(obj).then((res)=>{
      if (res.status.success){
        message.success("入库成功");
        setDataSource(null)
        scanCode(oldScanCodeString);
      }
    })
  }


  const exceptionRemark =(order: PurchaseOrder)=>{
    exceptionForm.resetFields();
    Modal.confirm({
      icon: false,
      width: 400,
      centered: true,
      content: (
        <Form labelCol={{flex: '100px'}} form={exceptionForm}>
          <ProFormSelect label={"异常理由"} name={"exceptionReason"} valueEnum={PurchaseExceptionReason} />
          <ProFormTextArea name={"exceptionRemark"} label={"异常备注"}/>
        </Form>
      ),
      onOk: function () {
        const params = exceptionForm.getFieldsValue();
        params.purchaseOrderId = order?.id;
        reqByBody("/purchase-mgmt-biz/purchase-center/order/purchaseExceptionMark", params).then(res=>{
          if(res?.status.success){
            message.success("标记成功！");
          }
        })
      },
    });
  }

  /**
   * 批量打印标签
   * @constructor
   */
  const batchPrintTags = () => {
    if (selectedRows.length<=0){
      message.error("请选择需要打印的数据");
      return null;
    }
    reqByUrl("/purchase-mgmt-biz/pd/sku/getBatchTagsPrintInfo", {skus:selectedRows?.map(item=>{return item?.sku}).join(",")}).then(res=>{
      if(res?.status.success){
        const printList = selectedRows?.map(item=>{
          const positon = res?.body.find((pi: { sku: string; })=> pi.sku==item.sku);
          return {sku: item?.sku, orderCode: item?.orderCode,position: positon?.position || '', printQuantity: item.printQuantity || item.purchaseQuantity, goodsWeight: item?.netWeight, goodsName: item.name};
        })
        window.open("/purchasing-center/print/goodsTagsPrintBatch?params="+encodeURI(JSON.stringify(printList)))
      }
    })
  }

  /**
   * 取消入库
   */
  const queryInstorage=(orderGoods: PurchaseOrderGoods)=>{
    setQueryInstorageParam(orderGoods);
    setInStorageModal(true);
  }


  /**
   * 打印标签
   * @constructor
   */
  const printTags = (orderGoods: PurchaseOrderGoods)=>{
    getTagsPrintInfo({sku: orderGoods.sku}).then(res => {
      window.open("/purchasing-center/print/goodsTagsPrint?position="+(res?.body?.position || '')+"&sku="+orderGoods.sku+"&goodsWeight="+orderGoods.netWeight+"&goodsName="+encodeURIComponent(orderGoods.name)+"&orderCode="+orderGoods.orderCode+"&printQuantity="+(orderGoods.printQuantity?orderGoods.printQuantity:orderGoods.purchaseQuantity));
    })
  }

  /**
   * 采购单商品信息
   */
  const columnsSub: ProColumns<PurchaseOrderGoods, 'text'>[] = [
    {
      title: '图片',
      dataIndex: 'auditStatus',
      align:"center",
      width:"80px",
      render: (v, record) => {
        return  <Image src={record.skuImg} width={120}/>;
      }
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      align:"left",
      width:"50px",
      render: (v, record) => {
        return <span style={{ cursor: 'pointer', color: '#1890ff' }} onClick={() => history.push({pathname: `/product-manager/goods/code/sku-${record.sku}`, state: { title: record.sku } })}>{ record.sku }</span>
      }
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      align:"center",
      width:"150px",
      render: (v, record) => {
        return (
          <>
            <div style={{maxWidth: 250}}>{record?.name}</div>
          </>
        );
      }
    },
    {
      title: '长/宽/高/净重(cm/g)',
      dataIndex: 'sku',
      align:"left",
      width:"150px",
      render: (v, record) => {
        return (
          <>
            {record.sizeLength+"/"+record.sizeWidth+"/"+record.sizeHeight+"/"+record.netWeight}&nbsp;&nbsp;
            <FormOutlined
              style={{ color: 'blue' }}
              onClick={() => addGoodsSize(record)}
            />
          </>
        );
      }
    },
    {
      title: '采购数',
      dataIndex: 'purchaseQuantity',
      align:"center",
      width:"80px"
    },
    {
      title: '实收数量',
      dataIndex: 'receivingQuantity',
      align:"left",
      key: 'receivingQuantity',
      width:"80px",
      render: (v, record) => {
        return <Input defaultValue={record?.purchaseQuantity} onChange={(value)=>{
          record.receivingQuantity = Number(value.target.value);
        }}></Input>;
      }
    },
    {
      title: '已收数量',
      dataIndex: 'arrivalQuantity',
      align:"center",
      width:"80px",
      render: (v, record) => {
        return record?.arrivalQuantity?record.arrivalQuantity:0;
      }
    },
    {
      title: '商品备注',
      dataIndex: 'goodsRemark',
      align:"center",
      width:"200px"
    },
    {
      title: '打印数量',
      dataIndex: 'printQuantity',
      align:"left",
      width:"80px",
      render: (v, record) => {
        return <Input onChange={(value)=>{
          record.printQuantity = (value.target.value);
        }} defaultValue={record?.purchaseQuantity}></Input>;
      }
    },
    {
      title: '操作',
      dataIndex: 'auditUsername',
      width:"150px",
      render: (v, record) => {
        return (
          <Space>
            <Button type="primary" size={"small"} onClick={() => printTags(record)}  ghost style={{fontSize:12,borderRadius:5}}>
              打印
            </Button>
            <Button type="primary" size={"small"} onClick={() => queryInstorage(record)}  ghost style={{fontSize:12,borderRadius:5}}>
              取消入库
            </Button>
            <Button type="primary" size={"small"} onClick={() => InventoryEntry(record)}  ghost style={{fontSize:12,borderRadius:5,color:"green",borderColor:"green"}}>
              确认入库
            </Button>
          </Space>
        );
      }
    }
  ];

  const columns: ProColumns<PurchaseOrder, 'text'>[] = [
    {
      title: '/',
      dataIndex: '/',
      hideInSearch: true,
      align:"left",
      render: (v, record) => {
        return (
          <>
            <Card style={{padding:"0"}} bordered={true}>
              仓库:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.purchaseWarehouse ? record.purchaseWarehouse : '/'}</span>
              采购单号:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.orderCode}</span>
              物流单号:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.trackingNumber ? record.trackingNumber : '/'}</span>
              采购员:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.purchaseUsername ? record.purchaseUsername : '/'}</span>
              跟单员:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.merchandiserUsername ? record.merchandiserUsername : '/'}</span>
              供应商:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.supplierName ? record.supplierName : '/'}</span>
              采购备注:<span className="ant-form-text" style={{margin:10,fontWeight:"bold"}}>{record?.remark ? record.remark : '/'}</span>
              {record.financeAfterAmount != null && record.financeAfterAmount>0 ? <Tag color='red'>售后单</Tag> : null}
              <div><b style={{color: "red"}}>实收数量默认填充为采购数量，请注意修改实际收货数量！！！</b></div>
            </Card>
            <Space style={{margin: 10}}>
              <Button key="print" size={"small"} type="primary"  ghost={true} onClick={() => batchPrintTags()} >
                批量打印
              </Button>
              <Button key="level" size={"small"} type="primary" style={{borderColor: "orange", backgroundColor: "orange"}} onClick={() => exceptionRemark(record)} >
                异常标记
              </Button>
              <Button key="level" size={"small"} type="primary" onClick={() => InventoryEntry(null)} >
                批量入库
              </Button>
            </Space>
            <Table
              showHeader={true}
              rowSelection={{
                onChange: (_, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }}
              dataSource={record.purchaseOrderGoodsList}
              columns={columnsSub}
              rowKey="id"
              size={'small'}
              bordered={true}
              pagination = {false}
            />
          </>
        );
      }

    }
  ];
  return (
    <>
      <Card style={{padding:"0", marginBottom:10}}>
        <Input.Group compact >
          <Select  defaultValue="广州仓" style={{ width: 150}} options={data} onChange={setWarehouseType}/>
          <Select  defaultValue="trackingNumber" style={{ width: 150}}  onChange={setScanCodeType}>
            <Option value="orderCode">采购单号</Option>
            <Option value="trackingNumber">快递单号</Option>
            <Option value="sku">SKU</Option>
          </Select>
          <Search placeholder="扫描单号" value={scanCodeString}  onChange={(e)=>setScanCodeString(e.target.value)} onSearch={scanCode}  style={{ width: 500}} />
        </Input.Group>
      </Card>
      <Card style={{padding:"0", marginBottom:10}}>
        <Table
          dataSource={dataSource}
          columns={columns}
          search={false}
          size={'small'}
          rowKey="supplierName"
          bordered
          showHeader={false}
          pagination={false}
        />
      </Card>
      <Access accessible={InStorageModal}>
        <QueryInStorageModal visible={InStorageModal} onCancel={() => setInStorageModal(false)} setInStorage={function (){
          setInStorageModal(false);
          scanCode(oldScanCodeString);
        }}  orderData={queryInstorageParam} onFinish={function (){
          setInStorageModal(false);

        }}/>
      </Access>
    </>
  );
};
export default ScanArrivalPage;
