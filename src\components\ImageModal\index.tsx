import { Modal } from 'antd';
import { forwardRef, memo, useImperativeHandle, useState } from 'react';
type ConfirmProps = {
  reload: () => void;
}
const Index = forwardRef((props: ConfirmProps, ref) => {
  const [visible, setVisible] = useState(false);
  const [params, setParams] = useState<{ imgUrl?: string, title?: string }>({});
  useImperativeHandle(ref, () => ({
    open: (row?: { imgUrl?: string, title?: string }) => {
      setVisible(true);
      if(row?.imgUrl) setParams(row);
    }
  }));

  return (
    <Modal
      visible={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      width={600}
    >
      <div>
        <img width={'100%'} src={params?.imgUrl}  />
        <div style={{ textAlign: 'center', fontWeight: 'bold', color: 'red' }}>{ params?.title }</div>
      </div>
    </Modal>
  );
});

export default memo(Index);
