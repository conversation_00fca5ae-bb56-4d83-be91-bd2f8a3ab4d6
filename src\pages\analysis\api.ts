import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "@/../config/mallApiConfig";

// 1 采购单分页查询
export type QueryOrderListParams = {
  purchaseOrderCode?: string,
  purchaseOrderStatus?: keyof typeof PurchaseOrderStatus,
  purchaseOrderCodeList?: any[],
  gmtCreateStartTime?: number,
  gmtCreateEndTime?: number,
  supplierId?: string,
  purchaseWarehouse?: string,
  purchaseEntity?: string,
  purchaseAuditStatus?: 0 | 1,
  orderBy?: {
    filed: string,
    direction: number
  },
  pageCondition: {
    pageNum: number,
    pageSize: number
  }
}
export type OrderItem = {
  id: number,
  purchaseOrderCode: string,
  supplierName: string,
  purchaseEntity: string,
  purchaseWarehouse: string,
  warehouseDetails: string,
  totalAmount: string,
  purchaseOrderStatus: number,
  auditStatus: string,
  receivingInfo: string,
  estimatedTimeArrival: number,
  remark: string,
  purchaseUserName: string,
  gmtCreate: number
}
export const PurchaseOrderStatus = {
  0: "已创建",
  40: "在途中",
  45: "部分收货",
  55: "部分收货",
  50: "收货完成",
  70: "已作废",
}

export function queryOrderList(data: QueryOrderListParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<OrderItem[]>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/pageQuery',
    method: 'POST',
    data,
  },
  );
}

// 2.1 采购单作废
export function queryAbandon(data: Record<string, any>) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/abandon',
    method: 'POST',
    params: data,
  },
  );
}
// 2.2 采购单删除
export function queryDelete(data: { purchaseOrderId: number }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/delete',
    method: 'POST',
    params: data,
  },
  );
}
// 2.3 采购单完成
export function queryComplete(data: { purchaseOrderId: number }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/complete',
    method: 'POST',
    params: data,
  },
  );
}
// 2.4 采购单审核
export function queryAudit(data: { purchaseOrderId: number }) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/passAudit',
    method: 'POST',
    params: data,
  },
  );
}
// 2.5采购单导出
export function queryExport(data: QueryOrderListParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<void>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/export',
    method: 'POST',
    data,
  },
  );
}
// 2.6 采购平台供应商商品价格同步差异获取
export async function getPurchaseSupplierGoodsSyncPriceDiff() {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/getSyncPlatformSupplierGoodsPriceDiff',
    method: 'POST',
  },
  );
}
// 2.7 采购平台供应商商品价格同步差异导出
export async function exportPurchaseSupplierGoodsSyncPriceDiff() {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/exportPlatformSupplierGoodsPriceDiff',
    method: 'POST',
  },
  );
}
