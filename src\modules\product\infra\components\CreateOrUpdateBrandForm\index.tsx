import UploadImage from '@/components/UploadImage';
import { useBrand } from '@/modules/product/application/brand';
import type { Brand } from '@/modules/product/domain/brand';
import { initBrand } from '@/modules/product/domain/brand';
import ProForm, { ModalForm, ProFormField, ProFormText } from '@ant-design/pro-form';
import { forwardRef, useImperativeHandle, useState } from 'react';

export type CreateOrUpdateBrandActions = {
  create: () => void;
  editRow: (brand: Brand) => void;
};

const CreateOrUpdateBrandForm = forwardRef((props, ref) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [form] = ProForm.useForm();
  const { create, update } = useBrand();
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<Brand>();

  const actions: CreateOrUpdateBrandActions = {
    create: () => {
      setIsEdit(false);
      form.setFieldsValue(initBrand());
      setModalVisible(true);
    },
    editRow: (brand: Brand) => {
      setCurrentRow(brand);
      form.setFieldsValue(brand);
      setModalVisible(true);
      setIsEdit(true);
    },
  };

  useImperativeHandle(ref, (): CreateOrUpdateBrandActions => {
    return actions;
  });

  const handleSubmit = async (values: any) => {
    let success;
    if (!isEdit) {
      success = await create(values);
    } else {
      success = await update({
        brandId: currentRow?.brandId,
        ...values,
      });
    }
    return success;
  };

  return (
    <ModalForm
      title={isEdit ? '编辑品牌' : '新建品牌'}
      width={500}
      visible={modalVisible}
      onVisibleChange={setModalVisible}
      onFinish={handleSubmit}
      form={form}
      layout="horizontal"
      labelCol={{ span: 7 }}
    >
      <ProFormText
        label="品牌名称"
        name="name"
        rules={[{ required: true, message: '请输入品牌名称' }]}
      />
      <ProFormText label="品牌别名" name="nickname" />
      <ProFormField label="品牌logo" name="logo">
        <UploadImage limit={1} />
      </ProFormField>
      <ProFormText label="品牌所属国家/地区" name="country" />
    </ModalForm>
  );
});

export default CreateOrUpdateBrandForm;
