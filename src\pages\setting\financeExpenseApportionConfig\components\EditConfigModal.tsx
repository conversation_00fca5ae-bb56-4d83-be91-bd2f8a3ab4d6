import React, {useEffect, useState} from 'react';
import {Modal, ModalProps, Form, message} from 'antd';
import ProForm, {ProFormText, ProFormTextArea} from "@ant-design/pro-form";
import {ProFormSelect} from "@ant-design/pro-components";
import {getDeptTreeByEndpoint} from "@/modules/user-center/infra/organize";
import mallApiConfig from "../../../../../config/mallApiConfig";
import {apportionCompanyEnum, auditModelEnum} from "@/modules/financeExpense/domain/expense";
import {executeSave, reqByUrl} from "@/modules/common/infra/api/common";

// 定义参数格式
export type editProps = {
  onFinish: () => void;
  configId: string;
} & ModalProps;

export default (props: editProps) => {
  const [form] = ProForm.useForm();
  const {onFinish, configId, ...rest} = props;
  const [deptList, setDeptList] = useState<any>();


  useEffect( () => {
    form.resetFields();
    if(!deptList){
      getDeptTreeByEndpoint({endpoint: mallApiConfig.currTerminal,parentId: "0"}).then(res=>{
        let valueEmun = {};
        res.body?.map(item=>{
          valueEmun[item?.deptId] = item?.name;
        });
        setDeptList(valueEmun);
      })
    }
    if (configId) {
      reqByUrl('/sales-mgmt-biz/sales-center/setting/expense-apportion/getApportionById',{id: configId}).then((res) => {
        if (res.status.success) {
          form.setFieldsValue(res?.body);
        }
      })
    }
    },
    [configId]
  );

  const onSubmit = () => {
    form.validateFields().then(()=>{
      const params = form.getFieldsValue();
      executeSave('/sales-mgmt-biz/sales-center/setting/expense-apportion/saveApportionConfig', params).then((result) => {
        if (result.status.success) {
          message.success('操作成功');
          onFinish();
        }
      });
    })
  }

  return (
    <>
      <Modal {...rest} title="报销分摊配置" className={'globalEnterKeySubmit'} onOk={() => onSubmit()} destroyOnClose={true}>
        <Form labelCol={{flex: '120px'}} form={form}>
          <ProFormText name="id" hidden={true}/>
          <ProFormSelect width={"md"} label="单据类型" name="auditModel" rules={[{required: true}]} valueEnum={
            auditModelEnum
          }/>
          <ProFormSelect width={"md"} label="公司" name="companyName" rules={[{required: true}]} valueEnum={
            apportionCompanyEnum
          }/>
          <ProFormSelect width={"md"} showSearch={true} label="小组" rules={[{required: true}]} name="organizationId" valueEnum={deptList}/>
          <ProFormText width={"md"} label="比例" name="rate" rules={[{required: true}]}/>
          <ProFormTextArea width={"md"} label="备注" name="remark"/>
        </Form>
      </Modal>
    </>
  );
};
