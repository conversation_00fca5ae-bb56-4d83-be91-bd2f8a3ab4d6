import {
  Modal,
  Col,
  Row,
  Radio,
  Table,
  Alert,
  Button,
  Space,
  Select,
  message,
  Checkbox,
  Card,
  Slider,
  Divider,
} from 'antd';
import { useState, useEffect } from 'react';
import type { PurchaseOrder } from '@/pages/PurchaseOrderList/data';
import type { ProColumns } from '@ant-design/pro-table';
import type { CreateModalProps } from './ChangeType';
import ChangeTypeMoal from './ChangeType';
import {
  settlementSearchType,
  SettleTypeMap,
  creditSearchType,
  crossSearchType,
  cardTitleMap,
  useAdvancedPayment,
  ApplyTypeOptions,
  payTypeOptions,
  settlementTypeOptions,
} from './api';
import styles from './index.less';

export type Props = {
  open?: boolean;
  queryParams?: any;
  onCancel?: () => void;
};

const Index = (props: Props) => {
  const { open, onCancel, queryParams } = props;
  const {
    tableData,
    setTableData,
    getTableData,
    loading,
    saveConfig,
    getCreditBalanceDetail,
    creditBalance,
  } = useAdvancedPayment();
  const [changeTypeProps, setChangeTypeProps] = useState<CreateModalProps>({ open: false });
  const [selectRows, setSelectedRows] = useState<PurchaseOrder[]>([]);
  const [params, setParams] = useState({});
  const [platformShppingFeeCheck, setPlatformShppingFeeCheck] = useState<boolean>(true);

  const onOk = () => {
    const config = tableData?.filter(vv => selectRows?.find(vvv => vvv?.id === vv?.id))?.map((v) => ({
      orderId: v?.id,
      payType: v?.payType,
      payStatus: v?.payStatus,
      platformTradeType: v?.platformTradeType,
      platformShppingFeeCheck: platformShppingFeeCheck,
      // 必传，不然后端校验报错
      accountName: '',
      bankName: '',
      account: '',
    }));
    if (!config || !config?.length) {
      onCancel && onCancel();
      return;
    }
    if (config?.find((v) => !v?.payType || !v?.payStatus || !v?.platformTradeType)) {
      message.error('勾选订单中存在未设置完成的订单，请确认！');
      return;
    }
    // @ts-ignore
    saveConfig(config, onCancel);
  };

  const onChangeValue = (key: string, value: string, index: number) => {
    const newTableData = tableData?.map((v, vi) => {
      if (vi === index) {
        return {
          ...v,
          [key]: value,
        };
      } else {
        return v;
      }
    });
    setTableData(newTableData);
  };

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '采购单号',
      dataIndex: 'orderCode',
      key: 'orderCode',
      fixed: 'left',
      width: 200,
      render: (v, record) => (
        <div>
          <div>采购单号：{record?.orderCode}</div>
          <div>采购账号：{record?.platformAccount || '--'}</div>
        </div>
      ),
    },
    {
      title: '采购单付款金额',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 120,
      // render: (v, record) => `${record?.totalPrice} CNY`,
      render: (v, record) => (
        <>
          <div style={{ fontSize: "12px" }}> 平台总金额：{record?.platformOrderAmount == null ? '--' : record.platformOrderAmount?.toFixed(2)}</div>
          <div style={{ fontSize: "12px" }}>本地总金额：{record?.shippingFee == null && record?.totalPrice == null ? '--' : (record.shippingFee + record.totalPrice + record.applyRefundAmount - record.applyPromotionAmount)?.toFixed(2)}</div>
          <div style={{ fontSize: "12px" }}>商品金额：{record?.totalPrice == null ? '--' : record.totalPrice?.toFixed(2)}</div>
        </>
      )
    },
    {
      title: '是否支持诚意赊',
      dataIndex: 'supportCreditBuy',
      key: 'supportCreditBuy',
      width: 120,
      render: (v, record) => (record?.supportCreditBuy + '' === '1' ? '是' : '否'),
    },
    {
      title: '是否支持跨境宝',
      dataIndex: 'isCross',
      key: 'isCross',
      width: 120,
      render: (v, record) => (record?.isCross + '' === '1' ? '是' : '否'),
    },
    {
      title: '设置结算方式',
      dataIndex: 'settleType',
      key: 'settleType',
      width: 120,
      render: (v, record) => SettleTypeMap[record?.settleType],
    },
    {
      title: '申请类型',
      dataIndex: 'payStatus',
      key: 'payStatus',
      width: 180,
      render: (v, record, i) => (
        <Select
          options={ApplyTypeOptions}
          style={{ width: '100%' }}
          value={v}
          onChange={(value) => onChangeValue('payStatus', value, i)}
        />
      ),
    },
    {
      title: '设置支付方式',
      dataIndex: 'payType',
      key: 'payType',
      width: 180,
      render: (v, record, i) => (
        <Select
          options={
            (record?.isCross + '') !== '1'
              ? payTypeOptions?.filter((option: any) => option?.value !== '5')
              : payTypeOptions
          }
          style={{ width: '100%' }}
          value={v}
          onChange={(value) => onChangeValue('payType', value, i)}
        />
      ),
    },
    {
      title: '设置1688平台结算方式',
      dataIndex: 'platformTradeType',
      key: 'platformTradeType',
      width: 180,
      render: (v, record, i) => (
        <Select
          disabled={record?.settleType === 'ACCOUNT_PERIOD'}
          options={settlementTypeOptions}
          style={{ width: '100%' }}
          value={v}
          onChange={(value) => onChangeValue('platformTradeType', value, i)}
        />
      ),
    },
    {
      title: '是否设置完成',
      dataIndex: 'finished',
      key: 'finished',
      fixed: 'right',
      width: 120,
      render: (v, record) => {
        if (record?.payStatus && record?.payType && record?.platformTradeType)
          return <span style={{ color: 'green' }}>已完成</span>;
        return <span style={{ color: 'red' }}>未完成</span>;
      },
    },
  ];

  const getTotal = (arr: PurchaseOrder[]) => {
    let total = 0;
    if (!arr || arr?.length === 0) return total;
    arr?.forEach((v) => (total += v?.totalPrice));
    return total;
  };
  // 融合/紫光诚意赊Total
  const getCreditTotalByType = (arr: PurchaseOrder[], key: string) => {
    let total = 0;
    if (!arr || arr?.length === 0) return total;
    arr?.forEach((v) => {
      if (v?.platformAccount === key && tableData?.find(vv => vv?.orderCode === v?.orderCode)?.platformTradeType === 'credit') {
        total += v?.totalPrice;
      }
    });
    return total;
  };

  useEffect(() => {
    if (!open) return;
    setParams({ ...queryParams, orderStatus: [20] });
    getCreditBalanceDetail();
  }, [queryParams, open]);

  useEffect(() => {
    if (JSON.stringify(params) === '{}') return;
    getTableData({ ...params });
    setSelectedRows([]);
  }, [params]);

  return (
    <Modal
      title={`申请付款（高级）`}
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      width={1200}
      bodyStyle={{
        minHeight: 500,
        maxHeight: '90vh',
      }}
    >
      <div className={styles.wrapper}>
        <div className={styles.top}>
          <div className={styles.search}>
            <Row>
              <Col span={8}>供应商结算类型:</Col>
              <Col span={16}>
                <Radio.Group
                  options={settlementSearchType}
                  onChange={(e) => {
                    setParams({
                      ...params,
                      settleType: e.target.value === 'ALL' ? '' : e.target.value,
                    });
                  }}
                  value={params?.settleType || 'ALL'}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Col>
            </Row>
            <Row style={{ margin: '12px 0' }}>
              <Col span={8}>供应商是否支持诚意赊：</Col>
              <Col span={16}>
                <Radio.Group
                  options={creditSearchType}
                  value={params?.supportCreditBuy || 'ALL'}
                  optionType="button"
                  buttonStyle="solid"
                  onChange={(e) => {
                    setParams({
                      ...params,
                      supportCreditBuy: e.target.value === 'ALL' ? '' : e.target.value,
                    });
                  }}
                />
              </Col>
            </Row>
            <Row>
              <Col span={8}>供应商是否支持跨境宝：</Col>
              <Col span={16}>
                <Radio.Group
                  options={crossSearchType}
                  onChange={(e) => {
                    setParams({
                      ...params,
                      isCross: e.target.value === 'ALL' ? '' : e.target.value,
                    });
                  }}
                  value={params?.isCross || 'ALL'}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Col>
            </Row>
          </div>
          <div className={styles.detail}>
            {creditBalance?.map((v) => {
              const balance = v?.residualAmount - getCreditTotalByType(selectRows, v?.accountName);
              return (
                <Card
                  key={v?.accountName}
                  title={v?.accountName}
                  bordered={false}
                  style={{ width: 300 }}
                >
                  <div className={styles.item}>总额度：{v?.totalAmount} CNY</div>
                  <div className={styles.item}>已消费：{v?.usedAmount} CNY</div>
                  <div className={styles.item}>冻结：{v?.freezeAmount} CNY</div>
                  <div className={styles.item}>可用：{v?.residualAmount} CNY</div>
                  <Divider type="horizontal" />
                  <div className={styles.item}>
                    当前勾选：{getCreditTotalByType(selectRows, v?.accountName)} CNY
                  </div>
                  <div className={styles.item} style={balance <= 0 ? { color: 'red' } : {}}>
                    余额：{balance} CNY
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.btns}>
            <div className={styles.left}>
              {/* @ts-ignore */}
              平台运费校验{' '}
              <Checkbox
                checked={platformShppingFeeCheck}
                onChange={(e) => setPlatformShppingFeeCheck(e.target?.checked)}
              />
            </div>
            <div className={styles.right}>
              <Space>
                <Button
                  type="primary"
                  disabled={!selectRows?.length}
                  onClick={() =>
                    setChangeTypeProps({
                      open: true,
                      options: ApplyTypeOptions,
                      name: 'payStatus',
                      title: '批量设置申请类型',
                    })
                  }
                >
                  批量设置申请类型
                </Button>
                <Button
                  type="primary"
                  disabled={!selectRows?.length}
                  onClick={() =>
                    setChangeTypeProps({
                      open: true,
                      options: payTypeOptions,
                      name: 'payType',
                      title: '批量设置支付方式',
                      tips: '注意：不支持跨境宝支付的订单，支付方式无法修改为跨境宝。',
                    })
                  }
                >
                  批量设置支付方式
                </Button>
                <Button
                  type="primary"
                  disabled={!selectRows?.length}
                  onClick={() =>
                    setChangeTypeProps({
                      open: true,
                      options: settlementTypeOptions,
                      name: 'platformTradeType',
                      title: '批量设置1688平台结算方式',
                      tips: '注意：结算方式为供应商账期的订单，不允许修改结算方式。',
                    })
                  }
                >
                  批量设置1688平台结算方式
                </Button>
              </Space>
            </div>
          </div>
          <div className={styles.info}>
            <Alert
              message={`已选择${selectRows?.length}项，已选采购总金额：${getTotal(selectRows)}`}
              type="info"
              showIcon
            />
          </div>
          <div className={styles.tableBox}>
            <Table
              rowKey={'orderCode'}
              dataSource={tableData}
              columns={columns}
              pagination={false}
              rowSelection={{
                selectedRowKeys: selectRows?.map((v) => v?.orderCode),
                onChange: (_, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }}
              scroll={{ y: 250, x: 1300 }}
              loading={loading}
            />
          </div>
        </div>
      </div>
      <ChangeTypeMoal
        onFinish={(values) => {
          setTableData(
            tableData?.map((v) => {
              if (selectRows?.find((vv) => vv?.orderCode === v?.orderCode)) {
                // 支付方式，不支持跨境宝的订单不允许设置
                if (
                  Object.keys(values).includes('payType') &&
                  values['payType'] === '5' &&
                  v?.isCross + '' !== '1'
                ) {
                  return {
                    ...v,
                  };
                }
                // 结算方式批量设置,账期类型订单不支持修改的
                if (
                  Object.keys(values).includes('platformTradeType') &&
                  v?.settleType === 'ACCOUNT_PERIOD'
                ) {
                  return {
                    ...v,
                  };
                }
                return {
                  ...v,
                  ...values,
                };
              } else {
                return v;
              }
            }),
          );
          setChangeTypeProps({ open: false });
        }}
        onCancel={() => setChangeTypeProps({ open: false })}
        {...changeTypeProps}
      />
    </Modal>
  );
};
export default Index;
