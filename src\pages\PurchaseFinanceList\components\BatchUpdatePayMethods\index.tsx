import { payTypeEnum } from "@/modules/purchaseOrder/domain/purchaseOrder";
import { batchChangePayment } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, Modal, Select, Table, message } from 'antd';
import { ColumnProps } from "antd/es/table";
import moment from 'moment';
import { forwardRef, useImperativeHandle, useState } from 'react';
import ChangePayMethodModal from './ChangePayMethodModal';
import styles from './index.less';

type ModalProps = {
  reload?: any;
}

type ChangeModalProps = {
  open: boolean;
  title?: string;
  onFinish?: () => void; 
  onCancel?: () => void 
}

const Index = forwardRef((props: ModalProps, ref) => {
  const { reload } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [orderData, setOrderData] = useState<PurchaseOrder[]>([]);
  const [changeModalProps, setChangeModalProps] = useState<ChangeModalProps>({ open: false });
  const onCancel = async () => {
    setOpen(false);
    setLoading(false);
  };

  const onOk = async () => {
    const res = await batchChangePayment({ purchaseOderUpdatePayTypeRequestList : orderData?.map(v => ({ id: v?.id, payType: v?.payType })) });
    setLoading(false);
    if (res.status.success) {
      message.success('修改成功');
      setOpen(false);
      reload();
    }
  };
  useImperativeHandle(ref, () => ({
    open: async (params: { orderData: PurchaseOrder[] }) => {
      setOpen(true);
      setOrderData(params?.orderData);
    },
    close: () => onCancel(),
  }));

  const columns: ColumnProps<PurchaseOrder>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.title}</span>;
      }
    },
    {
      title: '采购/平台单号',
      dataIndex: 'orderCode',
      width: 200,
      render: (v, record) => {
        return <><div>{record?.orderCode}</div><div>{record?.platformOrderCode}</div></>;
      }
    },
    {
      title: '采购员/跟单员',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.purchaseUsername}/{record?.merchandiserUsername}</span>;
      }
    },
    {
      title: '收款人',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span>{record?.accountName || '--'}</span>;
      }
    },
    {
      title: '账期审核状态',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.periodStatus || '--'}</span>;
      }
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      render: (v, record) => {
        const options = Object.entries(payTypeEnum).map(([key, value]) => ({
          value: key,
          label: value
        }));
        // @ts-ignore
        return <Select options={options} value={record?.payType} style={{ width: '100%' }} onChange={(value) => setOrderData(orderData?.map((item: PurchaseOrder) => ({ ...item, payType: item?.id === record?.id ? value : item?.payType  })))}/>
      }
    },
    {
      title: '申请结算金额',
      dataIndex: 'periodAmount',
      render: (v, record) => {
        return <>
          <div>
            {record?.periodAmount?.toFixed(2) || 0.00}
            {record?.periodAmount?.toFixed(2) != (record?.amountPayable - record?.afterAmount)?.toFixed(2) ? (
              // @ts-ignore
              <ExclamationCircleOutlined style={{ color: 'red', fontSize: 10, float: "right" }} />
            ) : null}
          </div>
        </>;
      }
    },
    {
      title: '应付金额',
      width: 300,
      dataIndex: 'amountPayable',
      render: (v, record) => {
        return <><span style={{ fontSize: 12 }}>{(record?.amountPayable - record?.afterAmount)?.toFixed(2)}(账期应付) =  {record?.amountPayable?.toFixed(2) || 0.00}(下单金额) - {record?.afterAmount?.toFixed(2) || 0.00}(售后金额)</span></>
      }
    },
    {
      title: '申请结算时间',
      dataIndex: 'periodTime',
      render: (v, record) => {
        return record?.periodTime ? moment(record?.periodTime)?.format("YYYY-MM-DD HH:mm") : '--';
      }
    }
  ];


  return (
    <Modal
      title="批量修改支付方式"
      visible={open}
      onCancel={onCancel}
      confirmLoading={loading}
      width="80%"
      onOk={onOk}
    >
      <div>
        <div className={styles.btnsWrapper}>
          {/* @ts-ignore */}
          <Button
            type="primary"
            onClick={() =>{
              setChangeModalProps({
                open: true,
                title: '批量设置支付方式',
              })
            }}
          >
            批量修改支付方式
          </Button>
        </div>
        {/* @ts-ignore */}
        <Table
          dataSource={orderData}
          scroll={{ y: 550 }}
          columns={columns as any}
          rowKey="id"
          pagination={false}
        />
      </div>
      <ChangePayMethodModal
        onFinish={(values) => {
          setOrderData(orderData?.map(v => (
            {
              ...v,
              ...values,
            }
          )));
          setChangeModalProps({ open: false })
        }}
        onCancel={() => setChangeModalProps({ open: false })}
        {...changeModalProps}
      />
    </Modal>
  )
})
export default Index;