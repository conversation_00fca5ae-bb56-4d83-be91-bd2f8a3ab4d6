import getServiceToken from "@/hooks/getServiceToken";
import { useContext, useState } from "react";
import type { PurchasePlanListItem } from "../data";

export type SelectedTableRowType = PurchasePlanListItem;

export const usePurchasePlanSelectStore = () => {
  const [selectedTableRowsState, setSelectedTableRowsState] = useState<SelectedTableRowType[]>([]);

  return {
    selectedTableRowsState,
    setSelectedTableRowsState,
  }
}

export const PurchasePlanSelectContext = getServiceToken(usePurchasePlanSelectStore);

export const usePurchasePlanSelectContext = () => useContext(PurchasePlanSelectContext);
