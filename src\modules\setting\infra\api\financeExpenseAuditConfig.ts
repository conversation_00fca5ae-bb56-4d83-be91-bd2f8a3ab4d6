import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {FinanceExpenseAuditConfig} from "@/pages/setting/financeExpenseAuditConfig/data";

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;


//
export async function pageQueryExpenseAudit(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<FinanceExpenseAuditConfig[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/setting/finance/pageQuery',
    data,
  });
}


export async function getExpenseAuditConfigDetail(configId?: string) {
  return mallRequest<API.ApiBaseResult<FinanceExpenseAuditConfig>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'POST',
    requestPath:'/sales-mgmt-biz/sales-center/setting/finance/queryById',
    params: {
      id: configId,
    },
  });
}

export async function seveExpenseAuditConfig(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'POST',
    requestPath:'/sales-mgmt-biz/sales-center/setting/finance/saveAuditConfig',
    data
  });
}
