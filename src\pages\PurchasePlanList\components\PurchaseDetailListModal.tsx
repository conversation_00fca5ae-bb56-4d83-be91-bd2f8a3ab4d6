import type { ModalProps } from 'antd';
import { Modal, Space, Table } from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import type { PurchasePlan } from '@/pages/PurchasePlanList/data';
import React, { useEffect, useRef } from 'react';
import { PurchaseDetailPageQuery } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import { OrderStatusEnum } from "@/modules/purchaseOrder/domain/purchaseOrder";
import { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import { ProTable } from '@ant-design/pro-components';
import { useRequestTable } from "@/hooks/useRequestTable";

// 定义参数格式
export type PurchaseDetailListModalProps = {
  data: any;
  onFinish: (values: PurchasePlan) => void;
} & ModalProps;

const PurchaseDetailListModal = (props: PurchaseDetailListModalProps) => {
  const { onFinish, data, ...rest } = props;
  const { fetchList, actionRef } = useRequestTable((params) => {
    return PurchaseDetailPageQuery({
      ...params,
      goodsKey: "sku",
      goodsValue: data?.sku,
      // orderStatus: activeStatusKey === '' ? undefined : activeStatusKey,
    });
  });
  // 当searchPlanLogParams发生变动的时候，重新发起请求
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [data?.sku]);

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '采购单号',
      width: 100,
      align: 'left',
      dataIndex: 'orderCode',
    },
    {
      title: '仓库',
      width: 80,
      align: 'left',
      dataIndex: 'purchaseWarehouse',
    },
    {
      title: '采购状态',
      width: 100,
      align: 'left',
      dataIndex: 'orderStatus',
      render: (v, record) => {
        const orderStatus = OrderStatusEnum[record?.orderStatus];

        const options = [
          orderStatus != undefined ? (
            <span style={{ fontSize: "12px" }}>
              <span className="ant-badge-status-dot ant-badge-status-success" />
              <span className="ant-badge-status-text" style={{ fontSize: "12px" }}>{orderStatus}</span>
            </span>
          ) : null
        ];
        return <Space direction="vertical">{options}</Space>;
      }
    },
    {
      title: 'SKU',
      width: 100,
      align: 'left',
      dataIndex: 'sku',
      render: (v, record) => {
        return data.sku;
      }
    },
    {
      title: '采购数量',
      width: 60,
      align: 'left',
      dataIndex: 'purchaseQuantity',
      render: (v, record) => {
        let purchaseQuantity = 0;
        for (const index in record.purchaseOrderGoodsList) {
          if (record.purchaseOrderGoodsList[index].sku == data.sku) {
            purchaseQuantity += record.purchaseOrderGoodsList[index].purchaseQuantity;
          }
        }
        return purchaseQuantity;
      }
    },
    {
      title: '已收数量',
      width: 60,
      align: 'left',
      dataIndex: 'arrivalQuantity',
      render: (v, record) => {
        let arrivalQuantity = 0;
        for (const index in record.purchaseOrderGoodsList) {
          if (record.purchaseOrderGoodsList[index].sku == data.sku) {
            arrivalQuantity += record.purchaseOrderGoodsList[index].arrivalQuantity;
          }
        }
        return arrivalQuantity;
      }
    },
    {
      title: '供应商',
      width: 150,
      align: 'left',
      dataIndex: 'supplierName',
    },
    {
      title: '采购价',
      width: 60,
      align: 'left',
      dataIndex: 'currentPurchasePrice',
      render: (v, record) => {
        let currentPurchasePrice = '';
        for (const index in record.purchaseOrderGoodsList) {
          if (record.purchaseOrderGoodsList[index].sku == data.sku) {
            currentPurchasePrice = record.purchaseOrderGoodsList[index].currentPurchasePrice;
          }
        }
        return currentPurchasePrice;
      }
    },
    {
      title: '采购员',
      width: 60,
      align: 'left',
      dataIndex: 'purchaseUsername',
    },
    {
      title: '跟单员',
      width: 60,
      align: 'left',
      dataIndex: 'merchandiserUsername',
    },
    {
      title: '下单时间',
      align: 'left',
      width: 150,
      dataIndex: 'platformOrderTime',
      valueType: 'dateTime',
    },
  ];

  return (
    <Modal {...rest} title="采购SKU明细" width="80%" onOk={onFinish}>
      <ProTable<any>
        search={false}
        options={false}
        scroll={{ y: 550 }}
        size="small"
        actionRef={actionRef}
        //切换页面不再刷新
        revalidateOnFocus={false}
        request={fetchList}
        columns={columns}
        rowKey="id"
        bordered
      />
    </Modal>
  );
};

export default PurchaseDetailListModal;
