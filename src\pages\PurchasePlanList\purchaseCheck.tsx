import React, { useEffect, useState } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import CustomPage from "@/components/CustomPage";
import CreatePurchasePlanModal from './components/CreatePurchasePlanModal';
import {Badge, Button, message, Table, Tag} from "antd";
import { useParams } from "umi";
import type {PurchasePlanGoods, SalesQuanityListItem} from "@/pages/PurchasePlanList/data";
import { useRequestTable } from "@/hooks/useRequestTable";
import type { updatePlanGoodsStatus } from "@/modules/purchasePlan/infra/api/purchasePlan";
import { pageQueryPlanDetail, updatePlanStatus } from "@/modules/purchasePlan/infra/api/purchasePlan";
import type { TabsItem } from "@/components/TableTabs";
import TableTabs from "@/components/TableTabs";
import { ArrowUpOutlined, FormOutlined, SnippetsTwoTone } from "@ant-design/icons";
import {copyText} from "@/utils/comUtil";



const TableList: React.FC = () => {
  const { id: orderId } = useParams<{ id: string }>();
  const { fetchList, actionRef } = useRequestTable((params) => {
    return pageQueryPlanDetail({
      ...params,
      purchasePlanId: orderId,
    });
  });

  const [selectedRowsState, setSelectedRows] = useState<PurchasePlanGoods[]>([]);
  const [activeStatusKey, setActiveStatusKey] = useState<string>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [auditStatus, setStatus] = useState<updatePlanGoodsStatus>()




  const audit = (values: PurchasePlanGoods[], status: string) => {
    const obj = {
      "ids": values.map(item => item.id) as unknown as number[],
      "status": status,
    }
    setStatus(obj);


    updatePlanStatus(auditStatus).then((result) => {
      if (result.status.success) {
        actionRef.current?.reloadAndRest?.();
      }
    });


  }
  /**
   * 各销售仓库需求table
   */
  const salesQuanitycolumns: ProColumns<SalesQuanityListItem>[] = [
    {
      title: "销售仓库",
      dataIndex: 'sku',
    },
    {
      title: "需求",
      dataIndex: 'lastPurchasePrice',
    },
    {
      title: "状态",
      dataIndex: 'currentPurchasePrice',
    }
  ];
  const columns: ProColumns<PurchasePlanGoods>[] = [
    {
      title: '销售仓库/需求数量',
      dataIndex: 'salesWarehouse',
      align: 'center',
      width: 230,
      render: (v, record) => {
        return (
          <>
            {/*<Table*/}
            {/*  dataSource={record.purchasePrice}*/}
            {/*  columns={salesQuanitycolumns}*/}
            {/*  rowKey="id"*/}
            {/*  pagination = {false}*/}
            {/*/>*/}
            <div className="showSummary">
              <table width="100%">
                <thead>
                <tr>
                  <th align="center">
                    销售仓库
                  </th>
                  <th align="center">
                    需求
                  </th>
                  <th align="center">
                    状态
                  </th>
                  <th align="center">
                    操作
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr>
                  <td align="center">广州直发</td>
                  <td align="center">5</td>
                  <td align="center">
                    <Tag color='red'>作废</Tag>
                  </td>
                  <td align="center">
                    <Tag color='blue'>审核</Tag>
                  </td>
                </tr>
                <tr>
                  <td align="center">AMMSUK</td>
                  <td align="center">2</td>
                  <td align="center">
                    <Tag color='green'>通过</Tag>
                  </td>
                  <td align="center">
                    <Tag color='blue'>审核</Tag>
                  </td>
                </tr>
                <tr>
                  <td align="center">飞鸟英国仓</td>
                  <td align="center">10</td>
                  <td align="center">
                    <Tag color='blue'>挂起</Tag>
                  </td>
                  <td align="center">
                    <Tag color='blue'>审核</Tag>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </>
        );
      },
    },
    {
      title: '商品信息',
      dataIndex: 'sku',
      align: "center",
      width: 90,
      render: (v, record) => {
        return (
          <>
            <img src='https://newnary-test-1300176682.cos.ap-guangzhou.myqcloud.com/TENANT3924117688555169382400/common/space/20220821/22fa1e454000ed929b462cb333551b62.jpg' width='80' />
            <div> {record.sku}<SnippetsTwoTone onClick={() => copyText(record.sku)}/></div>
          </>
        );
      },
    },
    {
      title: '备货信息',
      dataIndex: 'sku',
      // align:'center',
      width: 400,
      render: (v, record) => {
        return (
          <>
            <div>标题：S-通用-UK-68800mAh600A汽车应急启动电源 英规{record.goodsRemark} <Tag color='red'>加急</Tag></div>
            <div>数量：17 <FormOutlined color="success" /></div>
            <div>上次采购价：{record.purchasePrice?.toFixed(2)}<ArrowUpOutlined /></div>
            <div>当前采购价：{record.purchasePrice?.toFixed(2)}</div>
            <div>创建时间：2022-08-22</div>
            <div>修改时间：2022-08-22</div>
          </>
        );
      },
    },
    {
      title: '库存/销量/缺货',
      dataIndex: 'purchaseQuantity',
      align: "center",
      hideInSearch: true,
      render: (v, record) => {
        return (
          <>
            <div className="showSummary" data-rel="209685" batch_code="">    <table width="100%">
              <thead>
                <tr>
                  <th>
                    仓库
                  </th>
                  <th>
                    生命周期</th>
                  <th >
                    通途可用库存</th>
                  <th>
                    通途在途库存</th>
                  <th>
                    采购/转仓</th>
                  <th>
                    通途近7天销量</th>
                  <th>
                    通途15天销量</th>
                  <th>
                    通途30天销量</th>
                  <th>
                    通途未配货</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td align="center">广州仓&amp;DG</td>
                  <td align="center">新品</td>
                  <td align="center">14</td>
                  <td align="center">0</td>
                  <td align="center">
                    0                            </td>
                  <td align="center">2</td>
                  <td align="center">2</td>
                  <td align="center">5</td>
                  <td align="center">0</td>
                </tr>
                <tr>
                  <td align="center">广州2仓</td>
                  <td align="center">新品</td>
                  <td align="center">0</td>
                  <td align="center">0</td>
                  <td align="center">
                    /
                  </td>
                  <td align="center">0</td>
                  <td align="center">0</td>
                  <td align="center">0</td>
                  <td align="center">0</td>
                </tr>
                <tr>
                  <td align="center">海外仓</td>
                  <td align="center">新品</td>
                  <td align="center">13</td>
                  <td align="center">3</td>
                  <td align="center">
                    /
                  </td>
                  <td align="center">1</td>
                  <td align="center">2</td>
                  <td align="center">2</td>
                  <td align="center">0</td>
                </tr>
                <tr>
                  <td align="center">通途FBA</td>
                  <td align="center">新品</td>
                  <td align="center">0</td>
                  <td align="center">0</td>
                  <td align="center">
                    /
                  </td>
                  <td align="center">0</td>
                  <td align="center">4</td>
                  <td align="center">16</td>
                  <td align="center">0</td>
                </tr>
                <tr>
                  <td align="center">领星FBA</td>
                  <td align="center">新品</td>
                  <td align="center">/</td>
                  <td align="center">/</td>
                  <td align="center">
                    /
                  </td>
                  <td align="center">6</td>
                  <td align="center">12</td>
                  <td align="center">24</td>
                  <td align="center">/</td>
                </tr>
                <tr>
                  <td align="center">汇总</td>
                  <td align="center">新品</td>
                  <td align="center">27</td>
                  <td align="center">3</td>
                  <td align="center">
                    /
                  </td>
                  <td align="center">9</td>
                  <td align="center">16</td>
                  <td align="center">31</td>
                  <td align="center">0</td>
                </tr>
              </tbody>
            </table>
            </div>
          </>
        );
      }
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   align: 'center',
    //   width: 220,
    //   render: (v, record) => {
    //     return (
    //       <Space>
    //           <a onClick={()=>{alert("审核通过")}}>销售审核</a>
    //       </Space>
    //     );
    //   }
    // }
  ];

  // const tabs: TabsItem[] = [
  //   { key: 'all', tab: intl.formatMessage({ id: 'order.state.ALL' }) },
  // ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [activeStatusKey]);

  const tabs: TabsItem[] = [
    { key: 'all', tab: '全部' },
    { key: 'wait', tab: '待审核' },
    { key: 'up', tab: '已挂起' },
    { key: 'del', tab: '作废' },
    { key: 'com', tab: '已生成' }
  ];
  const expandedRowRender = () => {
    const columns = [
      {
        title: 'Date',
        dataIndex: 'date',
        key: 'date',
      },
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: 'Status',
        key: 'state',
        render: () => (
          <span>
            <Badge status="success" />
            Finished
          </span>
        ),
      },
      {
        title: 'Upgrade Status',
        dataIndex: 'upgradeNum',
        key: 'upgradeNum',
      }
    ];
    // console.log(this.record)
    const data = [];

    for (let i = 0; i < 3; ++i) {
      data.push({
        key: i.toString(),
        date: '2014-12-24 23:12:00',
        name: 'This is production name',
        upgradeNum: 'Upgraded: 56',
      });
    }

    return <Table columns={columns} dataSource={data} pagination={false} />;
  };

  return (
    <>
      <CustomPage<PurchasePlanGoods>
        actionRef={actionRef}
        rowKey="id"
        request={fetchList}
        columns={columns}
        // expandable={{
        //   expandedRowRender,
        //   defaultExpandedRowKeys: ['item'],
        //   defaultExpandAllRows: false
        // }}
        toolBarRender={() => [
          <Button key="level" type="primary" onClick={() => audit(selectedRowsState, "10")}>
            批量挂起
          </Button>,
          <Button key="level" type="primary" onClick={() => audit(selectedRowsState, "20")}>
            审核通过
          </Button>
        ]}
        tableRender={(tableProps, defaultDom) => {
          return (
            <TableTabs tabs={tabs} activeKey={activeStatusKey} onTabClick={setActiveStatusKey}>
              {defaultDom}
            </TableTabs>
          );
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        recordCreator={false}
        recordUpdater={false}
        recordDelete={false}
      />
      <CreatePurchasePlanModal visible={modalVisible} onCancel={() => setModalVisible(false)} />
    </>
  );
};

export default TableList;
