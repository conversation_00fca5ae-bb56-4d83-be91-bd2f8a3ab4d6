import { batchConfirmThePayment } from "@/modules/purchaseOrder/infra/api/purchaseOrder";
import type { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import { ProTable } from "@ant-design/pro-components";
import type { ProColumns } from '@ant-design/pro-table';
import { Modal, message } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';

type ModalProps = {
  reload?: any
}

const Index = forwardRef((props: ModalProps, ref) => {
  const { reload } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [orderData, setOrderData] = useState<PurchaseOrder[]>([]);
  const onCancel = async () => {
    setOpen(false);
    setLoading(false);
  };

  const onOk = async () => {
    setLoading(true);
    const res = await batchConfirmThePayment({ ids: orderData?.map(v => v?.id) });
    setLoading(false);
    if (res.status.success) {
      message.success('操作成功');
      setOpen(false);
      reload();
    }
  };
  useImperativeHandle(ref, () => ({
    open: async (params: { orderData: PurchaseOrder[] }) => {
      setOpen(true);
      setOrderData(params?.orderData);
    },
    close: () => onCancel(),
  }));

  const columns: ProColumns<PurchaseOrder>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.title}</span>;
      }
    },
    {
      title: '采购/平台单号',
      dataIndex: 'orderCode',
      width: 200,
      render: (v, record) => {
        return <><div>{record?.orderCode}</div><div>{record?.platformOrderCode}</div></>;
      }
    },
    {
      title: '采购员/跟单员',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span style={{ fontSize: 12 }}>{record?.purchaseUsername}/{record?.merchandiserUsername}</span>;
      }
    },
    {
      title: '收款人',
      dataIndex: 'purchaseUsername',
      render: (v, record) => {
        return <span>{record?.accountName || '--'}</span>;
      }
    },
    {
      title: '开户行',
      dataIndex: 'bankName',
    },
    {
      title: '开户名',
      dataIndex: 'accountName',
    },
    {
      title: '开户账号',
      dataIndex: 'account',
    },
    {
      title: '采购总额',
      dataIndex: 'purchaseUsername',
      render: (v, record) => record.shippingFee + record.totalPrice + record.applyRefundAmount - record.applyPromotionAmount
    },
    {
      title: '账期应付',
      dataIndex: 'periodAmount',
      render: (v, record) => record?.periodAmount?.toFixed(2),
    },
    {
      title: '申请付款总额',
      dataIndex: 'platformOrderAmount',
    },
  ];


  return (
    <Modal
      title="批量手工单付款"
      visible={open}
      onCancel={onCancel}
      confirmLoading={loading}
      width="80%"
      onOk={onOk}
    >
      <div>
        {/* @ts-ignore */}
        <ProTable
          dataSource={orderData}
          scroll={{ y: 550 }}
          columns={columns as any}
          rowKey="id"
          pagination={false}
          search={false}
        />
      </div>
    </Modal>
  )
})
export default Index;