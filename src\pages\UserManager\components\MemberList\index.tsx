import ProCard from '@ant-design/pro-card';
import { useMemo, useState } from 'react';
import UserList from '../UserList';
import DepartmentTreeComponent from './components/DepartmentTreeComponent';
import styles from './styles.less'

function MemberList() {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const deptId = useMemo(() => {
    const currentDeptID =  selectedKeys.length ? selectedKeys[0] : undefined;
    return currentDeptID !== '0' ? currentDeptID : undefined;
  }, [selectedKeys])

  return (
    <ProCard bodyStyle={{ padding: 0, alignItems: 'stretch' }} >
      <ProCard colSpan="300px" className={styles.tree} bordered>
        <DepartmentTreeComponent value={selectedKeys} onChange={setSelectedKeys} />
      </ProCard>
      <ProCard>
        <UserList deptId={deptId} />
      </ProCard>
    </ProCard>
  );
}

export default MemberList;
