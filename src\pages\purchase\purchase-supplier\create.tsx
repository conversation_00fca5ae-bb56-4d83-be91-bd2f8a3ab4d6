import CommonForm from '@/components/Common-UI/CommonForm';
import { useFormChild } from '@/components/Common-UI/CommonForm/hook';
import type { CommonFormAction, CommonFormLine } from '@/components/Common-UI/CommonForm/type';
import NationSelect from '@/components/common/NationSelect';
import { useCurrency } from '@/modules/currency/application/currency';
import { FooterToolbar } from '@ant-design/pro-layout';
import { Button, Card, message } from 'antd';
import { useEffect, useMemo, useRef } from 'react';
import { history, useParams } from 'umi';
import type { Detail } from './api';
import { PayTypeMap, SettleCircleTypeMap, SettleTypeMap } from './api';
import { usePurchaseSupplierInfo, usePurchaseSupplier } from './purchaseSupplier';
const Index = () => {
  const { id } = useParams<{ id: string }>();
  const setChild = useFormChild();
  const { currencyMap } = useCurrency();
  const { venderDetail } = usePurchaseSupplierInfo();
  const { editApi, addApi } = usePurchaseSupplier();
  const basicFormRef = useRef<CommonFormAction>();
  const settlementFormRef = useRef<CommonFormAction>();
  //  供应商详情
  const venderDetailData: Detail = venderDetail?.data as Detail;
  // 基本信息
  const basicColumns = useMemo<CommonFormLine[]>(() => {
    return [
      setChild('供应商名称', 'venderName', {
        placeholder: '请输入',
        colProps: { span: 8 },
        wrapperCol: { span: 20 },
        maxLength: 50,
        rules: [{
          pattern: /^[\u4e00-\u9fa5A-Za-z0-9]+$/,
          message: '仅支持输入中英文和数字',
        }, {
          required: true,
          message: '请补充完整信息',
        }]
      }),
      setChild('供应商编码', 'venderCode', {
        placeholder: '请输入',
        colProps: { span: 16 },
        wrapperCol: { span: 10 },
        required: true,
        maxLength: 50,
      }),
      setChild('联系人', 'contractor', {
        placeholder: '请输入',
        colProps: { span: 8 },
        wrapperCol: { span: 20 },
        maxLength: 50,
        rules: [{
          pattern: /^[\u4e00-\u9fa5A-Za-z0-9]+$/,
          message: '仅支持输入中英文和数字',
        }, {
          required: true,
          message: '请补充完整信息',
        }]
      }),
      setChild('联系人电话/手机', 'cellphone', {
        placeholder: '请输入',
        colProps: { span: 16 },
        wrapperCol: { span: 10 },
        maxLength: 50,
        rules: [{
          pattern: /^[0-9!@#$%^&*()_+={}\[\]:;"',.<>?~-]*$/,
          message: '仅支持输入数字和特殊字符',
        }, {
          required: true,
          message: '请补充完整信息',
        }]
      }),
      setChild('联系人地址', 'nationName', {
        placeholder: '请输入',
        colProps: { span: 8 },
        wrapperCol: { span: 20 },
        style: {
          width: '33%'
        },
        rules: [{
          required: true,
          message: '请补充完整信息',
        }],
        render: () => <NationSelect />,
      }),
      setChild('联系人邮箱', 'email', {
        placeholder: '请输入',
        colProps: { span: 8 },
        wrapperCol: { span: 20 },
        required: false,
        maxLength: 50,
      }),
      setChild('', 'detail', {
        type: 'ProFormTextArea',
        placeholder: '请输入详细地址',
        colProps: { span: 16 },
        wrapperCol: { span: 10 },
        maxLength: 200,
        rules: [{
          required: true,
          message: '请补充完整信息',
        }]
      }),
    ];
  }, [setChild]);
  // 结算资料
  const settlementColumns = useMemo<CommonFormLine[]>(() => {
    return [
      setChild('结算类型', 'settleType', {
        placeholder: '请选择',
        colProps: { span: 8 },
        wrapperCol: { span: 20 },
        required: true,
        valueEnum: SettleTypeMap
      }),
      setChild('结算周期', 'settleCircle', {
        placeholder: '请选择',
        colProps: { span: 16 },
        wrapperCol: { span: 10 },
        required: true,
        valueEnum: SettleCircleTypeMap,
        // formItemProps: {
        //   tooltip: "按交货时间统计应付金额"
        // }
      }),
      setChild('结算币种', 'settleCurrency', {
        placeholder: '请选择',
        colProps: { span: 8 },
        wrapperCol: { span: 20 },
        required: true,
        valueEnum: currencyMap,
      }),
      setChild('支付方式', 'payType', {
        placeholder: '请选择',
        colProps: { span: 16 },
        wrapperCol: { span: 10 },
        required: true,
        valueEnum: PayTypeMap
      })
    ];
  }, [setChild]);
  // 保存
  const submit = async () => {
    const basicFormValues = await basicFormRef?.current?.actionRef?.getFieldsValue();
    const settlementValues = await settlementFormRef?.current?.actionRef?.getFieldsValue();
    const {
      venderName,
      venderCode,
      contractor,
      cellphone,
      nationName,
      email,
      detail,
    } = basicFormValues;

    const {
      settleType,
      settleCircle,
      settleCurrency,
      payType,
    } = settlementValues;
    if (!venderName || !venderCode || !contractor || !cellphone || !nationName || !detail) {
      message.error('基本资料：供应商名称、供应商编码、联系人、联系人电话/手机、联系人地址、详细地址不允许为空！');
      return;
    }
    if (!settleType || !settleCircle || !settleCurrency || !payType) {
      message.error('结算资料：结算类型、结算周期、结算币种、支付方式！');
      return;
    }
    const params = {
      venderId: id,
      venderName,
      venderCode,
      contractor,
      cellphone,
      nationName,
      email,
      detail,
      settleType,
      settleCircle,
      settleCurrency,
      payType,
    }
    await (id ? editApi : addApi)(params);
  }

  // 修改 -- 获取详情
  useEffect(() => {
    if (!id) return;
    venderDetail?.run(id);
  }, [id]);

  // 修改 -- 回显详情
  useEffect(() => {
    if (!venderDetailData || !venderDetailData?.venderCode) return;
    const {
      // 基础资料
      venderName,
      venderCode,
      contractor,
      cellphone,
      nationName,
      email,
      detail,
      // 结算资料
      settleType,
      settleCircle,
      settleCurrency,
      payType,
    } = venderDetailData;
    basicFormRef?.current?.setFieldsValue({
      venderName,
      venderCode,
      contractor,
      cellphone,
      nationName,
      email,
      detail,
    });
    settlementFormRef?.current?.setFieldsValue({
      settleType,
      settleCircle,
      settleCurrency,
      payType,
    })
  }, [venderDetailData]);

  return (
    <>
      <Card title={'基本资料'}>
        <CommonForm layout='vertical' commonRef={basicFormRef} columns={basicColumns} showBtn={false} grid autoFocusFirstInput={false} />
      </Card>
      <Card title={'结算资料'} style={{ marginTop: 16 }}>
        <CommonForm layout='vertical' commonRef={settlementFormRef} columns={settlementColumns} showBtn={false} grid autoFocusFirstInput={false} />
      </Card>
      <FooterToolbar>
        {/* <Button onClick={() => history?.push('/purchase/purchase/purchase-supplier/list')}> */}
        <Button onClick={() => history?.goBack()}>
          取消
        </Button>
        <Button type="primary" onClick={submit}>
          保存
        </Button>
      </FooterToolbar>
    </>
  )
}
export default Index;
