import ProCard from '@ant-design/pro-card';
import React, {useEffect, useState} from "react";
import {useRequest} from "ahooks";
import { Input, List, Pagination, Space} from 'antd';
import AdviceList from "@/pages/purchaseAdvice/components/adviceList";
import {getPurchaseAdviceBatchList} from "@/modules/purchaseAdvice/infra/api/purchaseAdvice";
import {PurchaseAdviceBatch} from "@/pages/purchaseAdvice/data";

const PurchaseAdvice = () => {
  const [currentRow, setCurrentRow] = useState<PurchaseAdviceBatch>();
  const [page, setPage] = useState<number>(1);
  const [supplierSearchName, setSupplierSearchName] = useState<string>();
  const pageSize = 25;

  const { data, loading, refresh} = useRequest(() =>
    getPurchaseAdviceBatchList({ pageCondition: { pageNum: page, pageSize: pageSize }, batchCode: supplierSearchName }).then(
      (res) => res.body,
    ),
  );

  useEffect(() => {
    refresh();
  }, [page]);

  return (
    <>
      <ProCard
        headerBordered
        bodyStyle={{ padding: 0,  }}
        bordered={false}
      >
        <ProCard
          colSpan="200px"
          bordered
          bodyStyle={{
            padding: 0,
            overflowY: 'auto',
            // height: 'calc(100vh - 48px - 42px - 48px - 42px - 2px - 48px - 48px)',
          }}
          title={
            <Space direction="vertical">
              <Input.Search
                placeholder="批次号"
                value={supplierSearchName}
                onChange={(e) => setSupplierSearchName(e.target.value)}
                onSearch={() => refresh()}
              />
            </Space>
          }
          actions={[
            <Pagination
              key="pagination"
              defaultCurrent={1}
              total={data?.pageMeta?.total || 0}
              current={data?.pageMeta?.pageNum || 1}
              pageSize={pageSize}
              simple
              onChange={(p) => setPage(p)}
            />,
          ]}
        >
          <List
            style={{ borderTop: '1px solid #f0f0f0', marginTop: 24 }}
            loading={loading}
            pagination={false}
            dataSource={data?.items || []}
            bordered={false}
            renderItem={(item) => {
              return (
                <List.Item
                  key={item?.batchCode}
                  style={{
                    padding: 3,
                    backgroundColor: item.batchCode === currentRow?.batchCode ? 'rgb(230, 247, 255)' : '',
                  }}
                  onClick={() => setCurrentRow(item)}
                >
                  {item?.batchCode}
                </List.Item>
              );
            }}
          />
        </ProCard>
          <ProCard
              headerBordered
              headStyle={{ width: '100%', display: 'block', paddingBottom: 0 }}
              bodyStyle={{
                padding: 0,
                overflowY: 'auto',
                height: 'calc(150vh - 48px - 42px - 48px - 2px - 42px - 120px)',
              }}
            >
            <AdviceList adviceBatch={currentRow}/>
        </ProCard>
      </ProCard>
    </>
  );
};

export default PurchaseAdvice;
