import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import {backgroupTask} from "@/pages/setting/task/data";

//分页search参数
export type PageQueryParams = {
} & API.QueryPageParams;


//
export async function pageQueryTask(data?: PageQueryParams) {
  return mallRequest<API.ApiQueryPageResult<backgroupTask[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/setting/task/pageQuery',
    data,
  });
}
