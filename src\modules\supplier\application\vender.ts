import {useRequest} from 'ahooks';
import {message, Modal} from 'antd';
import {useEffect} from 'react';
import {history} from 'umi';
import type {CreateVenderParams} from '../infra/api/vender';
import {
  createVender,
  getAll,
  getVenderInfo,
  getVenderList,
  updateVender,
  venderActive,
  venderDisable
} from '../infra/api/vender';
import {useRequestTable} from "@/hooks/useRequestTable";

// export function useVenderList() {
//   const actionRef = useRef<ActionType>();
//   const fetchList = usePageQueryRequest(getVenderList);
//   return {
//     actionRef,
//     fetchList,
//   };
// }

export function useVender(venderId: string) {
  const venderList = useRequestTable((params) => {
    return getVenderList({
      ...params,
      venderId: venderId === '' ? undefined : venderId,
    });
  });

  const create = async (params: CreateVenderParams) => {
    const res = await createVender(params);
    if (res.status.success) {
      message.success('创建成功');
      history.goBack();
      venderList.actionRef.current?.reload();
    } else {
      message.error('操作失败');
    }
  };

  const update = async (params: CreateVenderParams) => {
    const res = await updateVender(params);
    if (res.status.success) {
      message.success('更新成功');
      venderList.actionRef.current?.reload();
    } else {
      message.error('更新失败');
    }
    return res.status.success;
  };

  const disable = async (venderId: string) => {
    Modal.confirm({
      title: '确认将该供应商状态改为禁用?',
      onOk: async () => {
        try {
          await venderDisable({ venderId });
          message.success('操作成功');
          venderList.actionRef?.current?.reload?.();
        } catch (error) {
          message.error('操作失败');
        }
      },
    });
  };

  const active = async (venderId: string) => {
    Modal.confirm({
      title: '确认将该供应商状态改为启用?',
      onOk: async () => {
        try {
          await venderActive({ venderId });
          message.success('操作成功');
          venderList.actionRef?.current?.reload?.();
        } catch (error) {
          message.error('操作失败');
        }
      },
    });
  };

  return {
    venderList,
    disable,
    active,
    create,
    update,
  };
}

export function useVenderInfo(venderId: string) {
  const {
    data: venderDetail,
    loading,
    refresh: reloadVenderInfo,
  } = useRequest(() => getVenderInfo({ venderId }).then((res) => res.body), { manual: true });
  useEffect(() => {
    if (venderId) {
      reloadVenderInfo();
    }
  }, [venderId]);
  return {
    venderDetail,
    loading,
    reloadVenderInfo,
  };
}

export function useAllVenderList() {
  const {
    data: allVenderList,
    loading,
    refresh: loadAllVenderList,
  } = useRequest(() => getAll().then((res) => res.body));
  return {
    allVenderList,
    loading,
    loadAllVenderList,
  };
}
