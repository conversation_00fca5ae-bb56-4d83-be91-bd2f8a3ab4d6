import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import { ProductSpecData } from '../../domain/categorySpec';

export interface CategoryData {
  categoryId: string;
  isLeaf: boolean;
  level: number;
  name: string;
  parentCategoryId: string;
}

export type CategorySpecData = {
  name: string;
  required: false;
  specId: string;
  values: string[];
};

export type QueryAllCategorySpec = {
  current: ProductSpecData[];
  parent: ProductSpecData[];
};

// 查询所有规格
export async function getAllCategorySpec(params: { categoryId: string }) {
  return mallRequest<API.ApiBaseResult<QueryAllCategorySpec>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/purchase-center/spec/queryByCategoryId',
      method: 'GET',
      params,
    },
  );
}

// 查询所有规格
export async function getAllUserCustomSpec(params: { categoryId: string }) {
  return mallRequest<API.ApiBaseResult<QueryAllCategorySpec>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/category/spec/user-custom/queryAll',
      method: 'GET',
      params,
    },
  );
}

// 获取规格值
export async function getUserCustomValueBySpecId(params: { specId: string }) {
  return mallRequest<API.ApiBaseResult<string[]>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/operation-mgmt-biz/category/spec/user-custom/getValues',
      method: 'GET',
      params,
    },
  );
}

export type CreateUserCustomCategorySpecParams = {
  categoryId: string;
  name: string;
  values: string[];
};

// 创建规格
export async function createUserCustomCategorySpec(data: CreateUserCustomCategorySpecParams) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/category/spec/user-custom/create',
    method: 'POST',
    data,
  });
}

export type AddUserCustomCategoryValueParams = {
  specId: string;
  value: string;
};

// 添加规格值
export async function addUserCustomCategorySpecValue(data: AddUserCustomCategoryValueParams) {
  return mallRequest<API.ApiBaseResult<QueryAllCategorySpec>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/operation-mgmt-biz/category/spec/user-custom/addValue',
      method: 'POST',
      data,
    },
  );
}
