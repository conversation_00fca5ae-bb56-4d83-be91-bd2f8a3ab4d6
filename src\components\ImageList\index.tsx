import { Modal } from 'antd';
import UploadList from 'antd/es/upload/UploadList';
import type { ReactNode } from 'react';
import { useState } from 'react';
import {UploadFile} from "antd/es/upload/interface";

export interface PreviewImageListProps {
  imageList: string[];
  onRemove: (file: UploadFile) => void;
  empty?: ReactNode;
}

const PreviewImageList = (props: PreviewImageListProps) => {
  const { imageList = [], empty, onRemove } = props;
  const [previewImage, setPreviewImage] = useState('');
  if (!imageList || !imageList.length) {
    return <>{empty}</>;
  }
  return (
    <>
      <UploadList
        locale={{ previewFile: '预览图片' }}
        showDownloadIcon={false}
        onPreview={(file) => {
          setPreviewImage(file.url as string);
        }}
        listType="picture-card"
        showRemoveIcon={true}
        onRemove={(file)=>onRemove(file)}
        items={imageList.map((item) => ({
          uid: item, // 注意，这个uid一定不能少，否则上传失败
          name: item,
          status: 'done',
          url: item,
          percent: 100, // 注意不要写100。100表示上传完成
          isCanDel: false,
        }))}
      />
      <Modal
        visible={!!previewImage}
        footer={null}
        onCancel={() => setPreviewImage('')}
        bodyStyle={{ padding: 0 }}
      >
        <img style={{ width: '100%' }} alt="" src={previewImage} />
      </Modal>
    </>
  );
};

export default PreviewImageList;
