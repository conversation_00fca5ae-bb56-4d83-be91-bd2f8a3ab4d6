import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "@/../config/mallApiConfig";

// 1 采购单分页查询
export type QueryOrderListParams = {
  purchaseOrderCode?: string,
  purchaseOrderStatus?: string,
  purchaseOrderCodeList?: any[],
  gmtCreateStartTime?: number,
  gmtCreateEndTime?: number,
  supplierId?: string,
  purchaseWarehouse?: string,
  purchaseEntity?: string,
  purchaseAuditStatus?: 0 | 1,
  orderBy?: {
    filed: string,
    direction: number
  },
  pageCondition: {
    pageNum: number,
    pageSize: number
  }
}
export type OrderItem = {
  id: number,
  purchaseOrderCode: string,
  supplierName: string,
  purchaseEntity: string,
  purchaseWarehouse: string,
  warehouseDetails: string,
  totalAmount: string,
  purchaseOrderStatus: number,
  auditStatus: string,
  receivingInfo: string,
  estimatedTimeArrival: number,
  remark: string,
  purchaseUserName: string,
  gmtCreate: number
}
export function queryOrderList(data: QueryOrderListParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<OrderItem[]>>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    requestPath: '/operation-mgmt-biz/purchase/purchaseOrder/pageQuery',
    method: 'POST',
    data,
  },
  );
}
