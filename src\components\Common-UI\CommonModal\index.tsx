import type { ModalProps } from 'antd';
import { Mo<PERSON>, Spin } from 'antd';
import { useImperativeHandle, useState } from 'react';
import styles from './styles.less';
export type CommonModalAction<T = any> = {
  open: (record?: T) => void;
};
export type ModalAction<T = any> = {
  row: T;
  open: (record?: T, ...parmas: any[]) => void;
  close: () => void;
};
export type CommonModalProps<T> = {
  modalRef?: React.Ref<ModalAction | undefined>;
  onOpen?: (recoed: T, params: any[]) => void;
  onConfirm?: (record: T, params: any[]) => void;
  onClose?: (record: T, params: any[]) => void;
  onReload?: () => void;
} & ModalProps;

function CommonModal<Data = any>(props: CommonModalProps<Data>) {
  const [open, setOpen] = useState(false);
  const [args, setArgs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [row, setRow] = useState<Partial<Data>>({});

  // 1) Cancel
  const onCancel = async () => {
    props.onClose && (await props.onClose(row as Data, args));
    setOpen(false);
    setRow({});
    setLoading(false);
  };

  // 2) Ok
  const onOk = async () => {
    try {
      if (props.onConfirm) {
        // 1 加载状态
        setLoading(true);
        // 2 等待执行
        await props.onConfirm(row as Data, args);
        // 3 执行完成后( 去掉这个 ，避免点击确认马上关闭弹窗)
        // onCancel();
        setLoading(false);
        // 4 一般会更新表格
        props.onReload && props.onReload();
      }
    } catch (error) {
      setLoading(false);
    }
  };

  // 3) Export
  useImperativeHandle(props.modalRef, () => ({
    row,
    open: async (record?: Data, ...params: any[]) => {
      // 1 显示 Modal
      setOpen(true);
      params && setArgs(params);
      // 2 请求接口需要加载
      if (props.onOpen) {
        setLoading(true);
        await props.onOpen(record as Data, params);
      }
      // 3 请求完成或无请求时
      setRow(record || {});
      setLoading(false);
    },
    close: () => onCancel(),
  }));

  return (
    <Modal
      centered
      maskClosable={false}
      wrapClassName={styles.wrapClassName}
      {...props}
      confirmLoading={loading}
      visible={open}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>{props.children}</Spin>
    </Modal>
  );
}

export default CommonModal;
