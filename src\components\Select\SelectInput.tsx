import { ProFormDigitRange } from '@ant-design/pro-form';
import { Input, Row, Select, DatePicker } from 'antd';
import { memo, useCallback, useState } from 'react';
import styled from 'styled-components';

const { RangePicker } = DatePicker;

const StyleWrapper = styled.div`
  flex: 1;

  .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-control {
      display: block !important;
      margin-bottom: 0 !important;
    }
  }
`;

const SelectInput = memo((props: Record<keyof any, any>) => {
  const { fixedInputVal } = props;
  const [disabled, setDisabled] = useState(false);
  const onChange = useCallback(
    (val: any, type: string) => {
      // 1 获取旧值
      const oldV = props.value;
      // 2 根据类型设置新值
      switch (type) {
        case 'select':
          // 固定值
          setDisabled(false);
          if (fixedInputVal) {
            const [key, fixedVal] = fixedInputVal;
            if (key === val) {
              props.onChange([val, fixedVal]);
              setDisabled(true);
            } else {
              props.onChange([val, undefined]);
            }
          } else {
            props.onChange([val, undefined]);
          }
          break;
        case 'input':
          if (val.trim().length === 0) {
            props.onChange([oldV?.[0], undefined]);
            return;
          }
          props.onChange([oldV?.[0] || props.options[0]?.value, val]);
          break;
        case 'time':
          if (!val) {
            props.onChange(undefined);
            return;
          }
          props.onChange([oldV?.[0] || props.options[0]?.value, val]);
          break;
      }
    },
    [fixedInputVal, props],
  );

  return (
    <Row wrap={false}>
      <Select
        defaultValue={props.options[0]?.value || ''}
        onChange={(val) => onChange(val, 'select')}
        style={{ width: Number(props.selectWidth) || 120 }}
        options={props.options || []}
      />
      {props.valueType === 'RangePicker' && (
        <RangePicker value={props.value?.[1]} picker="date" showTime onChange={(val) => onChange(val, 'time')} />
      )}
      {props.valueType === 'DigitRange' && (
        <StyleWrapper>
          <ProFormDigitRange
            fieldProps={{ value: props.value?.[1], onChange: (val) => onChange(val, 'time'), min: 0, ...props.inputProps }}
          />
        </StyleWrapper>
      )}
      {props.valueType === undefined && (
        <Input
          value={props.value?.[1]}
          disabled={disabled}
          placeholder={props.placeholder}
          onChange={(e) => onChange(e.target.value, 'input')}
          allowClear
        />
      )}
    </Row>
  );
});

export default SelectInput;

// 解析值
export const solveSelect = (value: any, obj: any, type: 'common' | 'batch' | 'time' | 'digit' = 'common') => {
  // 1 校验
  if (!value) return;
  // 2 获取属性和值
  const [key, val] = value;
  // 3 校验
  if (!val) return;
  // 4 赋值
  switch (type) {
    case 'common':
      obj[key] = val;
      break;
    case 'batch':
      obj[key] = val.split(/[,，\s]/).map((v: string) => v.trim());
      break;
    case 'time':
      const startTimeKey = key.split(' ')[0];
      const endTimeKey = key.split(' ')[1];
      obj[startTimeKey] = val[0];
      obj[endTimeKey] = val[1];
      break;
    case 'digit':
      const startKey = key.split(' ')[0];
      const endKey = key.split(' ')[1];
      if (val[0] && val[1]) {
        obj[startKey] = val[0];
        obj[endKey] = val[1];
      }
      break;
  }
};
