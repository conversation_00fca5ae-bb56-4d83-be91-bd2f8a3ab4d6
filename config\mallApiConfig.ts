export interface MallApiConfigData {
  userCenterApiGatewayUrl: string;
  commonApiGatewayUrl: string;
  staticSecretKey: string;
  currTerminal: string;
  purchaseCenterApiGatewayUrl: string;
  unifyApiGatewayUrl: string;
}

const mallApiConfig: { [key: string]: MallApiConfigData } = {
  fat: {
    // userCenterApiGatewayUrl: 'http://api-gsp-uc-fat.newnary.cn',
    // userCenterApiGatewayUrl: 'http://192.168.1.117:3936',
    // commonApiGatewayUrl: 'http://192.168.1.117:3938',
    // purchaseCenterApiGatewayUrl: 'http://10.23.16.17:3100',
    commonApiGatewayUrl: 'http://api-gsp-common-fat.newnary.cn',
    userCenterApiGatewayUrl: 'http://192.168.1.235:8220',
    purchaseCenterApiGatewayUrl: 'http://192.168.1.235:8222',
    staticSecretKey: 'IdZGQTOZ4Zh8H64uXKkFL6KMp6tawm0I',
    currTerminal: 'PURCHASE_MGMT',
    unifyApiGatewayUrl: 'http://api-fat.newnary.cn'
  },
  // 融合采购系统-测试环境
  '2bFat': {
    userCenterApiGatewayUrl: 'http://api-gsp-uc-fat.newnary.cn',
    commonApiGatewayUrl: 'http://api-gsp-common-fat.newnary.cn',
    purchaseCenterApiGatewayUrl: 'http://10.23.16.17:3100',
    staticSecretKey: 'IdZGQTOZ4Zh8H64uXKkFL6KMp6tawm0I',
    currTerminal: 'PURCHASE_MGMT',
    unifyApiGatewayUrl: 'http://api-fat.newnary.cn'
  },
   // 融合采购系统-正式环境
   '2bPro': {
    userCenterApiGatewayUrl: 'http://api-gsp-uc.newnary.cn',
    purchaseCenterApiGatewayUrl: 'http://api-gsp-op.newnary.cn',
    commonApiGatewayUrl: 'http://api-gsp-common.newnary.cn',
    staticSecretKey: 'IdZGQTOZ4Zh8H64uXKkFL6KMp6tawm0I',
    currTerminal: 'PURCHASE_MGMT',
    unifyApiGatewayUrl: 'http://api-pro.newnary.cn'
  },
  pro: {
    userCenterApiGatewayUrl: location.protocol + "//" + location.hostname + ':8220',
    commonApiGatewayUrl: location.protocol + "//" + location.hostname + ':8222',
    purchaseCenterApiGatewayUrl: location.protocol + "//" + location.hostname + ':8222',
    staticSecretKey: '42yd4TiFUNgqndLzt2CvDM13Qsu0qZ',
    currTerminal: 'PURCHASE_MGMT',
    unifyApiGatewayUrl: 'http://api-pro.newnary.cn'
  },
};

const curApiDomain = mallApiConfig[REACT_APP_ENV || 'fat'];

export const apiDomainMap = {
    [curApiDomain.userCenterApiGatewayUrl]: `${curApiDomain.unifyApiGatewayUrl}/user`,
    [curApiDomain.commonApiGatewayUrl]: `${curApiDomain.unifyApiGatewayUrl}/common`,
    [curApiDomain.purchaseCenterApiGatewayUrl]: `${curApiDomain.unifyApiGatewayUrl}/purchase`,
}

export default mallApiConfig[REACT_APP_ENV || 'fat'] as MallApiConfigData;
