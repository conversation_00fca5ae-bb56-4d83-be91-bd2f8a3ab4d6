import { logOut } from '@/modules/auth/application/auth';
import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import {Avatar, Form, Menu, message, Modal, notification, Space, Spin} from 'antd';
import type { ItemType } from 'antd/lib/menu/hooks/useItems';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, {useCallback, useState} from 'react';
import {Access, history, useModel} from 'umi';
import styles from './index.less';
import PasswordField from "@/pages/UserManager/components/MemberList/components/PasswordField";
import {useUser} from "@/modules/user-center/application/user";
import UpdatePasswordForm from "@/pages/UserManager/components/UserList/components/UpdatePasswordForm";

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

/**
 * 退出登录，并且将当前的 url 保存
 */
const loginOut = async () => {
  logOut();
  const { query = {}, search, pathname } = history.location;
  const { redirect } = query;
  // Note: There may be security issues, please note
  if (window.location.pathname !== '/user/login' && !redirect) {
    history.replace({
      pathname: '/user/login',
      search: stringify({
        redirect: pathname + search,
      }),
    });
  }
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        setInitialState((s) => ({ ...s, currentUser: undefined }));
        loginOut();
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const onOutClick = () =>{
    setInitialState((s) => ({ ...s, currentUser: undefined }));
    loginOut();
  }

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.nickName) {
    return loading;
  }

  const menuItems: ItemType[] = [
    ...(menu
      ? [
        {
          key: 'center',
          icon: <UserOutlined />,
          label: '个人中心',
        },
        {
          key: 'settings',
          icon: <SettingOutlined />,
          label: '个人设置',
        },
        {
          type: 'divider' as const,
        },
      ]
      : []),
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick} items={menuItems} />
  );
  const [pwdModalVisible, setPwdModalVisible] = useState<boolean>(false);
  return (
    // <HeaderDropdown overlay={menuHeaderDropdown}>
    <>
      <span className={`${styles.action} ${styles.account}`}>
        <Avatar size="small" className={styles.avatar} src={currentUser.avatarUrl || "https://img95.699pic.com/xsj/1k/bl/27.jpg%21/fw/700/watermark/url/L3hzai93YXRlcl9kZXRhaWwyLnBuZw/align/southeast"} alt="avatar" />
        <span className={`${styles.name} anticon`} onClick={()=>setPwdModalVisible(true)} >{currentUser.nickName}</span>
        <span style={{color: "white", marginLeft: 10, marginTop: 4}} onClick={onOutClick}><LogoutOutlined /></span>
      </span>
      <Access accessible={pwdModalVisible}>
        <UpdatePasswordForm userId={currentUser?.userId} visible={pwdModalVisible} onVisibleChange={(v)=>setPwdModalVisible(v)}/>
      </Access>
    </>
    // </HeaderDropdown>
  );
};

export default AvatarDropdown;
