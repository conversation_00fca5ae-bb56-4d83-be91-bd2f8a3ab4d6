import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import type {Goods} from '../../domain/goods';
import {SupplierGoods} from "@/modules/supplier/domain/vender";
import {SkuWarehouse} from "@/pages/PurchasePlanList/PurchasePlanTableList/data";

export interface QueryGoodsParams {
  sku?: string;
}

export async function getGoodsList(params: QueryGoodsParams) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Goods[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/sku/pageQuery',
      method: 'POST',
      data: params,
    },
  );
}

/**
 * 查询商品供应商列表
 */
export type queryGoodsSupplierParams = {
  goodsSku: string;
  spuId: string;
};
export async function queryGoodsSupplier(data?: queryGoodsSupplierParams) {
  return mallRequest<API.ApiQueryPageResult<SupplierGoods>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/purchase-mgmt-biz/purchase-center/supplier/goods/pageQuery',
      data,
    },
  );
}



/**
 * 修改商品尺寸
 * @param params
 */
export async function updateGoodsSize(data?: any) {
  return mallRequest<API.ApiBaseResult<API.ApiBasePageResult<Goods[]>>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/spu/updateGoodsSize',
      method: 'POST',
      data,
    },
  );
}

/***
 * 修改供应商商品数据
 * @param data
 */
export async function updateSupplierGoods(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/supplier/goods/updateSupplierGoods',
    data,
  });
}


/***
 * 新增供应商商品数据
 * @param data
 */
export async function createSupplierGoods(data?: any) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/supplier/goods/createSupplierGoods',
    data,
  });
}

/***
 * 删除供应商商品数据
 * @param data
 */
export async function removeSupplierGoods(supplierGoodsId?: string) {
  return mallRequest<API.ApiQueryPageResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/supplier/goods/removeSupplierGoods',
    params: {supplierGoodsId: supplierGoodsId},
  });
}

/***
 * 通过sku跟供应上id请求一行数据
 * @param data
 */

export type getSupplierGoodsParams = {
  supplierId: string;
  sku: string;
};
export async function getSupplierGoods(data: getSupplierGoodsParams) {
  return mallRequest<API.ApiBaseResult<SupplierGoods>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/purchase-center/supplier/goods/bySupplierIdAndSku',
    params: data
  });
}

export type getGoodsSalesStockParams = {
  sku: string;
};
export async function getGoodsSalesStock(data: getGoodsSalesStockParams) {
  return mallRequest<API.ApiBaseResult<SkuWarehouse[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/getGoodsSalesStock',
    params:{sku: data.sku}
  });
}

/**
 * 根据sku获取单个sku信息
 * @param data
 */
export async function getGoodsSkuInfo(data: any) {
  return mallRequest<API.ApiBaseResult<Goods>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/getSkuInfo',
    params:{skuId: data?.skuId}
  });
}

export async function updateGoodsSkuInfo(data: any) {
  return mallRequest<API.ApiBaseResult<Goods>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/update',
    data
  });
}

export async function createGoodsSkuInfo(data: any) {
  return mallRequest<API.ApiBaseResult<Goods>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/create',
    data
  });
}

export async function saveSkuTags(data: any) {
  return mallRequest<API.ApiBaseResult<Goods>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/saveSkuTags',
    data
  });
}


export async function querySkuListBySpuId(spuId: string) {
  return mallRequest<API.ApiBaseResult<Goods[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/querySkuListBySpuId',
    params: {spuId: spuId}
  });
}

export async function querySkuLogBySku(sku: string) {
  return mallRequest<API.ApiBaseResult<any[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method:'post',
    requestPath:'/purchase-mgmt-biz/pd/sku/querySkuLogList',
    params: {sku: sku}
  });
}

/**
 * 获取打印需要的信息
 * @param params
 */
export async function getTagsPrintInfo(params: any) {
  return mallRequest<API.ApiBaseResult<any>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      requestPath: '/purchase-mgmt-biz/pd/sku/getTagsPrintInfo',
      method: 'POST',
      params: params
    },
  );
}



/**
 * 导出商品sku列表
 */
export async function exportSkuAsync(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/pd/sku/exportSkuInfo',
    data,
  });
}

