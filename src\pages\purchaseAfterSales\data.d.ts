export type PurchaseAfterSales = {
  "id": string,
  "afterSalesOrderId": string,
  "title": string,
  "code": string,
  "afterSalesStatus": string,
  "purchaseOrderId": string,
  "purchaseOrderCode": string,
  "afterSalesType": string,
  "remark": string,
  "applyAmount": number,
  "applyGoodsAmount": number,
  "applyShippingFee": number,
  "applyBalance": number,
  "applyUsername": string,
  "applyUid": string,
  "auditUsername": string,
  "auditUid": string,
  "gmtCreate": number,
  "gmtModified": string,
  "amountReceived": number,
  "orderGoods": PurchaseAfterSalesGoods
}

export type PurchaseAfterSalesGoods = {
    "afterSalesOrderId": string,
    "afterSalesQuantity": number,
    "currency": string,
    "currentPurchasePrice": number,
    "gmtCreate": number,
    "gmtModified": number,
    "id": number,
    "isDelete": number,
    "lastPurchasePrice": number,
    "name": string,
    "platformPurchasePrice": number,
    "purchaseQuantity": number,
    "sku": string
}
