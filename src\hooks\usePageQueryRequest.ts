import type { ProTableProps } from '@ant-design/pro-table';
import { omit } from 'lodash';

export type PageQueryRequest = (params: any) => Promise<API.ApiQueryPageResult<any>>;

const usePageQueryRequest = (request: PageQueryRequest) => {
  const fetch: ProTableProps<any, any>['request'] = async (params: any) => {
    const res = await request({
      ...omit(params, ['current', 'pageSize']),
      pageCondition: {
        pageNum: params.current || 1,
        pageSize: params.pageSize || 20,
      },
    });
    return {
      total: res.body?.pageMeta?.total || 0,
      data: res.body?.items || [],
      success: res.status.success,
    };
  };
  return fetch;
};

export default usePageQueryRequest;
