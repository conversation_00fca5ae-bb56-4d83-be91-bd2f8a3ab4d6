import mallRequest from '@/utils/mallRequest';
import mallApiConfig from 'config/mallApiConfig';
import { PurchaseOrder } from "@/pages/PurchaseOrderList/data";
import mallRequest1 from "@/utils/mallRequest1";

//分页search参数
export type PageQueryOrderParams = {
  gmtCreateEndDateTime?: string;
  gmtCreateStartDateTime?: string;
  channelOrderNo?: string;
  orderState?: string;
  distributionOrderId?: string;
} & API.QueryPageParams;

// 采购计划列表
export async function pageQueryOrder(data?: PageQueryOrderParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/pageQuery',
    data,
  });
}



// 采购单详情
export async function getOrderDetail(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'get',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/queryOrderDetail',
    params: {
      orderId,
    },
  });
}


//采购单详情列表
export async function getOrderGoods(orderId?: string) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'get',
    requestPath: '/purchase-mgmt-biz/purchase-center/order/goods/getOrderGoods',
    params: {
      orderId,
    },
  });
}


// 财务审核列表
export async function pageQueryFinanceOrder(data?: PageQueryOrderParams) {
  return mallRequest<API.ApiQueryPageResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/pageQuery',
    data,
  });
}

// 财务审核订单列表
export async function financeAuditQuery(data?: any) {
  return mallRequest<API.ApiBaseResult<PurchaseOrder[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/financeAuditQuery',
    data,
  });
}


// 财务审核
export async function financeAudit(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/financeAudit',
    data,
  });
}


// 付款处理
export async function payHandle(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/payHandle',
    data,
  });
}

// 账期财务审核
export async function periodFinanceAudit(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/periodFinanceAudit',
    data,
  });
}


// 账期付款处理
export async function periodPayHandle(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/periodPayHandle',
    data,
  });
}


// 财务审核
export async function FinanceOrder(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/updateStatus',
    data,
  });
}

//财务批量付款
export async function aliBatchPayment(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/aliBatchPayment',
    data,
  });
}

//账期财务批量付款
export async function periodBatchPayment(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/periodBatchPayment',
    data,
  });
}


//财务付款价格校验
export async function verifyPriceBatch(data?: any) {
  return mallRequest<API.ApiBaseResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/verifyPriceBatch',
    data,
  });
}


//财务导出--同步
export async function exportFinance(data?: any) {
  return mallRequest1<API.ApiBaseResult<String>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/exportFinanceData',
    data,
    responseType: 'blob',
  });
}
//财务导出--异步
export async function exportFinanceAsync(data?: any) {
  return mallRequest<API.ApiQueryPageResult<string>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/finance/exportFinanceDataTask',
    data,
  });
}
export async function financeAnalysis() {
  return mallRequest<API.ApiBaseResult<any>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/purchase-mgmt-biz/purchase-center/data-center/financeDataCount',
  });
}


