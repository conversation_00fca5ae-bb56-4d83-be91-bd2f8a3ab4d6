

import { message, Modal } from 'antd';
import { activeUser, createUser, CreateUserParams, disableUser, updateUser, UpdateUserParams, updateUserPassword, UpdateUserPasswordParams } from '../infra/user';


export function useUser() {
  const disable = (userId: string) => {
    return new Promise<boolean>((resolve, reject) => {
      Modal.confirm({
        title: '确认将该用户状态改为禁用?',
        onOk: async () => {
          try {
            const res = await disableUser({ userId });
            if (res.status.success) {
              message.success('操作成功');
            } else {
              message.error('操作失败');
            }
            resolve(res.status.success);
          } catch (error) {
            message.error('操作失败');
            reject(false);
          }
        },
      });
    });
  };

  const active = (userId: string) => {
    return new Promise<boolean>((resolve, reject) => {
      Modal.confirm({
        title: '确认将该用户状态改为启用?',
        onOk: async () => {
          try {
            const res = await activeUser({ userId });
            if (res.status.success) {
              message.success('操作成功');
            } else {
              message.error('操作失败');
            }
            resolve(res.status.success);
          } catch (error) {
            message.error('操作失败');
            reject(false);
          }
        },
      });
    });
  };

  const create = async (user: CreateUserParams) => {
    try {
      const res = await createUser(user);
      if (res.status.success) {
        message.success('创建成功');
      } else {
        message.error('创建失败，请稍后重试');
      }

      return res.status.success;
    } catch (error) {
      message.error('创建失败，请稍后重试');
      return false;
    }
  };

  const update = async (user: UpdateUserParams) => {
    try {
      const res = await updateUser(user);
      if (res.status.success) {
        message.success('更新成功');
      } else {
        message.error('更新失败，请稍后重试');
      }

      return res.status.success;
    } catch (error) {
      message.error('更新失败，请稍后重试');
      return false;
    }
  };

  const updatePassword = async (params: UpdateUserPasswordParams) => {
    try {
      const res = await updateUserPassword(params);
      if (res.status.success) {
        message.success('更改成功');
      } else {
        message.error('更改失败');
      }
      return res.status.success;
    } catch (error) {
      message.error('更改失败，请稍后重试');
      return false;
    }
  };

  return {
    disable,
    active,
    create,
    updatePassword,
    update
  };
}
