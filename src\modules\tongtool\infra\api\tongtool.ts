//分页search参数
import mallRequest from "@/utils/mallRequest";
import mallApiConfig from "../../../../../config/mallApiConfig";
import {TongtoolGoodsSales, TongtoolGoodsStock} from "@/pages/tongtool/data";
import {SupplierGoods} from "@/modules/supplier/domain/vender";

export type PageQueryGoodsStockParams = {
  sku?: string;
  warehouseName?: string;
} & API.QueryPageParams;

//
export async function pageQueryGoodsStock(data?: PageQueryGoodsStockParams) {
  return mallRequest<API.ApiQueryPageResult<TongtoolGoodsStock[]>>(mallApiConfig.purchaseCenterApiGatewayUrl, {
    method: 'POST',
    requestPath: '/sales-mgmt-biz/sales-center/tongtool/goodsStockPageQuery',
    data,
  });
}

/**
 * 查询通途商品销量
 */
export type queryGoodsSalesParams = {
  goodsSku?: string;
} & API.QueryPageParams;
export async function pageQueryGoodsSales(data?: queryGoodsSalesParams) {
  return mallRequest<API.ApiQueryPageResult<TongtoolGoodsSales>>(
    mallApiConfig.purchaseCenterApiGatewayUrl,
    {
      method: 'POST',
      requestPath: '/sales-mgmt-biz/sales-center/tongtool/goodsSalesPageQuery',
      data,
    },
  );
}
