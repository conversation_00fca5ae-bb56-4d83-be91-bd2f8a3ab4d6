import { Select } from 'antd';
import { memo, useEffect, useState } from 'react';
import { queryCountryList } from './api';
import useLocael from '@/hooks/useLocael';
import type { SelectProps } from 'antd';

const SelectCountry = memo((props: SelectProps) => {
  const [selectOption, setSelectOption] = useState<any[]>([]);
  const { locale, $t } = useLocael();

  useEffect(() => {
    queryCountryList().then((res) => {
      const { body } = res;
      setSelectOption(
        body?.map((v) => (locale === 'en-US' ? { value: v.englishName, label: v.englishName } : { value: v.name, label: v.name })) || [],
      );
    });
  }, [locale]);

  return (
    <Select
      showSearch
      allowClear
      placeholder={$t('请选择国家')}
      optionFilterProp="children"
      // @ts-ignore
      filterOption={(input, option) => (option?.label ?? '').includes(input)}
      // @ts-ignore
      filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
      options={selectOption}
      {...props}
    />
  );
});

export default SelectCountry;
